using System;
using System.Collections.Concurrent;
using System.ComponentModel;
using System.Diagnostics;
using System.Linq;

namespace FengLink.Core.Util;

/// <summary>
///     枚举扩展
/// </summary>
public static class EnumUtil
{
    // 枚举类型缓存
    private static readonly ConcurrentDictionary<string, Type> _enumTypeDict = null!;

    /// <summary>
    /// </summary>
    /// <param name="tField"></param>
    /// <typeparam name="T"></typeparam>
    /// <returns></returns>
    public static string GetEnumDesc<T>(T tField)
    {
        try
        {
            if (tField == null)
                return "";
            if (!Enum.IsDefined(typeof(T), tField))
                return "";
            var description = string.Empty; //结果
            var inputType = tField?.GetType(); //输入的类型
            var descType = typeof(DescriptionAttribute); //目标查找的描述类型

            var fieldStr = tField?.ToString(); //输入的字段字符串
            var field = inputType?.GetField(fieldStr!); //目标字段

            Debug.Assert(field != null, nameof(field) + " != null");
            var isDefined = field.IsDefined(descType, false); //判断描述是否在字段的特性
            if (!isDefined) return description;
            var enumAttributes = (DescriptionAttribute[]) field //得到特性信息
                .GetCustomAttributes(descType, false);
            description = enumAttributes.FirstOrDefault()?.Description ?? string.Empty;
            return description;
        }
        catch (Exception)
        {
            return "";
        }
    }
    
    /// <summary>
    ///     根据属性描述获取枚举值
    /// </summary>
    /// <typeparam name="T">类型</typeparam>
    /// <param name="des">属性说明</param>
    /// <returns>枚举值</returns>
    public static T  GetEnum<T>(string des) where T : struct, IConvertible
    {
        var type = typeof(T);
        if (!type.IsEnum) return default;

        var enums = (T[]) Enum.GetValues(type);
        if (!Enum.TryParse(des, out T temp)) temp = default;

        foreach (var t in enums)
        {
            var name = t.ToString();
            var field = type.GetField(name);
            var objs = field?.GetCustomAttributes(typeof(DescriptionAttribute), false);
            if (objs == null || objs.Length == 0) continue;

            var descriptionAttribute = (DescriptionAttribute) objs[0];
            var edes = descriptionAttribute.Description;
            if (des != edes) continue;
            temp = t;
            break;
        }

        return temp;
    }
}