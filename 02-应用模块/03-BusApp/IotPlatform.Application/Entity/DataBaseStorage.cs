using IotPlatform.Core.Extension;

namespace IotPlatform.Application.Entity;

/// <summary>
///     备份管理-存储管理
/// </summary>
[SugarTable("business_dataBaseStorage", "备份管理-存储管理")]
public class DataBaseStorage : EntityBaseId
{
    /// <summary>
    ///     配置名称
    /// </summary>
    [SugarColumn(ColumnDescription = "配置名称")]
    public string Name { get; set; }

    /// <summary>
    ///     存储类型 1本地存储；2SFTP存储
    /// </summary>
    [SugarColumn(ColumnDescription = "存储类型 1本地存储；2SFTP存储")]
    public DataBaseStorageTypeEnum DataBaseStorageType { get; set; }

    /// <summary>
    ///     存储类型 1本地存储；2SFTP存储
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public string DataBaseStorageTypeName => DataBaseStorageType.GetDescription();

    /// <summary>
    ///     存储策略 1无限制；2数量限制；3容量限制
    /// </summary>
    [SugarColumn(ColumnDescription = "存储策略 1无限制；2数量限制；3容量限制")]
    public DataBaseStoragePolicyEnum DataBaseStoragePolicy { get; set; }

    /// <summary>
    ///     存储策略 1无限制；2数量限制；3容量限制
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public string DataBaseStoragePolicyName => DataBaseStoragePolicy.GetDescription();

    /// <summary>
    ///     数量限制/容量限制 配置
    /// </summary>
    [SugarColumn(IsJson = true, IsNullable = true)]
    public DataBaseStoragePolicyByQuantity Quantity { get; set; }
}

/// <summary>
///     数量限制
/// </summary>
public class DataBaseStoragePolicyByQuantity
{
    /// <summary>
    ///     最大备份数量
    /// </summary>
    public short Max { get; set; }

    /// <summary>
    ///     备份上限 1：删除最早备份；2:停止备份
    /// </summary>
    public DataBaseStorageBackupEnum Backup { get; set; }
}

/// <summary>
///     备份上限 1：删除最早备份；2:停止备份
/// </summary>
public enum DataBaseStorageBackupEnum
{
    /// <summary>
    ///     删除最早备份
    /// </summary>
    [Description("删除最早备份")] Delete = 1,

    /// <summary>
    ///     停止备份
    /// </summary>
    [Description("停止备份")] Stop = 2
}

/// <summary>
///     存储策略 1无限制；2数量限制；3容量限制
/// </summary>
public enum DataBaseStoragePolicyEnum
{
    /// <summary>
    ///     无限制
    /// </summary>
    [Description("无限制")] Non = 1,

    /// <summary>
    ///     数量限制
    /// </summary>
    [Description("数量限制")] Quantity = 2,

    /// <summary>
    ///     容量限制
    /// </summary>
    [Description("容量限制")] Capacity = 3
}

/// <summary>
///     存储类型 1本地存储；2SFTP存储
/// </summary>
public enum DataBaseStorageTypeEnum
{
    /// <summary>
    ///     本地存储
    /// </summary>
    [Description("本地存储")] Local = 1,

    /// <summary>
    ///     SFTP存储
    /// </summary>
    [Description("SFTP存储")] SFtp = 2
}