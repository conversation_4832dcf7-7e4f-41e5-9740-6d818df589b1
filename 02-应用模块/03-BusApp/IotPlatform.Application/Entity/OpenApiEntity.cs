using IotPlatform.Core.Extension;

namespace IotPlatform.Application.Entity;

/// <summary>
///     开放Api
/// </summary>
[SugarTable("business_openApi", "开放Api")]
public class OpenApiEntity : EntityTenantId
{
    /// <summary>
    ///     名称
    /// </summary>
    [SugarColumn(ColumnDescription = "名称", Length = 64)]
    public string Name { get; set; }

    /// <summary>
    ///     Token
    /// </summary>
    [SugarColumn(ColumnDescription = "Token", Length = 256)]
    public string Token { get; set; }

    /// <summary>
    ///     认证模式：1签名认证；2Token认证；3无认证
    /// </summary>
    [SugarColumn(ColumnDescription = "认证模式：1签名认证；2Token认证；3无认证")]
    public AuthorizeTypeEnum AuthorizeType { get; set; }

    /// <summary>
    ///     认证模式：1签名认证；2Token认证；3无认证
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public string AuthorizeTypeName => EnumExtensions.GetDescription(AuthorizeType);

    /// <summary>
    ///     请求协议:1Http;2WebSocket;3WebServer
    /// </summary>
    [SugarColumn(ColumnDescription = "请求协议:1Http;2WebSocket;3WebServer")]
    public RequestProtocolEnum RequestProtocol { get; set; }

    /// <summary>
    ///     请求协议:1Http;2WebSocket;3WebServer
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public string RequestProtocolName => EnumExtensions.GetDescription(RequestProtocol);

    /// <summary>
    ///     请求协议:1WebSocket;2Http
    /// </summary>
    [SugarColumn(ColumnDescription = "请求协议:1WebSocket;2Http")]
    public PushProtocolEnum PushProtocol { get; set; }

    /// <summary>
    ///     请求协议:1WebSocket;2Http
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public string PushProtocolName => EnumExtensions.GetDescription(PushProtocol);

    /// <summary>
    ///     描述
    /// </summary>
    [SugarColumn(ColumnDescription = "描述")]
    public string Desc { get; set; }

    /// <summary>
    ///     创建时间
    /// </summary>
    [SugarColumn(ColumnDescription = "创建时间")]
    public System.DateTime? CreatedTime { get; set; }

    /// <summary>
    ///     开放Api-明细
    /// </summary>
    [Navigate(NavigateType.OneToMany, nameof(Entity.OpenApiDetailEntity.OpenApiEntityId))]
    [SugarColumn(IsIgnore = true)]
    public List<OpenApiDetailEntity> OpenApiDetailEntity { get; set; }
}

/// <summary>
///     认证模式：1签名认证；2Token认证；3无认证
/// </summary>
public enum AuthorizeTypeEnum
{
    /// <summary>
    ///     签名认证
    /// </summary>
    [Description("签名认证")] Sign = 1,

    /// <summary>
    ///     Token
    /// </summary>
    [Description("Token")] Token = 2,

    /// <summary>
    ///     无认证
    /// </summary>
    [Description("无认证")] No = 3
}

/// <summary>
///     请求协议:1Http;2WebSocket;3WebServer
/// </summary>
public enum RequestProtocolEnum
{
    /// <summary>
    ///     HTTP
    /// </summary>
    [Description("HTTP")] Http = 1,

    /// <summary>
    ///     WebSocket
    /// </summary>
    [Description("WebSocket")] WebSocket = 2,

    /// <summary>
    ///     WebServer
    /// </summary>
    [Description("WebServer")] WebServer = 3
}

/// <summary>
///     请求协议:1WebSocket;2Http
/// </summary>
public enum PushProtocolEnum
{
    /// <summary>
    ///     WebSocket
    /// </summary>
    [Description("WebSocket")] WebSocket = 1,

    /// <summary>
    ///     HTTP
    /// </summary>
    [Description("HTTP")] Http = 2
}