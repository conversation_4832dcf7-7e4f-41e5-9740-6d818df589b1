namespace IotPlatform.Application.SeedData;

public class SystemConfigSeedData : ISqlSugarEntitySeedData<SystemConfig>
{
    /// <summary>
    ///     种子数据
    /// </summary>
    /// <returns></returns>
    public IEnumerable<SystemConfig> HasData()
    {
        yield return new SystemConfig
        {
            Id = 327117826257093, Name = "峰回物联网云平台应用系统", Copyright = "Copyright © 2022-present All rights reserved", CreatedUserName = "admin"
        };
    }
}