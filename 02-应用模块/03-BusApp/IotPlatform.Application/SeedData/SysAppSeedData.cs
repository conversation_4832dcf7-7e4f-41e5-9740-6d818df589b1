using Common.Security;

namespace IotPlatform.Application.SeedData;

/// <summary>
///     app
/// </summary>
public class SysAppSeedData : ISqlSugarEntitySeedData<SysService>
{
    /// <summary>
    ///     App种子数据
    /// </summary>
    /// <returns></returns>
    public IEnumerable<SysService> HasData()
    {
        IEnumerable<SysService> sysService = SeedDataUtil.GetSeedData<SysService>("system_service.json");
        return sysService;
    }
}