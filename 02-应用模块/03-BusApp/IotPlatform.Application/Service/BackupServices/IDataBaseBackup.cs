using Microsoft.AspNetCore.Http;

namespace IotPlatform.Application.Service.BackupServices;

/// <summary>
///     数据库备份接口
/// </summary>
public interface IDataBaseBackup
{
    /// <summary>
    ///     导出数据库
    /// </summary>
    /// <param name="connectionString">连接字符串</param>
    /// <param name="name">备份名称</param>
    /// <returns>备份结果</returns>
    Task<dynamic> ExportDatabase(string connectionString, string name);

    /// <summary>
    ///     导入还原数据库
    /// </summary>
    /// <param name="connectionString">连接字符串</param>
    /// <param name="file">备份文件</param>
    Task RestoreDatabase(string connectionString, IFormFile file);

    /// <summary>
    ///     取消导出
    /// </summary>
    void ExCancel();

    /// <summary>
    ///     取消导入
    /// </summary>
    void InCancel();
}
