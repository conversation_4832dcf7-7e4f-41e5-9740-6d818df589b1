using IotPlatform.Application.Entity;
using Microsoft.AspNetCore.Http;

namespace IotPlatform.Application.BackupServices.Dto;

/// <summary>
///     数据备份-新增请求参数
/// </summary>
public class DataBaseBackupAddInput
{
    /// <summary>
    ///     规则名称
    /// </summary>
    [Required]
    public string Name { get; set; }

    /// <summary>
    ///     备份应用 1：WebSocket-数据
    /// </summary>
    public List<DataBaseBackupAppEnum> BackupApp { get; set; }

    /// <summary>
    ///     自动备份 1：禁用； 2：自动备份
    /// </summary>
    public DataBaseAutoBackupEnum AutoBackup { get; set; }

    /// <summary>
    /// Cron表达式
    /// </summary>
    public string Cron { get; set; }
    
    /// <summary>
    ///     存储管理Id
    /// </summary>
    [SugarColumn(ColumnDescription = "存储管理Id")]
    public long DataBaseStorageId { get; set; }
}

/// <summary>
///     数据备份-手动备份请求参数
/// </summary>
public class DataBaseBackupInput
{
    /// <summary>
    /// 备份Id
    /// </summary>
    [Required]
    public long Id { get; set; }

    /// <summary>
    /// 备份文件名称
    /// </summary>
    [Required]
    public string Name { get; set; }
}

/// <summary>
/// 数据备份-备份还原请求参数
/// </summary>
public class DataBaseBackupRecoveryInput
{
    /// <summary>
    /// </summary>
    public IFormFile File { get; set; }
}