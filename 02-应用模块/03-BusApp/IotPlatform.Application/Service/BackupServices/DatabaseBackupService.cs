namespace IotPlatform.Application.Service.BackupServices;

/// <summary>
///     备份管理-数据备份
///     版 本:V4.0.0
///     版 权:杭州峰回科技有限公司
///     作 者:蒋状
///     日 期:2023-08-15
/// </summary>
[ApiDescriptionSettings("备份管理")]
public class DatabaseBackupService : IDynamicApiController, ITransient
{
    private readonly ISqlSugarRepository<DataBaseBackup> _dataBaseBackup;
    private readonly DataBaseBackupFactory _backupFactory;
    private readonly ISchedulerFactory _schedulerFactory;
    private readonly SqlSugarScope _db;

    public DatabaseBackupService(ISqlSugarRepository<DataBaseBackup> dataBaseBackup, DataBaseBackupFactory backupFactory, ISchedulerFactory schedulerFactory, ISqlSugarClient context)
    {
        _dataBaseBackup = dataBaseBackup;
        _backupFactory = backupFactory;
        _schedulerFactory = schedulerFactory;
        _db = (SqlSugarScope) context;
    }

    /// <summary>
    ///     数据备份-列表
    /// </summary>
    /// <returns></returns>
    [HttpGet("/dataBaseBackup/page")]
    public async Task<SqlSugarPagedList<DataBaseBackup>> DataBaseBackupPage([FromQuery] BasePageInput input)
    {
        SqlSugarPagedList<DataBaseBackup> dataBaseBackupPage = await _dataBaseBackup.AsQueryable()
            .WhereIF(input.SearchValue.IsNotEmptyOrNull(), u => u.Name.Contains(input.SearchValue))
            .Includes(w => w.DataBaseStorage)
            .ToPagedListAsync(input.PageNo, input.PageSize);
        return dataBaseBackupPage;
    }

    /// <summary>
    ///     数据备份-备份记录列表
    /// </summary>
    /// <returns></returns>
    [HttpGet("/dataBaseBackup/record/page")]
    public async Task<List<BackupRecord>> DataBaseBackupRecordPage([FromQuery] BaseId input)
    {
        List<BackupRecord> backupRecordList = await _dataBaseBackup.AsSugarClient().Queryable<BackupRecord>()
            .Where(w => w.DataBaseBackupId == input.Id)
            .Includes(w => w.DataBaseBackup)
            .ToListAsync();
        return backupRecordList;
    }

    /// <summary>
    ///     数据备份-备份记录下载
    /// </summary>
    /// <returns></returns>
    [HttpPost("/dataBaseBackup/record/download")]
    public async Task<IActionResult> DataBaseBackupRecordDownload(BaseId input)
    {
        BackupRecord backupRecord = await _dataBaseBackup.AsSugarClient().Queryable<BackupRecord>().FirstAsync(f => f.Id == input.Id);
        if (!File.Exists(backupRecord.BackupFilePath))
        {
            throw Oops.Bah("备份文件已经被删除，无法下载！");
        }

        return new FileStreamResult(new FileStream(backupRecord.BackupFilePath, FileMode.Open), "application/octet-stream") {FileDownloadName = backupRecord.BackupFileName};
    }

    /// <summary>
    ///     数据备份-备份记录删除
    /// </summary>
    /// <returns></returns>
    [HttpPost("/dataBaseBackup/record/delete")]
    public async Task DataBaseBackupRecordDelete(BaseId input)
    {
        BackupRecord backupRecord = await _dataBaseBackup.AsSugarClient().Queryable<BackupRecord>().FirstAsync(f => f.Id == input.Id);
        if (File.Exists(backupRecord.BackupFilePath))
        {
            File.Delete(backupRecord.BackupFilePath);
        }

        await _dataBaseBackup.AsSugarClient().Deleteable(backupRecord).ExecuteCommandAsync();
    }

    /// <summary>
    ///     数据备份-详情
    /// </summary>
    /// <returns></returns>
    [HttpGet("/dataBaseBackup/detail")]
    public async Task<DataBaseBackup> DataBaseBackupDetail([FromQuery] BaseId input)
    {
        return await _dataBaseBackup.AsQueryable().FirstAsync(f => f.Id == input.Id);
    }

    /// <summary>
    ///     数据备份-新增
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/dataBaseBackup/add")]
    public async Task DataBaseBackupAdd(DataBaseBackupAddInput input)
    {
        DataBaseStorage dataBaseStorage = await _dataBaseBackup.AsSugarClient().Queryable<DataBaseStorage>().FirstAsync(f => f.Id == input.DataBaseStorageId);
        if (dataBaseStorage == null)
        {
            throw Oops.Oh("选择存储管理已经被删除！");
        }

        DataBaseBackup map = input.Adapt<DataBaseBackup>();
        map.Id = YitIdHelper.NextId();
        map.DataBaseStorage = dataBaseStorage;
        // 创建定时任务
        if (map.AutoBackup == DataBaseAutoBackupEnum.Auto)
        {
            CreateJob(map);
        }

        await _dataBaseBackup.InsertAsync(map);
    }

    /// <summary>
    ///     创建数据库备份定时任务
    /// </summary>
    /// <param name="input"></param>
    /// <exception cref="AppFriendlyException"></exception>
    /// <exception cref="ArgumentOutOfRangeException"></exception>
    private void CreateJob(DataBaseBackup input)
    {
        foreach (DataBaseBackupAppEnum backupApp in input.BackupApp)
        {
            switch (backupApp)
            {
                case DataBaseBackupAppEnum.Web:
                {
                    switch (input.DataBaseStorage?.DataBaseStorageType)
                    {
                        case DataBaseStorageTypeEnum.Local:
                        {
                            JobBuilder jobBuilder = JobBuilder.Create<ExportDatabaseJob>()
                                    .SetJobId(input.Id.ToString()) // 作业 Id
                                    .SetGroupName(input.AutoBackupName) // 作业组名称
                                    .SetJobType("IotPlatform.Application", "IotPlatform.Application.Service.Job") // 作业类型，支持多个重载
                                    .SetJobType<ExportDatabaseJob>() // 作业类型，支持多个重载
                                    .SetJobType(typeof(ExportDatabaseJob)) // 作业类型，支持多个重载
                                    .SetDescription(input.Name) // 作业描述
                                    .SetConcurrent(true) // 并行还是串行方式，false 为 串行
                                    .SetIncludeAnnotations(true) // 是否扫描 IJob 类型的触发器特性，true 为 扫描
                                    .SetProperties("{}") // 作业额外数据 Dictionary<string, object> 类型序列化，支持多个重载
                                    .SetProperties(new Dictionary<string, object>
                                        {{"DataBaseBackup", input.ToJson()}, {"connectionString", _db.CurrentConnectionConfig.ConnectionString}}) // 作业类型额外数据，支持多个重载，推荐！！！
                                ;
                            _schedulerFactory.TryAddJob(jobBuilder, new[]
                            {
                                Triggers.Cron(input.Cron.ConvertCron(), CronStringFormat.WithSeconds).SetTriggerId(input.Id.ToString())
                            }, out IScheduler scheduler);
                            scheduler.UpdateDetail(jobBuilder =>
                            {
                                jobBuilder.SetDescription(input.Name);
                                jobBuilder.SetGroupName(input.AutoBackupName);
                            });
                            scheduler.Persist();
                        }
                            break;
                        case DataBaseStorageTypeEnum.SFtp:
                            throw Oops.Oh("暂不支持SFTP！");
                        default:
                            throw new ArgumentOutOfRangeException();
                    }

                    break;
                }
                default:
                    throw new ArgumentOutOfRangeException();
            }
        }
    }

    /// <summary>
    ///     数据备份-修改
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/dataBaseBackup/update")]
    public async Task DataBaseBackupUpdate(DataBaseBackup input)
    {
        // 存储管理
        DataBaseStorage dataBaseStorage = await _dataBaseBackup.AsSugarClient().Queryable<DataBaseStorage>().FirstAsync(f => f.Id == input.DataBaseStorageId);
        if (dataBaseStorage == null)
        {
            throw Oops.Oh("选择存储管理已经被删除！");
        }

        // 数据备份
        DataBaseBackup dataBaseBackup = await _dataBaseBackup.AsQueryable().FirstAsync(f => f.Id == input.Id);
        if (dataBaseBackup == null)
        {
            throw Oops.Oh(ErrorCode.D1002);
        }

        // 停止原来的定时任务
        if (dataBaseBackup.AutoBackup == DataBaseAutoBackupEnum.Auto)
        {
            _schedulerFactory.RemoveJob(dataBaseBackup.Id.ToString());
        }

        input.DataBaseStorage = dataBaseStorage;
        // 创建定时任务
        if (input.AutoBackup == DataBaseAutoBackupEnum.Auto)
        {
            CreateJob(input);
        }

        await _dataBaseBackup.AsSugarClient().Updateable(input).IgnoreColumns(w => new {w.BackupStatus, w.BackupTime}).ExecuteCommandAsync();
    }

    /// <summary>
    ///     数据备份-删除
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/dataBaseBackup/delete")]
    public async Task DataBaseBackupDelete(BaseId input)
    {
        DataBaseBackup dataBaseBackup = await _dataBaseBackup.AsQueryable().FirstAsync(f => f.Id == input.Id);
        if (dataBaseBackup == null)
        {
            return;
        }

        // 停止原来的定时任务
        if (dataBaseBackup.AutoBackup == DataBaseAutoBackupEnum.Auto)
        {
            _schedulerFactory.RemoveJob(dataBaseBackup.Id.ToString());
        }

        await _dataBaseBackup.DeleteAsync(dataBaseBackup);
    }

    /// <summary>
    ///     数据备份-手动备份
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/dataBaseBackup/backup")]
    public async Task DataBaseBackup(DataBaseBackupInput input)
    {
        DataBaseBackup dataBaseBackup = await _dataBaseBackup.AsQueryable().Where(f => f.Id == input.Id)
            .Includes(w => w.DataBaseStorage)
            .FirstAsync();
        if (dataBaseBackup == null)
        {
            throw Oops.Oh(ErrorCode.D1002);
        }

        if (dataBaseBackup.DataBaseStorage == null)
        {
            throw Oops.Oh("关联存储配置已经删除！");
        }

        foreach (DataBaseBackupAppEnum backupApp in dataBaseBackup.BackupApp)
        {
            try
            {
                long length;
                string backupFileName;
                string backupFilePath;
                switch (backupApp)
                {
                    case DataBaseBackupAppEnum.Web:
                    {
                        switch (dataBaseBackup.DataBaseStorage.DataBaseStorageType)
                        {
                            case DataBaseStorageTypeEnum.Local:
                                // 根据数据库类型创建相应的备份实例
                                IDataBaseBackup backup = _backupFactory.CreateBackup(_db.CurrentConnectionConfig);
                                dynamic output = await backup.ExportDatabase(_db.CurrentConnectionConfig.ConnectionString, input.Name);
                                length = output.Length;
                                backupFileName = output.BackupFileName;
                                backupFilePath = output.BackupFilePath;
                                break;
                            case DataBaseStorageTypeEnum.SFtp:
                                throw Oops.Oh("暂不支持SFTP！");
                            default:
                                throw new ArgumentOutOfRangeException();
                        }

                        break;
                    }
                    default:
                        throw new ArgumentOutOfRangeException();
                }

                BackupRecord backupRecord = new()
                {
                    Size = Math.Round((decimal) length / 1024 / 1024, 2),
                    BackupTime = DateTime.Now,
                    BackupFileName = backupFileName,
                    BackupFilePath = backupFilePath,
                    DataBaseBackupId = input.Id
                };
                await _dataBaseBackup.AsSugarClient().Insertable(backupRecord).ExecuteCommandAsync();
                dataBaseBackup.BackupTime = backupRecord.BackupTime;
                dataBaseBackup.BackupStatus = "备份成功";
                await _dataBaseBackup.UpdateAsync(dataBaseBackup);
            }
            catch (Exception e)
            {
                dataBaseBackup.BackupTime = DateTime.Now;
                dataBaseBackup.BackupStatus = "备份失败,Error:" + e.Message;
                await _dataBaseBackup.UpdateAsync(dataBaseBackup);
            }
        }
    }

    /// <summary>
    ///     数据备份-停止备份
    /// </summary>
    /// <returns></returns>
    [HttpPost("/dataBaseBackup/exCancel")]
    public Task DataBaseBackupExCancel()
    {
        // 取消所有类型的备份
        try
        {
            IDataBaseBackup backup = _backupFactory.CreateBackup(_db.CurrentConnectionConfig);
            backup.ExCancel();
        }
        catch (Exception ex)
        {
            Log.Warning($"取消备份时出错: {ex.Message}");
        }
        return Task.CompletedTask;
    }

    /// <summary>
    ///     数据备份-备份还原
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/dataBaseBackup/recovery")]
    public async Task DataBaseBackupRecovery([FromQuery] DataBaseBackupRecoveryInput input)
    {
        // 根据数据库类型创建相应的备份实例
        IDataBaseBackup backup = _backupFactory.CreateBackup(_db.CurrentConnectionConfig);
        await backup.RestoreDatabase(_db.CurrentConnectionConfig.ConnectionString, input.File);
    }

    /// <summary>
    ///     数据备份-停止还原数据
    /// </summary>
    /// <returns></returns>
    [HttpPost("/dataBaseBackup/inCancel")]
    public Task DataBaseBackupInCancel()
    {
        // 取消所有类型的恢复
        try
        {
            IDataBaseBackup backup = _backupFactory.CreateBackup(_db.CurrentConnectionConfig);
            backup.InCancel();
        }
        catch (Exception ex)
        {
            Log.Warning($"取消恢复时出错: {ex.Message}");
        }
        return Task.CompletedTask;
    }

    /// <summary>
    ///     初始化
    /// </summary>
    [NonAction]
    public async Task Init()
    {
        // 数据备份
        List<DataBaseBackup> dataBaseBackupList = await _dataBaseBackup.AsQueryable().Includes(w => w.DataBaseStorage).ToListAsync();
        foreach (DataBaseBackup dataBaseBackup in dataBaseBackupList)
            // 创建定时任务
        {
            if (dataBaseBackup.AutoBackup == DataBaseAutoBackupEnum.Auto)
            {
                CreateJob(dataBaseBackup);
            }
        }
    }
}