
namespace IotPlatform.Application.Service.BackupServices;

/// <summary>
///     备份管理-存储管理
///     版 本:V4.0.0
///     版 权:杭州峰回科技有限公司
///     作 者:蒋状
///     日 期:2023-08-10
/// </summary>
[ApiDescriptionSettings("备份管理")]
public class DatabaseStorageService : IDynamicApiController, ITransient
{
    private readonly ISqlSugarRepository<DataBaseStorage> _dataBaseStorage;

    public DatabaseStorageService(ISqlSugarRepository<DataBaseStorage> dataBaseStorage)
    {
        _dataBaseStorage = dataBaseStorage;
    }

    /// <summary>
    ///     存储管理-列表
    /// </summary>
    /// <returns></returns>
    [HttpGet("/dataBaseStorage/page")]
    public async Task<SqlSugarPagedList<DataBaseStorage>> DataBaseStoragePage([FromQuery] BasePageInput input)
    {
        SqlSugarPagedList<DataBaseStorage> dataBaseStoragePage = await _dataBaseStorage.AsQueryable()
            .WhereIF(input.SearchValue.IsNotEmptyOrNull(), u => u.Name.Contains(input.SearchValue))
            .ToPagedListAsync(input.PageNo, input.PageSize);
        return dataBaseStoragePage;
    }

    /// <summary>
    ///     存储管理-下拉
    /// </summary>
    /// <returns></returns>
    [HttpGet("/dataBaseStorage/select")]
    public async Task<List<DataBaseStorage>> DataBaseStorageSelect()
    {
        List<DataBaseStorage> dataBaseStorageList = await _dataBaseStorage.AsQueryable().ToListAsync();
        return dataBaseStorageList;
    }

    /// <summary>
    ///     存储管理-详情
    /// </summary>
    /// <returns></returns>
    [HttpGet("/dataBaseStorage/detail")]
    public async Task<DataBaseStorage> DataBaseStorageDetail([FromQuery] BaseId input)
    {
        return await _dataBaseStorage.AsQueryable().FirstAsync(f => f.Id == input.Id);
    }

    /// <summary>
    ///     存储管理-新增
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/dataBaseStorage/add")]
    public async Task DataBaseStorageAdd(DataBaseStorageAddInput input)
    {
        DataBaseStorage map = input.Adapt<DataBaseStorage>();
        await _dataBaseStorage.InsertAsync(map);
    }

    /// <summary>
    ///     存储管理-修改
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/dataBaseStorage/update")]
    public async Task DataBaseStorageUpdate(DataBaseStorage input)
    {
        DataBaseStorage dataBaseStorage = await _dataBaseStorage.AsQueryable().FirstAsync(f => f.Id == input.Id);
        if (dataBaseStorage == null)
        {
            throw Oops.Oh(ErrorCode.D1002);
        }

        await _dataBaseStorage.UpdateAsync(input);
    }

    /// <summary>
    ///     存储管理-删除
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/dataBaseStorage/delete")]
    public async Task DataBaseStorageDelete(BaseId input)
    {
        if (await _dataBaseStorage.AsSugarClient().Queryable<DataBaseBackup>().AnyAsync(a => a.DataBaseStorageId == input.Id))
        {
            throw Oops.Oh(ErrorCode.D1007);
        }

        DataBaseStorage dataBaseStorage = await _dataBaseStorage.AsQueryable().FirstAsync(f => f.Id == input.Id);
        if (dataBaseStorage == null)
        {
            return;
        }

        await _dataBaseStorage.DeleteAsync(dataBaseStorage);
    }
}