using System.IO.Compression;
using Common.Hubs;
using Furion.Extensions;
using Microsoft.AspNetCore.Http;
using MySql.Data.MySqlClient;

namespace IotPlatform.Application.Service.BackupServices;

/// <summary>
///     Mysql 备份
/// </summary>
public class MysqlBackup : IDataBaseBackup, IScoped
{
    private readonly SocketSingleton _socketService;

    private readonly ISqlSugarClient _db;

    // 取消导出
    private static bool exCancel;

    // 取消导入
    private static bool inCancel;

    public MysqlBackup(SocketSingleton socketService, ISqlSugarClient db)
    {
        _socketService = socketService;
        _db = db;
    }

    public void ExCancel()
    {
        exCancel = true;
    }

    public void InCancel()
    {
        inCancel = true;
    }

    /// <summary>
    ///     导出数据库
    /// </summary>
    /// <param name="connectionString"></param>
    /// <param name="name"></param>
    public async Task<dynamic> ExportDatabase(string connectionString, string name)
    {
        exCancel = false;
        MemoryStream ms = new();
        string backupFilePath = App.Configuration["JNPF_App:BackupPath"];
        await using (MySqlConnection connection = new(connectionString))
        {
            await connection.OpenAsync();
            await using (MySqlCommand command = new())
            {
                command.Connection = connection;
                using (MySqlBackup backup = new(command))
                {
                    // 表总记录数
                    long totalRowsInAllTables = 0;
                    backup.ExportProgressChanged += async (sender, e) =>
                    {
                        if (exCancel)
                        {
                            // Calling mb to halt
                            backup.StopAllProcess();
                            return;
                        }

                        totalRowsInAllTables = e.TotalRowsInAllTables;
                        // 导出进度变化时的处理逻辑
                        string console = new
                        {
                            e.TotalRowsInAllTables,
                            e.CurrentRowIndexInAllTables,
                            e.CurrentTableIndex,
                            e.CurrentTableName,
                            e.TotalRowsInCurrentTable,
                            e.CurrentRowIndexInCurrentTable,
                            e.TotalTables
                        }.ToJson();
                        Console.WriteLine(console);
                        await _socketService.Send(console, "dataBaseExPort");
                    };

                    backup.ExportCompleted += async (sender, e) =>
                    {
                        string console = new
                        {
                            TotalRowsInAllTables = totalRowsInAllTables,
                            CurrentRowIndexInAllTables = totalRowsInAllTables
                        }.ToJson();
                        await _socketService.Send(console, "dataBaseExPort");
                    };

                    backup.ExportToMemoryStream(ms);
                    await connection.CloseAsync();
                    // backup.ExportToFile(backupFilePath + name);
                }
            }
        }

        byte[] ba = ms.ToArray();

        // 1st Compress the file data
        // The size is 50%-70% smaller
        ba = CompressData(ba);

        // 2nd Encrypt the file data
        ba = AESEncryption.Encrypt(ba, "202308");

        if (backupFilePath != null)
        {
            if (!Directory.Exists(backupFilePath))
            {
                Directory.CreateDirectory(backupFilePath);
            }
        }

        // 3rd Write the file data to disk
        await File.WriteAllBytesAsync(backupFilePath + name + ".backup", ba);
        Log.Information($"【数据库备份】 已保存到目录:【{backupFilePath + name}.backup】");
        return new
        {
            ba.Length,
            BackupFileName = name + ".backup",
            BackupFilePath = backupFilePath + name + ".backup"
        };
    }

    /// <summary>
    ///     导入还原数据库
    /// </summary>
    /// <param name="connectionString"></param>
    /// <param name="file"></param>
    public async Task RestoreDatabase(string connectionString, IFormFile file)
    {
        inCancel = false;
        // 1st Read the file bytes
        byte[] ba = file.ToByteArray();
        await _socketService.Send(new {Message = "开始解密数据..."}.ToJson(), "dataBaseInPort");
        try
        {
            // 2nd Decrypt the file data
            ba = AESEncryption.Decrypt(ba, "202308");
        }
        catch
        {
            await _socketService.Send(new {Message = "解密数据失败..."}.ToJson(), "dataBaseInPort");
            return;
        }

        try
        {
            await _socketService.Send(new {Message = "开始解压缩文件..."}.ToJson(), "dataBaseInPort");
            // 3rd Decompress the file data
            ba = DecompressData(ba);
        }
        catch (Exception e)
        {
            await _socketService.Send(new {Message = "解压缩文件失败..."}.ToJson(), "dataBaseInPort");
            return;
        }

        MemoryStream ms = new(ba);
        await _socketService.Send(new {Message = "开始恢复数据..."}.ToJson(), "dataBaseInPort");
        await using MySqlConnection connection = new(connectionString);
        await connection.OpenAsync();

        await using MySqlCommand command = new();
        command.Connection = connection;

        using MySqlBackup backup = new(command);
        // 总记录数
        long totalBytes = 0;
        backup.ImportProgressChanged += async (sender, e) =>
        {
            if (inCancel)
            {
                // Calling mb to halt
                backup.StopAllProcess();
                await _socketService.Send(new {Message = "停止恢复数据"}.ToJson(), "dataBaseInPort");
                return;
            }

            totalBytes = e.TotalBytes;
            string console = new
            {
                e.TotalBytes,
                e.CurrentBytes
            }.ToJson();
            // 还原进度变化时的处理逻辑
            await _socketService.Send(console, "dataBaseInPort");
        };

        backup.ImportCompleted += async (sender, e) =>
        {
            string console = new
            {
                TotalBytes = totalBytes,
                CurrentBytes = totalBytes,
                Message = "已完成数据恢复"
            }.ToJson();
            Console.WriteLine(console);
            await _socketService.Send(console, "dataBaseInPort");
        };
        await _socketService.Send(new {Message = "开始清理恢复数据产生临时文件..."}.ToJson(), "dataBaseInPort");
        backup.ImportFromMemoryStream(ms);
        await connection.CloseAsync();
        await _socketService.Send(new {Message = "已完成清理恢复数据产生临时文件"}.ToJson(), "dataBaseInPort");
        // backup.ImportFromFile(backupFilePath);
    }

    /// <summary>
    ///     压缩文件
    /// </summary>
    /// <param name="data"></param>
    /// <returns></returns>
    private byte[] CompressData(byte[] data)
    {
        MemoryStream output = new();
        using (DeflateStream dstream = new(output, CompressionLevel.Optimal))
        {
            dstream.Write(data, 0, data.Length);
        }

        return output.ToArray();
    }

    /// <summary>
    ///     解压缩文件
    /// </summary>
    /// <param name="data"></param>
    /// <returns></returns>
    private byte[] DecompressData(byte[] data)
    {
        MemoryStream input = new(data);
        MemoryStream output = new();
        using (DeflateStream dstream = new(input, CompressionMode.Decompress))
        {
            dstream.CopyTo(output);
        }

        return output.ToArray();
    }
}