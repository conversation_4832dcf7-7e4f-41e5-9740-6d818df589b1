using System.Diagnostics;
using System.IO.Compression;
using System.Runtime.InteropServices;
using System.Text;
using System.Text.RegularExpressions;
using System.Timers;
using Common.Hubs;
using Furion.Extensions;
using Microsoft.AspNetCore.Http;
using Npgsql;
using Timer = System.Timers.Timer;

namespace IotPlatform.Application.Service.BackupServices;

/// <summary>
///     PostgreSQL 备份
/// </summary>
public class PostgreSqlBackup : IDataBaseBackup, IScoped
{
    private readonly SocketSingleton _socketService;
    private readonly ISqlSugarClient _db;

    // 取消导出
    private static bool exCancel;
    // 取消导入
    private static bool inCancel;
    // 当前进程
    private static Process? currentProcess;

    public PostgreSqlBackup(SocketSingleton socketService, ISqlSugarClient db)
    {
        _socketService = socketService;
        _db = db;
    }

    public void ExCancel()
    {
        exCancel = true;
        if (currentProcess != null && !currentProcess.HasExited)
        {
            try
            {
                currentProcess.Kill();
            }
            catch (Exception ex)
            {
                Log.Warning($"终止PostgreSQL备份进程时出错: {ex.Message}");
            }
        }
    }

    public void InCancel()
    {
        inCancel = true;
        if (currentProcess != null && !currentProcess.HasExited)
        {
            try
            {
                currentProcess.Kill();
            }
            catch (Exception ex)
            {
                Log.Warning($"终止PostgreSQL恢复进程时出错: {ex.Message}");
            }
        }
    }

    /// <summary>
    ///     导出数据库
    /// </summary>
    /// <param name="connectionString"></param>
    /// <param name="name"></param>
    public async Task<dynamic> ExportDatabase(string connectionString, string name)
    {
        exCancel = false;
        currentProcess = null;

        // 验证PostgreSQL工具
        var (isAvailable, message, pgDumpPath, _, useDocker) = PostgreSqlToolDetector.ValidateTools();
        if (!isAvailable)
        {
            throw new InvalidOperationException(message);
        }

        string backupFilePath = App.Configuration["JNPF_App:BackupPath"];
        if (string.IsNullOrEmpty(backupFilePath))
        {
            throw new InvalidOperationException("未配置备份路径");
        }

        if (!Directory.Exists(backupFilePath))
        {
            Directory.CreateDirectory(backupFilePath);
        }

        // 解析连接字符串
        var connectionInfo = ParseConnectionString(connectionString);
        
        // 获取数据库大小用于进度估算
        long totalSize = await GetDatabaseSize(connectionString);
        
        // 创建临时文件
        string tempFilePath = Path.Combine(Path.GetTempPath(), $"pg_backup_{Guid.NewGuid()}.sql");
        
        try
        {
            // 根据是否使用Docker构建不同的命令
            if (useDocker)
            {
                // 检查是否需要连接宿主机PostgreSQL
                if (ShouldConnectToHostPostgreSQL())
                {
                    await ExecutePgDumpWithDockerToHost(connectionInfo, tempFilePath, totalSize);
                }
                else
                {
                    await ExecutePgDumpWithDocker(connectionInfo, tempFilePath, totalSize);
                }
            }
            else
            {
                // 构建pg_dump命令
                string arguments = BuildPgDumpArguments(connectionInfo, tempFilePath);

                // 执行pg_dump
                await ExecutePgDump(pgDumpPath!, arguments, totalSize);
            }
            
            if (exCancel)
            {
                throw new OperationCanceledException("备份操作已取消");
            }

            // 读取备份文件
            byte[] backupData = await File.ReadAllBytesAsync(tempFilePath);
            
            // 压缩数据
            backupData = CompressData(backupData);
            
            // 加密数据
            backupData = AESEncryption.Encrypt(backupData, "202308");
            
            // 保存最终备份文件
            string finalBackupPath = Path.Combine(backupFilePath, name + ".backup");
            await File.WriteAllBytesAsync(finalBackupPath, backupData);
            
            Log.Information($"【PostgreSQL数据库备份】 已保存到目录:【{finalBackupPath}】");
            
            return new
            {
                Length = backupData.Length,
                BackupFileName = name + ".backup",
                BackupFilePath = finalBackupPath
            };
        }
        finally
        {
            // 清理临时文件
            if (File.Exists(tempFilePath))
            {
                try
                {
                    File.Delete(tempFilePath);
                }
                catch (Exception ex)
                {
                    Log.Warning($"删除临时备份文件失败: {ex.Message}");
                }
            }
        }
    }

    /// <summary>
    ///     导入还原数据库
    /// </summary>
    /// <param name="connectionString"></param>
    /// <param name="file"></param>
    public async Task RestoreDatabase(string connectionString, IFormFile file)
    {
        inCancel = false;
        currentProcess = null;

        // 验证PostgreSQL工具
        var (isAvailable, message, _, pgRestorePath, useDocker) = PostgreSqlToolDetector.ValidateTools();
        if (!isAvailable)
        {
            throw new InvalidOperationException(message);
        }

        // 读取文件数据
        byte[] fileData = file.ToByteArray();
        
        await _socketService.Send(new { Message = "开始解密数据..." }.ToJson(), "dataBaseInPort");
        
        try
        {
            // 解密数据
            fileData = AESEncryption.Decrypt(fileData, "202308");
        }
        catch
        {
            await _socketService.Send(new { Message = "解密数据失败..." }.ToJson(), "dataBaseInPort");
            return;
        }

        try
        {
            await _socketService.Send(new { Message = "开始解压缩文件..." }.ToJson(), "dataBaseInPort");
            // 解压缩数据
            fileData = DecompressData(fileData);
        }
        catch
        {
            await _socketService.Send(new { Message = "解压缩文件失败..." }.ToJson(), "dataBaseInPort");
            return;
        }

        // 创建临时文件
        string tempFilePath = Path.Combine(Path.GetTempPath(), $"pg_restore_{Guid.NewGuid()}.sql");
        
        try
        {
            await File.WriteAllBytesAsync(tempFilePath, fileData);
            
            await _socketService.Send(new { Message = "开始恢复数据..." }.ToJson(), "dataBaseInPort");
            
            // 解析连接字符串
            var connectionInfo = ParseConnectionString(connectionString);

            // 根据是否使用Docker执行不同的恢复方式
            if (useDocker)
            {
                // 检查是否需要连接宿主机PostgreSQL
                if (ShouldConnectToHostPostgreSQL())
                {
                    await ExecutePsqlWithDockerToHost(connectionInfo, tempFilePath, fileData.Length);
                }
                else
                {
                    await ExecutePsqlWithDocker(connectionInfo, tempFilePath, fileData.Length);
                }
            }
            else
            {
                // 构建psql命令（用于执行SQL文件）
                string arguments = BuildPsqlArguments(connectionInfo, tempFilePath);

                // 执行恢复
                await ExecutePsql(arguments, fileData.Length);
            }
            
            if (!inCancel)
            {
                await _socketService.Send(new 
                { 
                    TotalBytes = fileData.Length,
                    CurrentBytes = fileData.Length,
                    Message = "已完成数据恢复" 
                }.ToJson(), "dataBaseInPort");
            }
        }
        finally
        {
            // 清理临时文件
            if (File.Exists(tempFilePath))
            {
                try
                {
                    File.Delete(tempFilePath);
                }
                catch (Exception ex)
                {
                    Log.Warning($"删除临时恢复文件失败: {ex.Message}");
                }
            }
        }
    }

    /// <summary>
    ///     解析连接字符串
    /// </summary>
    private static ConnectionInfo ParseConnectionString(string connectionString)
    {
        var builder = new NpgsqlConnectionStringBuilder(connectionString);
        return new ConnectionInfo
        {
            Host = builder.Host ?? "localhost",
            Port = builder.Port,
            Database = builder.Database ?? "",
            Username = builder.Username ?? "",
            Password = builder.Password ?? ""
        };
    }

    /// <summary>
    ///     获取数据库大小
    /// </summary>
    private async Task<long> GetDatabaseSize(string connectionString)
    {
        try
        {
            using var connection = new NpgsqlConnection(connectionString);
            await connection.OpenAsync();
            
            using var command = new NpgsqlCommand("SELECT pg_database_size(current_database())", connection);
            var result = await command.ExecuteScalarAsync();
            
            return result != null ? Convert.ToInt64(result) : 0;
        }
        catch (Exception ex)
        {
            Log.Warning($"获取数据库大小失败: {ex.Message}");
            return 0;
        }
    }

    /// <summary>
    ///     构建pg_dump命令参数
    /// </summary>
    private static string BuildPgDumpArguments(ConnectionInfo connectionInfo, string outputPath)
    {
        var args = new StringBuilder();
        args.Append($"-h {connectionInfo.Host} ");
        args.Append($"-p {connectionInfo.Port} ");
        args.Append($"-U {connectionInfo.Username} ");
        args.Append($"-d {connectionInfo.Database} ");
        args.Append("--verbose ");
        args.Append("--no-password ");
        args.Append($"-f \"{outputPath}\"");
        
        return args.ToString();
    }

    /// <summary>
    ///     构建psql命令参数
    /// </summary>
    private static string BuildPsqlArguments(ConnectionInfo connectionInfo, string inputPath)
    {
        var args = new StringBuilder();
        args.Append($"-h {connectionInfo.Host} ");
        args.Append($"-p {connectionInfo.Port} ");
        args.Append($"-U {connectionInfo.Username} ");
        args.Append($"-d {connectionInfo.Database} ");
        args.Append("--no-password ");
        args.Append($"-f \"{inputPath}\"");
        
        return args.ToString();
    }

    /// <summary>
    ///     使用Docker执行pg_dump命令
    /// </summary>
    private async Task ExecutePgDumpWithDocker(ConnectionInfo connectionInfo, string outputPath, long totalSize)
    {
        string containerName = App.Configuration["PostgreSQL:ContainerName"] ?? "postgresql";
        string backupPath = App.Configuration["JNPF_App:BackupPath"] ?? "/app/backup/";

        // 确保备份目录存在
        if (!Directory.Exists(backupPath))
        {
            Directory.CreateDirectory(backupPath);
        }

        // 在容器内的备份文件路径
        string containerBackupPath = "/tmp/backup.sql";

        // 构建Docker命令
        var args = new StringBuilder();
        args.Append($"exec {containerName} ");
        args.Append($"pg_dump ");
        args.Append($"-h localhost ");
        args.Append($"-p 5432 ");
        args.Append($"-U {connectionInfo.Username} ");
        args.Append($"-d {connectionInfo.Database} ");
        args.Append($"--verbose ");
        args.Append($"-f {containerBackupPath}");

        var startInfo = new ProcessStartInfo
        {
            FileName = "docker",
            Arguments = args.ToString(),
            RedirectStandardOutput = true,
            RedirectStandardError = true,
            UseShellExecute = false,
            CreateNoWindow = true
        };

        // 设置环境变量避免密码提示
        startInfo.Environment["PGPASSWORD"] = connectionInfo.Password;

        currentProcess = Process.Start(startInfo);
        if (currentProcess == null)
        {
            throw new InvalidOperationException("无法启动Docker pg_dump进程");
        }

        // 监控进度
        _ = MonitorBackupProgress(totalSize);

        // 等待进程完成
        await currentProcess.WaitForExitAsync();

        if (currentProcess.ExitCode != 0 && !exCancel)
        {
            string error = await currentProcess.StandardError.ReadToEndAsync();
            throw new InvalidOperationException($"Docker pg_dump执行失败: {error}");
        }

        // 从容器复制备份文件到宿主机
        if (!exCancel)
        {
            await CopyFileFromContainer(containerName, containerBackupPath, outputPath);

            // 清理容器内的临时文件
            await CleanupContainerFile(containerName, containerBackupPath);
        }
    }

    /// <summary>
    ///     从容器复制文件到宿主机
    /// </summary>
    private static async Task CopyFileFromContainer(string containerName, string containerPath, string hostPath)
    {
        var startInfo = new ProcessStartInfo
        {
            FileName = "docker",
            Arguments = $"cp {containerName}:{containerPath} \"{hostPath}\"",
            RedirectStandardOutput = true,
            RedirectStandardError = true,
            UseShellExecute = false,
            CreateNoWindow = true
        };

        using var process = Process.Start(startInfo);
        if (process == null)
        {
            throw new InvalidOperationException("无法启动Docker cp进程");
        }

        await process.WaitForExitAsync();

        if (process.ExitCode != 0)
        {
            string error = await process.StandardError.ReadToEndAsync();
            throw new InvalidOperationException($"从容器复制文件失败: {error}");
        }
    }

    /// <summary>
    ///     清理容器内的临时文件
    /// </summary>
    private static async Task CleanupContainerFile(string containerName, string containerPath)
    {
        try
        {
            var startInfo = new ProcessStartInfo
            {
                FileName = "docker",
                Arguments = $"exec {containerName} rm -f {containerPath}",
                RedirectStandardOutput = true,
                RedirectStandardError = true,
                UseShellExecute = false,
                CreateNoWindow = true
            };

            using var process = Process.Start(startInfo);
            if (process != null)
            {
                await process.WaitForExitAsync();
            }
        }
        catch (Exception ex)
        {
            Log.Warning($"清理容器临时文件失败: {ex.Message}");
        }
    }

    /// <summary>
    ///     执行pg_dump命令
    /// </summary>
    private async Task ExecutePgDump(string pgDumpPath, string arguments, long totalSize)
    {
        var startInfo = new ProcessStartInfo
        {
            FileName = pgDumpPath,
            Arguments = arguments,
            RedirectStandardOutput = true,
            RedirectStandardError = true,
            UseShellExecute = false,
            CreateNoWindow = true
        };

        // 设置环境变量避免密码提示
        var connectionInfo = ParseConnectionString(_db.CurrentConnectionConfig.ConnectionString);
        startInfo.Environment["PGPASSWORD"] = connectionInfo.Password;

        currentProcess = Process.Start(startInfo);
        if (currentProcess == null)
        {
            throw new InvalidOperationException("无法启动pg_dump进程");
        }

        // 监控进度
        _ = MonitorBackupProgress(totalSize);

        // 等待进程完成
        await currentProcess.WaitForExitAsync();

        if (currentProcess.ExitCode != 0 && !exCancel)
        {
            string error = await currentProcess.StandardError.ReadToEndAsync();
            throw new InvalidOperationException($"pg_dump执行失败: {error}");
        }
    }

    /// <summary>
    ///     使用Docker执行psql命令
    /// </summary>
    private async Task ExecutePsqlWithDocker(ConnectionInfo connectionInfo, string inputPath, long totalSize)
    {
        string containerName = App.Configuration["PostgreSQL:ContainerName"] ?? "postgresql";

        // 在容器内的恢复文件路径
        string containerRestorePath = "/tmp/restore.sql";

        // 复制恢复文件到容器
        await CopyFileToContainer(containerName, inputPath, containerRestorePath);

        try
        {
            // 构建Docker命令
            var args = new StringBuilder();
            args.Append($"exec {containerName} ");
            args.Append($"psql ");
            args.Append($"-h localhost ");
            args.Append($"-p 5432 ");
            args.Append($"-U {connectionInfo.Username} ");
            args.Append($"-d {connectionInfo.Database} ");
            args.Append($"-f {containerRestorePath}");

            var startInfo = new ProcessStartInfo
            {
                FileName = "docker",
                Arguments = args.ToString(),
                RedirectStandardOutput = true,
                RedirectStandardError = true,
                UseShellExecute = false,
                CreateNoWindow = true
            };

            // 设置环境变量避免密码提示
            startInfo.Environment["PGPASSWORD"] = connectionInfo.Password;

            currentProcess = Process.Start(startInfo);
            if (currentProcess == null)
            {
                throw new InvalidOperationException("无法启动Docker psql进程");
            }

            // 监控进度
            _ = MonitorRestoreProgress(totalSize);

            // 等待进程完成
            await currentProcess.WaitForExitAsync();

            if (currentProcess.ExitCode != 0 && !inCancel)
            {
                string error = await currentProcess.StandardError.ReadToEndAsync();
                throw new InvalidOperationException($"Docker psql执行失败: {error}");
            }
        }
        finally
        {
            // 清理容器内的临时文件
            await CleanupContainerFile(containerName, containerRestorePath);
        }
    }

    /// <summary>
    ///     复制文件到容器
    /// </summary>
    private static async Task CopyFileToContainer(string containerName, string hostPath, string containerPath)
    {
        var startInfo = new ProcessStartInfo
        {
            FileName = "docker",
            Arguments = $"cp \"{hostPath}\" {containerName}:{containerPath}",
            RedirectStandardOutput = true,
            RedirectStandardError = true,
            UseShellExecute = false,
            CreateNoWindow = true
        };

        using var process = Process.Start(startInfo);
        if (process == null)
        {
            throw new InvalidOperationException("无法启动Docker cp进程");
        }

        await process.WaitForExitAsync();

        if (process.ExitCode != 0)
        {
            string error = await process.StandardError.ReadToEndAsync();
            throw new InvalidOperationException($"复制文件到容器失败: {error}");
        }
    }

    /// <summary>
    ///     执行psql命令
    /// </summary>
    private async Task ExecutePsql(string arguments, long totalSize)
    {
        // 检测psql路径
        string? psqlPath = DetectPsqlPath();
        if (string.IsNullOrEmpty(psqlPath))
        {
            throw new InvalidOperationException("未找到psql工具");
        }

        var startInfo = new ProcessStartInfo
        {
            FileName = psqlPath,
            Arguments = arguments,
            RedirectStandardOutput = true,
            RedirectStandardError = true,
            UseShellExecute = false,
            CreateNoWindow = true
        };

        // 设置环境变量避免密码提示
        var connectionInfo = ParseConnectionString(_db.CurrentConnectionConfig.ConnectionString);
        startInfo.Environment["PGPASSWORD"] = connectionInfo.Password;

        currentProcess = Process.Start(startInfo);
        if (currentProcess == null)
        {
            throw new InvalidOperationException("无法启动psql进程");
        }

        // 监控进度
        _ = MonitorRestoreProgress(totalSize);

        // 等待进程完成
        await currentProcess.WaitForExitAsync();

        if (currentProcess.ExitCode != 0 && !inCancel)
        {
            string error = await currentProcess.StandardError.ReadToEndAsync();
            throw new InvalidOperationException($"psql执行失败: {error}");
        }
    }

    /// <summary>
    ///     检测psql工具路径
    /// </summary>
    private static string? DetectPsqlPath()
    {
        // 首先检查配置文件
        string? configPath = App.Configuration["PostgreSQL:PsqlPath"];
        if (!string.IsNullOrEmpty(configPath) && File.Exists(configPath))
        {
            return configPath;
        }

        // 根据pg_dump路径推断psql路径
        string? pgDumpPath = PostgreSqlToolDetector.DetectPgDumpPath();
        if (!string.IsNullOrEmpty(pgDumpPath))
        {
            string directory = Path.GetDirectoryName(pgDumpPath)!;
            string psqlFileName = RuntimeInformation.IsOSPlatform(OSPlatform.Windows)
                ? "psql.exe" : "psql";
            string psqlPath = Path.Combine(directory, psqlFileName);

            if (File.Exists(psqlPath))
            {
                return psqlPath;
            }
        }

        return null;
    }

    /// <summary>
    ///     监控备份进度
    /// </summary>
    private async Task MonitorBackupProgress(long totalSize)
    {
        long currentProgress = 0;
        var timer = new Timer(1000); // 每秒更新一次

        timer.Elapsed += (sender, e) =>
        {
            if (exCancel || currentProcess?.HasExited == true)
            {
                timer.Stop();
                return;
            }

            // 简单的进度估算，实际实现可以更复杂
            currentProgress += totalSize / 100; // 假设每秒完成1%
            if (currentProgress > totalSize) currentProgress = totalSize;

            string console = new
            {
                TotalRowsInAllTables = totalSize,
                CurrentRowIndexInAllTables = currentProgress,
                CurrentTableIndex = 1,
                CurrentTableName = "备份中...",
                TotalRowsInCurrentTable = totalSize,
                CurrentRowIndexInCurrentTable = currentProgress,
                TotalTables = 1
            }.ToJson();

            // 使用Task.Run来处理异步调用
            Task.Run(async () => await _socketService.Send(console, "dataBaseExPort"));
        };

        timer.Start();

        // 等待进程完成或取消
        while (currentProcess != null && !currentProcess.HasExited && !exCancel)
        {
            await Task.Delay(100);
        }

        timer.Stop();

        // 发送完成消息
        if (!exCancel)
        {
            string console = new
            {
                TotalRowsInAllTables = totalSize,
                CurrentRowIndexInAllTables = totalSize
            }.ToJson();
            await _socketService.Send(console, "dataBaseExPort");
        }
    }

    /// <summary>
    ///     监控恢复进度
    /// </summary>
    private async Task MonitorRestoreProgress(long totalSize)
    {
        long currentProgress = 0;
        var timer = new Timer(1000); // 每秒更新一次

        timer.Elapsed += (sender, e) =>
        {
            if (inCancel || currentProcess?.HasExited == true)
            {
                timer.Stop();
                return;
            }

            // 简单的进度估算
            currentProgress += totalSize / 100; // 假设每秒完成1%
            if (currentProgress > totalSize) currentProgress = totalSize;

            string console = new
            {
                TotalBytes = totalSize,
                CurrentBytes = currentProgress
            }.ToJson();

            // 使用Task.Run来处理异步调用
            Task.Run(async () => await _socketService.Send(console, "dataBaseInPort"));
        };

        timer.Start();

        // 等待进程完成或取消
        while (currentProcess != null && !currentProcess.HasExited && !inCancel)
        {
            await Task.Delay(100);
        }

        timer.Stop();
    }

    /// <summary>
    ///     压缩数据
    /// </summary>
    private static byte[] CompressData(byte[] data)
    {
        using var output = new MemoryStream();
        using (var gzip = new GZipStream(output, CompressionMode.Compress))
        {
            gzip.Write(data, 0, data.Length);
        }
        return output.ToArray();
    }

    /// <summary>
    ///     解压缩数据
    /// </summary>
    private static byte[] DecompressData(byte[] data)
    {
        using var input = new MemoryStream(data);
        using var gzip = new GZipStream(input, CompressionMode.Decompress);
        using var output = new MemoryStream();
        gzip.CopyTo(output);
        return output.ToArray();
    }

    /// <summary>
    ///     判断是否应该连接宿主机PostgreSQL
    /// </summary>
    private static bool ShouldConnectToHostPostgreSQL()
    {
        string deploymentMode = App.Configuration["PostgreSQL:DeploymentMode"] ?? "Auto";

        // 如果明确配置为宿主机模式，且当前应用在容器中
        if (deploymentMode.Equals("Host", StringComparison.OrdinalIgnoreCase))
        {
            return PostgreSqlToolDetector.IsCurrentApplicationInContainer();
        }

        // 自动模式下，如果当前应用在容器中且没有PostgreSQL容器运行
        if (deploymentMode.Equals("Auto", StringComparison.OrdinalIgnoreCase))
        {
            if (PostgreSqlToolDetector.IsCurrentApplicationInContainer())
            {
                string containerName = App.Configuration["PostgreSQL:ContainerName"] ?? "postgresql";
                return !PostgreSqlToolDetector.IsPostgreSqlContainerRunning(containerName);
            }
        }

        return false;
    }

    /// <summary>
    ///     使用Docker容器内工具连接宿主机PostgreSQL执行备份
    /// </summary>
    private async Task ExecutePgDumpWithDockerToHost(ConnectionInfo connectionInfo, string outputPath, long totalSize)
    {
        // 获取宿主机地址配置
        string hostAddress = App.Configuration["PostgreSQL:HostConnection:HostAddress"] ?? "host.docker.internal";

        // 修改连接信息以连接宿主机
        var hostConnectionInfo = new ConnectionInfo
        {
            Host = hostAddress,
            Port = connectionInfo.Port,
            Database = connectionInfo.Database,
            Username = connectionInfo.Username,
            Password = connectionInfo.Password
        };

        // 在容器内创建临时备份文件
        string containerBackupPath = $"/tmp/pg_backup_{Guid.NewGuid()}.sql";

        // 构建pg_dump命令
        string arguments = $"exec -i {App.Configuration["PostgreSQL:ContainerName"] ?? "postgresql"} " +
                          $"pg_dump -h {hostConnectionInfo.Host} -p {hostConnectionInfo.Port} " +
                          $"-U {hostConnectionInfo.Username} -d {hostConnectionInfo.Database} " +
                          $"-f {containerBackupPath} --verbose --no-password";

        ProcessStartInfo startInfo = new()
        {
            FileName = "docker",
            Arguments = arguments,
            RedirectStandardOutput = true,
            RedirectStandardError = true,
            UseShellExecute = false,
            CreateNoWindow = true
        };

        // 设置环境变量避免密码提示
        startInfo.Environment["PGPASSWORD"] = hostConnectionInfo.Password;

        currentProcess = Process.Start(startInfo);
        if (currentProcess == null)
        {
            throw new InvalidOperationException("无法启动Docker pg_dump进程连接宿主机");
        }

        // 监控进度
        _ = MonitorBackupProgress(totalSize);

        // 等待进程完成
        await currentProcess.WaitForExitAsync();

        if (currentProcess.ExitCode != 0 && !exCancel)
        {
            string error = await currentProcess.StandardError.ReadToEndAsync();
            throw new InvalidOperationException($"Docker pg_dump连接宿主机执行失败: {error}");
        }

        // 从容器复制备份文件到宿主机
        if (!exCancel)
        {
            string containerName = App.Configuration["PostgreSQL:ContainerName"] ?? "postgresql";
            await CopyFileFromContainer(containerName, containerBackupPath, outputPath);

            // 清理容器内的临时文件
            await CleanupContainerFile(containerName, containerBackupPath);
        }
    }

    /// <summary>
    ///     使用Docker容器内工具连接宿主机PostgreSQL执行还原
    /// </summary>
    private async Task ExecutePsqlWithDockerToHost(ConnectionInfo connectionInfo, string inputPath, long totalSize)
    {
        // 获取宿主机地址配置
        string hostAddress = App.Configuration["PostgreSQL:HostConnection:HostAddress"] ?? "host.docker.internal";

        // 修改连接信息以连接宿主机
        var hostConnectionInfo = new ConnectionInfo
        {
            Host = hostAddress,
            Port = connectionInfo.Port,
            Database = connectionInfo.Database,
            Username = connectionInfo.Username,
            Password = connectionInfo.Password
        };

        string containerName = App.Configuration["PostgreSQL:ContainerName"] ?? "postgresql";

        // 在容器内的恢复文件路径
        string containerRestorePath = "/tmp/restore.sql";

        // 复制恢复文件到容器
        await CopyFileToContainer(containerName, inputPath, containerRestorePath);

        // 构建psql命令
        string arguments = $"exec -i {containerName} " +
                          $"psql -h {hostConnectionInfo.Host} -p {hostConnectionInfo.Port} " +
                          $"-U {hostConnectionInfo.Username} -d {hostConnectionInfo.Database} " +
                          $"-f {containerRestorePath} --no-password";

        ProcessStartInfo startInfo = new()
        {
            FileName = "docker",
            Arguments = arguments,
            RedirectStandardOutput = true,
            RedirectStandardError = true,
            UseShellExecute = false,
            CreateNoWindow = true
        };

        // 设置环境变量避免密码提示
        startInfo.Environment["PGPASSWORD"] = hostConnectionInfo.Password;

        currentProcess = Process.Start(startInfo);
        if (currentProcess == null)
        {
            throw new InvalidOperationException("无法启动Docker psql进程连接宿主机");
        }

        // 监控进度
        _ = MonitorRestoreProgress(totalSize);

        // 等待进程完成
        await currentProcess.WaitForExitAsync();

        if (currentProcess.ExitCode != 0 && !inCancel)
        {
            string error = await currentProcess.StandardError.ReadToEndAsync();
            throw new InvalidOperationException($"Docker psql连接宿主机执行失败: {error}");
        }

        // 清理容器内的临时文件
        if (!inCancel)
        {
            await CleanupContainerFile(containerName, containerRestorePath);
        }
    }

    /// <summary>
    ///     连接信息
    /// </summary>
    private class ConnectionInfo
    {
        public string Host { get; set; } = "";
        public int Port { get; set; } = 5432;
        public string Database { get; set; } = "";
        public string Username { get; set; } = "";
        public string Password { get; set; } = "";
    }
}
