namespace IotPlatform.Application.Service.BackupServices;

/// <summary>
///     PostgreSQL备份验证服务
/// </summary>
[ApiDescriptionSettings("备份管理")]
public class PostgreSqlBackupValidationService : IDynamicApiController, ITransient
{
    private readonly DataBaseBackupFactory _backupFactory;
    private readonly ISqlSugarClient _db;

    public PostgreSqlBackupValidationService(DataBaseBackupFactory backupFactory, ISqlSugarClient db)
    {
        _backupFactory = backupFactory;
        _db = db;
    }

    /// <summary>
    ///     验证PostgreSQL备份功能
    /// </summary>
    /// <returns>验证结果</returns>
    [HttpGet("/postgresql/backup/validate")]
    public dynamic ValidatePostgreSqlBackup()
    {
        try
        {
            // 1. 验证工厂模式
            var backup = _backupFactory.CreateBackup(_db.CurrentConnectionConfig);
            
            // 2. 验证工具检测
            var (isAvailable, message, pgDumpPath, pgRestorePath, useDocker) = PostgreSqlToolDetector.ValidateTools();
            
            // 3. 验证数据库连接
            bool canConnect = false;
            string connectionError = "";
            try
            {
                using var connection = new Npgsql.NpgsqlConnection(_db.CurrentConnectionConfig.ConnectionString);
                connection.Open();
                canConnect = true;
                connection.Close();
            }
            catch (Exception ex)
            {
                connectionError = ex.Message;
            }

            return new
            {
                Success = true,
                DatabaseType = _db.CurrentConnectionConfig.DbType.ToString(),
                BackupInstanceType = backup.GetType().Name,
                ToolsValidation = new
                {
                    IsAvailable = isAvailable,
                    Message = message,
                    PgDumpPath = pgDumpPath,
                    PgRestorePath = pgRestorePath,
                    UseDocker = useDocker
                },
                DatabaseConnection = new
                {
                    CanConnect = canConnect,
                    Error = connectionError
                },
                Recommendations = GetRecommendations(isAvailable, canConnect)
            };
        }
        catch (Exception ex)
        {
            return new
            {
                Success = false,
                Error = ex.Message,
                StackTrace = ex.StackTrace
            };
        }
    }

    private static List<string> GetRecommendations(bool toolsAvailable, bool canConnect)
    {
        var recommendations = new List<string>();

        if (!toolsAvailable)
        {
            recommendations.Add("请安装PostgreSQL客户端工具 (pg_dump, pg_restore, psql)");
            recommendations.Add("Windows: 下载并安装PostgreSQL");
            recommendations.Add("Linux: sudo apt-get install postgresql-client");
            recommendations.Add("或在配置文件中指定工具路径");
        }

        if (!canConnect)
        {
            recommendations.Add("请检查数据库连接配置");
            recommendations.Add("确保PostgreSQL服务正在运行");
            recommendations.Add("检查网络连接和防火墙设置");
        }

        if (toolsAvailable && canConnect)
        {
            recommendations.Add("✅ 系统已准备就绪，可以使用PostgreSQL备份功能");
        }

        return recommendations;
    }
}
