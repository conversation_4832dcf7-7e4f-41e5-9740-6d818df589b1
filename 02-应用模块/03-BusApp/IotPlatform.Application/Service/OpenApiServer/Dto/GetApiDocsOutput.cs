using IotPlatform.Core.Attribute;
using IotPlatform.Core.Extension;
using Newtonsoft.Json;

namespace IotPlatform.Application.Service.OpenApiServer.Dto;

/// <summary>
///     开放Api--获取api描述信息返回
/// </summary>
public class GetApiDocsOutput
{
    /// <summary>
    ///     认证模式：1签名认证；2Token认证；3无认证
    /// </summary>
    [JsonIgnore]
    public AuthorizeTypeEnum AuthorizeType { get; set; }

    /// <summary>
    ///     认证模式：1签名认证；2Token认证；3无认证
    /// </summary>
    public string AuthorizeTypeName => EnumExtensions.GetDescription(AuthorizeType);

    /// <summary>
    ///     请求协议:1Http;2WebSocket;3WebServer
    /// </summary>
    [JsonIgnore]
    public RequestProtocolEnum RequestProtocol { get; set; }

    /// <summary>
    ///     请求协议:1Http;2WebSocket;3WebServer
    /// </summary>
    public string RequestProtocolName => EnumExtensions.GetDescription(RequestProtocol);

    /// <summary>
    ///     请求协议:1WebSocket;2Http
    /// </summary>
    [JsonIgnore]
    public PushProtocolEnum PushProtocol { get; set; }

    /// <summary>
    ///     请求协议:1WebSocket;2Http
    /// </summary>
    public string PushProtocolName => EnumExtensions.GetDescription(PushProtocol);

    /// <summary>
    ///     具体api
    /// </summary>
    public List<ResultOutput> Result { get; set; }
}

/// <summary>
/// </summary>
public class ResultOutput
{
    /// <summary>
    ///     Api名称
    /// </summary>
    [Description("Api名称")]
    public string Name { get; set; }

    /// <summary>
    ///     Http请求方式:1:GET;2POST
    /// </summary>
    [Description("Http请求方式:1:GET;2:POST")]
    public HttpRequestTypeEnum HttpRequestType { get; set; }

    /// <summary>
    ///     Path
    /// </summary>
    [Description("请求地址")]
    public string Path { get; set; }

    /// <summary>
    ///     参数
    /// </summary>
    [SugarColumn(IsJson = true)]
    [Description("请求参数")]
    public List<RequestParams> Params { get; set; }

    /// <summary>
    ///     返回结果
    /// </summary>
    [SugarColumn(IsJson = true)]
    [Description("返回结果")]
    public List<RequestParams> Result { get; set; }
}