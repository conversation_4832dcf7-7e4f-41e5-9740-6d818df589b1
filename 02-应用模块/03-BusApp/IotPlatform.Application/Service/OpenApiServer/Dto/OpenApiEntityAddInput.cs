namespace IotPlatform.Application.Service.OpenApiServer.Dto;

/// <summary>
/// </summary>
public class OpenApiEntityAddInput
{
    /// <summary>
    ///     名称
    /// </summary>
    [Required]
    public string Name { get; set; }

    /// <summary>
    ///     Token
    /// </summary>
    public string Token { get; set; }

    /// <summary>
    ///     认证模式：1签名认证；2Token认证；3无认证
    /// </summary>
    public AuthorizeTypeEnum AuthorizeType { get; set; }

    /// <summary>
    ///     请求协议:1Http;2WebSocket;3WebServer
    /// </summary>
    [SugarColumn(ColumnDescription = "请求协议:1Http;2WebSocket;3WebServer")]
    public RequestProtocolEnum RequestProtocol { get; set; }

    /// <summary>
    ///     请求协议:1WebSocket;2Http
    /// </summary>
    [SugarColumn(ColumnDescription = "请求协议:1WebSocket;2Http")]
    public PushProtocolEnum PushProtocol { get; set; }

    /// <summary>
    ///     描述
    /// </summary>
    public string Desc { get; set; }
}

/// <summary>
///     开放Api--修改
/// </summary>
public class OpenApiEntityUpdateInput : OpenApiEntityAddInput
{
    /// <summary>
    ///     Id
    /// </summary>
    [Required]
    public long Id { get; set; }
}