using Furion.JsonSerialization;
using JsScript.Engine.EngineMethods;

namespace IotPlatform.Application.Service.SysScript;

/// <summary>
///     规则引擎-脚本列表
///     版 本:V4.0.0
///     版 权:杭州峰回科技有限公司
///     作 者:蒋状
///     日 期:2023-07-11
/// </summary>
[ApiDescriptionSettings("任务定义")]
[Route("")]
[Route("webhook/")]
public class SysScriptService : IDynamicApiController, ITransient
{
    private readonly JsScriptEnginePool _enginePool;
    private readonly ISqlSugarRepository<ProgramBlock.Entity.ProgramBlock> _script;
    private readonly ILogger<SysScriptService> _logger;

    /// <summary>
    ///     初始化系统脚本服务
    /// </summary>
    /// <param name="enginePool">脚本引擎池</param>
    /// <param name="script">脚本仓储</param>
    /// <param name="logger">日志记录器</param>
    public SysScriptService(
        JsScriptEnginePool enginePool,
        ISqlSugarRepository<ProgramBlock.Entity.ProgramBlock> script,
        ILogger<SysScriptService> logger)
    {
        _enginePool = enginePool;
        _script = script;
        _logger = logger;
    }

    /// <summary>
    ///     执行自定义脚本-自定义格式
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("script/otherAction")]
    [NonUnify]
    public async Task<IActionResult> OtherActionScript(EngineExecuteActionInput input)
    {
        try
        {
            await using IScriptContext context = await _enginePool.CreateContextAsync();
            (object result, LogEngine scriptLog) = await context.ExecuteAsync(
                "custom-script",
                input.Content,
                new { timestamp = DateTime.Now }
            );

            return new JsonResult(result, App.GetOptions<JsonOptions>()?.JsonSerializerOptions);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "执行自定义脚本失败");
            return new JsonResult(
                "{\"code\":500,\"error\":\"" + ex.Message + "\"}",
                App.GetOptions<JsonOptions>()?.JsonSerializerOptions
            );
        }
    }

    /// <summary>
    ///     执行带参数脚本
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/engine/action")]
    public async Task<ActionScriptDto> ActionScriptConValue(ActionScriptConValueInput input)
    {
        try
        {
            await using IScriptContext context = await _enginePool.CreateContextAsync();

            // 设置租户ID
            string? tenantId = App.User?.FindFirst(ClaimConst.TenantId)?.Value;
            if (!string.IsNullOrEmpty(tenantId))
            {
                context.SetVariable("tenantId", tenantId);
            }

            // 设置其他变量
            foreach ((string key, object value) in input.Value)
            {
                if (value != null)
                {
                    try
                    {
                        context.SetVariable(key, value);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "设置脚本变量失败: {Key}={Value}", key, value);
                    }
                }
            }

            // 执行脚本
            (object result, LogEngine scriptLog) = await context.ExecuteAsync(
                "parameterized-script",
                input.Content,
                new { timestamp = DateTime.Now }
            );

            return new ActionScriptDto
            {
                Value = result ?? string.Empty,
                Logs = scriptLog.Logs
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "执行带参数脚本失败");
            throw;
        }
    }

    /// <summary>
    ///     根据脚本Id获取对应脚本并执行
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("script/getAction")]
    [AllowAnonymous]
    public async Task<ActionScriptDto> GetAction(GetActionInput input)
    {
        try
        {
            // 获取脚本
            ProgramBlock.Entity.ProgramBlock script = await _script.AsSugarClient()
                .Queryable<ProgramBlock.Entity.ProgramBlock>()
                .FirstAsync(f => f.Id == input.Id);

            if (script == null)
            {
                throw Oops.Oh(ErrorCode.COM1005);
            }

            if (string.IsNullOrEmpty(script.Content))
            {
                throw Oops.Oh("脚本内容为空！");
            }

            // 创建执行上下文
            await using IScriptContext context = await _enginePool.CreateContextAsync();

            // 设置变量
            foreach ((string key, object value) in input.Value)
            {
                if (value != null)
                {
                    try
                    {
                        context.SetVariable(key, value);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "设置脚本变量失败: {Key}={Value}", key, value);
                    }
                }
            }

            // 执行脚本
            (object result, LogEngine scriptLog) = await context.ExecuteAsync(
                $"script-{script.Id}",
                script.Content,
                new { timestamp = DateTime.Now }
            );

            return new ActionScriptDto
            {
                Value = result,
                Logs = scriptLog.Logs
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "执行脚本失败 - ID: {ScriptId}", input.Id);
            throw;
        }
    }

    /// <summary>
    ///     根据脚本Id获取对应脚本并执行-自定义返回结果
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("script/otherGetAction")]
    [AllowAnonymous]
    [NonUnify]
    public async Task<IActionResult> OtherGetAction(GetActionInput input)
    {
        try
        {
            ProgramBlock.Entity.ProgramBlock script = await _script.AsSugarClient()
                .Queryable<ProgramBlock.Entity.ProgramBlock>()
                .FirstAsync(f => f.Id == input.Id);

            if (script == null)
            {
                // 使用 Newtonsoft.Json 序列化错误响应
                return new ContentResult
                {
                    Content = "{\"code\":500,\"error\":\"" + "检测数据不存在" + "\"}",
                    ContentType = "application/json",
                    StatusCode = 500
                };
            }

            if (string.IsNullOrEmpty(script.Content))
            {
                throw Oops.Oh("脚本内容为空！");
            }

            await using IScriptContext context = await _enginePool.CreateContextAsync();

            foreach ((string key, object value) in input.Value)
            {
                if (value != null)
                {
                    try
                    {
                        context.SetVariable(key, value);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "设置脚本变量失败: {Key}={Value}", key, value);
                    }
                }
            }

            (object result, LogEngine scriptLog) = await context.ExecuteAsync(
                $"other-script-{script.Id}",
                script.Content,
                new { timestamp = DateTime.Now }
            );

            // 检查结果是否已经是JSON字符串
            string jsonContent;
            if (result is string resultString &&
                ((resultString.StartsWith("{") && resultString.EndsWith("}")) ||
                 (resultString.StartsWith("[") && resultString.EndsWith("]"))))
            {
                // 结果已经是JSON字符串
                jsonContent = resultString;
            }
            else
            {
                // 结果不是JSON字符串，需要序列化
                // 使用System.Text.Json序列化
                jsonContent = JSON.Serialize(result);
            }

            // 使用 ContentResult 返回 JSON 字符串
            return new ContentResult
            {
                Content = jsonContent,
                ContentType = "application/json"
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "执行自定义脚本失败 - ID: {ScriptId}", input.Id);

            // 使用 ContentResult 返回错误信息
            return new ContentResult
            {
                Content = "{\"code\":500,\"error\":\"" + ex.Message.Replace("\"", "\\\"") + "\"}",
                ContentType = "application/json",
                StatusCode = 500
            };
        }
    }
}