namespace IotPlatform.Application.Service.System;

/// <summary>
///     系统版本信息设置
/// </summary>
[ApiDescriptionSettings("系统服务")]
public class SystemConfigService : IDynamicApiController, ITransient
{
    private readonly ISqlSugarRepository<SystemConfig> _systemSetting;

    /// <summary>
    /// </summary>
    /// <param name="systemSetting"></param>
    public SystemConfigService(ISqlSugarRepository<SystemConfig> systemSetting)
    {
        _systemSetting = systemSetting;
    }

    /// <summary>
    ///     系统设置详情
    /// </summary>
    /// <returns></returns>
    [HttpGet("/systemSetting/detail")]
    [AllowAnonymous]
    [DisplayName("系统设置详情")]
    public async Task<SystemConfig> Detail()
    {
        SystemConfig config = await _systemSetting.AsQueryable().FirstAsync();
        config.Version = StringExtension.Version;
        return config;
    }

    /// <summary>
    ///     系统设置修改
    /// </summary>
    /// <returns></returns>
    [HttpPost("/systemSetting/save")]
    [DisplayName("系统设置修改")]
    public async Task Save(SystemConfig input)
    {
        await _systemSetting.AsSugarClient().Storageable(input).ExecuteCommandAsync();
    }

    /// <summary>
    ///     生成随机id
    /// </summary>
    /// <returns></returns>
    [HttpGet("/system/getId")]
    [DisplayName("生成随机id")]
    public Task<long> GetId()
    {
        return Task.FromResult(YitIdHelper.NextId());
    }
}