global using Furion.DependencyInjection;
global using Furion.DynamicApiController;
global using Furion.FriendlyException;
global using Furion.JsonSerialization;
global using Furion.Logging;
global using Mapster;
global using Microsoft.AspNetCore.Mvc;
global using SqlSugar;
global using System.ComponentModel.DataAnnotations;
global using System.Text.Json.Serialization;
global using System.ComponentModel;
global using IotPlatform.PolicyMessage.Entity;
global using IotPlatform.PolicyMessage.Entity.Dto.Group;
global using Furion.EventBus;
global using IotPlatform.PolicyMessage.Servers;
global using Microsoft.Extensions.DependencyInjection;
global using IotPlatform.PolicyMessage.Entity.Dto.Template;
global using IotPlatform.PolicyMessage.Entity.Dto.Channel;
global using Systems.Entity;
global using Common.Models;
global using Extras.DatabaseAccessor.SqlSugar.Repositories;
global using Extras.Thridparty.Email;
global using Extras.Thridparty.WeChat;
global using Extras.DatabaseAccessor.SqlSugar.Extensions;
global using Extras.DatabaseAccessor.SqlSugar.Internal;