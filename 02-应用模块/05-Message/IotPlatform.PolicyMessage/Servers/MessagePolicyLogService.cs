using IotPlatform.Core.Extension;
using IotPlatform.PolicyMessage.Entity.Dto.Log;
using DateTime = System.DateTime;

namespace IotPlatform.PolicyMessage.Servers;

/// <summary>
///     消息配置-推送日志
///     版 本:V5.0.0
///     版 权:杭州峰回科技有限公司
///     作 者:蒋状
///     日 期:2023-05-25
/// </summary>
[ApiDescriptionSettings("消息配置")]
public class MessagePolicyLogService : IDynamicApiController, ITransient
{
    private readonly ISqlSugarRepository<SendStrategyLog> _sendStrategyLog;

    /// <summary>
    /// </summary>
    /// <param name="sendStrategyLog"></param>
    public MessagePolicyLogService(ISqlSugarRepository<SendStrategyLog> sendStrategyLog)
    {
        _sendStrategyLog = sendStrategyLog;
    }

    /// <summary>
    ///     推送日志-列表
    /// </summary>
    /// <returns></returns>
    [HttpGet("/sendStrategyLog/page")]
    [DisplayName("推送日志-列表")]
    public async Task<SqlSugarPagedList<SendStrategyLog>> MessagePolicyLogPage([FromQuery] MessagePolicyLogPageInput input)
    {
        SqlSugarPagedList<SendStrategyLog>? sendStrategyPage = await _sendStrategyLog.AsQueryable()
            .WhereIF(input.SendStrategyId > 0, w => w.SendStrategyId == input.SendStrategyId)
            .WhereIF(input.SearchValue.IsNotEmptyOrNull(), u => u.Name.Contains(input.SearchValue))
            .WhereIF(!string.IsNullOrEmpty(input.SearchBeginTime?.Trim()), u =>
                u.CreatedTime >= DateTime.Parse(input.SearchBeginTime!.Trim()) &&
                u.CreatedTime <= DateTime.Parse(input.SearchEndTime.Trim()))
            .OrderByDescending(o => o.CreatedTime)
            .ToPagedListAsync(input.PageNo, input.PageSize);
        return sendStrategyPage;
    }
}