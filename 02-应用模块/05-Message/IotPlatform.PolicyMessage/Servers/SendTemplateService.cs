using IotPlatform.Core.Enum;
using IotPlatform.Core.Extension;
using SendTemplateInput = IotPlatform.PolicyMessage.Entity.Dto.Template.SendTemplateInput;

namespace IotPlatform.PolicyMessage.Servers;

/// <summary>
///     消息配置-消息模板
///     版 本:V5.0.0
///     版 权:杭州峰回科技有限公司
///     作 者:蒋状
///     日 期:2023-07-01
/// </summary>
[ApiDescriptionSettings("消息配置")]
public class SendTemplateService : IDynamicApiController, ITransient
{
    private readonly ISqlSugarRepository<SendTemplate> _sendTemplate;
    private readonly MessagePushTemplatePreviewService _messagePushTemplatePreviewService;

    /// <summary>
    ///     消息推送模板
    /// </summary>
    /// <param name="sendTemplate"></param>
    /// <param name="db"></param>
    /// <param name="messagePushTemplatePreviewService"></param>
    public SendTemplateService(ISqlSugarRepository<SendTemplate> sendTemplate, MessagePushTemplatePreviewService messagePushTemplatePreviewService)
    {
        _sendTemplate = sendTemplate;
        _messagePushTemplatePreviewService = messagePushTemplatePreviewService;
    }

    /// <summary>
    ///     消息模板-列表
    /// </summary>
    /// <returns></returns>
    [HttpGet("/sendTemplate/page")]
    public async Task<SqlSugarPagedList<SendTemplatePageOutput>> SendTemplatePage([FromQuery] SendTemplatePageInput input)
    {
        SqlSugarPagedList<SendTemplate>? sendTemplatePage = await _sendTemplate.AsQueryable()
            .WhereIF(input.Type > 0, w => w.Type == (SendTemplateTypeEnum) input.Type)
            .WhereIF(input.SearchValue.IsNotEmptyOrNull(), u => u.Name.Contains(input.SearchValue))
            .Includes(w => w.SendTemplateInputs)
            .ToPagedListAsync(input.PageNo, input.PageSize);

        return sendTemplatePage.Adapt<SqlSugarPagedList<SendTemplatePageOutput>>();
    }

    /// <summary>
    ///     消息模板-详情
    /// </summary>
    /// <returns></returns>
    [HttpGet("/sendTemplate/detail")]
    public async Task<SendTemplate> SendTemplateDetail([FromQuery] BaseId input)
    {
        return await _sendTemplate.AsQueryable().Where(w => w.Id == input.Id)
            .Includes(w => w.SendTemplateInputs)
            .FirstAsync();
    }

    /// <summary>
    ///     消息模板-下拉框
    /// </summary>
    /// <returns></returns>
    [HttpGet("/sendTemplate/select")]
    public async Task<List<StandardSelectOutput>> SendTemplateSelect()
    {
        return await _sendTemplate.AsQueryable().Select(s => new StandardSelectOutput
        {
            Id = s.Id,
            Text = s.Name,
            Value = s.Name
        }).ToListAsync();
    }

    /// <summary>
    ///     消息模板-新增
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/sendTemplate/add")]
    public async Task SendTemplateAdd(SendTemplateInput input)
    {
        bool isExist = await _sendTemplate.IsAnyAsync(u => u.Name == input.Name);
        if (isExist)
        {
            throw Oops.Oh(ErrorCode.Com1004);
        }

        SendTemplate sendTemplate = input.Adapt<SendTemplate>();
        switch (input.Type)
        {
            case SendTemplateTypeEnum.Text:
                sendTemplate.Content = JSON.Serialize(input.TextContent);
                break;
            case SendTemplateTypeEnum.Image:
                sendTemplate.Content = JSON.Serialize(input.ImageContent);
                break;
            case SendTemplateTypeEnum.Card:
                sendTemplate.Content = JSON.Serialize(input.CardContent);
                break;
        }

        await _sendTemplate.AsSugarClient().InsertNav(sendTemplate).Include(w => w.SendTemplateInputs)
            .ExecuteCommandAsync();
    }

    /// <summary>
    ///     消息模板-修改
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/sendTemplate/update")]
    public async Task SendTemplateUpdate(SendTemplateUpdateInput input)
    {
        bool isExist = await _sendTemplate.IsAnyAsync(u => u.Name == input.Name && u.Id != input.Id);
        if (isExist)
        {
            throw Oops.Oh(ErrorCode.Com1004);
        }

        SendTemplate? sendTemplate = await _sendTemplate.GetSingleAsync(s => s.Id == input.Id);
        if (sendTemplate == null)
        {
            throw Oops.Oh(ErrorCode.D1002);
        }

        sendTemplate = input.Adapt<SendTemplate>();
        switch (input.Type)
        {
            case SendTemplateTypeEnum.Text:
                sendTemplate.Content = JSON.Serialize(input.TextContent);
                break;
            case SendTemplateTypeEnum.Image:
                sendTemplate.Content = JSON.Serialize(input.ImageContent);
                break;
            case SendTemplateTypeEnum.Card:
                sendTemplate.Content = JSON.Serialize(input.CardContent);
                break;
            default:
                throw new ArgumentOutOfRangeException();
        }

        await _sendTemplate.AsSugarClient().UpdateNav(sendTemplate)
            .Include(x => x.SendTemplateInputs)
            .ExecuteCommandAsync();
    }

    /// <summary>
    ///     消息模板-删除
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/sendTemplate/delete")]
    public async Task SendTemplateDelete(BaseId input)
    {
        bool isExist = await _sendTemplate.AsSugarClient().Queryable<SendStrategy>().AnyAsync(u => u.SendTemplateId == input.Id);
        if (isExist)
        {
            throw Oops.Oh(ErrorCode.D1007);
        }

        SendTemplate? sendTemplate = await _sendTemplate.AsQueryable()
            .Where(w => w.Id == input.Id)
            .Includes(w => w.SendTemplateInputs)
            .FirstAsync();
        if (sendTemplate == null)
        {
            throw Oops.Oh(ErrorCode.D1002);
        }

        await _sendTemplate.AsSugarClient().DeleteNav(sendTemplate).Include(w => w.SendTemplateInputs)
            .ExecuteCommandAsync();
    }


    /// <summary>
    ///     消息模板-预览
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/sendTemplate/run")]
    public Task<Dictionary<string, string>> SendTemplateRun(SendTemplateRun input)
    {
        return _messagePushTemplatePreviewService.MessagePushTemplatePreview(input);
    }
}