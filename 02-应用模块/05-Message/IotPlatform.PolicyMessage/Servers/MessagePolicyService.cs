using IotPlatform.Core.Enum;
using IotPlatform.Core.Extension;
using IotPlatform.PolicyMessage.Entity.Dto.Policy;

namespace IotPlatform.PolicyMessage.Servers;

/// <summary>
///     消息配置-消息策略
///     版 本:V5.0.0
///     版 权:杭州峰回科技有限公司
///     作 者:蒋状
///     日 期:2023-05-25
/// </summary>
[ApiDescriptionSettings("消息配置")]
public class MessagePolicyService : IDynamicApiController, ITransient
{
    private readonly ISqlSugarRepository<SendStrategy> _sendStrategy;
    private readonly ExecuteMessagePushStrategy _sendStrategyServiceOpen;

    /// <summary>
    ///     消息推送策略
    /// </summary>
    /// <param name="sendStrategy"></param>
    /// <param name="sendStrategyServiceOpen"></param>
    public MessagePolicyService(ISqlSugarRepository<SendStrategy> sendStrategy, ExecuteMessagePushStrategy sendStrategyServiceOpen)
    {
        _sendStrategy = sendStrategy;
        _sendStrategyServiceOpen = sendStrategyServiceOpen;
    }

    /// <summary>
    ///     消息策略-列表
    /// </summary>
    /// <returns></returns>
    [HttpGet("/sendStrategy/page")]
    [DisplayName("消息策略-列表")]
    public async Task<SqlSugarPagedList<SendStrategyPageOutput>> SendStrategyPage([FromQuery] BasePageInput input)
    {
        SqlSugarPagedList<SendStrategyPageOutput>? sendStrategyPage = await _sendStrategy.AsQueryable()
            .WhereIF(input.SearchValue.IsNotEmptyOrNull(), u => u.Name.Contains(input.SearchValue))
            .Select<SendStrategyPageOutput>()
            .ToPagedListAsync(input.PageNo, input.PageSize);

        return sendStrategyPage;
    }

    /// <summary>
    ///     消息策略-详情
    /// </summary>
    /// <returns></returns>
    [HttpGet("/sendStrategy/detail")]
    [DisplayName("消息策略-详情")]
    public async Task<SendStrategy> SendStrategyDetail([FromQuery] BaseId input)
    {
        return await _sendStrategy.GetFirstAsync(f => f.Id == input.Id);
    }

    /// <summary>
    ///     消息策略-下拉框
    /// </summary>
    /// <returns></returns>
    [HttpGet("/sendStrategy/select")]
    [DisplayName("消息策略-下拉框")]
    public async Task<List<StandardSelectOutput>> SendStrategySelect()
    {
        List<StandardSelectOutput>? sendStrategyPage = await _sendStrategy.AsQueryable()
            .Select(s => new StandardSelectOutput
            {
                Id = s.Id,
                Text = s.Name,
                Value = s.Name
            })
            .ToListAsync();
        return sendStrategyPage;
    }

    /// <summary>
    ///     消息策略-新增
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/sendStrategy/add")]
    [DisplayName("消息策略-新增")]
    public async Task SendStrategyAdd(SendStrategyAddInput input)
    {
        bool isExist = await _sendStrategy.IsAnyAsync(u => u.Name == input.Name);
        if (isExist)
        {
            throw Oops.Oh(ErrorCode.Com1004);
        }

        SendStrategy sendStrategy = input.Adapt<SendStrategy>();
        List<SendChannel>? sendChannels = await _sendStrategy.AsSugarClient().Queryable<SendChannel>()
            .Where(w => input.SendChannelId.Contains(w.Id)).ToListAsync();
        sendStrategy.SendChannel = sendChannels;
        sendStrategy.PushObjects = JSON.Serialize(input.PushObjectsModel);
        sendStrategy.ExPushObjects = JSON.Serialize(input.ExPushObjectsModel);
        await _sendStrategy.InsertAsync(sendStrategy);
    }

    /// <summary>
    ///     消息策略-修改
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/sendStrategy/update")]
    [DisplayName("消息策略-修改")]
    public async Task SendStrategyUpdate(SendStrategyUpdateInput input)
    {
        bool isExist = await _sendStrategy.IsAnyAsync(u => u.Name == input.Name && u.Id != input.Id);
        if (isExist)
        {
            throw Oops.Oh(ErrorCode.Com1004);
        }

        SendStrategy? sendStrategy = await _sendStrategy.GetSingleAsync(s => s.Id == input.Id);
        if (sendStrategy == null)
        {
            throw Oops.Oh(ErrorCode.D1002);
        }

        sendStrategy = input.Adapt<SendStrategy>();
        List<SendChannel>? sendChannels = await _sendStrategy.AsSugarClient().Queryable<SendChannel>()
            .Where(w => input.SendChannelId.Contains(w.Id)).ToListAsync();
        sendStrategy.SendChannel = sendChannels;
        sendStrategy.PushObjects = JSON.Serialize(input.PushObjectsModel);
        sendStrategy.ExPushObjects = JSON.Serialize(input.ExPushObjectsModel);
        await _sendStrategy.AsUpdateable(sendStrategy).ExecuteCommandAsync();
    }

    /// <summary>
    ///     消息策略-删掉
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/sendStrategy/delete")]
    [DisplayName("消息策略-删掉")]
    public async Task SendStrategyDelete(BaseId input)
    {
        SendStrategy? sendStrategy = await _sendStrategy.GetSingleAsync(s => s.Id == input.Id);
        if (sendStrategy == null)
        {
            throw Oops.Oh(ErrorCode.D1002);
        }

        //删除策略并删除日志
        await _sendStrategy.AsSugarClient().DeleteNav(sendStrategy).Include(w => w.SendStrategyLog)
            .ExecuteCommandAsync();
    }

    /// <summary>
    ///     消息策略-执行策略
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/sendStrategy/run")]
    [DisplayName("消息策略-执行策略")]
    public async Task<bool> SendStrategyRun(BaseId input)
    {
        return await _sendStrategyServiceOpen.ExecuteMessagePushStrategyTask(input);
    }
}