namespace IotPlatform.PolicyMessage.Entity.Dto.Policy;

/// <summary>
///     增加推送策略请求
/// </summary>
public class SendStrategyAddInput
{
    /// <summary>
    ///     名称
    /// </summary>
    [Required]
    public string Name { get; set; }

    /// <summary>
    ///     推送渠道Ids
    /// </summary>
    public List<long> SendChannelId { get; set; }

    /// <summary>
    ///     推送对象
    /// </summary>
    public PushObjectsModel PushObjectsModel { get; set; }

    /// <summary>
    ///     额外推送对象
    /// </summary>
    public ExPushObjectsModel ExPushObjectsModel { get; set; }

    /// <summary>
    ///     推送模板Id
    /// </summary>
    public long SendTemplateId { get; set; }


    /// <summary>
    ///     策略描述
    /// </summary>
    public string Remark { get; set; }
}