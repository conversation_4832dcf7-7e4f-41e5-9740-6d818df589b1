namespace IotPlatform.PolicyMessage.Entity.Dto.Policy;

/// <summary>
/// </summary>
public class SendStrategyPageOutput
{
    /// <summary>
    ///     Id
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    ///     名称
    /// </summary>
    public string Name { get; set; }

    /// <summary>
    ///     推送渠道
    /// </summary>
    [JsonIgnore]
    public string SendChannel { get; set; }

    /// <summary>
    ///     推送渠道
    /// </summary>
    [JsonIgnore]
    public List<SendChannel> SendChannelDto => JSON.Deserialize<List<SendChannel>>(SendChannel);

    /// <summary>
    ///     推送渠道Id
    /// </summary>
    public List<long> SendChannelId => SendChannelDto?.Select(s => s.Id).ToList()!;

    /// <summary>
    ///     推送对象
    /// </summary>
    [JsonIgnore]
    public string PushObjects { get; set; }

    /// <summary>
    /// </summary>
    public PushObjectsModel PushObjectsModel => JSON.Deserialize<PushObjectsModel>(PushObjects);

    /// <summary>
    ///     额外推送对象
    /// </summary>
    [JsonIgnore]
    public string ExPushObjects { get; set; }

    /// <summary>
    /// </summary>
    public ExPushObjectsModel ExPushObjectsModel => JSON.Deserialize<ExPushObjectsModel>(ExPushObjects);

    /// <summary>
    ///     推送模板Id
    /// </summary>
    public long SendTemplateId { get; set; }

    /// <summary>
    ///     推送模板
    /// </summary>
    public SendTemplate SendTemplate { get; set; }

    /// <summary>
    ///     策略描述
    /// </summary>
    public string Remark { get; set; }

    /// <summary>
    ///     租户Id
    /// </summary>
    public long? TenantId { get; set; }

    /// <summary>
    ///     创建时间
    /// </summary>
    public System.DateTime? CreatedTime { get; set; }
}