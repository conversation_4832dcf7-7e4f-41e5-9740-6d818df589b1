namespace IotPlatform.PolicyMessage.Entity.Dto.Channel;

/// <summary>
///     增加推送渠道请求
/// </summary>
public class SendChannelInput
{
    /// <summary>
    ///     名称
    /// </summary>
    [Required(ErrorMessage = "名称不能是空！")]
    public string Name { get; set; }

    /// <summary>
    ///     消息类型：1:企业微信；2:钉钉；3：邮件；4:短信
    /// </summary>
    public SendChannelTypeEnum Type { get; set; }

    /// <summary>
    ///     是否启用
    /// </summary>
    public bool Enable { get; set; }

    /// <summary>
    ///     企业微信
    /// </summary>
    public WechatConf? WechatConf { get; set; }

    /// <summary>
    ///     邮件
    /// </summary>
    public EmailConf? EmailConf { get; set; }
}

/// <summary>
///     修改推送渠道请求
/// </summary>
public class SendChannelUpdateInput : SendChannelInput
{
    /// <summary>
    ///     Id
    /// </summary>
    [Required]
    public long Id { get; set; }
}