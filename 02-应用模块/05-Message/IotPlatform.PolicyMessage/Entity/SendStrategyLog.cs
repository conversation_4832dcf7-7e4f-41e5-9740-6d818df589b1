namespace IotPlatform.PolicyMessage.Entity;

/// <summary>
///     消息推送策略执行日志
/// </summary>
[SugarTable("business_sendMsgStrategyLog", "消息推送策略执行日志")]
public class SendStrategyLog : EntityTenant
{
    /// <summary>
    ///     推送来源
    /// </summary>
    [SugarColumn(ColumnDescription = "推送来源", Length = 64)]
    public string Name { get; set; }

    /// <summary>
    ///     推送人员
    /// </summary>
    [SugarColumn(ColumnDescription = "推送人员", IsNullable = true)]
    public string? ToUserNames { get; set; }

    /// <summary>
    ///     额外推送人员
    /// </summary>
    [SugarColumn(ColumnDescription = "额外推送人员", IsNullable = true)]
    public string? ToExUserNames { get; set; }

    /// <summary>
    ///     推送是否成功
    /// </summary>
    [SugarColumn(ColumnDescription = "推送是否成功")]
    public bool Success { get; set; }

    /// <summary>
    ///     推送策略Id
    /// </summary>
    public long SendStrategyId { get; set; }


    #region 关联表

    /// <summary>
    ///     推送渠道
    /// </summary>
    [SugarColumn(IsJson = true)]
    public SendChannel SendChannel { get; set; }

    /// <summary>
    ///     推送策略
    /// </summary>
    [Navigate(NavigateType.OneToOne, nameof(SendStrategyId))]
    public SendStrategy SendStrategy { get; set; }

    #endregion
}