namespace IotPlatform.PolicyMessage.Entity;

/// <summary>
///     消息推送模板参数
/// </summary>
[SugarTable("business_sendMsgTemplateInput", "消息推送模板参数")]
public class SendTemplateInputData : EntityTenant
{
    /// <summary>
    ///     推送模板Id
    /// </summary>
    [SugarColumn(ColumnDescription = "推送模板Id")]
    public long SendTemplateId { get; set; }

    /// <summary>
    ///     名称
    /// </summary>
    [SugarColumn(ColumnDescription = "名称")]
    public string Name { get; set; }

    /// <summary>
    ///     推送模板
    /// </summary>
    [Navigate(NavigateType.OneToOne, nameof(SendTemplateId))]
    public SendTemplate SendTemplate { get; set; }

    /// <summary>
    ///     消息推送模板参数类型：1：常量；2：属性；3：脚本
    /// </summary>
    [SugarColumn(ColumnDescription = "消息推送模板参数类型：1：常量；2：属性；3：脚本")]
    public SendTemplateInputTypeEnum Type { get; set; }

    /// <summary>
    ///     内容
    /// </summary>
    [SugarColumn(ColumnDescription = "内容", IsNullable = true)]
    public string? Content { get; set; }
}

/// <summary>
///     消息推送模板参数类型：1：常量；2：属性；3：脚本
/// </summary>
public enum SendTemplateInputTypeEnum
{
    /// <summary>
    ///     常量
    /// </summary>
    [Description("常量")] Const = 1,

    /// <summary>
    ///     属性
    /// </summary>
    [Description("属性")] Property = 2,

    /// <summary>
    ///     脚本
    /// </summary>
    [Description("脚本")] Script = 3
}