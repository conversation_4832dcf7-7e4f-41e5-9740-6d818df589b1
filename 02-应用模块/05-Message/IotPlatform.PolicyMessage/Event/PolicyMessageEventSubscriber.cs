using IotPlatform.Core.Const;
using IotPlatform.Core.Extension;

namespace IotPlatform.PolicyMessage.Event;

/// <summary>
///     监听触发消息通知
/// </summary>
public class PolicyMessageEventSubscriber : IEventSubscriber
{
    /// <summary>
    ///     监听触发消息通知
    /// </summary>
    /// <param name="services"></param>
    public PolicyMessageEventSubscriber(IServiceProvider services)
    {
        Services = services;
    }

    /// <summary>
    /// </summary>
    public IServiceProvider Services { get; }

    /// <summary>
    /// </summary>
    /// <returns></returns>
    [EventSubscribe(EventConst.PolicyMessage)]
    public async Task ProcessingThingExampleData(EventHandlerExecutingContext context)
    {
        // 队列处理设备数据
        using IServiceScope scope = Services.CreateScope();
        ExecuteMessagePushStrategy rep = scope.ServiceProvider.GetRequiredService<ExecuteMessagePushStrategy>();
        long id = context.GetPayload<long>();
        await rep.ExecuteMessagePushStrategyTask(new BaseId {Id = id});
    }
}