using Furion.Logging;
using IotPlatform.WorkFlow.Entity;
using IotPlatform.WorkFlow.Services;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;

namespace IotPlatform.WorkFlow.HostedService;

/// <summary>
/// 工作流托管服务
/// 负责管理和执行工作流实例，包括初始化、启动、停止等操作
/// </summary>
public class WorkFlowHostedService : IHostedService, IDisposable
{
    /// <summary>
    /// 数据库访问客户端
    /// </summary>
    private readonly ISqlSugarClient _db;

    /// <summary>
    /// 服务作用域工厂，用于创建服务作用域
    /// </summary>
    private readonly IServiceScope _serviceScope;

    /// <summary>
    /// 事件总线工厂，用于处理事件订阅和发布
    /// </summary>
    private readonly IEventBusFactory _eventBusFactory;

    /// <summary>
    /// JavaScript脚本引擎池，用于管理和复用脚本引擎实例
    /// </summary>
    private readonly JsScriptEnginePool _enginePool;

    /// <summary>
    /// 日志记录器
    /// </summary>
    private readonly ILogger<WorkFlowHostedService> _logger;

    /// <summary>
    /// 日志工厂
    /// </summary>
    private readonly ILoggerFactory _loggerFactory;

    /// <summary>
    /// 定时器，用于定期检查和更新工作流
    /// </summary>
    private Timer _timer;

    /// <summary>
    /// 运行中的工作流线程字典
    /// </summary>
    public readonly ConcurrentDictionary<long, FlowProjectThread> FlowProjectThreads = new();

    /// <summary>
    /// 初始化工作流托管服务
    /// </summary>
    /// <param name="db">数据库访问客户端</param>
    /// <param name="scopeFactory">服务作用域工厂</param>
    /// <param name="eventBusFactory">事件总线工厂</param>
    /// <param name="enginePool">脚本引擎池</param>
    /// <param name="logger">日志记录器</param>
    /// <param name="loggerFactory">日志工厂</param>
    public WorkFlowHostedService(
        ISqlSugarClient db,
        IServiceScopeFactory scopeFactory,
        IEventBusFactory eventBusFactory,
        JsScriptEnginePool enginePool,
        ILogger<WorkFlowHostedService> logger,
        ILoggerFactory loggerFactory)
    {
        _db = db;
        _serviceScope = scopeFactory.CreateScope();
        _eventBusFactory = eventBusFactory;
        _enginePool = enginePool;
        _logger = logger;
        _loggerFactory = loggerFactory;
    }

    /// <summary>
    /// 启动服务
    /// </summary>
    public Task StartAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation("工作流服务正在启动...");
        // 延迟15秒启动，每15分钟执行一次
        _timer = new Timer(DoWork, null, TimeSpan.FromSeconds(15), TimeSpan.FromMinutes(15));
        return Task.CompletedTask;
    }

    /// <summary>
    /// 定时执行工作
    /// </summary>
    private void DoWork(object? state)
    {
        try
        {
            // 获取所有已发布的工作流
            var projectList = _db.Queryable<FlowProject>()
                .Where(w => w.Status == FlowProjectStatusEnum.Publish && w.Pid > 0)
                .Includes(w => w.FlowNode, w => w.FlowNodeData)
                .Includes(w => w.FlowNode, w => w.ActionNodeData)
                .Includes(w => w.FlowNode, w => w.FlowProject)
                .Includes(w => w.FlowLine)
                .ToList();

            if (projectList.Count == 0)
            {
                return;
            }

            // 初始化新的工作流
            foreach (var project in projectList.Where(project => !FlowProjectThreads.ContainsKey(project.Id)))
            {
                InitializeWorkFlow(project);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "工作流服务执行时发生错误");
        }
    }

    /// <summary>
    /// 初始化工作流
    /// </summary>
    private void InitializeWorkFlow(FlowProject project)
    {
        try
        {
            _logger.LogInformation("正在初始化工作流: {Name}", project.Name);

            // 初始化节点状态
            foreach (var flowNode in project.FlowNode)
            {
                flowNode.Status = flowNode.NodeType == NodeTypeEnum.Start 
                    ? FlowNodeStatusEnum.Starting 
                    : FlowNodeStatusEnum.NotStarted;
                flowNode.StatusChangeTime = DateTime.Now;
            }

            // 创建工作流线程
            var flowProjectThread = new FlowProjectThread(
                _db,
                _eventBusFactory,
                _serviceScope,
                _enginePool,
                _loggerFactory.CreateLogger<FlowProjectThread>())
            {
                FlowProject = project
            };

            if (FlowProjectThreads.TryAdd(project.Id, flowProjectThread))
            {
                _logger.LogInformation("启动工作流: {Name}", project.Name);
                ThreadPool.QueueUserWorkItem(_ => flowProjectThread.Start());
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "工作流 {Name} 初始化失败", project.Name);
        }
    }

    /// <summary>
    /// 添加新的工作流
    /// </summary>
    public Task AddWorkFlow(FlowProject project)
    {
        if (FlowProjectThreads.ContainsKey(project.Id))
        {
            Stop(project.Id);
        }

        // 初始化节点状态
        foreach (var flowNode in project.FlowNode)
        {
            flowNode.Status = flowNode.NodeType == NodeTypeEnum.Start 
                ? FlowNodeStatusEnum.Starting 
                : FlowNodeStatusEnum.NotStarted;
            flowNode.StatusChangeTime = DateTime.Now;
        }

        // 创建并启动工作流线程
        var flowProjectThread = new FlowProjectThread(
            _db,
            _eventBusFactory,
            _serviceScope,
            _enginePool,
            _loggerFactory.CreateLogger<FlowProjectThread>())
        {
            FlowProject = project
        };

        if (FlowProjectThreads.TryAdd(project.Id, flowProjectThread))
        {
            ThreadPool.QueueUserWorkItem(_ => flowProjectThread.Start());
        }

        return Task.CompletedTask;
    }

    /// <summary>
    /// 停止指定的工作流
    /// </summary>
    public Task Stop(long id)
    {
        if (!FlowProjectThreads.TryGetValue(id, out var thread))
        {
            return Task.CompletedTask;
        }

        try
        {
            thread?.Stop();
            FlowProjectThreads[id]?.Dispose();
            FlowProjectThreads.TryRemove(id, out _);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "停止工作流 {Id} 时发生错误", id);
            FlowProjectThreads[id]?.Dispose();
            FlowProjectThreads.TryRemove(id, out _);
        }

        return Task.CompletedTask;
    }

    /// <summary>
    /// 停止服务
    /// </summary>
    public Task StopAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation("工作流服务正在停止...");
        _timer?.Change(Timeout.Infinite, 0);
        return Task.CompletedTask;
    }

    /// <summary>
    /// 释放资源
    /// </summary>
    public void Dispose()
    {
        _timer?.Dispose();
        _serviceScope?.Dispose();

        // 清理所有运行中的工作流
        foreach (var thread in FlowProjectThreads.Values)
        {
            thread?.Dispose();
        }
        FlowProjectThreads.Clear();
    }
}