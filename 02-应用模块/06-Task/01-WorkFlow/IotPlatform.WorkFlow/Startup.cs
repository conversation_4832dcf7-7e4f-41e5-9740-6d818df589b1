using Furion;
using IotPlatform.WorkFlow.HostedService;

namespace IotPlatform.WorkFlow;

/// <summary>
/// </summary>
[AppStartup(100)]
public class Startup : AppStartup
{
    /// <summary>
    /// </summary>
    /// <param name="services"></param>
    public void ConfigureServices(IServiceCollection services)
    {
        // 注册工作流服务
        services.AddSingleton<WorkFlowHostedService>();
        services.AddHostedService(provider => provider.GetRequiredService<WorkFlowHostedService>());
    }
}