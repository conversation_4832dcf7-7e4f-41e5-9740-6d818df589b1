namespace IotPlatform.WorkFlow.Entity;

/// <summary>
///     工作流触发动作参数定义
/// </summary>
[SugarTable("business_flowActionNodeData", "工作流触发动作参数定义")]
public class ActionNodeData : EntityTenantId
{
    /// <summary>
    ///     节点Id
    /// </summary>
    [SugarColumn(ColumnDescription = "节点Id")]
    public Guid NodeId { get; set; }

    /// <summary>
    ///     排序
    /// </summary>
    public short SortNum { get; set; }

    /// <summary>
    ///     节点
    /// </summary>
    [Navigate(NavigateType.OneToOne, nameof(NodeId))]
    public FlowNode FlowNode { get; set; }

    /// <summary>
    ///     数据节点定义数据类型枚举:1:读属性值;2:读数据库;3:读变量值;4:定义变量;5:执行任务
    /// </summary>
    public ActionNodeDataTypeEnum ActionNodeDataType { get; set; }

    /// <summary>
    ///     内容
    /// </summary>
    [SugarColumn(ColumnDescription = "具体内容", ColumnDataType = "longtext,text,clob")]
    public string Content { get; set; }
}

/// <summary>
///     写属性值
/// </summary>
public class WriteNodeDataPropertyModel
{
    /// <summary>
    /// 校验周期(ms)
    /// </summary>
    public int Period { get; set; }

    /// <summary>
    /// 写属性值内容
    /// </summary>
    public List<WriteNodeDataPropertyValue> Values { get; set; } = new();

}

/// <summary>
/// 写属性值内容
/// </summary>
public class WriteNodeDataPropertyValue
{
    /// <summary>
    ///     多层Id
    /// </summary>
    public string Id { get; set; }

    /// <summary>
    ///     属性名称
    /// </summary>
    public string Name { get; set; }
    
    /// <summary>
    ///     值
    /// </summary>
    public string Value { get; set; }
}

/// <summary>
///     写变量值
/// </summary>
public class WriteNodeDataVariableModel
{
    /// <summary>
    ///     变量名称
    /// </summary>
    public string Name { get; set; }

    /// <summary>
    ///     值来源
    /// </summary>
    public WriteNodeDataPropertyTypeEnum WriteNodeDataPropertyType { get; set; }

    /// <summary>
    ///     值
    /// </summary>
    public string Value { get; set; }
}

/// <summary>
///     休眠
/// </summary>
public class NodeDataSleepModel
{
    /// <summary>
    ///     休眠时间(ms)
    /// </summary>
    public short Second { get; set; }
}

/// <summary>
///     工作流触发动作参数定义类型枚举
/// </summary>
public enum ActionNodeDataTypeEnum
{
    /// <summary>
    ///     写数据库
    /// </summary>
    [Description("写数据库")] Db = 1,
    
    /// <summary>
    ///     写属性值
    /// </summary>
    [Description("写属性值")] Property = 3,

    /// <summary>
    ///     写变量值
    /// </summary>
    [Description("写变量值")] Variable = 4,

    /// <summary>
    ///     休眠
    /// </summary>
    [Description("休眠")] Sleep = 5,
    
    /// <summary>
    ///     程序块
    /// </summary>
    [Description("程序块")] TaskTemplate = 6,
}

/// <summary>
/// </summary>
public enum WriteNodeDataPropertyTypeEnum
{
    /// <summary>
    ///     常量
    /// </summary>
    [Description("常量")] Const = 1,

    /// <summary>
    ///     定义数据
    /// </summary>
    [Description("定义数据")] Variable = 2
}