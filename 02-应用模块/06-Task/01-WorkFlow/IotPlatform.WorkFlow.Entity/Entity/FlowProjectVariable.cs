namespace IotPlatform.WorkFlow.Entity;

/// <summary>
///     工作流模板变量
/// </summary>
[SugarTable("business_flowProjectVariable", "工作流模板变量")]
public class FlowProjectVariable : EntityTenantId
{
    /// <summary>
    ///     变量名称
    /// </summary>
    [SugarColumn(ColumnDescription = "变量名称", Length = 64)]
    public string Name { get; set; }

    /// <summary>
    ///     变量描述
    /// </summary>
    [SugarColumn(ColumnDescription = "变量描述", IsNullable = true, Length = 255)]
    public string? Description { get; set; }

    /// <summary>
    ///     所属模板ID
    /// </summary>
    [SugarColumn(ColumnDescription = "所属模板ID")]
    public long TemplateId { get; set; }

    /// <summary>
    ///     关联模板
    /// </summary>
    [Navigate(NavigateType.OneToOne, nameof(TemplateId))]
    public FlowProject Template { get; set; }

}