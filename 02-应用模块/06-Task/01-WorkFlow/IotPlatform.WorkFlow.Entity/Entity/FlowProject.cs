namespace IotPlatform.WorkFlow.Entity;

/// <summary>
///     工作流项目
/// </summary>
[SugarTable("business_flowProject", "工作流项目")]
public class FlowProject : EntityTenantId
{
    /// <summary>
    ///     名称
    /// </summary>
    [SugarColumn(ColumnDescription = "名称", Length = 64)]
    public string Name { get; set; }

    /// <summary>
    ///     流程实例类别： 1.组；2：节点
    /// </summary>
    [SugarColumn(ColumnDescription = "流程实例类别： 1.组；2：节点")]
    public FlowProjectTypeEnum FlowProjectType { get; set; }
    
    /// <summary>
    ///     上级节点
    /// </summary>
    [SugarColumn(ColumnDescription = "Pid")]
    public long Pid { get; set; }

    /// <summary>
    ///     状态：1：未发布；2：已发布；
    /// </summary>
    [SugarColumn(ColumnDescription = "状态：1：未发布；2：已发布；")]
    public FlowProjectStatusEnum Status { get; set; }

    /// <summary>
    ///     描述
    /// </summary>
    [SugarColumn(ColumnDescription = "描述", IsNullable = true, Length = 500)]
    public string? Remark { get; set; }

    #region 关联表

    /// <summary>
    ///     节点
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public List<FlowProject> Children { get; set; }

    /// <summary>
    ///     节点关系
    /// </summary>
    [Navigate(NavigateType.OneToMany, nameof(Entity.FlowLine.ProjectId))]
    [SugarColumn(IsIgnore = true)]
    public List<FlowLine> FlowLine { get; set; }

    /// <summary>
    ///     节点
    /// </summary>
    [Navigate(NavigateType.OneToMany, nameof(Entity.FlowNode.ProjectId))]
    [SugarColumn(IsIgnore = true)]
    public List<FlowNode> FlowNode { get; set; }

    #endregion
}

/// <summary>
///     流程实例类别： 1.组；2：节点
/// </summary>
public enum FlowProjectTypeEnum
{
    /// <summary>
    ///     组
    /// </summary>
    [Description("组")] Group = 1,

    /// <summary>
    ///     节点
    /// </summary>
    [Description("节点")] Node = 2
}

/// <summary>
///     流程项目状态
/// </summary>
public enum FlowProjectStatusEnum
{
    /// <summary>
    ///     未发布
    /// </summary>
    [Description("未发布")] NoPublish = 1,

    /// <summary>
    ///     已发布
    /// </summary>
    [Description("已发布")] Publish = 2
}