namespace IotPlatform.WorkFlow.Entity;

/// <summary>
///     工作流节点执行记录
/// </summary>
[SplitTable(SplitType.Day)]
[SugarTable("business_flowNodeRecord_{year}{month}{day}", "工作流节点执行记录")]
public class FlowNodeRecord : EntityTenantId
{
    /// <summary>
    ///     执行次数
    /// </summary>
    [SugarColumn(ColumnDescription = "执行次数")]
    public long ActionCount { get; set; }

    /// <summary>
    ///     执行开始时间
    /// </summary>
    [SugarColumn(ColumnDescription = "执行开始时间")]
    [SplitField]
    public DateTime StartTime { get; set; }

    /// <summary>
    ///     执行结束时间
    /// </summary>
    [SugarColumn(ColumnDescription = "执行结束时间")]
    public DateTime EndTime { get; set; }

    /// <summary>
    ///     执行耗时(毫秒)
    /// </summary>
    [SugarColumn(ColumnDescription = "执行耗时(毫秒)")]
    public string ConsumeTime { get; set; }

    /// <summary>
    ///     详细日志
    /// </summary>
    [SugarColumn(IsJson = true, ColumnDescription = "详细日志", ColumnDataType = "longtext,text,clob")]
    public List<FlowNodeRecordDetail> Details { get; set; }
    
    #region 关联表

    /// <summary>
    ///     项目Id
    /// </summary>
    [SugarColumn(ColumnDescription = "项目Id")]
    public long ProjectId { get; set; }

    /// <summary>
    /// </summary>
    [Navigate(NavigateType.OneToOne, nameof(ProjectId))]
    public FlowProject FlowProject { get; set; }

    #endregion
}

/// <summary>
///     任务执行日志
/// </summary>
public class FlowNodeRecordDetail
{
    /// <summary>
    ///     节点名称
    /// </summary>
    public string NodeName { get; set; }

    /// <summary>
    ///     执行开始时间
    /// </summary>
    public DateTime CreatedTime { get; set; }

    /// <summary>
    ///     执行完成时间
    /// </summary>
    public DateTime EndTime { get; set; }

    /// <summary>
    ///     执行耗时(毫秒)
    /// </summary>
    public string ConsumeTime { get; set; }

    /// <summary>
    ///     执行日志
    /// </summary>
    public string Logs { get; set; }

    /// <summary>
    ///     是否成功
    /// </summary>
    public bool Success { get; set; } = true;
}