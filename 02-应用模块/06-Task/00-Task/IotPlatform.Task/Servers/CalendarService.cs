using DateTime = System.DateTime;

namespace IotPlatform.Task.Servers;

/// <summary>
///     任务定义-任务日历
///     版 本:V5.0.0
///     版 权:杭州峰回科技有限公司
///     作 者:蒋状
///     日 期:2023-07-07
/// </summary>
public class TaskCalendarService : IDynamicApiController, ITransient
{
    private readonly ISqlSugarRepository<TaskCalendar> _calendar; // 任务日历表仓储
    private readonly ISqlSugarClient _db;

    /// <summary>
    /// </summary>
    /// <param name="calendar"></param>
    /// <param name="db"></param>
    public TaskCalendarService(ISqlSugarRepository<TaskCalendar> calendar, ISqlSugarClient db)
    {
        _calendar = calendar;
        _db = db;
    }

    /// <summary>
    ///     任务日历-日历树
    /// </summary>
    /// <returns></returns>
    /// <exception cref="AppFriendlyException"></exception>
    [HttpGet("/calendar/tree")]
    public async Task<List<TaskCalendar>> TaskCalendarTree()
    {
        ISugarQueryable<TaskCalendar>? calendars = _calendar.AsQueryable()
            .OrderBy(u => u.CreatedTime);
        return await calendars.ToTreeAsync(u => u.Children, u => u.Pid, 0);
    }

    /// <summary>
    ///     任务日历-增加
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/calendar/add")]
    public async System.Threading.Tasks.Task TaskCalendarAdd(CalendarAddInput input)
    {
        bool isExist = await _calendar.IsAnyAsync(u => u.Name == input.Name);
        if (isExist)
        {
            throw Oops.Oh(ErrorCode.D1002);
        }

        if (input.Pid > 0)
        {
            if (await _db.Queryable<TaskCalendarDetail>().AnyAsync(a => a.CalendarId == input.Pid))
            {
                throw Oops.Oh("当前节点下已存在日历数据，请先删除日历数据再建立子节点！");
            }
        }

        TaskCalendar calendar = input.Adapt<TaskCalendar>();
        calendar.Pids = await CreateNewPids(input.Pid);
        await _calendar.InsertAsync(calendar);
    }

    /// <summary>
    ///     创建Pids格式
    ///     如果pid是0顶级节点，pids就是 [0];
    ///     如果pid不是顶级节点，pids就是 pid菜单的 pids + [pid] + ,
    /// </summary>
    /// <param name="pid"></param>
    /// <returns></returns>
    private async Task<string> CreateNewPids(long pid)
    {
        if (pid == 0L)
        {
            return "[0],";
        }

        TaskCalendar? group = await _calendar.GetFirstAsync(u => u.Id == pid);
        return group!.Pids + "[" + pid + "],";
    }

    /// <summary>
    ///     任务日历-重命名
    /// </summary>
    /// <returns></returns>
    /// <exception cref="Exception"></exception>
    [HttpPost("/calendar/rename")]
    public async System.Threading.Tasks.Task ReName(BaseReName input)
    {
        TaskCalendar? calendar = await _calendar.GetFirstAsync(f => f.Id == input.Id);
        if (calendar == null)
        {
            throw Oops.Oh(ErrorCode.COM1005);
        }

        calendar.Name = input.Name;
        await _calendar.UpdateAsync(calendar);
    }

    /// <summary>
    ///     任务日历-删除
    /// </summary>
    /// <returns></returns>
    /// <exception cref="Exception"></exception>
    [HttpPost("/calendar/delete")]
    public async System.Threading.Tasks.Task Delete(BaseId input)
    {
        if (await _calendar.IsAnyAsync(a => a.Pid == input.Id))
        {
            throw Oops.Oh("存在子日历，无法删除！");
        }

        if (input.Id == 142307070994321)
        {
            throw Oops.Oh("默认根日历，无法删除！");
        }

        TaskCalendar? calendar = await _calendar.AsQueryable().Where(w => w.Id == input.Id)
            .Includes(w => w.CalendarDetails)
            .FirstAsync();
        if (calendar == null)
        {
            throw Oops.Oh(ErrorCode.COM1005);
        }

        await _calendar.AsSugarClient().DeleteNav(calendar).Include(w => w.CalendarDetails).ExecuteCommandAsync();
    }

    /// <summary>
    ///     任务日历-标记
    /// </summary>
    /// <returns></returns>
    /// <exception cref="Exception"></exception>
    [HttpPost("/calendar/addDetail")]
    public async System.Threading.Tasks.Task AddDetail(CalendarDetailAddInput input)
    {
        if (await _db.Queryable<TaskCalendarDetail>().AnyAsync(a => a.Time == input.Time && a.CalendarId == input.CalendarId))
        {
            throw Oops.Oh("选择日期已经被标记！");
        }

        if (await _calendar.IsAnyAsync(a => a.Pid == input.CalendarId))
        {
            throw Oops.Oh("请选择具体日历内容后再进行日期标记！");
        }

        TaskCalendar? calendar = await _calendar.GetFirstAsync(f => f.Id == input.CalendarId);
        if (calendar == null)
        {
            throw Oops.Oh("任务日历不存在！");
        }

        if (await _db.Queryable<TaskCalendarDetail>().AnyAsync(a => a.Time == input.Time && a.CalendarIds.Contains(calendar.Pid.ToString())))
        {
            throw Oops.Oh("选择日期已经被标记！");
        }

        TaskCalendarDetail calendarDetail = input.Adapt<TaskCalendarDetail>();
        calendarDetail.TaskCalendar = calendar;
        calendarDetail.CalendarIds = calendar.Pids + "[" + calendar.Id + "],";
        await _db.Insertable(calendarDetail).ExecuteCommandAsync();
    }


    /// <summary>
    ///     任务日历-删除标记日历
    /// </summary>
    /// <param name="input"></param>
    /// <exception cref="AppFriendlyException"></exception>
    [HttpPost("/calendar/deleteDetail")]
    public async System.Threading.Tasks.Task DeleteDetail(BaseId input)
    {
        await _db.Deleteable<TaskCalendarDetail>().Where(f => f.Id == input.Id).ExecuteCommandAsync();
    }

    /// <summary>
    ///     任务日历-标记日历列表
    /// </summary>
    /// <returns></returns>
    /// <exception cref="Exception"></exception>
    [HttpGet("/calendar/listDetail")]
    public async Task<List<TaskCalendarDetail>> DetailList([FromQuery] DetailListInput input)
    {
        return await _db.Queryable<TaskCalendarDetail>().Where(f => f.CalendarIds.Contains(input.Id.ToString()) && f.Month == input.Month && f.CreatedTime.Value.Year == DateTime.Now.Year)
            .ToListAsync();
    }
}