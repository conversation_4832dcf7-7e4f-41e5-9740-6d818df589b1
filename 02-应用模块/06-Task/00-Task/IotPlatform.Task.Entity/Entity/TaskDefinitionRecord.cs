namespace IotPlatform.Task.Entity;

/// <summary>
///     任务配置详情执行记录
/// </summary>
[SplitTable(SplitType.Day)]
[SugarTable("business_taskDefinitionRecord_{year}{month}{day}", "任务配置详情执行记录")]
public class TaskDefinitionRecord : EntityTenantId
{
    /// <summary>
    ///     任务配置Id
    /// </summary>
    [SugarColumn(ColumnDescription = "任务配置Id")]
    public long SysJobTaskId { get; set; }

    /// <summary>
    ///     任务配置详情Id
    /// </summary>
    [SugarColumn(ColumnDescription = "任务配置详情Id")]
    public long TaskDetailId { get; set; }

    /// <summary>
    ///     执行时间
    /// </summary>
    [SugarColumn(ColumnDescription = "执行时间")]
    [SplitField]
    public DateTime ActionTime { get; set; }

    /// <summary>
    ///     执行结果
    /// </summary>
    [SugarColumn(ColumnDescription = "执行结果", ColumnDataType = "longtext,text,clob", IsNullable = true,IsJson = true)]
    public object? Result { get; set; }

    /// <summary>
    /// 执行用时（毫秒）
    /// </summary>
    public long ElapsedMilliseconds { get; set; }
    
    /// <summary>
    ///     是否成功
    /// </summary>
    [SugarColumn(ColumnDescription = "是否成功")]
    public bool Success { get; set; }

    #region 关联表

    /// <summary>
    ///     定时任务
    /// </summary>
    [Navigate(NavigateType.OneToOne, nameof(SysJobTaskId))]
    public Task.Entity.TaskDefinition SysJobTask { get; set; }

    #endregion
}