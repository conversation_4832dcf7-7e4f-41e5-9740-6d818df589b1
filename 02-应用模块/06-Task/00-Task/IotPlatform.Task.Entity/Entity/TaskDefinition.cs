namespace IotPlatform.Task.Entity;

/// <summary>
///     任务配置
/// </summary>
[SugarTable("business_taskDefinition", "任务配置")]
public class TaskDefinition : EntityTenantId
{
    /// <summary>
    ///     任务名称
    /// </summary>
    [SugarColumn(ColumnDescription = "任务名称", Length = 64)]
    public string Name { get; set; }

    /// <summary>
    ///     是否启用任务
    /// </summary>
    [SugarColumn(ColumnDescription = "是否启用任务")]
    public bool Enable { get; set; }

    /// <summary>
    ///     任务描述
    /// </summary>
    [SugarColumn(ColumnDescription = "任务描述", IsNullable = true)]
    public string? Remark { get; set; }

    /// <summary>
    ///     分组Id
    /// </summary>
    [SugarColumn(ColumnDescription = "分组Id", IsNullable = true)]
    public long TaskDefinitionGroupId { get; set; }

    #region 关联表

    /// <summary>
    /// </summary>
    [Navigate(NavigateType.OneToOne, nameof(TaskDefinitionGroupId))]
    public TaskDefinitionGroup TaskDefinitionGroup { get; set; }

    /// <summary>
    ///     任务详情
    /// </summary>
    [Navigate(NavigateType.OneToMany, nameof(TaskDefinitionDetail.SysJobTaskId))]
    public List<TaskDefinitionDetail> SysJobTaskDetails { get; set; }

    /// <summary>
    ///     任务详情执行日志
    /// </summary>
    [Navigate(NavigateType.OneToMany, nameof(TaskDefinitionRecord.SysJobTaskId))]
    public List<TaskDefinitionRecord> SysJobTaskRecords { get; set; }

    #endregion
}