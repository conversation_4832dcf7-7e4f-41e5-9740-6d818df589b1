namespace IotPlatform.Task.Entity;

/// <summary>
///     任务配置详情
/// </summary>
[SugarTable("business_taskDefinitionDetail", "任务配置详情")]
public class TaskDefinitionDetail : EntityTenantId
{
    /// <summary>
    ///     定时任务Id
    /// </summary>
    [SugarColumn(ColumnDescription = "定时任务Id")]
    public long SysJobTaskId { get; set; }

    /// <summary>
    ///     触发类型：1：定时触发；2：MQTT触发；3：属性触发；4:变量触发；5：消息触发
    /// </summary>
    [SugarColumn(ColumnDescription = "触发类型：1：定时触发；2：MQTT触发；3：属性触发；4:变量触发；5：消息触发")]
    public SysJobTaskDetailTypeEnum EventType { get; set; }

    /// <summary>
    ///     触发内容
    /// </summary>
    [SugarColumn(ColumnDescription = "触发内容", ColumnDataType = "longtext,text,clob", IsNullable = true)]
    public string? EventParams { get; set; }

    /// <summary>
    ///     执行内容：1：脚本任务；2：程序块；3：消息推送；
    /// </summary>
    [SugarColumn(ColumnDescription = "执行内容：1：脚本任务；2：程序块；3：消息推送；")]
    public ExecuteTypeEnum ExecuteType { get; set; }

    /// <summary>
    ///     执行内容
    /// </summary>
    [SugarColumn(ColumnDescription = "执行内容", ColumnDataType = "longtext,text,clob")]
    public string ExecuteContent { get; set; }

    #region 忽略字段

    /// <summary>
    ///     执行时间
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public DateTime? LastTime { get; set; }

    /// <summary>
    ///     执行结果
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public object? LastExecValue { get; set; }

    /// <summary>
    ///     任务状态
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public TriggerStatus TimerStatus { get; set; } = TriggerStatus.Running;

    /// <summary>
    ///     定时任务状态
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public string TimerStatusName
    {
        get
        {
            switch (TimerStatus)
            {
                case TriggerStatus.Ready:
                case TriggerStatus.Running:
                case TriggerStatus.ErrorToReady:
                    return "正在运行";
                case TriggerStatus.Backlog:
                case TriggerStatus.Pause:
                case TriggerStatus.Blocked:
                    return "暂停";
                case TriggerStatus.Archived:
                    return "归档";
                case TriggerStatus.NotStart:
                    return "任务已取消或没有该任务";
                case TriggerStatus.Panic:
                case TriggerStatus.Overrun:
                case TriggerStatus.Unoccupied:
                case TriggerStatus.Unknown:
                case TriggerStatus.Unhandled:
                default:
                    return "异常";
            }
        }
    }

    /// <summary>
    ///     累计运行次数
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public long RunNumber { get; set; }

    /// <summary>
    ///     异常信息
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public string Exception { get; set; } = "";

    /// <summary>
    ///     触发类型：1：定时触发；2：MQTT触发；3：属性触发；4:变量触发；5：消息触发
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public string EventTypeName => EventType.GetDescription();

    /// <summary>
    ///     执行内容：1：脚本任务；2：程序块；3：消息推送；
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public string ExecuteTypeName => ExecuteType.GetDescription();

    #endregion

    #region 关联表

    /// <summary>
    ///     定义任务
    /// </summary>
    [Navigate(NavigateType.OneToOne, nameof(SysJobTaskId))]
    public TaskDefinition SysJobTask { get; set; }

    /// <summary>
    ///     定义任务详情执行日志
    /// </summary>
    [Navigate(NavigateType.OneToMany, nameof(TaskDefinitionRecord.TaskDetailId))]
    public List<TaskDefinitionRecord> SysJobTaskRecords { get; set; }

    #endregion
}

/// <summary>
///     触发类型：1：定时触发；2：MQTT触发；3：属性触发；4:变量触发；5：消息触发
/// </summary>
public enum SysJobTaskDetailTypeEnum
{
    /// <summary>
    ///     定时触发
    /// </summary>
    [Description("定时触发")] Time = 1,

    /// <summary>
    ///     属性触发
    /// </summary>
    [Description("属性触发")] Property = 3,

    /// <summary>
    ///     变量触发
    /// </summary>
    [Description("变量触发")] Variable = 4,

    /// <summary>
    ///     消息触发
    /// </summary>
    [Description("消息触发")] Message = 5,

    /// <summary>
    ///     Http触发
    /// </summary>
    [Description("Http触发")] Http = 6
}

/// <summary>
///     执行内容：1：脚本任务；2：程序块；3：消息推送；
/// </summary>
public enum ExecuteTypeEnum
{
    /// <summary>
    ///     脚本任务
    /// </summary>
    [Description("脚本任务")] Script = 1,

    /// <summary>
    ///     程序块
    /// </summary>
    [Description("程序块")] Task = 2,

    /// <summary>
    ///     消息推送
    /// </summary>
    [Description("消息推送")] Messgae = 3
}

/// <summary>
/// </summary>
public enum CompareEnum
{
    /// <summary>
    ///     值变化时执行
    /// </summary>
    [Description("值变化时执行")] Change = 1,

    /// <summary>
    ///     时间变化时执行
    /// </summary>
    [Description("时间变化时执行")] Time = 2,

    /// <summary>
    ///     等于
    /// </summary>
    [Description("等于")] Equi = 3,

    /// <summary>
    ///     大于
    /// </summary>
    [Description("大于")] Greater = 4,

    /// <summary>
    ///     小于
    /// </summary>
    [Description("小于")] Less = 5,

    /// <summary>
    ///     不等于
    /// </summary>
    [Description("不等于")] False = 6
}