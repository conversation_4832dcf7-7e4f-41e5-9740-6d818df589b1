using System.ComponentModel.DataAnnotations;
using Common.Models;

namespace IotPlatform.ProgramBlock.Entity;

/// <summary>
///     程序块-下拉
/// </summary>
public class ProgramBlockSelectInput
{
    /// <summary>
    ///     共享模块
    /// </summary>
    [Required]
    public string SharedModule { get; set; }
}

/// <summary>
///     程序块-新增
/// </summary>
public class ProgramBlockAddInput
{
    /// <summary>
    ///     名称
    /// </summary>
    [Required]
    public string Name { get; set; }

    /// <summary>
    ///     编码
    /// </summary>
    public string Code { get; set; }

    /// <summary>
    ///     备注
    /// </summary>
    public string? Remark { get; set; }

    /// <summary>
    ///     分组id
    /// </summary>
    [Required]
    public long ProgramBlockGroupId { get; set; }

    /// <summary>
    ///     共享模块
    /// </summary>
    [SugarColumn(IsJson = true)]
    public List<string> SharedModule { get; set; }
}

/// <summary>
///     程序块-修改
/// </summary>
public class ProgramBlockUpdateInput : ProgramBlockAddInput
{
    /// <summary>
    ///     id
    /// </summary>
    [Required]
    public long Id { get; set; }
}

/// <summary>
///     程序块-保存脚本内容
/// </summary>
public class ProgramBlockSaveInput : BaseId
{
    /// <summary>
    ///     JSON配置
    /// </summary>
    [SugarColumn(IsJson = true)]
    public dynamic? Config { get; set; }

    /// <summary>
    ///     执行内容
    /// </summary>
    [SugarColumn(ColumnDescription = "执行内容", ColumnDataType = "longtext,text,clob", IsNullable = true)]
    public string? Content { get; set; }
}

/// <summary>
///     程序块分组-保存
/// </summary>
public class ProgramBlockGroupSaveInput
{
    /// <summary>
    ///     程序块分组Id,不填默认新增
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    ///     分组名称
    /// </summary>
    public string Name { get; set; }

    /// <summary>
    ///     上级Id
    /// </summary>
    [Required]
    public long Pid { get; set; }
}

/// <summary>
/// </summary>
public class ProgramActionScriptConValue : BaseId
{
    /// <summary>
    ///     参数
    /// </summary>
    public Dictionary<string, object> Values { get; set; } = new();
}

/// <summary>
/// 程序块-分组
/// </summary>
public class UpdatePidInput
{
    /// <summary>
    /// 程序块Id
    /// </summary>
    [Required]
    public long Id { get; set; }

    /// <summary>
    /// 分组Id
    /// </summary>
    [Required]
    public long GroupId { get; set; }
}