namespace IotPlatform.ProgramBlock.Entity;

/// <summary>
///     程序块分组
/// </summary>
[SugarTable("business_programBlockGroup", "程序块分组")]
public class ProgramBlockGroup : EntityTenantId
{
    /// <summary>
    ///     名称
    /// </summary>
    [SugarColumn(ColumnDescription = "名称", Length = 64)]
    public string Name { get; set; }

    /// <summary>
    ///     上级节点
    /// </summary>
    [SugarColumn(ColumnDescription = "Pid")]
    public long Pid { get; set; }

    #region 关联表

    /// <summary>
    ///     节点
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public List<ProgramBlockGroup> Children { get; set; }

    #endregion
}