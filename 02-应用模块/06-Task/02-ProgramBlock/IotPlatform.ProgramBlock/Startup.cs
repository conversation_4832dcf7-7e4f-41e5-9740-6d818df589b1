using Furion;
using IotPlatform.ProgramBlock.Services;
using Microsoft.Extensions.DependencyInjection;

namespace IotPlatform.ProgramBlock;

/// <summary>
/// </summary>
[AppStartup(100)]
public class Startup : AppStartup
{
    /// <summary>
    /// </summary>
    /// <param name="services"></param>
    public void ConfigureServices(IServiceCollection services)
    {
        services.AddSingleton<BlockSingleton>(); // 注册为单例服务
        services.AddHostedService(provider => provider.GetRequiredService<BlockSingleton>());
    }
}