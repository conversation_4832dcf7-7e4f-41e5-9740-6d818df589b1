using Furion;
using IotPlatform.Core.Const;
using IotPlatform.Core.Extension;
using Jint;
using JsScript.Engine;
using JsScript.Engine.Model;
using SqlSugar;

namespace IotPlatform.ProgramBlock.Services;

/// <summary>
///     程序块
///     版 本:V6.0.0
///     版 权:杭州峰回科技有限公司
///     作 者:蒋状
///     日 期:2024-5-20
/// </summary>
[ApiDescriptionSettings("程序块")]
public class ProgramBlockService : IDynamicApiController, ITransient
{
    private readonly ISqlSugarRepository<Entity.ProgramBlock> _sysTemplate;
    private readonly JsScriptEnginePool _enginePool;
    private readonly BlockSingleton _blockSingleton;

    /// <summary>
    /// 
    /// </summary>
    /// <param name="sysTemplate"></param>
    /// <param name="blockSingleton"></param>
    /// <param name="enginePool"></param>
    public ProgramBlockService(ISqlSugarRepository<Entity.ProgramBlock> sysTemplate, BlockSingleton blockSingleton, JsScriptEnginePool enginePool)
    {
        _sysTemplate = sysTemplate;
        _blockSingleton = blockSingleton;
        _enginePool = enginePool;
    }

    /// <summary>
    ///     程序块分组-保存
    /// </summary>
    /// <returns></returns>
    /// <exception cref="Exception"></exception>
    [HttpPost("/program/block/group/save")]
    public async Task ProgramBlockGroupSave(ProgramBlockGroupSaveInput input)
    {
        if (input.Id > 0)
        {
            // 名称是否唯一
            if (await _sysTemplate.AsSugarClient().Queryable<ProgramBlockGroup>().AnyAsync(a => a.Name == input.Name && a.Id != input.Id))
            {
                throw Oops.Oh(ErrorCode.Com1004);
            }

            // 
            ProgramBlockGroup? programBlockGroup = await _sysTemplate.AsSugarClient().Queryable<ProgramBlockGroup>().FirstAsync(f => f.Id == input.Id);
            if (programBlockGroup == null)
            {
                throw Oops.Oh("程序块分组已经被删除！");
            }

            programBlockGroup.Name = input.Name;
            programBlockGroup.Pid = input.Pid;
            await _sysTemplate.AsSugarClient().Updateable(programBlockGroup).UpdateColumns(w => new { w.Name, w.Pid }).ExecuteCommandAsync();
        }
        else
        {
            ProgramBlockGroup programBlockGroup = new()
            {
                Pid = input.Pid,
                Name = input.Name
            };
            await _sysTemplate.AsSugarClient().Insertable(programBlockGroup).ExecuteCommandAsync();
        }
    }

    /// <summary>
    ///     程序块分组-删除
    /// </summary>
    /// <returns></returns>
    /// <exception cref="Exception"></exception>
    [HttpPost("/program/block/group/delete")]
    public async Task ProgramBlockGroupDelete(BaseId input)
    {
        // 名称是否唯一
        if (await _sysTemplate.AsSugarClient().Queryable<ProgramBlockGroup>().AnyAsync(a => a.Pid == input.Id) ||
            await _sysTemplate.AsSugarClient().Queryable<Entity.ProgramBlock>().AnyAsync(a => a.ProgramBlockGroupId == input.Id))
        {
            throw Oops.Oh("该分组已经被使用，请先删除下级节点！");
        }

        // 
        ProgramBlockGroup? programBlockGroup = await _sysTemplate.AsSugarClient().Queryable<ProgramBlockGroup>().FirstAsync(f => f.Id == input.Id);
        if (programBlockGroup == null)
        {
            throw Oops.Oh("程序块分组已经被删除！");
        }

        await _sysTemplate.AsSugarClient().Deleteable(programBlockGroup).ExecuteCommandAsync();
    }

    /// <summary>
    ///     程序块分组树
    /// </summary>
    /// <returns></returns>
    [HttpGet("/program/block/tree")]
    public async Task<dynamic> ProgramBlockTree()
    {
        // 分组信息
        List<ProgramBlockGroup>? allProgramBlockGroups = await _sysTemplate.AsSugarClient().Queryable<ProgramBlockGroup>()
            .ToTreeAsync(u => u.Children, u => u.Pid, 0);
        // 程序块
        List<Entity.ProgramBlock>? allProgramBlocks = await _sysTemplate.AsQueryable()
            .Includes(w => w.ProgramBlockGroup)
            .ToListAsync();

        // 递归生成树结构
        List<dynamic> taskDefinitionTree = await RecursiveProgramBlockTree(allProgramBlockGroups, allProgramBlocks);
        return taskDefinitionTree;
    }

    /// <summary>
    ///     递归生成程序块分组树
    /// </summary>
    /// <param name="programBlockGroups">原始程序块分组列表</param>
    /// <param name="programBlocks">程序块列表</param>
    /// <returns>任务配置分组树结构</returns>
    private static async Task<List<dynamic>> RecursiveProgramBlockTree(List<ProgramBlockGroup> programBlockGroups, List<Entity.ProgramBlock> programBlocks)
    {
        if (programBlockGroups.IsNullOrEmpty())
        {
            return new List<dynamic>();
        }

        List<dynamic> output = new();

        foreach (ProgramBlockGroup programBlockGroup in programBlockGroups)
        {
            List<ProgramBlockGroup> children = programBlockGroup.Children?.ToList() ?? new List<ProgramBlockGroup>();
            var groupTasks = programBlocks.Where(w => w.ProgramBlockGroupId == programBlockGroup.Id).Select(s => new
            {
                s.Id,
                s.Name,
                Pid = programBlockGroup.Id
            });

            var data = new
            {
                programBlockGroup.Id,
                programBlockGroup.Name,
                programBlockGroup.Pid,
                Children = await RecursiveProgramBlockTree(children, programBlocks)
            };
            data.Children.AddRange(groupTasks);
            output.Add(data);
        }

        return output;
    }

    /// <summary>
    ///     程序块-详情
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpGet("/program/block/detail")]
    public async Task<dynamic> TemplatePage([FromQuery] BaseId input)
    {
        Entity.ProgramBlock? taskTemplate = await _sysTemplate.AsQueryable()
            .Includes(w => w.ProgramBlockGroup)
            .FirstAsync(f => f.Id == input.Id);
        return taskTemplate;
    }

    /// <summary>
    ///     程序块-下拉
    /// </summary>
    /// <returns></returns>
    [HttpGet("/program/block/select")]
    public async Task<List<ProgramBlockSelectOutput>> Select([FromQuery] ProgramBlockSelectInput input)
    {
        return await _sysTemplate.AsQueryable()
            .Where(w => SqlFunc.JsonLike(w.SharedModule, input.SharedModule))
            .OrderByDescending(w => w.Id)
            .Select<ProgramBlockSelectOutput>(s => new ProgramBlockSelectOutput
            {
                Id = s.Id,
                Name = s.Name,
                Remark = s.Remark
            })
            .ToListAsync();
    }

    /// <summary>
    ///     程序块-新增
    /// </summary>
    /// <returns></returns>
    /// <exception cref="Exception"></exception>
    [HttpPost("/program/block/add")]
    public async Task<long> TemplateAdd(ProgramBlockAddInput input)
    {
        if (await _sysTemplate.IsAnyAsync(a => a.Name == input.Name))
        {
            throw Oops.Oh(ErrorCode.Com1004);
        }

        // 新增
        Entity.ProgramBlock programBlock = input.Adapt<Entity.ProgramBlock>();
        await _sysTemplate.InsertAsync(programBlock);
        _blockSingleton.ProgramBlockCache.TryAdd(programBlock.Id, programBlock);
        return programBlock.Id;
    }

    /// <summary>
    ///     程序块-修改
    /// </summary>
    /// <returns></returns>
    /// <exception cref="Exception"></exception>
    [HttpPost("/program/block/update")]
    public async Task TimerUpdate(ProgramBlockUpdateInput input)
    {
        // 检查任务名称是否唯一
        if (await _sysTemplate.IsAnyAsync(a => a.Name == input.Name && a.Id != input.Id))
        {
            throw Oops.Oh(ErrorCode.Com1004);
        }

        Entity.ProgramBlock? programBlock = await _sysTemplate.AsQueryable().FirstAsync(f => f.Id == input.Id);
        if (programBlock == null)
        {
            throw Oops.Oh("程序块已经被删除！");
        }

        programBlock.Name = input.Name;
        programBlock.Code = input.Code;
        programBlock.Remark = input.Remark;
        programBlock.SharedModule = input.SharedModule;
        // 仅修改支持字段
        await _sysTemplate.AsSugarClient().Updateable(programBlock).UpdateColumns(w => new { w.Name, w.Code, w.Remark, w.SharedModule }).ExecuteCommandAsync();
    }

    /// <summary>
    ///     程序块-保存内容
    /// </summary>
    /// <returns></returns>
    /// <exception cref="Exception"></exception>
    [HttpPost("/program/block/save")]
    public async Task Save(ProgramBlockSaveInput input)
    {
        Entity.ProgramBlock? programBlock = await _sysTemplate.AsQueryable().FirstAsync(f => f.Id == input.Id);
        if (programBlock == null)
        {
            throw Oops.Oh("程序块已经被删除！");
        }

        programBlock.Config = input.Config;
        programBlock.Content = input.Content;
        // 仅修改支持字段
        await _sysTemplate.AsSugarClient().Updateable(programBlock).UpdateColumns(w => new { w.Content, w.Config }).ExecuteCommandAsync();
        _blockSingleton.ProgramBlockCache[programBlock.Id] = programBlock;
    }

    /// <summary>
    ///     程序块-删除
    /// </summary>
    /// <returns></returns>
    /// <exception cref="Exception"></exception>
    [HttpPost("/program/block/delete")]
    public async Task Delete(BaseId<List<long>> input)
    {
        List<Entity.ProgramBlock>? programBlockList = await _sysTemplate.AsQueryable().Where(f => input.Id.Contains(f.Id)).ToListAsync();
        // todo 
        await _sysTemplate.DeleteAsync(programBlockList);
        foreach (long id in input.Id)
        {
            _blockSingleton.ProgramBlockCache.Remove(id, out _);
        }
    }

    /// <summary>
    ///     执行带参数脚本
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/program/engine/action")]
    public async Task<ActionScriptDto> ProgramActionScriptConValue(ProgramActionScriptConValue input)
    {
        Entity.ProgramBlock? programBlock = await _sysTemplate.AsQueryable().FirstAsync(f => f.Id == input.Id);
        if (programBlock == null)
        {
            throw Oops.Oh("程序块已经被删除！");
        }

        if (programBlock.Content == null)
        {
            throw Oops.Oh("程序块未开发脚本内容！");
        }

        // 通过koken获取租户id
        string tenantId = App.User?.FindFirst(ClaimConst.TenantId)?.Value;
        // 设置租户id变量
        if (!input.Values.ContainsKey("tenantId"))
        {
            input.Values.TryAdd("tenantId", tenantId);
        }
        // 执行脚本
        var completionValue = await _enginePool.ExecuteScriptWithLogAsync("action", programBlock.Content, null, input.Values);
        ActionScriptDto result = new() { Value = completionValue.Result ?? string.Empty, Logs = completionValue.Log.Logs };
        return result;
    }

    /// <summary>
    ///     程序块-分组
    /// </summary>
    /// <returns></returns>
    [HttpPost("/program/block/updatePid")]
    public async Task UpdatePid(UpdatePidInput input)
    {
        Entity.ProgramBlock? programBlock = await _sysTemplate.AsQueryable().FirstAsync(f => f.Id == input.Id);
        if (programBlock == null)
        {
            throw Oops.Oh("程序块已经被删除！");
        }

        programBlock.ProgramBlockGroupId = input.GroupId;
        // 修改上级Id
        await _sysTemplate.AsSugarClient().Updateable(programBlock).UpdateColumns(w => w.ProgramBlockGroupId).ExecuteCommandAsync();
    }
}