<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <PropertyGroup Condition=" '$(Configuration)' == 'Release' ">
      <DebugSymbols>true</DebugSymbols>
      <DebugType>none</DebugType>
    </PropertyGroup>

    <ItemGroup>
      <ProjectReference Include="..\..\..\09-Engine\JsScript.Engine\JsScript.Engine.csproj" />
      <ProjectReference Include="..\IotPlatform.ProgramBlock.Entity\IotPlatform.ProgramBlock.Entity.csproj" />
    </ItemGroup>

</Project>
