namespace IotPlatform.DataWeaving.Entity;

/// <summary>
///     维度管理
/// </summary>
[SugarTable("business_manageDimension", "维度管理")]
public class ManageDimension : EntityTenant
{
    /// <summary>
    ///     维度名称
    /// </summary>
    [SugarColumn(ColumnDescription = "维度名称")]
    [Required]
    public string Name { get; set; }

    /// <summary>
    ///     维度标识
    /// </summary>
    [SugarColumn(ColumnDescription = "维度标识")]
    public string Identifying { get; set; }

    /// <summary>
    ///     描述
    /// </summary>
    [SugarColumn(ColumnDescription = "描述", Length = 1024,IsNullable = true)]
    public string? Remark { get; set; }

    /// <summary>
    ///     维度管理数据
    /// </summary>
    [Navigate(NavigateType.OneToMany, nameof(ManageDimensionData.ManageDimensionId))]
    public List<ManageDimensionData> ManageDimensionDatas { get; set; }
}