namespace IotPlatform.DataWeaving.Entity;

/// <summary>
///     实体管理
/// </summary>
[SugarTable("business_dbEntityManage", "实体管理")]
public class DbEntityManage : EntityTenantId
{
    /// <summary>
    ///     数据库连接标识
    /// </summary>
    [SugarColumn(ColumnDescription = "数据库连接标识")]
    public string ConfigId { get; set; }
    
    /// <summary>
    ///     表名
    /// </summary>
    [SugarColumn(ColumnDescription = "表名")]
    public string Table { get; set; }

    /// <summary>
    ///     表描述内容
    /// </summary>
    [SugarColumn(ColumnDescription = "表描述内容", IsNullable = true)]
    public string? Description { get; set; }
    
    /// <summary>
    ///     标签
    /// </summary>
    [SugarColumn(ColumnDescription = "标签", IsJson = true, ColumnDataType = "longtext,text,clob")]
    public List<string> Tags { get; set; }
}