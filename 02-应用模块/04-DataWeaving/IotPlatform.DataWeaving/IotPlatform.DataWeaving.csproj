<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <PropertyGroup Condition=" '$(Configuration)' == 'Debug' ">
        <DocumentationFile>IotPlatform.DataWeaving.xml</DocumentationFile>
    </PropertyGroup>

    <PropertyGroup Condition=" '$(Configuration)' == 'Release' ">
        <DebugType>none</DebugType>
        <DebugSymbols>true</DebugSymbols>
    </PropertyGroup>

    <ItemGroup>
        <None Remove="IotPlatform.DataWeaving.xml"/>
        <None Update="IotPlatform.DataWeaving.xml">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Remove="IotPlatform.DataWeaving.csproj.DotSettings" />
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\..\..\01-架构核心\IotPlatform.Core\IotPlatform.Core.csproj" />
        <ProjectReference Include="..\..\00-Common\Common.Core\Common.Core.csproj" />
        <ProjectReference Include="..\..\02-System\Systems.Entity\Systems.Entity.csproj" />
    </ItemGroup>

</Project>
