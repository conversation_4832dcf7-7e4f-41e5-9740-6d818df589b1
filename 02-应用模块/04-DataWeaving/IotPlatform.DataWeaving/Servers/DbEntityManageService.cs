using System.Data;
using System.Dynamic;
using System.Text;
using Common.Core.Manager.DataBase;
using Common.Dto.DataBase;
using Extras.DatabaseAccessor.SqlSugar.Repositories;
using Furion.DatabaseAccessor;
using IotPlatform.Core.Enum;
using IotPlatform.Core.Extension;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Systems.Entity.Dto;
using Systems.Entity.Dto.DataBase;
using Systems.Entity.Model.System.DataBase;

namespace IotPlatform.DataWeaving.Servers;

/// <summary>
///     数据编织-实体管理
/// </summary>
[ApiDescriptionSettings("数据编织")]
public class DbEntityManageService : IDynamicApiController, ITransient
{
    /// <summary>
    ///     服务基础仓储.
    /// </summary>
    private readonly ISqlSugarRepository<DbEntityManage> _dbEntityManage;

    private readonly IDataBaseManager _changeDataBase;
    private const string configId = "16545203149510";

    public DbEntityManageService(ISqlSugarRepository<DbEntityManage> dbEntityManage, IDataBaseManager changeDataBase)
    {
        _dbEntityManage = dbEntityManage;
        _changeDataBase = changeDataBase;
    }

    /// <summary>
    ///     实体管理列表
    /// </summary>
    /// <param name="input">过滤条件.</param>
    /// <returns></returns>
    [HttpGet("/dbEntity/table/list")]
    public async Task<dynamic> DbEntityManagePage([FromQuery] DbEntityManageListInput input)
    {
        try
        {
            DbLink dbLink;
            if (input.configId == configId)
            {
                dbLink = new DbLink
                {
                    Id = Convert.ToInt64(input.configId),
                    DbType = DbTypeEnum.MySql
                };
            }
            else
            {
                dbLink = await GetDbLink(input.configId);
            }

            // 表结构
            List<DbTableInfo> tables = _changeDataBase.GetTableInfos(dbLink);
            // 过滤表名称
            if (input.keyword.IsNotEmptyOrNull())
            {
                tables = tables.Where(w => w.Name == input.keyword).ToList();
            }

            // 实体管理集合
            List<DbEntityManage>? dataList = await _dbEntityManage.AsQueryable()
                .Where(w => w.ConfigId == dbLink.Id.ToString())
                .Where(w => tables.Select(s => s.Name).Contains(w.Table))
                .ToListAsync();

            List<dynamic> output = new();
            foreach (DbTableInfo table in tables)
            {
                DbEntityManage? data = dataList.FirstOrDefault(w => table.Name == w.Table);
                var dynOutput = new
                {
                    table.Name,
                    table.Description,
                    Tags = data != null ? data.Tags : ["未分组"]
                };
                output.Add(dynOutput);
            }

            return output;
        }
        catch
        {
            throw Oops.Oh("连接失败！");
        }
    }

    /// <summary>
    ///     详情.
    /// </summary>
    /// <param name="linkId">连接Id.</param>
    /// <param name="tableName">主键值.</param>
    /// <returns></returns>
    [HttpGet("/dbEntity/table/{linkId}/{tableName}")]
    public async Task<dynamic> GetInfo(string linkId, string tableName)
    {
        DbLink link = await GetDbLink(linkId);
        if (string.IsNullOrEmpty(tableName))
        {
            return new PageResult();
        }

        DatabaseTableInfoOutput output = _changeDataBase.GetDataBaseTableInfo(link, tableName);
        output.hasTableData = _changeDataBase.IsAnyData(link, tableName);
        DbEntityManage dbEntityManage = await _dbEntityManage.AsQueryable().Where(w => w.Table == tableName && w.ConfigId == linkId).FirstAsync();
        output.tags = dbEntityManage != null ? dbEntityManage.Tags : ["未分组"];
        return output;
    }

    /// <summary>
    ///     预览数据.
    /// </summary>
    /// <param name="input">请求参数.</param>
    /// <param name="linkId">连接Id.</param>
    /// <param name="tableName">表名.</param>
    /// <returns></returns>
    [HttpGet("/dbEntity/table/{linkId}/{tableName}/Preview")]
    public async Task<dynamic> GetData([FromQuery] DatabaseTablePreviewQuery input, string linkId, string tableName)
    {
        DbLink link = await GetDbLink(linkId);
        if (string.IsNullOrEmpty(tableName))
        {
            return new PageResult();
        }

        StringBuilder dbSql = new();
        dbSql.AppendFormat("SELECT * FROM {0} WHERE 1=1", tableName);
        if (!string.IsNullOrEmpty(input.field) && !string.IsNullOrEmpty(input.SearchValue))
        {
            dbSql.AppendFormat(" AND {0} like '%{1}%'", input.field, input.SearchValue);
        }

        return await _changeDataBase.GetDataTablePage(link, dbSql.ToString(), input.PageNo, input.PageSize);
    }

    /// <summary>
    ///     字段列表.
    /// </summary>
    /// <param name="linkId">连接Id.</param>
    /// <param name="tableName">表名.</param>
    /// <param name="type">字段类型.</param>
    /// <returns></returns>
    [HttpGet("/dbEntity/table/{linkId}/{tableName}/Fields")]
    public async Task<dynamic> GetFieldList(string linkId, string tableName, [FromQuery] string type)
    {
        DbLink link = await GetDbLink(linkId);
        if (string.IsNullOrEmpty(tableName))
        {
            return new PageResult();
        }

        if (!_changeDataBase.IsConnection(link))
        {
            throw Oops.Oh(ErrorCode.D1507);
        }

        if (!_changeDataBase.IsAnyTable(link, tableName))
        {
            throw Oops.Oh(ErrorCode.D1521);
        }

        List<TableFieldOutput> data = _changeDataBase.GetFieldList(link, tableName).Adapt<List<TableFieldOutput>>();
        // 应用数据类型转换，确保不同数据库返回的类型统一
        data = _changeDataBase.ViewDataTypeConversion(data, _changeDataBase.ToDbType(link.DbType));

        if (type.Equals("1"))
        {
            data.ForEach(item => { item.field = item.field.ReplaceRegex("^f_", string.Empty).ParseToPascalCase().ToLowerCase(); });
        }

        return new { list = data };
    }

    /// <summary>
    ///     删除.
    /// </summary>
    /// <param name="linkId">连接Id.</param>
    /// <param name="tableName">表名.</param>
    /// <returns></returns>
    [HttpPost("/dbEntity/table/{linkId}/{tableName}/delete")]
    [UnitOfWork]
    public async Task Delete(string linkId, string tableName)
    {
        DbLink link = await GetDbLink(linkId);
        DataTable data = _changeDataBase.GetData(link, tableName);
        // if (data.Rows.Count > 0)
        // {
        //     throw Oops.Oh(ErrorCode.D1508);
        // }

        if (!_changeDataBase.Delete(link, tableName))
        {
            throw Oops.Oh(ErrorCode.D1500);
        }

        DbEntityManage dbEntityManage = await _dbEntityManage.AsQueryable().Where(w => w.Table == tableName && w.ConfigId == linkId).FirstAsync();
        if (dbEntityManage != null)
        {
            await _dbEntityManage.DeleteAsync(dbEntityManage);
        }
    }

    /// <summary>
    ///     新建.
    /// </summary>
    /// <param name="linkId">连接Id.</param>
    /// <param name="input">请求参数.</param>
    /// <returns></returns>
    [HttpPost("/dbEntity/table/{linkId}/add")]
    [UnitOfWork]
    public async Task Create(string linkId, [FromBody] DatabaseTableInfoOutput input)
    {
        DbLink link = await GetDbLink(linkId);
        if (_changeDataBase.IsAnyTable(link, input.tableInfo.newTable))
        {
            throw Oops.Oh(ErrorCode.D1503);
        }

        if (input.tableInfo.newTable.Length >= 30)
        {
            throw Oops.Oh(ErrorCode.D1514);
        }

        if (input.tableFieldList.Any(x => x.field.Length >= 30))
        {
            throw Oops.Oh(ErrorCode.D1515);
        }

        DbTableModel tableInfo = input.tableInfo.Adapt<DbTableModel>();
        tableInfo.table = input.tableInfo.newTable;
        List<DbTableFieldModel> tableFieldList = input.tableFieldList.Adapt<List<DbTableFieldModel>>();
        if (!_changeDataBase.Create(link, tableInfo, tableFieldList))
        {
            throw Oops.Oh(ErrorCode.D1501);
        }

        DbEntityManage dbEntityManage = new()
        {
            ConfigId = linkId,
            Table = input.tableInfo.newTable,
            Description = input.tableInfo.tableName ?? "",
            Tags = !input.tags.Any() ? ["未分组"] : input.tags
        };
        await _dbEntityManage.AsSugarClient().Insertable(dbEntityManage).ExecuteCommandAsync();
    }

    /// <summary>
    ///     更新.
    /// </summary>
    /// <param name="linkId">连接Id.</param>
    /// <param name="input">请求参数.</param>
    /// <returns></returns>
    [HttpPost("/dbEntity/table/{linkId}/update")]
    [UnitOfWork]
    public async Task Update(string linkId, [FromBody] DatabaseTableUpInput input)
    {
        DbLink link = await GetDbLink(linkId);
        if (input.tableInfo.newTable is { Length: >= 30 })
        {
            throw Oops.Oh(ErrorCode.D1514);
        }

        if (input.tableFieldList.Any(x => x.field.Length >= 30))
        {
            throw Oops.Oh(ErrorCode.D1515);
        }

        List<TableFieldOutput> oldFieldList = _changeDataBase.GetFieldList(link, input.tableInfo.table).Adapt<List<TableFieldOutput>>();
        oldFieldList = _changeDataBase.ViewDataTypeConversion(oldFieldList, _changeDataBase.ToDbType(link.DbType));
        DbTableModel oldTableInfo = _changeDataBase.GetTableInfos(link).Find(m => m.Name == input.tableInfo.table).Adapt<DbTableModel>();
        DataTable data = _changeDataBase.GetData(link, input.tableInfo.table);
        if (data.Rows.Count > 0)
        {
            throw Oops.Oh(ErrorCode.D1508);
        }

        DbTableModel tableInfo = input.tableInfo.Adapt<DbTableModel>();
        tableInfo.table = input.tableInfo.newTable;

        List<DbTableFieldModel> tableFieldList = input.tableFieldList.Adapt<List<DbTableFieldModel>>();
        foreach (DbTableFieldModel field in tableFieldList)
        {
            if (field.dataType == "decimal")
            {
                field.dataType = "numeric";
            }
        }

        if (!input.tableInfo.table.Equals(input.tableInfo.newTable) && _changeDataBase.IsAnyTable(link, input.tableInfo.newTable))
        {
            throw Oops.Oh(ErrorCode.D1503);
        }

        try
        {
            if (!_changeDataBase.Update(link, input.tableInfo.table, tableInfo, tableFieldList))
            {
                _changeDataBase.Create(link, oldTableInfo, oldFieldList.Adapt<List<DbTableFieldModel>>());
                throw Oops.Oh(ErrorCode.D1502);
            }
        }
        catch (Exception ex)
        {
            _changeDataBase.Create(link, oldTableInfo, oldFieldList.Adapt<List<DbTableFieldModel>>());
            throw Oops.Oh(ex.Message);
        }

        DbEntityManage dbEntityManage = await _dbEntityManage.AsQueryable().Where(w => w.Table == input.tableInfo.table && w.ConfigId == linkId).FirstAsync();
        if (dbEntityManage == null)
        {
            dbEntityManage = new DbEntityManage
            {
                ConfigId = linkId,
                Table = input.tableInfo.newTable,
                Description = input.tableInfo.tableName ?? "",
                Tags = !input.tags.Any() ? ["未分组"] : input.tags
            };
            await _dbEntityManage.AsSugarClient().Insertable(dbEntityManage).ExecuteCommandAsync();
        }
        else
        {
            dbEntityManage.Table = input.tableInfo.newTable;
            dbEntityManage.Description = input.tableInfo.tableName ?? "";
            dbEntityManage.Tags = !input.tags.Any() ? ["未分组"] : input.tags;
            await _dbEntityManage.AsSugarClient().Updateable(dbEntityManage).ExecuteCommandAsync();
        }
    }

    /// <summary>
    ///     生成表模板SQL
    /// </summary>
    /// <param name="linkId"></param>
    /// <param name="tableName">表名称.</param>
    /// <returns></returns>
    [HttpGet("/dbEntity/table/sqlTemplateGenerator")]
    public async Task<dynamic> GetData(string linkId, string tableName)
    {
        DbLink link = await GetDbLink(linkId);
        List<TableFieldOutput> fields = _changeDataBase.GetFieldList(link, tableName)
            .Adapt<List<TableFieldOutput>>();
        // 应用数据类型转换，确保不同数据库返回的类型统一
        fields = _changeDataBase.ViewDataTypeConversion(fields, _changeDataBase.ToDbType(link.DbType));

        // 初始化SQL语句  
        StringBuilder insertSql = new($"INSERT INTO {tableName} (");
        string deleteSql = $"DELETE FROM {tableName} ";
        string updateSql = $"UPDATE {tableName} SET ";
        string selectSql = $"SELECT * FROM `{tableName}`";

        List<string> whereClauses = new();
        List<string> setClauses = new();
        StringBuilder valuesSql = new("VALUES(");

        foreach (TableFieldOutput field in fields)
        {
            // 构建 where 条件和 set 语句  
            if (field.primaryKey == 1)
            {
                whereClauses.Add($"{field.field} = ?");
            }

            insertSql.Append($"{field.field}, ");
            setClauses.Add($"{field.field} = ?");
            valuesSql.Append("?, ");
        }

        // 构建最终的 SQL 语句  
        insertSql.Length -= 2; // 去除最后的逗号和空格  
        insertSql.Append(") ").Append(valuesSql.Append(")").ToString());

        deleteSql += "WHERE " + string.Join(" AND ", whereClauses);
        updateSql += string.Join(", ", setClauses) + " WHERE " + string.Join(" AND ", whereClauses);

        return new
        {
            Insert = insertSql.ToString(),
            Delete = deleteSql,
            Update = updateSql,
            Select = selectSql
        };
    }

    /// <summary>
    ///     导出.
    /// </summary>
    /// <param name="linkId">连接ID.</param>
    /// <param name="tableName">表名称.</param>
    /// <returns></returns>
    [HttpGet("/dbEntity/table/{linkId}/{tableName}/Actions/Export")]
    public async Task<dynamic> ActionsExport(string linkId, string tableName)
    {
        DbLink link = await GetDbLink(linkId);
        DatabaseTableInfoOutput data = _changeDataBase.GetDataBaseTableInfo(link, tableName);
        return data;
    }

    /// <summary>
    ///     导入.
    /// </summary>
    /// <param name="linkId"></param>
    /// <param name="file"></param>
    /// <returns></returns>
    [HttpPost("/dbEntity/table/{linkId}/Actions/Import")]
    public async Task ActionsImport(string linkId, IFormFile file)
    {
        string fileType = Path.GetExtension(file.FileName).Replace(".", string.Empty);
        if (!fileType.ToLower().Equals(nameof(ExportFileType.json)) && !fileType.ToLower().Equals(nameof(ExportFileType.bdb)))
        {
            throw Oops.Oh(ErrorCode.D3006);
        }

        Stream stream = file.OpenReadStream();
        byte[] byteList = new byte[file.Length];
        await stream.ReadAsync(byteList, 0, (int)file.Length);
        stream.Position = 0;
        StreamReader sr = new(stream, Encoding.Default);
        string json = await sr.ReadToEndAsync();
        sr.Close();
        stream.Close();
        DatabaseTableInfoOutput? data = json.ToObjectOld<DatabaseTableInfoOutput>();
        if (data == null || data.tableFieldList == null || data.tableInfo == null)
        {
            throw Oops.Oh(ErrorCode.D3006);
        }

        // 处理decimal类型字段的dataLength属性，解析逗号分隔的长度和小数位
        foreach (var field in data.tableFieldList.Where(field => field.dataType == "decimal"))
        {
            field.dataType = "numeric";
            if (string.IsNullOrEmpty(field.dataLength) || !field.dataLength.Contains(","))
            {
                continue;
            }

            string[] parts = field.dataLength.Split(',');
            if (parts.Length == 2 && int.TryParse(parts[0], out int length) && int.TryParse(parts[1], out int decimalDigits))
            {
                field.dataLength = parts[0];
                field.decimalDigits = decimalDigits;
            }
        }

        data.tableInfo.newTable = data.tableInfo.table;
        await Create(linkId, data);
    }

    #region 私有方法

    /// <summary>
    ///     获取DbLink配置
    /// </summary>
    /// <param name="dbLinkConfigId"></param>
    /// <returns></returns>
    /// <exception cref="AppFriendlyException"></exception>
    private async Task<DbLink> GetDbLink(string dbLinkConfigId)
    {
        DbLink? dbLink;
        if (dbLinkConfigId.IsNullOrEmpty() || dbLinkConfigId == configId)
        {
            dbLink = new DbLink
            {
                Id = Convert.ToInt64(dbLinkConfigId),
                DbType = DbTypeEnum.MySql
            };
        }
        else
        {
            // 数据连接
            dbLink = await _dbEntityManage.AsSugarClient().Queryable<DbLink>().FirstAsync(m => m.Id == Convert.ToInt64(dbLinkConfigId));
            if (dbLink == null)
            {
                throw Oops.Oh(ErrorCode.D1002);
            }
        }

        return dbLink;
    }

    /// <summary>
    ///     将DataTable 转换成 List
    /// </summary>
    /// <param name="table"></param>
    /// <param name="reverse">反转:控制返回结果中是只存在 FilterField 指定的字段,还是排除.[flase 返回FilterField 指定的字段]|[true 返回结果剔除 FilterField 指定的字段]</param>
    /// <param name="filterField">字段过滤,FilterField 为空 忽略 reverse 参数；返回DataTable中的全部数</param>
    /// <returns></returns>
    private static List<dynamic> ToDynamicList(DataTable table, bool reverse = true, params string[] filterField)
    {
        List<dynamic> modelList = new();
        foreach (DataRow row in table.Rows)
        {
            dynamic model = new ExpandoObject();
            IDictionary<string, object> dict = (IDictionary<string, object>)model;
            foreach (DataColumn column in table.Columns)
            {
                if (filterField.Length != 0)
                {
                    if (reverse)
                    {
                        if (!filterField.Contains(column.ColumnName))
                        {
                            dict[column.ColumnName] = row[column];
                        }
                    }
                    else
                    {
                        if (filterField.Contains(column.ColumnName))
                        {
                            dict[column.ColumnName] = row[column];
                        }
                    }
                }
                else
                {
                    dict[column.ColumnName] = row[column];
                }
            }

            modelList.Add(model);
        }

        return modelList;
    }

    #endregion
}