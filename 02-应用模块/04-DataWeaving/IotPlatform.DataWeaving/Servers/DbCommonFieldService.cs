using Extras.DatabaseAccessor.SqlSugar.Repositories;

namespace IotPlatform.DataWeaving.Servers;

/// <summary>
///     数据编织-实体管理-常用字段
///     版 本:V5.0.5
///     版 权:杭州峰回科技有限公司
///     作 者:蒋状
///     日 期:2024-03-28
/// </summary>
[ApiDescriptionSettings("数据编织")]
public class DbCommonFieldService : IDynamicApiController, ITransient
{
    /// <summary>
    ///     服务基础仓储.
    /// </summary>
    private readonly ISqlSugarRepository<CommonField> _commonField;

    public DbCommonFieldService(ISqlSugarRepository<CommonField> commonField)
    {
        _commonField = commonField;
    }

    /// <summary>
    ///     实体管理-常用字段-列表
    /// </summary>
    /// <returns></returns>
    [HttpGet("/dbCommonField/list")]
    public async Task<dynamic> CommonFieldList()
    {
        List<CommonField>? dataList = await _commonField.AsQueryable().OrderByDescending(o => o.Id).ToListAsync();

        return dataList.Select(s => new
        {
            field = s.Field,
            fieldName = s.FieldName,
            dataType = s.DataType,
            dataLength = s.DataLength,
            allowNull = s.AllowNull,
            id = s.Id
        }).ToList();
    }

    /// <summary>
    ///     实体管理-常用字段-更新
    /// </summary>
    /// <param name="input">表名</param>
    /// <returns></returns>
    [HttpPost("/dbCommonField/save")]
    public async Task CommonFieldSave(CommonFieldSaveInput input)
    {
        if (input.Id > 0)
        {
            // update
            CommonField? commonField = await _commonField.AsQueryable().Where(f => f.Id == input.Id).FirstAsync();
            if (commonField == null)
            {
                throw Oops.Oh("该常用字段已经被删除！");
            }

            commonField.Field = input.Field;
            commonField.FieldName = input.FieldName;
            commonField.DataType = input.DataType;
            commonField.DataLength = input.DataLength;
            commonField.AllowNull = input.AllowNull;
            await _commonField.UpdateAsync(commonField);
        }
        else
        {
            CommonField commonField = input.Adapt<CommonField>();
            await _commonField.InsertAsync(commonField);
        }
    }

    /// <summary>
    ///     实体管理-常用字段-删除表
    /// </summary>
    /// <param name="input">表名.</param>
    /// <returns></returns>
    [HttpPost("/dbCommonField/delete")]
    public async Task CommonFieldDelete(BaseId input)
    {
        CommonField? commonField = await _commonField.AsQueryable().Where(f => f.Id == input.Id).FirstAsync();
        if (commonField == null)
        {
            return;
        }

        await _commonField.DeleteAsync(commonField);
    }
}