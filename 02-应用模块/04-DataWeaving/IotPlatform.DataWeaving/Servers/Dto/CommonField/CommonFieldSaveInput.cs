namespace IotPlatform.DataWeaving.Servers;

/// <summary>
///     实体管理-常用字段-更新
/// </summary>
public class CommonFieldSaveInput
{
    /// <summary>
    ///     Id
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    ///     字段列名
    /// </summary>
    [Required]
    public string Field { get; set; }

    /// <summary>
    ///     字段名称
    /// </summary>
    [Required]
    public string FieldName { get; set; }

    /// <summary>
    ///     数据类型
    /// </summary>
    [Required]
    public string DataType { get; set; }

    /// <summary>
    ///     数据长度
    /// </summary>
    public string? DataLength { get; set; }

    /// <summary>
    ///     是否可空
    /// </summary>
    [Required]
    public int AllowNull { get; set; }
}