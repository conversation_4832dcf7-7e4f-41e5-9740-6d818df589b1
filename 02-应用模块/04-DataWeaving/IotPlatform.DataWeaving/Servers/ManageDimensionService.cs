using Extras.DatabaseAccessor.SqlSugar.Internal;
using Extras.DatabaseAccessor.SqlSugar.Repositories;

namespace IotPlatform.DataWeaving.Servers;

/// <summary>
///     数据编织-维度管理
///     版 本:V5.0.0
///     版 权:杭州峰回科技有限公司
///     作 者:蒋状
///     日 期:2023-07-07
/// </summary>
[ApiDescriptionSettings("数据编织")]
public class ManageDimensionService : IDynamicApiController, ITransient
{
    /// <summary>
    ///     服务基础仓储.
    /// </summary>
    private readonly ISqlSugarRepository<ManageDimension> _manageDimension;

    /// <summary>
    /// </summary>
    /// <param name="dbLinkRepository"></param>
    public ManageDimensionService(ISqlSugarRepository<ManageDimension> dbLinkRepository)
    {
        _manageDimension = dbLinkRepository;
    }

    #region Get

    /// <summary>
    ///     维度管理-列表
    /// </summary>
    /// <param name="input">过滤条件.</param>
    /// <returns></returns>
    [HttpGet("/manageDimension/page")]
    public async Task<SqlSugarPagedList<ManageDimension>> Page([FromQuery] BasePageInput input)
    {
        SqlSugarPagedList<ManageDimension>? manageDimensionList = await _manageDimension.AsQueryable()
            .WhereIF(!string.IsNullOrEmpty(input.SearchValue?.Trim()), w => w.Name.Contains(input.SearchValue!)
                                                                            || w.Identifying.Contains(input.SearchValue!)
                                                                            || (w.Remark != null && w.Remark.Contains(input.SearchValue!)))
            .WhereIF(!string.IsNullOrEmpty(input.SearchBeginTime?.Trim()), u =>
                u.CreatedTime >= DateTime.Parse(input.SearchBeginTime!.Trim()) &&
                u.CreatedTime <= DateTime.Parse(input.SearchEndTime.Trim()))
            .OrderBy(o => o.CreatedTime)
            .ToPagedListAsync(input.PageNo, input.PageSize);
        return manageDimensionList;
    }

    /// <summary>
    ///     维度管理-下拉框
    /// </summary>
    /// <returns></returns>
    [HttpGet("/manageDimension/select")]
    public async Task<List<StandardSelectOutput>> Select()
    {
        List<StandardSelectOutput>? manageDimensionList = await _manageDimension.AsQueryable()
            .OrderBy(o => o.CreatedTime)
            .Select(s => new StandardSelectOutput
            {
                Id = s.Id,
                Text = s.Name,
                Value = s.Name,
                ExtendField1 = s.Identifying,
                ExtendField2 = s.Remark
            })
            .ToListAsync();
        return manageDimensionList;
    }

    /// <summary>
    ///     维度管理-详情
    /// </summary>
    /// <param name="input">过滤条件.</param>
    /// <returns></returns>
    [HttpGet("/manageDimension/detail")]
    public async Task<ManageDimension> Detail([FromQuery] BaseId input)
    {
        ManageDimension? manageDimension = await _manageDimension.AsQueryable().FirstAsync(f => f.Id == input.Id);
        if (manageDimension == null)
        {
            throw Oops.Oh("数据已被删除！");
        }

        return manageDimension;
    }

    /// <summary>
    ///     维度管理-数据列表
    /// </summary>
    /// <param name="input">过滤条件.</param>
    /// <returns></returns>
    [HttpGet("/manageDimension/data/list")]
    public async Task<List<ManageDimensionData>> DataList([FromQuery] ManageDimensionDataPageInput input)
    {
        List<ManageDimensionData>? addItem = await _manageDimension.AsSugarClient().Queryable<ManageDimensionData>()
            .Where(w => w.ManageDimensionId == input.ManageDimensionId)
            .WhereIF(!string.IsNullOrEmpty(input.SearchValue?.Trim()), w => w.Name.Contains(input.SearchValue)
                                                                            || w.NodeId.Contains(input.SearchValue))
            .Includes(w => w.PManageDimensionData)
            .ToChildListAsync(it => it.Pid, input.Pid);
        return addItem;
    }

    /// <summary>
    ///     维度管理-数据树
    /// </summary>
    /// <param name="input">过滤条件.</param>
    /// <returns></returns>
    [HttpGet("/manageDimension/data/tree")]
    public async Task<List<ManageDimensionDataTreeOutput>> Tree([FromQuery] BaseId input)
    {
        List<ManageDimensionData>? trees = await _manageDimension.AsSugarClient().Queryable<ManageDimensionData>()
            .Where(z => z.Pid == 0 && z.ManageDimensionId == input.Id).ToListAsync(); //过滤全写在这里
        if (!trees.Any())
        {
            return new List<ManageDimensionDataTreeOutput>();
        }

        foreach (ManageDimensionData? data in trees)
        {
            List<ManageDimensionData>? addItem = await _manageDimension.AsSugarClient().Queryable<ManageDimensionData>()
                .ToTreeAsync(z => z.Children, it => it.Pid, data.Id);
            data.Children = addItem;
        }

        return trees.Adapt<List<ManageDimensionDataTreeOutput>>();
    }

    #endregion Get

    #region Post

    /// <summary>
    ///     维度管理-新增
    /// </summary>
    /// <param name="input"></param>
    /// <exception cref="AppFriendlyException"></exception>
    [HttpPost("/manageDimension/add")]
    public async Task<long> ManageDimensionAdd(ManageDimensionAddInput input)
    {
        if (await _manageDimension.IsAnyAsync(a => a.Identifying == input.Identifying))
        {
            throw Oops.Oh("标识已存在！");
        }

        ManageDimension mapTable = input.Adapt<ManageDimension>();
        await _manageDimension.InsertAsync(mapTable);
        return mapTable.Id;
    }

    /// <summary>
    ///     维度管理-新建数据
    /// </summary>
    /// <param name="input"></param>
    /// <exception cref="AppFriendlyException"></exception>
    [HttpPost("/manageDimension/data/add")]
    public async Task ManageDimensionDataAdd(ManageDimensionDataAddInput input)
    {
        if (await _manageDimension.AsSugarClient().Queryable<ManageDimensionData>().AnyAsync(a =>
                a.Name == input.Name && a.ManageDimensionId == input.ManageDimensionId && a.Pid == input.Pid))
        {
            throw Oops.Oh("节点已存在！");
        }

        ManageDimensionData mapTable = input.Adapt<ManageDimensionData>();
        await _manageDimension.AsSugarClient().Insertable(mapTable).ExecuteCommandAsync();
    }

    /// <summary>
    ///     维度管理-修改
    /// </summary>
    /// <param name="input"></param>
    /// <exception cref="AppFriendlyException"></exception>
    [HttpPost("/manageDimension/update")]
    public async Task ManageDimensionUpdate(ManageDimensionUpdateInput input)
    {
        if (await _manageDimension.IsAnyAsync(a => a.Identifying == input.Identifying && a.Id != input.Id))
        {
            throw Oops.Oh("标识已存在！");
        }

        ManageDimension mapTable = input.Adapt<ManageDimension>();
        await _manageDimension.AsSugarClient().Updateable(mapTable).IgnoreColumns(w => new {w.Identifying}).ExecuteCommandAsync();
    }

    /// <summary>
    ///     维度管理-修改数据
    /// </summary>
    /// <param name="input"></param>
    /// <exception cref="AppFriendlyException"></exception>
    [HttpPost("/manageDimension/data/update")]
    public async Task ManageDimensionDataUpdate(ManageDimensionData input)
    {
        if (await _manageDimension.AsSugarClient().Queryable<ManageDimensionData>().AnyAsync(a =>
                a.Name == input.Name && a.ManageDimensionId == input.ManageDimensionId && a.Pid == input.Pid &&
                a.Id != input.Id))
        {
            throw Oops.Oh("节点名称已存在！");
        }

        await _manageDimension.AsSugarClient().Updateable(input).ExecuteCommandAsync();
    }

    /// <summary>
    ///     维度管理-删除
    /// </summary>
    /// <param name="input"></param>
    /// <exception cref="AppFriendlyException"></exception>
    [HttpPost("/manageDimension/delete")]
    public async Task ManageDimensionDelete(BaseId input)
    {
        ManageDimension? manageDimension = await _manageDimension.AsQueryable().FirstAsync(f => f.Id == input.Id);
        if (manageDimension == null)
        {
            throw Oops.Oh("数据不存在,请刷新重试!");
        }

        await _manageDimension.AsSugarClient().DeleteNav(manageDimension).Include(w => w.ManageDimensionDatas)
            .ExecuteCommandAsync();
    }

    /// <summary>
    ///     维度管理-删除数据
    /// </summary>
    /// <param name="input"></param>
    /// <exception cref="AppFriendlyException"></exception>
    [HttpPost("/manageDimension/data/delete")]
    public async Task ManageDimensionDataDelete(BaseId input)
    {
        if (await _manageDimension.AsSugarClient().Queryable<ManageDimensionData>().AnyAsync(a => a.Pid == input.Id))
        {
            throw Oops.Oh("节点被使用,禁止删除！");
        }

        ManageDimensionData? manageDimensionData = await _manageDimension.AsSugarClient().Queryable<ManageDimensionData>()
            .FirstAsync(f => f.Id == input.Id);
        if (manageDimensionData == null)
        {
            throw Oops.Oh("数据不存在,请刷新重试!");
        }

        await _manageDimension.AsSugarClient().Deleteable(manageDimensionData).ExecuteCommandAsync();
    }

    #endregion
}