using Common.Models;
using IotPlatform.Core.Enum;

namespace Systems.Core;

/// <summary>
///     系统服务
/// </summary>
[ApiDescriptionSettings("系统服务")]
public class SysServiceService : IDynamicApiController, ITransient
{
    private readonly ISqlSugarRepository<SysService> _sysServiceRep; // 服务表仓储
    private readonly ISqlSugarRepository<SysShortcutMenu> _sysShortcutMenu;
    private readonly SysMenuService _sysMenuService;
    private readonly IUserManager _userManager;

    /// <summary>
    /// </summary>
    /// <param name="sysServiceRep"></param>
    /// <param name="userManager"></param>
    /// <param name="sysMenuService"></param>
    /// <param name="sysShortcutMenu">快捷菜单</param>
    public SysServiceService(ISqlSugarRepository<SysService> sysServiceRep, IUserManager userManager, SysMenuService sysMenuService, ISqlSugarRepository<SysShortcutMenu> sysShortcutMenu)
    {
        _sysServiceRep = sysServiceRep;
        _userManager = userManager;
        _sysMenuService = sysMenuService;
        _sysShortcutMenu = sysShortcutMenu;
    }

    /// <summary>
    ///     快捷菜单
    /// </summary>
    [HttpGet("/shortcutMenu/current-user/list")]
    public async Task<List<ShortcutMenuList>> ShortcutMenuList()
    {
        // 个人用户菜单
        List<ShortcutMenuList> shortcutMenuList = await _sysShortcutMenu.AsQueryable().Where(u => u.SysUserId == _userManager.UserId)
            .Includes(w => w.SysMenu)
            .Select(s => new ShortcutMenuList
            {
                Id = s.Id,
                Code = s.SysMenu.Code,
                Name = s.SysMenu.Name,
                Router = s.SysMenu.Router,
                SysMenuId = s.SysMenuId
            })
            .ToListAsync();
        return shortcutMenuList;
    }

    /// <summary>
    ///     用户拥有服务及菜单
    /// </summary>
    [HttpGet("/sysService/current-user/shortcuts")]
    public async Task<List<CurrentUserServiceOutput>> CurrentUserServiceList()
    {
        ISugarQueryable<SysService> apps = _sysServiceRep.AsQueryable().Where(u => u.Status == true);
        if (!_userManager.IsAdministrator)
        {
            List<string> appCodeList = await _sysMenuService.GetUserMenuAppCodeList(_userManager.UserId);
            apps = apps.Where(u => appCodeList.Contains(u.Code));
        }

        List<CurrentUserServiceOutput> appList = await apps.OrderBy(u => u.Sort).Select(u => new CurrentUserServiceOutput
        {
            Code = u.Code,
            Name = u.Name,
            Id = u.Id,
            Shortcuts = new List<Shortcut>()
        }).ToListAsync();

        foreach (CurrentUserServiceOutput app in appList)
        {
            app.Shortcuts = await _sysMenuService.GetLoginMenusAppCode(_userManager.UserId, app.Code);
        }

        return appList;
    }

    /// <summary>
    ///     快捷菜单-新增
    /// </summary>
    /// <param name="input"></param>
    /// <exception cref="AppFriendlyException"></exception>
    [HttpPost("/sysService/current-user/save")]
    public async Task ShortcutMenuSave(ShortcutMenuAdd input)
    {
        // 当前用户已经分配过的菜单
        List<SysShortcutMenu> shortcutMenus = await _sysShortcutMenu.AsQueryable().Where(u => u.SysUserId == _userManager.UserId).ToListAsync();
        // 直接删除,再新增，避免对比
        await _sysShortcutMenu.DeleteAsync(shortcutMenus);
        // 重新新增
        List<SysShortcutMenu> addShortcutMenus = input.MenuId.Select(menuId => new SysShortcutMenu {SysMenuId = menuId, SysUserId = _userManager.UserId}).ToList();
        await _sysShortcutMenu.InsertRangeAsync(addShortcutMenus);
    }

    /// <summary>
    ///     服务排序
    /// </summary>
    /// <param name="input"></param>
    /// <exception cref="AppFriendlyException"></exception>
    [HttpPost("/sysService/sort")]
    public async Task SysServiceSort(SysServiceSortInput input)
    {
        List<SysService> services = await _sysServiceRep.AsQueryable().Where(u => u.Status == true).ToListAsync();
        foreach (SysService service in services)
        {
            int index = input.ServiceNames.FindIndex(x => x == service.Code);
            if (index != -1)
            {
                service.Sort = index;
            }
        }

        await _sysServiceRep.AsSugarClient().Updateable(services).UpdateColumns(w => w.Sort).ExecuteCommandAsync();
    }

    /// <summary>
    ///     获取用户服务相关信息
    /// </summary>
    /// <param name="userId"></param>
    /// <returns></returns>
    [NonAction]
    public async Task<List<ServiceOutput>> GetLoginServices(long userId)
    {
        ISugarQueryable<SysService> apps = _sysServiceRep.AsQueryable().Where(u => u.Status == true);
        if (!_userManager.IsAdministrator)
        {
            List<string> appCodeList = await _sysMenuService.GetUserMenuAppCodeList(userId);
            apps = apps.Where(u => appCodeList.Contains(u.Code));
        }

        List<ServiceOutput> appList = await apps.OrderBy(u => u.Sort).Select(u => new ServiceOutput
        {
            Code = u.Code,
            Name = u.Name,
            Active = u.Active,
            Id = u.Id,
            Remark = u.Remark,
            OpenType = u.OpenType,
            Sort = u.Sort,
            Menus = new List<AntDesignTreeNode>()
        }).ToListAsync();

        foreach (ServiceOutput app in appList)
        {
            app.Menus = await _sysMenuService.GetLoginMenusAntDesign(userId, app.Code);
        }

        return appList;
    }

    /// <summary>
    ///     分页查询系统服务
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpGet("/sysService/page")]
    public async Task<SqlSugarPagedList<SysService>> QueryServicePageList([FromQuery] BasePageInput input)
    {
        SqlSugarPagedList<SysService> apps = await _sysServiceRep.AsQueryable()
            .WhereIF(!string.IsNullOrEmpty(input.SearchValue?.Trim()),
                w => w.Name.Contains(input.SearchValue) || w.Code.Contains(input.SearchValue))
            //.Where(u => u.Status == true)
            .OrderBy(u => u.Sort)
            .ToPagedListAsync(input.PageNo, input.PageSize);
        return apps;
    }

    /// <summary>
    ///     增加系统服务
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/sysService/add")]
    public async Task AddService(AddServiceInput input)
    {
        bool isExist = await _sysServiceRep.IsAnyAsync(u => u.Name == input.Name || u.Code == input.Code);
        if (isExist)
        {
            throw Oops.Oh(ErrorCode.Com1004);
        }

        if (input.Active == YesNoEnum.Y.ToString())
        {
            isExist = await _sysServiceRep.IsAnyAsync(u => u.Active == input.Active);
            if (isExist)
            {
                throw Oops.Oh(ErrorCode.D5001);
            }
        }

        SysService app = input.Adapt<SysService>();
        await _sysServiceRep.InsertAsync(app);
    }

    /// <summary>
    ///     删除系统服务
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/sysService/delete")]
    public async Task DeleteService(BaseId input)
    {
        SysService app = await _sysServiceRep.GetFirstAsync(u => u.Id == input.Id);
        // 该服务下是否有状态为正常的菜单
        bool hasMenu = await _sysMenuService.HasMenu(app.Code);
        if (hasMenu)
        {
            throw Oops.Oh(ErrorCode.D5002);
        }

        await _sysServiceRep.DeleteAsync(app);
    }

    /// <summary>
    ///     更新系统服务
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/sysService/edit")]
    public async Task UpdateApp(UpdateServiceInput input)
    {
        bool isExist = await _sysServiceRep.IsAnyAsync(u => (u.Name == input.Name || u.Code == input.Code) && u.Id != input.Id);
        if (isExist)
        {
            throw Oops.Oh(ErrorCode.Com1004);
        }

        if (input.Active == YesNoEnum.Y.ToString())
        {
            isExist = await _sysServiceRep.IsAnyAsync(u => u.Active == input.Active && u.Id != input.Id);
            if (isExist)
            {
                throw Oops.Oh(ErrorCode.D5001);
            }
        }

        SysService app = input.Adapt<SysService>();
        await _sysServiceRep.AsUpdateable(app).IgnoreColumns(it => new {it.CreatedUserId, it.CreatedTime, it.CreatedUserName}).ExecuteCommandAsync();
    }

    /// <summary>
    ///     获取系统服务
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpGet("/sysService/detail")]
    public async Task<SysService> GetService([FromQuery] BaseId input)
    {
        return await _sysServiceRep.GetFirstAsync(u => u.Id == input.Id);
    }

    /// <summary>
    ///     获取系统服务列表
    /// </summary>
    /// <returns></returns>
    [HttpGet("/sysService/list")]
    public async Task<List<SysService>> GetServiceList()
    {
        return await _sysServiceRep.AsQueryable().Where(u => u.Status == true).OrderBy(u => u.Sort).ToListAsync();
    }

    /// <summary>
    ///     设为默认服务
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/sysService/setAsDefault")]
    public async Task SetAsDefault(BaseId input)
    {
        List<SysService> apps = await _sysServiceRep.AsQueryable().Where(u => u.Status == true).ToListAsync();
        apps.ForEach(u => { u.Active = YesNoEnum.N.ToString(); });

        SysService app = await _sysServiceRep.GetFirstAsync(u => u.Id == input.Id);
        app.Active = YesNoEnum.Y.ToString();
    }

    /// <summary>
    ///     修改服务状态
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/sysService/changeStatus")]
    public async Task ChangeUserServiceStatus(ChangeUserServiceStatusInput input)
    {
        SysService app = await _sysServiceRep.GetFirstAsync(u => u.Id == input.Id);
        app.Status = input.Status;
        await _sysServiceRep.UpdateAsync(app);
    }
}