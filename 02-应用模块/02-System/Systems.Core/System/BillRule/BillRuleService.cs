using Common.Core.Manager.Files;
using IotPlatform.Core.Enum;
using IotPlatform.Core.Extension;
using DateTime = System.DateTime;

namespace Systems.Core;

/// <summary>
///     单据规则.
/// </summary>
public class BillRuleService : IDynamicApiController, ITransient
{
    /// <summary>
    ///     服务基础仓储.
    /// </summary>
    private readonly ISqlSugarRepository<BillRuleEntity> _repository;

    /// <summary>
    ///     文件服务.
    /// </summary>
    private readonly IFileManager _fileManager;

    /// <summary>
    ///     缓存管理器.
    /// </summary>
    private readonly SysCacheService _cacheManager;

    /// <summary>
    ///     用户管理.
    /// </summary>
    private readonly IUserManager _userManager;

    /// <summary>
    ///     初始化一个<see cref="BillRuleService" />类型的新实例.
    /// </summary>
    public BillRuleService(
        ISqlSugarRepository<BillRuleEntity> repository,
        SysCacheService cacheManager,
        IUserManager userManager,
        IFileManager fileManager)
    {
        _repository = repository;
        _cacheManager = cacheManager;
        _userManager = userManager;
        _fileManager = fileManager;
    }

    #region Get

    /// <summary>
    ///     列表.
    /// </summary>
    /// <returns></returns>
    [HttpGet("/billRule/selector")]
    public async Task<dynamic> GetSelector([FromQuery] BillRuleListQueryInput input)
    {
        SqlSugarPagedList<BillRuleListOutput> list = await _repository.AsSugarClient().Queryable<BillRuleEntity, SysUser>((a, b) => new JoinQueryInfos(JoinType.Left, b.Id == a.CreatedUserId))
            .Where(a => a.EnabledMark == 1)
            .WhereIF(input.categoryId > 0, a => a.Category == input.categoryId)
            .WhereIF(!string.IsNullOrEmpty(input.keyword), a => a.FullName.Contains(input.keyword) || a.EnCode.Contains(input.keyword))
            .OrderBy(a => a.CreatedTime, OrderByType.Desc)
            .Select((a, b) => new BillRuleListOutput
            {
                id = a.Id,
                lastModifyTime = a.UpdatedTime,
                creatorUser = b.Name,
                fullName = a.FullName,
                enCode = a.EnCode,
                enabledMark = a.EnabledMark,
                outputNumber = a.OutputNumber,
                digit = a.Digit,
                startNumber = a.StartNumber,
                creatorTime = a.CreatedTime
            }).ToPagedListAsync(input.currentPage, input.pageSize);
        return list;
    }

    /// <summary>
    ///     获取单据规则列表(带分页).
    /// </summary>
    /// <param name="input">请求参数.</param>
    /// <returns></returns>
    [HttpGet("/billRule/getList")]
    public async Task<dynamic> GetList([FromQuery] BillRuleListQueryInput input)
    {
        SqlSugarPagedList<BillRuleListOutput> list = await _repository.AsSugarClient()
            .Queryable<BillRuleEntity, SysUser, BillRuleCategory>((a, b, c) => new JoinQueryInfos(JoinType.Left, a.CreatedUserId == b.Id, JoinType.Left, a.Category == c.Id))
            .WhereIF(input.categoryId > 0, a => a.Category == input.categoryId)
            .WhereIF(input.enabledMark.IsNotEmptyOrNull(), a => a.EnabledMark.Equals(input.enabledMark))
            .WhereIF(!string.IsNullOrEmpty(input.keyword), a => a.FullName.Contains(input.keyword) || a.EnCode.Contains(input.keyword))
            .Select((a, b, c) => new BillRuleListOutput
            {
                id = a.Id,
                enabledMark = a.EnabledMark,
                lastModifyTime = a.UpdatedTime,
                creatorUser = b.Name,
                fullName = a.FullName,
                enCode = a.EnCode,
                outputNumber = a.OutputNumber ?? "",
                digit = a.Digit,
                startNumber = a.StartNumber,
                creatorTime = a.CreatedTime,
                category = c.Name
            }).MergeTable()
            .OrderBy(a => a.creatorTime, OrderByType.Desc)
            .OrderByIF(!string.IsNullOrEmpty(input.keyword), a => a.lastModifyTime, OrderByType.Desc).ToPagedListAsync(input.currentPage, input.pageSize);
        return list;
    }

    /// <summary>
    ///     获取单据流水号（工作流程调用）.
    /// </summary>
    /// <param name="enCode">编码.</param>
    /// <returns></returns>
    [HttpGet("/billRule/billNumber/{enCode}")]
    public async Task<dynamic> GetBillNumber(string enCode)
    {
        return await GetBillNumber(enCode, false);
    }

    /// <summary>
    ///     信息.
    /// </summary>
    /// <param name="id">主键.</param>
    /// <returns></returns>
    [HttpGet("/billRule/{id}")]
    public async Task<dynamic> GetInfo(long id)
    {
        return (await _repository.GetFirstAsync(x => x.Id == id)).Adapt<BillRuleInfoOutput>();
    }

    /// <summary>
    ///     导出.
    /// </summary>
    /// <param name="id">主键.</param>
    /// <returns></returns>
    [HttpGet("/billRule/{id}/Actions/Export")]
    public async Task<dynamic> ActionsExport(long id)
    {
        BillRuleEntity? data = await _repository.GetFirstAsync(x => x.Id == id);
        return data;
    }

    #endregion

    #region Post

    /// <summary>
    ///     新建.
    /// </summary>
    /// <param name="input">实体对象.</param>
    /// <returns></returns>
    [HttpPost("/billRule/create")]
    public async Task Create([FromBody] BillRuleCrInput input)
    {
        if (await _repository.IsAnyAsync(x => x.EnCode == input.enCode || x.FullName == input.fullName))
        {
            throw Oops.Oh(ErrorCode.Com1004);
        }

        BillRuleEntity entity = input.Adapt<BillRuleEntity>();
        int isOk = await _repository.AsInsertable(entity).IgnoreColumns(true).ExecuteCommandAsync();
        if (isOk < 1)
        {
            throw Oops.Oh(ErrorCode.COM1000);
        }
    }

    /// <summary>
    ///     修改.
    /// </summary>
    /// <param name="id">主键值.</param>
    /// <param name="input">实体对象.</param>
    /// <returns></returns>
    [HttpPut("/billRule/{id}/update")]
    public async Task Update(long id, [FromBody] BillRuleUpInput input)
    {
        if (await _repository.IsAnyAsync(x => x.Id != id && (x.EnCode == input.enCode || x.FullName == input.fullName)))
        {
            throw Oops.Oh(ErrorCode.Com1004);
        }

        BillRuleEntity entity = input.Adapt<BillRuleEntity>();
        bool isOk = await _repository.AsUpdateable(entity).IgnoreColumns(true).ExecuteCommandHasChangeAsync();
        if (!isOk)
        {
            throw Oops.Oh(ErrorCode.COM1001);
        }
    }

    /// <summary>
    ///     删除.
    /// </summary>
    /// <param name="id">主键.</param>
    /// <returns></returns>
    [HttpDelete("/billRule/{id}/delete")]
    public async Task Delete(long id)
    {
        BillRuleEntity? entity = await _repository.GetFirstAsync(x => x.Id == id);
        if (entity == null)
        {
            throw Oops.Oh(ErrorCode.COM1005);
        }

        if (!string.IsNullOrEmpty(entity.OutputNumber))
        {
            throw Oops.Oh(ErrorCode.BR0001);
        }

        bool isOk = await _repository.DeleteAsync(entity);
        if (!isOk)
        {
            throw Oops.Oh(ErrorCode.COM1002);
        }
    }

    /// <summary>
    /// 修改单据规则状态.
    /// </summary>
    /// <param name="id">主键值.</param>
    /// <returns></returns>
    [HttpPut("/billRule/{id}/Actions/State")]
    public async Task ActionsState(string id)
    {
        var isOk = await _repository.AsUpdateable().SetColumns(it => new BillRuleEntity()
        {
            EnabledMark = SqlFunc.IIF(it.EnabledMark == 1, 0, 1),
            UpdatedUserId = _userManager.UserId,
            UpdatedTime = SqlFunc.GetDate()
        }).Where(it => it.Id.Equals(id)).ExecuteCommandHasChangeAsync();
        if (!isOk)
            throw Oops.Oh(ErrorCode.COM1003);
    }

    /// <summary>
    /// 导入.
    /// </summary>
    /// <param name="file"></param>
    /// <param name="type"></param>
    /// <returns></returns>
    [HttpPost("/billRule/actions/Import")]
    public async Task ActionsImport(IFormFile file, int type)
    {
        var fileType = Path.GetExtension(file.FileName).Replace(".", string.Empty);
        if (!fileType.ToLower().Equals("json"))
            throw Oops.Oh(ErrorCode.D3006);
        var josn = _fileManager.Import(file);
        BillRuleEntity? data;
        try
        {
            data = josn.ToObjectOld<BillRuleEntity>();
        }
        catch
        {
            throw Oops.Oh(ErrorCode.D3006);
        }
        if (data == null || data.Prefix.IsNullOrEmpty())
            throw Oops.Oh(ErrorCode.D3006);
    
        var errorMsgList = new List<string>();
        var errorList = new List<string>();
        if (await _repository.AsQueryable().AnyAsync(it => it.Id.Equals(data.Id))) errorList.Add("ID");
        if (await _repository.AsQueryable().AnyAsync(it => it.EnCode.Equals(data.EnCode))) errorList.Add("编码");
        if (await _repository.AsQueryable().AnyAsync(it => it.FullName.Equals(data.FullName))) errorList.Add("名称");
    
        if (errorList.Any())
        {
            if (type.Equals(0))
            {
                var error = string.Join("、", errorList);
                errorMsgList.Add(string.Format("{0}重复", error));
            }
            else
            {
                var random = new Random().NextLetterAndNumberString(5);
                data.Id = YitIdHelper.NextId();
                data.FullName = string.Format("{0}.副本{1}", data.FullName, random);
                data.EnCode += random;
            }
        }
        if (errorMsgList.Any() && type.Equals(0)) throw Oops.Oh(ErrorCode.COM1018, string.Join(";", errorMsgList));
    
        try
        {
            var storModuleModel = await _repository.AsSugarClient().Storageable(data).Saveable().ToStorageAsync(); // 存在更新不存在插入 根据主键
            await storModuleModel.AsInsertable.ExecuteCommandAsync(); // 执行插入
            await storModuleModel.AsUpdateable.ExecuteCommandAsync(); // 执行更新
        }
        catch (Exception ex)
        {
            throw Oops.Oh(ErrorCode.COM1020, ex.Message);
        }
    }

    #endregion

    #region PublicMethod

    /// <summary>
    ///     获取流水号.
    /// </summary>
    /// <param name="enCode">流水编码.</param>
    /// <param name="isCache">是否缓存：每个用户会自动占用一个流水号，这个刷新页面也不会跳号.</param>
    /// <returns></returns>
    [NonAction]
    public async Task<string> GetBillNumber(string enCode, bool isCache = false)
    {
        string cacheKey = string.Format("{0}{1}_{2}", CommonConst.CACHEKEYBILLRULE, _userManager.TenantId, _userManager.UserId + enCode);
        string strNumber = string.Empty;
        if (isCache)
        {
            if (!_cacheManager.ExistKey(cacheKey))
            {
                strNumber = await GetNumber(enCode);
                _cacheManager.Set(cacheKey, strNumber, new TimeSpan(0, 3, 0));
            }
            else
            {
                strNumber = _cacheManager.Get<string>(cacheKey);
            }
        }
        else
        {
            strNumber = await GetNumber(enCode);
            _cacheManager.Set(cacheKey, strNumber, new TimeSpan(0, 3, 0));
        }

        return strNumber;
    }

    #endregion

    #region PrivateMethod

    /// <summary>
    ///     获取流水号.
    /// </summary>
    /// <param name="enCode"></param>
    /// <returns></returns>
    private async Task<string> GetNumber(string enCode)
    {
        StringBuilder strNumber = new();
        BillRuleEntity? entity = await _repository.GetFirstAsync(m => m.EnCode == enCode);
        if (entity != null)
        {
            // 处理隔天流水号归0
            if (entity.OutputNumber != null)
            {
                string serialDate = entity.OutputNumber.Remove(entity.OutputNumber.Length - (int)entity.Digit).Replace(entity.Prefix, string.Empty);
                string thisDate = entity.DateFormat == "no" ? string.Empty : DateTime.Now.ToString(entity.DateFormat);
                if (serialDate != thisDate)
                {
                    entity.ThisNumber = 0;
                }
                else
                {
                    entity.ThisNumber++;
                }
            }
            else
            {
                entity.ThisNumber = 0;
            }

            // 拼接单据编码
            // 前缀
            strNumber.Append(entity.Prefix);
            if (entity.DateFormat != "no")
            {
                strNumber.Append(DateTime.Now.ToString(entity.DateFormat)); // 日期格式
            }

            int? number = int.Parse(entity.StartNumber) + entity.ThisNumber;
            strNumber.Append(number.ToString().PadLeft((int)entity.Digit, '0')); // 流水号
            entity.OutputNumber = strNumber.ToString();
            // 更新流水号
            await _repository.AsUpdateable(entity).IgnoreColumns(true).ExecuteCommandHasChangeAsync();
        }
        else
        {
            strNumber.Append("单据规则不存在");
        }

        return strNumber.ToString();
    }

    #endregion
}