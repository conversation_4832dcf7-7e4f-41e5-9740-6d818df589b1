using Microsoft.AspNetCore.SignalR;
using Systems.Core.Config;
using Systems.Core.Hub;
using Systems.Core.OnlineUser.Dto;

namespace Systems.Core.OnlineUser;

/// <summary>
/// 系统在线用户服务 🧩
/// </summary>
[ApiDescriptionSettings(Order = 300)]
public class SysOnlineUserService : IDynamicApiController, ITransient
{
    private readonly ISqlSugarRepository<SysOnlineUser> _sysOnlineUerRep;
    private readonly SysConfigService _sysConfigService;
    private readonly IHubContext<OnlineUserHub, IOnlineUserHub> _onlineUserHubContext;

    public SysOnlineUserService(ISqlSugarRepository<SysOnlineUser> sysOnlineUerRep,
        SysConfigService sysConfigService,
        IHubContext<OnlineUserHub, IOnlineUserHub> onlineUserHubContext)
    {
        _sysOnlineUerRep = sysOnlineUerRep;
        _sysConfigService = sysConfigService;
        _onlineUserHubContext = onlineUserHubContext;
    }

    /// <summary>
    /// 获取在线用户分页列表 🔖
    /// </summary>
    /// <returns></returns>
    [DisplayName("获取在线用户分页列表")]
    public async Task<SqlSugarPagedList<SysOnlineUser>> Page(PageOnlineUserInput input)
    {
        return await _sysOnlineUerRep.AsQueryable()
            .WhereIF(!string.IsNullOrWhiteSpace(input.UserName), u => u.UserName.Contains(input.UserName))
            .WhereIF(!string.IsNullOrWhiteSpace(input.RealName), u => u.RealName.Contains(input.RealName))
            .ToPagedListAsync(input.PageNo, input.PageSize);
    }

    /// <summary>
    /// 强制下线 🔖
    /// </summary>
    /// <param name="user"></param>
    /// <returns></returns>
    [NonValidation]
    [DisplayName("强制下线")]
    public async Task ForceOffline(SysOnlineUser user)
    {
        await _onlineUserHubContext.Clients.Client(user.ConnectionId).ForceOffline("强制下线");
        await _sysOnlineUerRep.DeleteAsync(user);
    }

    // /// <summary>
    // /// 发布站内消息
    // /// </summary>
    // /// <param name="notice"></param>
    // /// <param name="userIds"></param>
    // /// <returns></returns>
    // [NonAction]
    // public async Task PublicNotice(SysNotice notice, List<long> userIds)
    // {
    //     var userList = await _sysOnlineUerRep.GetListAsync(u => userIds.Contains(u.UserId));
    //     if (!userList.Any()) return;
    //
    //     foreach (var item in userList)
    //     {
    //         await _onlineUserHubContext.Clients.Client(item.ConnectionId).PublicNotice(notice);
    //     }
    // }

    /// <summary>
    /// 单用户登录
    /// </summary>
    /// <returns></returns>
    [NonAction]
    public async Task SingleLogin(long userId)
    {
        if (await _sysConfigService.GetConfigValue<bool>(CommonConst.SysSingleLogin))
        {
            var users = await _sysOnlineUerRep.GetListAsync(u => u.UserId == userId);
            foreach (var user in users)
            {
                await ForceOffline(user);
            }
        }
    }

    /// <summary>
    /// 通过用户ID踢掉在线用户
    /// </summary>
    /// <param name="userId"></param>
    /// <returns></returns>
    [NonAction]
    public async Task ForceOffline(long userId)
    {
        var users = await _sysOnlineUerRep.GetListAsync(u => u.UserId == userId);
        foreach (var user in users)
        {
            await ForceOffline(user);
        }
    }
}