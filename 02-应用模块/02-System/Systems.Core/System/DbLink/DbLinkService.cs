using Common.Core.Manager.DataBase;
using Common.Models;
using IotPlatform.Core.Enum;
using IotPlatform.Core.Extension;
using IotPlatform.DataWeaving.Entity;
using Systems.Entity.Dto.DataBase;
using Jint;

namespace Systems.Core;

/// <summary>
///     数据应用-数据连接
/// </summary>
[ApiDescriptionSettings("数据应用")]
public class DbLinkService : IDynamicApiController, ITransient
{
    private readonly IDataBaseManager _changeDataBase;
    private readonly ISqlSugarRepository<DbLink> _dbLinkRepository;

    /// <summary>
    /// </summary>
    public DbLinkService(ISqlSugarRepository<DbLink> dbLinkRepository, IDataBaseManager changeDataBase)
    {
        _dbLinkRepository = dbLinkRepository;
        _changeDataBase = changeDataBase;
    }

    #region GET

    /// <summary>
    /// 信息.
    /// </summary>
    /// <param name="id">主键值.</param>
    /// <returns></returns>
    [NonAction]
    public async Task<DbLink> GetInfo(long id)
    {
        return await _dbLinkRepository.GetFirstAsync(m => m.Id == id);
    }

    /// <summary>
    ///     数据连接-列表
    /// </summary>
    /// <returns></returns>
    [HttpGet("/dbLink/page")]
    [AllowAnonymous]
    public async Task<SqlSugarPagedList<DbLink>> Page([FromQuery] BasePageInput input)
    {
        SqlSugarPagedList<DbLink>? list = await _dbLinkRepository.AsQueryable()
            .OrderBy(o => o.SortCode).OrderByDescending(o => o.CreatedTime)
            .ToPagedListAsync(input.PageNo, input.PageSize);
        return list;
    }

    /// <summary>
    ///     数据连接-下拉框
    /// </summary>
    /// <returns></returns>
    [HttpGet("/dbLink/select")]
    public async Task<List<DbLinkSelectOutput>> Select()
    {
        List<DbLinkSelectOutput>? list = await _dbLinkRepository.AsQueryable()
            .OrderBy(o => o.SortCode).OrderByDescending(o => o.CreatedTime)
            .Select<DbLinkSelectOutput>()
            .ToListAsync();
        return list;
    }

    /// <summary>
    ///     数据连接-详情
    /// </summary>
    /// <param name="input">主键值</param>
    /// <returns></returns>
    [HttpGet("/dbLink/detail")]
    public async Task<DbLink> Detail([FromQuery] BaseId input)
    {
        DbLink? data = await _dbLinkRepository.GetFirstAsync(m => m.Id == input.Id);
        if (data == null)
        {
            throw Oops.Oh(ErrorCode.D1002);
        }

        return data;
    }

    /// <summary>
    ///     连接库
    /// </summary>
    /// <returns></returns>
    [HttpGet("/table/list")]
    public async Task<dynamic> GetDbLinkList()
    {
        List<DbLink>? list = await _dbLinkRepository.AsQueryable().ToListAsync();
        return list;
    }

    /// <summary>
    ///     表名列表
    /// </summary>
    /// <returns></returns>
    [HttpGet("/table/tables")]
    public async Task<dynamic> GetList([FromQuery] BaseId input)
    {
        // 数据连接
        DbLink? dbLink = await _dbLinkRepository.GetFirstAsync(m => m.Id == input.Id);
        if (dbLink == null)
        {
            throw Oops.Oh(ErrorCode.D1002);
        }

        try
        {
            // 全部数据得表结构
            List<DbTableInfo> tables = _changeDataBase.GetTableInfos(dbLink);
            return tables.Select(s => s.Name).ToList();
        }
        catch
        {
            throw Oops.Oh("连接失败！");
        }
    }

    /// <summary>
    /// 表名列表.
    /// </summary>
    /// <param name="id">连接Id.</param>
    /// <param name="input">过滤条件.</param>
    /// <returns></returns>
    [HttpGet("/DbLink/{id}/Tables")]
    public async Task<dynamic> GetList(string id, [FromQuery] PageInputBase input)
    {
        // 数据连接
        DbLink? dbLink = await _dbLinkRepository.GetFirstAsync(m => m.Id == id.ParseToLong());
        if (dbLink == null)
        {
            throw Oops.Oh(ErrorCode.D1002);
        }

        if (!_changeDataBase.IsConnection(dbLink)) throw Oops.Oh(ErrorCode.D1507);
        // 全部数据得表结构
        List<DbTableInfo> tables = _changeDataBase.GetTableInfos(dbLink);
        tables = tables.Where((x, i) => tables.FindIndex(z => z.Name == x.Name) == i).ToList();//去重
        var output = tables.Adapt<List<DatabaseTableListOutput>>();
        if (!string.IsNullOrEmpty(input.keyword))
            output = output.FindAll(d => d.table.ToLower().Contains(input.keyword.ToLower()) || (d.tableName.IsNotEmptyOrNull() && d.tableName.ToLower().Contains(input.keyword.ToLower())));

        var totalPages = (int)Math.Ceiling(output.Count / (double)input.pageSize);
        return new SqlSugarPagedList<DatabaseTableListOutput>
        {
            Rows = output.OrderBy(x => x.table).Skip((input.currentPage - 1) * input.pageSize).Take(input.pageSize).OrderBy(x => x.table).ToList(),
            PageNo = input.currentPage,
            PageSize = input.pageSize,
            TotalRows = output.Count,
            TotalPage = totalPages,
            HasNextPage = input.currentPage < totalPages,
            HasPrevPage = input.currentPage - 1 > 0,
        };
    }

    /// <summary>
    ///     字段列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    /// <exception cref="AppFriendlyException"></exception>
    [HttpGet("/table/fields")]
    public async Task<List<DbTableFieldModel>> GetFieldList([FromQuery] GetFieldListInput input)
    {
        DbLink? dbLink = await _dbLinkRepository.GetFirstAsync(m => m.Id == input.Id);
        if (dbLink == null)
        {
            throw Oops.Oh(ErrorCode.D1002);
        }

        try
        {
            return _changeDataBase.GetFieldList(dbLink, input.Name);
        }
        catch
        {
            throw Oops.Oh("连接失败！");
        }
    }

    #endregion

    #region POST

    /// <summary>
    ///     数据连接-删除
    /// </summary>
    /// <param name="input">主键值</param>
    /// <returns></returns>
    [HttpPost("/dbLink/delete")]
    public async Task Delete(BaseId input)
    {
        DbLink? dbLink = await _dbLinkRepository.GetFirstAsync(m => m.Id == input.Id);
        if (dbLink == null)
        {
            throw Oops.Oh(ErrorCode.D1002);
        }

        await _dbLinkRepository.DeleteAsync(dbLink);
    }

    /// <summary>
    ///     数据连接-生成连接字符串
    /// </summary>
    /// <param name="input">实体对象</param>
    /// <returns></returns>
    [HttpPost("/dbLink/create/connectStr")]
    public async Task<string> CreateConnectStr(CreateConnectStrInput input)
    {
        DbLink entity = input.Adapt<DbLink>();
        return _changeDataBase.ToConnectionString(entity, false);
    }

    /// <summary>
    ///     数据连接-新增
    /// </summary>
    /// <param name="input">实体对象</param>
    /// <returns></returns>
    [HttpPost("/dbLink/add")]
    [UnitOfWork]
    public async Task Create(DbLinkCrInput input)
    {
        if (await _dbLinkRepository.IsAnyAsync(m => m.FullName == input.FullName))
        {
            throw Oops.Oh(ErrorCode.Com1004);
        }

        DbLink entity = input.Adapt<DbLink>();
        if (string.IsNullOrEmpty(entity.ConnectString))
        {
            entity.ConnectString = _changeDataBase.ToConnectionString(entity, false);
        }

        await _dbLinkRepository.InsertAsync(entity);
    }

    /// <summary>
    ///     数据连接-编辑
    /// </summary>
    /// <param name="input">实体对象</param>
    /// <returns></returns>
    [HttpPost("/dbLink/update")]
    [UnitOfWork]
    public async Task Update(DbLink input)
    {
        if (await _dbLinkRepository.IsAnyAsync(x => x.Id != input.Id && x.FullName == input.FullName))
        {
            throw Oops.Oh(ErrorCode.Com1004);
        }

        DbLink entity = input.Adapt<DbLink>();
        await _dbLinkRepository.AsSugarClient().Updateable(entity).ExecuteCommandAsync();
    }

    /// <summary>
    ///     数据连接-测试连接
    /// </summary>
    /// <param name="input">实体对象</param>
    /// <returns></returns>
    [HttpPost("/dbLink/actions/test")]
    public void TestDbConnection(DbLink input)
    {
        DbLink entity = input.Adapt<DbLink>();
        entity.Id = entity.Id == 0L ? YitIdHelper.NextId() : input.Id;
        bool flag = _changeDataBase.IsConnection(entity);
        if (!flag)
        {
            throw Oops.Oh(ErrorCode.D1507);
        }
    }

    /// <summary>
    ///     数据连接-动态执行SQL
    /// </summary>
    /// <returns></returns>
    [HttpPost("/dbLink/dynamic-query")]
    public async Task<dynamic> Query(ScreenDataSourceDynamicQueryInput input)
    {
        if (input.Id <= 0)
        {
            return Task.FromResult(true);
        }

        DbLink? entity = await _dbLinkRepository.GetSingleAsync(v => v.Id == input.Id);
        if (entity == null)
        {
            throw Oops.Oh("数据源不存在！");
        }
        dynamic data = _changeDataBase.GetSqlData(entity, input.Sql, true, null);
        return data;
    }

    /// <summary>
    ///     预览数据
    /// </summary>
    /// <param name="input"></param>
    /// <param name="dbLinkId">连接数据库.</param>
    /// <param name="tableName">表名.</param>
    /// <returns></returns>
    [HttpGet("/table/{dbLinkId}/{tableName}/preview")]
    public async Task<SqlSugarPagedList<dynamic>> GetData([FromQuery] PreviewDataInput input, long dbLinkId, string tableName)
    {
        DbLink? dbLink = await _dbLinkRepository.GetFirstAsync(m => m.Id == dbLinkId);
        if (dbLink == null)
        {
            throw Oops.Oh(ErrorCode.D1002);
        }

        if (string.IsNullOrEmpty(tableName))
        {
            return new SqlSugarPagedList<dynamic>();
        }

        StringBuilder dbSql = new();
        dbSql.AppendFormat("SELECT * FROM {0} WHERE 1=1", tableName);

        return await _changeDataBase.GetDataTablePage(dbLink, dbSql.ToString(), input.PageNo, input.PageSize);
    }

    /// <summary>
    ///     动态执行SQL(不支持查询)
    /// </summary>
    /// <param name="input">请求参数.</param>
    /// <param name="dataBase">连接数据库.</param>
    /// <returns></returns>
    [HttpGet("/table/{dataBase}/executeCommand")]
    public async Task<dynamic> ExecuteCommand([FromQuery] DynamicQueryInput input, string dataBase)
    {
        DbLink? dbLink = await _dbLinkRepository.GetFirstAsync(m => m.Id == input.Id);
        if (dbLink == null)
        {
            throw Oops.Oh(ErrorCode.D1002);
        }

        if (string.IsNullOrEmpty(dataBase))
        {
            throw Oops.Oh("请指定连接数据库！");
        }

        int data = await _changeDataBase.ExecuteSql(dbLink, input.Sql);
        return data;
    }

    /// <summary>
    /// 信息.
    /// </summary>
    /// <param name="linkId">连接Id.</param>
    /// <param name="tableName">主键值.</param>
    /// <returns></returns>
    [HttpGet("/table/{linkId}/Table/{tableName}")]
    public async Task<dynamic> GetInfo(long linkId, string tableName)
    {
        DbLink link = await _dbLinkRepository.GetFirstAsync(m => m.Id == linkId);
        if (string.IsNullOrEmpty(tableName))
            throw Oops.Oh("表名是空！");
        var tenantLink = link ?? _changeDataBase.GetTenantDbLink();
        var output = _changeDataBase.GetDataBaseTableInfo(tenantLink, tableName);
        output.hasTableData = _changeDataBase.IsAnyData(tenantLink, tableName);
        return output;
    }

    /// <summary>
    /// 更新.
    /// </summary>
    /// <param name="linkId">连接Id.</param>
    /// <param name="input">请求参数.</param>
    /// <returns></returns>
    [HttpPost("/table/{linkId}/addFields")]
    [UnitOfWork]
    public async Task AddFields(long linkId, [FromBody] DatabaseTableUpInput input)
    {
        try
        {
            if (input.tableFieldList.Any(x => x.field.Length >= 30)) throw Oops.Oh(ErrorCode.D1515);
            DbLink link = await _dbLinkRepository.GetFirstAsync(m => m.Id == linkId);
            var tenantLink = link ?? _changeDataBase.GetTenantDbLink();
            _changeDataBase.AddTableColumn(tenantLink, input.tableInfo, input.tableFieldList.Adapt<List<DbTableFieldModel>>());
            DbEntityManage dbEntityManage = await _dbLinkRepository.AsSugarClient().Queryable<DbEntityManage>().Where(w => w.Table == input.tableInfo.table && w.ConfigId == linkId.ToString()).FirstAsync();
            if (dbEntityManage == null)
            {
                dbEntityManage = new DbEntityManage
                {
                    ConfigId = linkId.ToString(),
                    Table = input.tableInfo.newTable,
                    Description = input.tableInfo.tableName ?? "",
                    Tags = !input.tags.Any() ? ["未分组"] : input.tags
                };
                await _dbLinkRepository.AsSugarClient().Insertable(dbEntityManage).ExecuteCommandAsync();
            }
            else
            {
                dbEntityManage.Table = input.tableInfo.newTable;
                dbEntityManage.Description = input.tableInfo.tableName ?? "";
                dbEntityManage.Tags = !input.tags.Any() ? ["未分组"] : input.tags;
                await _dbLinkRepository.AsSugarClient().Updateable(dbEntityManage).ExecuteCommandAsync();
            }
        }
        catch (Exception ex)
        {
            throw Oops.Oh(ErrorCode.D1510 + "：" + ex.Message);
        }
    }

    

    #endregion
}