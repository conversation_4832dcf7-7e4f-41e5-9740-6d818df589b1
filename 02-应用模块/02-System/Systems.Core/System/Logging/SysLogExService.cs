namespace Systems.Core;

/// <summary>
///     系统异常日志服务
/// </summary>
[ApiDescriptionSettings("系统服务",Order = 350)]
public class SysLogExService : IDynamicApiController, ITransient
{
    private readonly ISqlSugarRepository<SysLogEx> _sysLogExRep;

    public SysLogExService(ISqlSugarRepository<SysLogEx> sysLogExRep)
    {
        _sysLogExRep = sysLogExRep;
    }

    /// <summary>
    ///     获取异常日志分页列表
    /// </summary>
    /// <returns></returns>
    [SuppressMonitor]
    [HttpGet("/sysExlog/page")]
    [DisplayName("获取异常日志分页列表")]
    public async Task<SqlSugarPagedList<SysLogEx>> Page([FromQuery] PageLogInput input)
    {
        return await _sysLogExRep.AsQueryable()
            .WhereIF(!string.IsNullOrWhiteSpace(input.SearchBeginTime), u => u.CreatedTime >= Convert.ToDateTime(input.SearchBeginTime))
            .WhereIF(!string.IsNullOrWhiteSpace(input.SearchEndTime), u => u.CreatedTime <= Convert.ToDateTime(input.SearchEndTime))
            .OrderBy(u => u.CreatedTime, OrderByType.Desc)
            .ToPagedListAsync(input.PageNo, input.PageSize);
    }

    /// <summary>
    ///     清空异常日志
    /// </summary>
    /// <returns></returns>
    [HttpPost("/sysExlog/delete")]
    [DisplayName("清空异常日志")]
    public async Task<bool> Clear()
    {
        return await _sysLogExRep.DeleteAsync(u => u.Id > 0);
    }

    /// <summary>
    ///     导出异常日志
    /// </summary>
    /// <returns></returns>
    [NonUnify]
    [HttpPost("/sysExlog/export")]
    [DisplayName("导出异常日志")]
    public async Task<IActionResult> ExportLogEx(LogInput input)
    {
        List<ExportLogDto> logExList = await _sysLogExRep.AsQueryable()
            .WhereIF(!string.IsNullOrWhiteSpace(input.SearchBeginTime) && !string.IsNullOrWhiteSpace(input.SearchEndTime),
                u => u.CreatedTime >= Convert.ToDateTime(input.SearchBeginTime) && u.CreatedTime <= Convert.ToDateTime(input.SearchEndTime))
            .OrderBy(u => u.CreatedTime, OrderByType.Desc)
            .Select<ExportLogDto>().ToListAsync();

        IExcelExporter excelExporter = new ExcelExporter();
        byte[] res = await excelExporter.ExportAsByteArray(logExList);
        return new FileStreamResult(new MemoryStream(res), "application/octet-stream") {FileDownloadName = DateTime.Now.ToString("yyyyMMddHHmm") + "异常日志.xlsx"};
    }
}