using System.Diagnostics;
using System.Security.Cryptography;
using IotPlatform.Core.Extension;
using IotPlatform.Thing.Entity;
using Microsoft.Extensions.Hosting;
using Newtonsoft.Json;

namespace Systems.Core;

/// <summary>
///     授权服务
/// </summary>
[ApiDescriptionSettings("系统服务")]
public class AuthorizeService : IDynamicApiController, ITransient
{
    /// <summary>
    ///   缓存服务
    /// </summary>
    private readonly SysCacheService _sysCacheService;
    /// <summary>
    ///   主机环境
    /// </summary>
    private readonly IHostEnvironment _hostEnvironment;

    /// <summary>
    ///     服务基础仓储.
    /// </summary>
    private readonly ISqlSugarClient _sqlSugarClient;

    /// <summary>
    ///   缓存键
    /// </summary>
    private const string CACHE_KEY = "authorize:";

    /// <summary>
    ///   机器码文件名
    /// </summary>
    private const string MACHINE_CODE_FILE = "machine.code";

    /// <summary>
    ///   授权信息存储目录名
    /// </summary>
    private const string AUTHORIZE_DIRECTORY = "Authorize";

    /// <summary>
    ///   授权信息文件后缀
    /// </summary>
    private const string AUTHORIZE_FILE_SUFFIX = ".auth.json";

    /// <summary>
    ///   构造函数
    /// </summary>
    /// <param name="sysCacheService">缓存服务</param>
    /// <param name="hostEnvironment">主机环境</param>
    public AuthorizeService(SysCacheService sysCacheService, IHostEnvironment hostEnvironment, ISqlSugarClient sqlSugarClient)
    {
        _sysCacheService = sysCacheService;
        _hostEnvironment = hostEnvironment;
        _sqlSugarClient = sqlSugarClient;

        // 初始化授权信息
        Task.Run(async () => await InitializeAuthorizeInfo()).Wait();
    }

    /// <summary>
    ///     获取机器码
    /// </summary>
    /// <returns></returns>
    [HttpGet("/authorize/machineCode")]
    public async Task<dynamic> GetMachineCode()
    {
        return new { machineCode = await GetMachineFingerprint() };
    }

    /// <summary>
    ///     设置授权
    /// </summary>
    /// <param name="input">加密文本</param>
    /// <returns></returns>
    [HttpPost("/authorize/setAuthorize")]
    public async Task<dynamic> SetAuthorize([FromBody] SetAuthorizeInput input)
    {
        try
        {
            // 解密授权信息
            string decryptedText = await RSADecrypt(input.EncryptedText);
            SetAuthorizeOutput authorizeInfo = decryptedText.ToObjectOld<SetAuthorizeOutput>();

            // 验证指纹
            if (authorizeInfo.Sn != await GetMachineFingerprint())
            {
                throw Oops.Oh("机器码不匹配");
            }

            // 验证过期时间
            if (!string.IsNullOrEmpty(authorizeInfo.EndTime) && System.DateTime.Now >= Convert.ToDateTime(authorizeInfo.EndTime))
            {
                throw Oops.Oh("授权已过期");
            }

            // 缓存授权信息
            _sysCacheService.Set(CACHE_KEY + authorizeInfo.Sn, authorizeInfo);

            // 保存授权信息到文件
            await SaveAuthorizeInfoToFile(authorizeInfo);

            return authorizeInfo;
        }
        catch (Exception ex)
        {
            throw Oops.Oh($"授权验证失败:{ex.Message}");
        }
    }

    /// <summary>
    ///     获取授权信息
    /// </summary>
    /// <returns></returns>
    [HttpGet("/authorize/info")]
    public async Task<dynamic> GetAuthorizeInfo()
    {
        string fingerprint = await GetMachineFingerprint();
        var authorizeInfo = _sysCacheService.Get<SetAuthorizeOutput>(CACHE_KEY + fingerprint);

        // 如果缓存中不存在授权信息，则尝试从文件加载
        if (authorizeInfo == null)
        {
            authorizeInfo = await LoadAuthorizeInfoFromFile(fingerprint);

            // 如果从文件成功加载到授权信息，则更新缓存
            if (authorizeInfo != null)
            {
                _sysCacheService.Set(CACHE_KEY + fingerprint, authorizeInfo);
            }
        }

        // 如果缓存和文件中都不存在授权信息，则创建新的空授权信息
        if (authorizeInfo == null)
        {
            authorizeInfo = new SetAuthorizeOutput
            {
                Sn = fingerprint,
                Version = StringExtension.Version
            };
        }

        // 获取设备数量
        int deviceCount = await _sqlSugarClient.Queryable<ModelThing>().Where(x => x.ModelType == ModelTypeEnum.Device).CountAsync();

        // 获取点位数量
        int pointCount = await _sqlSugarClient.Queryable<ModelAttributes>().CountAsync();

        return new
        {
            authorizeInfo.Sn,
            authorizeInfo.Version,
            authorizeInfo.EndTime,
            authorizeInfo.DeviceNumber,
            authorizeInfo.VariableNumber,
            authorizeInfo.Status,
            UsedDeviceNumber = deviceCount,
            UsedVariableNumber = pointCount
        };
    }

    #region Private Methods

    /// <summary>
    ///     获取机器指纹
    /// </summary>
    /// <returns></returns>
    private async Task<string> GetMachineFingerprint()
    {
        // 获取当前程序目录
        string machineCodePath = Path.Combine(_hostEnvironment.ContentRootPath, MACHINE_CODE_FILE);

        // 如果文件存在则读取
        if (System.IO.File.Exists(machineCodePath))
        {
            return await System.IO.File.ReadAllTextAsync(machineCodePath);
        }
        // 执行shell命令获取机器码
        string machineCode;
        if (System.Runtime.InteropServices.RuntimeInformation.IsOSPlatform(System.Runtime.InteropServices.OSPlatform.Linux))
        {
            machineCode = (await ExecuteShellCommand("hogodevinfotools sn -r")).Replace("read the sn : ", "").Trim();
            if (string.IsNullOrEmpty(machineCode))
            {
                machineCode = Guid.NewGuid().ToString("N").ToUpper().Substring(0, 16);
            }
        }
        else
        {
            machineCode = Guid.NewGuid().ToString("N").ToUpper().Substring(0, 16);
        }
        try
        {
            // 保存机器码
            await System.IO.File.WriteAllTextAsync(machineCodePath, machineCode);
            return machineCode;
        }
        catch (Exception ex)
        {
            throw Oops.Oh($"无法保存机器码:{ex.Message}");
        }
    }

    /// <summary>
    ///     RSA解密
    /// </summary>
    private async Task<string> RSADecrypt(string encryptedText)
    {
        // 读取当前程序目录下的RSA.Private文件
        string privateKeyPath = Path.Combine(_hostEnvironment.ContentRootPath, "RSA.Private");
        if (!System.IO.File.Exists(privateKeyPath))
        {
            throw Oops.Oh("未找到RSA私钥文件");
        }
        // 读取RSA.Private文件内容
        var privateKey = await System.IO.File.ReadAllTextAsync(privateKeyPath);
        if (string.IsNullOrEmpty(privateKey))
        {
            throw Oops.Oh("RSA私钥文件内容为空");
        }

        using var rsa = new RSACryptoServiceProvider(2048);
        try
        {
            rsa.FromXmlString(privateKey);
            var bytesEncrypted = Convert.FromBase64String(encryptedText);
            var bytesPlainText = rsa.Decrypt(bytesEncrypted, false);
            return Encoding.Unicode.GetString(bytesPlainText);
        }
        finally
        {
            rsa.PersistKeyInCsp = false;
        }
    }


    /// <summary>
    ///     执行shell命令
    /// </summary>
    /// <param name="command">命令</param>
    /// <returns></returns>
    private async Task<string> ExecuteShellCommand(string command)
    {
        var escapedArgs = command.Replace("\"", "\\\"");
        var process = new Process
        {
            StartInfo = new ProcessStartInfo
            {
                FileName = "/bin/bash",
                Arguments = $"-c \"{escapedArgs}\"",
                RedirectStandardOutput = true,
                UseShellExecute = false,
                CreateNoWindow = true
            }
        };
        process.Start();
        var result = await process.StandardOutput.ReadToEndAsync();
        await process.WaitForExitAsync();
        process.Dispose();
        return result;
    }

    /// <summary>
    ///   获取授权信息存储目录
    /// </summary>
    /// <returns>授权信息存储完整路径</returns>
    private string GetAuthorizeDirectory()
    {
        string directoryPath = Path.Combine(Common.Configuration.FileVariable.SystemFilePath, AUTHORIZE_DIRECTORY);
        if (!Directory.Exists(directoryPath))
        {
            Directory.CreateDirectory(directoryPath);
        }
        return directoryPath;
    }

    /// <summary>
    ///   保存授权信息到文件
    /// </summary>
    /// <param name="authorizeInfo">授权信息</param>
    /// <returns>异步任务</returns>
    private async Task SaveAuthorizeInfoToFile(SetAuthorizeOutput authorizeInfo)
    {
        if (authorizeInfo == null || string.IsNullOrEmpty(authorizeInfo.Sn))
        {
            return;
        }

        try
        {
            string directoryPath = GetAuthorizeDirectory();
            string filePath = Path.Combine(directoryPath, authorizeInfo.Sn + AUTHORIZE_FILE_SUFFIX);
            string jsonContent = JsonConvert.SerializeObject(authorizeInfo, Formatting.Indented);
            await System.IO.File.WriteAllTextAsync(filePath, jsonContent);
        }
        catch (Exception ex)
        {
            // 记录异常但不抛出，避免影响主流程
            Console.WriteLine($"保存授权信息到文件失败: {ex.Message}");
        }
    }

    /// <summary>
    ///   从文件加载授权信息
    /// </summary>
    /// <param name="machineCode">机器码</param>
    /// <returns>授权信息，如果文件不存在或加载失败则返回null</returns>
    private async Task<SetAuthorizeOutput> LoadAuthorizeInfoFromFile(string machineCode)
    {
        if (string.IsNullOrEmpty(machineCode))
        {
            return null;
        }

        try
        {
            string directoryPath = GetAuthorizeDirectory();
            string filePath = Path.Combine(directoryPath, machineCode + AUTHORIZE_FILE_SUFFIX);

            if (!System.IO.File.Exists(filePath))
            {
                return null;
            }

            string jsonContent = await System.IO.File.ReadAllTextAsync(filePath);
            return JsonConvert.DeserializeObject<SetAuthorizeOutput>(jsonContent);
        }
        catch (Exception ex)
        {
            // 记录异常但不抛出，返回null表示加载失败
            Console.WriteLine($"从文件加载授权信息失败: {ex.Message}");
            return null;
        }
    }

    /// <summary>
    ///   初始化授权信息
    /// </summary>
    /// <returns>异步任务</returns>
    private async Task InitializeAuthorizeInfo()
    {
        string fingerprint = await GetMachineFingerprint();
        var authorizeInfo = _sysCacheService.Get<SetAuthorizeOutput>(CACHE_KEY + fingerprint);

        // 如果缓存中不存在授权信息，则尝试从文件加载
        if (authorizeInfo == null)
        {
            authorizeInfo = await LoadAuthorizeInfoFromFile(fingerprint);

            // 如果从文件成功加载到授权信息，则更新缓存
            if (authorizeInfo != null)
            {
                _sysCacheService.Set(CACHE_KEY + fingerprint, authorizeInfo);
            }
        }
    }
    #endregion
}