<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <PropertyGroup Condition=" '$(Configuration)' == 'Debug' ">
      <DocumentationFile>bin\Debug\Systems.Core.xml</DocumentationFile>
    </PropertyGroup>

    <PropertyGroup Condition=" '$(Configuration)' == 'Release' ">
      <DebugSymbols>true</DebugSymbols>
      <DebugType>none</DebugType>
    </PropertyGroup>

    <ItemGroup>
        <ProjectReference Include="..\..\..\01-架构核心\Extras.Thridparty\Extras.Thridparty.csproj" />
        <ProjectReference Include="..\..\00-Common\Common.Core\Common.Core.csproj"/>
        <ProjectReference Include="..\..\04-DataWeaving\IotPlatform.DataWeaving\IotPlatform.DataWeaving.csproj" />
        <ProjectReference Include="..\..\09-Engine\JsScript.Engine\JsScript.Engine.csproj" />
        <ProjectReference Include="..\Systems.Entity\Systems.Entity.csproj"/>
        <ProjectReference Include="..\Systems.Interface\Systems.Interface.csproj" />
    </ItemGroup>

    <ItemGroup>
        <None Remove="Systems.Core.csproj.DotSettings"/>
    </ItemGroup>

    <ItemGroup>
      <Folder Include="System\DataSet\Handlers\" />
    </ItemGroup>

</Project>
