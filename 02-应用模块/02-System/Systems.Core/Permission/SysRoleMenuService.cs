namespace Systems.Core.Permission;

/// <summary>
///     系统角色菜单服务
/// </summary>
public class SysRoleMenuService : ITransient
{
    private readonly ISqlSugarRepository<SysRoleMenu> _sysRoleMenuRep;

    public SysRoleMenuService(ISqlSugarRepository<SysRoleMenu> sysRoleMenuRep)
    {
        _sysRoleMenuRep = sysRoleMenuRep;
    }

    /// <summary>
    ///     根据角色Id集合获取菜单Id集合
    /// </summary>
    /// <param name="roleIdList"></param>
    /// <returns></returns>
    public async Task<List<long>> GetRoleMenuIdList(List<long> roleIdList)
    {
        return await _sysRoleMenuRep.AsQueryable()
            .Where(u => roleIdList.Contains(u.RoleId))
            .Select(u => u.MenuId).ToListAsync();
    }

    /// <summary>
    ///     授权角色菜单
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [UnitOfWork]
    public async Task GrantRoleMenu(RoleMenuInput input)
    {
        await _sysRoleMenuRep.DeleteAsync(u => u.RoleId == input.Id);
        List<SysRoleMenu> menus = input.GrantMenuIdList.Select(u => new SysRoleMenu
        {
            RoleId = input.Id,
            MenuId = u
        }).ToList();
        await _sysRoleMenuRep.InsertRangeAsync(menus);
    }

    /// <summary>
    ///     根据菜单Id集合删除角色菜单
    /// </summary>
    /// <param name="menuIdList"></param>
    /// <returns></returns>
    public async Task DeleteRoleMenuByMenuIdList(List<long> menuIdList)
    {
        await _sysRoleMenuRep.DeleteAsync(u => menuIdList.Contains(u.MenuId));
    }

    /// <summary>
    ///     根据角色Id删除角色菜单
    /// </summary>
    /// <param name="roleId"></param>
    /// <returns></returns>
    public async Task DeleteRoleMenuByRoleId(long roleId)
    {
        await _sysRoleMenuRep.DeleteAsync(u => u.RoleId == roleId);
    }
}