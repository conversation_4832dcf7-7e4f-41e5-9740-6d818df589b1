<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <ItemGroup>
        <ProjectReference Include="..\..\..\01-架构核心\Extras.DatabaseAccessor.SqlSugar\Extras.DatabaseAccessor.SqlSugar.csproj"/>
        <ProjectReference Include="..\..\..\01-架构核心\IotPlatform.Core\IotPlatform.Core.csproj"/>
        <ProjectReference Include="..\..\00-Common\Common\Common.csproj"/>
        <ProjectReference Include="..\..\09-Engine\Engine.Entity\Engine.Entity.csproj" />
    </ItemGroup>

    <ItemGroup>
        <None Remove="Systems.Entity.csproj.DotSettings"/>
    </ItemGroup>
    
    <PropertyGroup Condition=" '$(Configuration)' == 'Release' ">
        <DebugSymbols>true</DebugSymbols>
        <DebugType>none</DebugType>
    </PropertyGroup>

    <ItemGroup>
        <Folder Include="Model\System\"/>
    </ItemGroup>

</Project>
