using Common.Dto;
using Common.Models;
using Furion.DependencyInjection;

namespace Systems.Entity.Dto;

/// <summary>
///     生成数据库
/// </summary>
public class CreateConnectStrInput
{
    /// <summary>
    ///     连接名称
    /// </summary>
    [Required]
    public string FullName { get; set; }

    /// <summary>
    ///     数据库名
    /// </summary>
    [Required]
    public string DataBaseName { get; set; }

    /// <summary>
    ///     用户名
    /// </summary>
    [Required]
    public string UserName { get; set; }

    /// <summary>
    ///     端口
    /// </summary>
    [Required]
    public int Port { get; set; }

    /// <summary>
    ///     数据库驱动类型
    /// </summary>
    [Required]
    public DbTypeEnum DbType { get; set; }

    /// <summary>
    ///     主机地址
    /// </summary>
    [Required]
    public string Host { get; set; }

    /// <summary>
    ///     密码
    /// </summary>
    [Required]
    public string Password { get; set; }

    /// <summary>
    ///     版本号
    /// </summary>
    [Required]
    public string Version { get; set; }
}

/// <summary>
/// </summary>
[SuppressSniffer]
public class DbLinkCrInput
{
    /// <summary>
    ///     连接名称
    /// </summary>
    [Required]
    public string FullName { get; set; }

    /// <summary>
    ///     数据库名
    /// </summary>
    [Required]
    public string DataBaseName { get; set; }

    /// <summary>
    ///     用户名
    /// </summary>
    [Required]
    public string UserName { get; set; }

    /// <summary>
    ///     端口
    /// </summary>
    [Required]
    public int Port { get; set; }

    /// <summary>
    ///     数据库驱动类型
    /// </summary>
    [Required]
    public DbTypeEnum DbType { get; set; }

    /// <summary>
    ///     主机地址
    /// </summary>
    [Required]
    public string Host { get; set; }

    /// <summary>
    ///     密码
    /// </summary>
    [Required]
    public string Password { get; set; }

    /// <summary>
    ///     排序
    /// </summary>
    public long? SortCode { get; set; }

    /// <summary>
    /// 模式
    /// </summary>
    public string?  DBSchema { get; set; }
    
    /// <summary>
    ///     版本号
    /// </summary>
    [Required]
    public string Version { get; set; }

    /// <summary>
    ///     连接字符串
    /// </summary>
    public string ConnectString { get; set; }
}

/// <summary>
///     表字段
/// </summary>
public class GetFieldListInput
{
    /// <summary>
    ///     Id
    /// </summary>
    [Required]
    public long Id { get; set; }

    /// <summary>
    ///     表名称
    /// </summary>
    [Required]
    public string Name { get; set; }
}

/// <summary>
///     预览数据
/// </summary>
public class PreviewDataInput : BasePageInput
{
}

/// <summary>
///     动态查询sql
/// </summary>
public class DynamicQueryInput
{
    /// <summary>
    ///     Id
    /// </summary>
    [Required]
    public long Id { get; set; }

    /// <summary>
    ///     执行语句
    /// </summary>
    [Required]
    public string Sql { get; set; }
}

/// <summary>
///     数据动态查询
/// </summary>
public class ScreenDataSourceDynamicQueryInput
{
    /// <summary>
    /// </summary>
    [Required]
    public long Id { get; set; }

    /// <summary>
    /// </summary>
    [Required]
    public string Sql { get; set; }

}