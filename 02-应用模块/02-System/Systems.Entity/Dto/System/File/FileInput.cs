namespace Systems.Entity.Dto;

/// <summary>
/// </summary>
public class FileInput : BaseId
{
    /// <summary>
    ///     文件名称
    /// </summary>
    public string FileName { get; set; }

    /// <summary>
    ///     文件Url
    /// </summary>
    public string? Url { get; set; }
}

/// <summary>
/// 
/// </summary>
public class GetTextContentInput
{
    /// <summary>
    ///  文件名称
    /// </summary>
    public string FileName { get; set; }
    
    /// <summary>
    /// 编码格式
    /// </summary>
    public string Encoding { get; set; }
}

/// <summary>
/// 
/// </summary>
public class SaveTextContentInput
{
    /// <summary>
    ///  文件名称
    /// </summary>
    public string FileName { get; set; }
    
    /// <summary>
    /// 内容
    /// </summary>
    public string Content { get; set; }
}

/// <summary>
/// 文件预览
/// </summary>
public class FilePreviewInput
{
    /// <summary>
    /// 文件名称
    /// </summary>
    [Required]
    public string fileName { get; set; }
    
    /// <summary>
    /// 文件下载地址
    /// </summary>
    [Required]
    public string fileDownloadUrl { get; set; }
}

/// <summary>
/// </summary>
public class PageFileInput : BasePageInput
{
    /// <summary>
    ///     文件名称
    /// </summary>
    public string FileName { get; set; }

    /// <summary>
    ///     开始时间
    /// </summary>
    public DateTime? StartTime { get; set; }

    /// <summary>
    ///     结束时间
    /// </summary>
    public DateTime? EndTime { get; set; }
}

/// <summary>
/// </summary>
public class DeleteFileInput : BaseId
{
}

/// <summary>
/// </summary>
public class UploadFileFromBase64Input
{
    /// <summary>
    ///     文件内容
    /// </summary>
    public string FileDataBase64 { get; set; }

    /// <summary>
    ///     文件类型( "image/jpeg",)
    /// </summary>
    public string ContentType { get; set; }

    /// <summary>
    ///     文件名称
    /// </summary>
    public string FileName { get; set; }

    /// <summary>
    ///     保存路径
    /// </summary>
    public string Path { get; set; }
}