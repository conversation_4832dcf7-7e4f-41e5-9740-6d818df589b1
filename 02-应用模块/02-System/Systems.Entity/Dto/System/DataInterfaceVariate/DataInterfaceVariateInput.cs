namespace Systems.Entity.Dto.DataInterfaceVariate;

public class DataInterfaceVariateInput
{
    /// <summary>
    /// id.
    /// </summary>
    public long id { get; set; }

    /// <summary>
    /// 数据接口id.
    /// </summary>
    public long interfaceId { get; set; }

    /// <summary>
    /// 变量名.
    /// </summary>
    public string fullName { get; set; }

    /// <summary>
    /// 表达式.
    /// </summary>
    public string expression { get; set; }
}
