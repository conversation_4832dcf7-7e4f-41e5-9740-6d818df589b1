namespace Systems.Entity.Dto.DataInterfaceVariate;

public class DataInterfaceVariateOutput
{
    /// <summary>
    /// id.
    /// </summary>
    public long id { get; set; }

    /// <summary>
    /// 数据接口id.
    /// </summary>
    public long interfaceId { get; set; }

    /// <summary>
    /// 变量名.
    /// </summary>
    public string fullName { get; set; }

    /// <summary>
    /// 表达式.
    /// </summary>
    public string expression { get; set; }

    /// <summary>
    /// 变量值.
    /// </summary>
    public string value { get; set; }

    /// <summary>
    /// 创建人.
    /// </summary>
    public string creatorUser { get; set; }

    /// <summary>
    /// 创建时间.
    /// </summary>
    public DateTime? creatorTime { get; set; }

    /// <summary>
    /// 修改时间.
    /// </summary>
    public DateTime? lastModifyTime { get; set; }
}
