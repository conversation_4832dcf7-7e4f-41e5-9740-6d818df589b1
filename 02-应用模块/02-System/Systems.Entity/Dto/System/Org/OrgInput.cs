using Common.Models;

namespace Systems.Entity.Dto.Org;

/// <summary>
///     组织机构参数
/// </summary>
public class OrgListInput
{
    /// <summary>
    ///     父Id
    /// </summary>
    public long Pid { get; set; }
}

/// <summary>
/// </summary>
public class OrgAddInput
{
    /// <summary>
    ///     名称
    /// </summary>
    [Required(ErrorMessage = "机构名称不能为空")]
    public string Name { get; set; }

    /// <summary>
    ///     编码
    /// </summary>
    [Required(ErrorMessage = "机构编码不能为空")]
    public string Code { get; set; }

    /// <summary>
    ///     父Id
    /// </summary>
    public long Pid { get; set; }

    /// <summary>
    ///     父Ids
    /// </summary>
    public string Pids { get; set; }

    /// <summary>
    ///     电话
    /// </summary>
    public string Tel { get; set; }

    /// <summary>
    ///     排序
    /// </summary>
    public int Sort { get; set; }

    /// <summary>
    ///     备注
    /// </summary>
    public string Remark { get; set; }

    /// <summary>
    /// 机构分类【company-公司、department-部门】.
    /// </summary>
    [SugarColumn(ColumnName = "F_CATEGORY")]
    public string Category { get; set; }
    
    /// <summary>
    ///     状态
    /// </summary>
    public bool Status { get; set; }
}

/// <summary>
/// </summary>
public class DeleteOrgInput
{
    /// <summary>
    ///     机构Id
    /// </summary>
    [Required(ErrorMessage = "机构Id不能为空")]
    public long Id { get; set; }
}

/// <summary>
/// </summary>
public class UpdateOrgInput
{
    /// <summary>
    ///     机构Id
    /// </summary>
    [Required(ErrorMessage = "机构Id不能为空")]
    public long Id { get; set; }

    /// <summary>
    ///     名称
    /// </summary>
    [Required(ErrorMessage = "机构名称不能为空")]
    public string Name { get; set; }

    /// <summary>
    ///     编码
    /// </summary>
    [Required(ErrorMessage = "机构编码不能为空")]
    public string Code { get; set; }

    /// <summary>
    ///     父Id
    /// </summary>
    public long Pid { get; set; }

    /// <summary>
    ///     排序
    /// </summary>
    public int Sort { get; set; }

    /// <summary>
    ///     备注
    /// </summary>
    public string Remark { get; set; }
}

/// <summary>
/// </summary>
public class QueryOrgInput
{
    /// <summary>
    ///     机构Id
    /// </summary>
    [Required(ErrorMessage = "机构Id不能为空")]
    public long Id { get; set; }
}

/// <summary>
/// </summary>
public class OrgPageInput : BasePageInput
{
    /// <summary>
    /// </summary>
    public long Id { get; set; }
}