using Common.Models;

namespace Systems.Entity.Dto.Role;

public class RoleInput : BaseId
{
    /// <summary>
    ///     状态
    /// </summary>
    public virtual bool Status { get; set; }
}

public class PageRoleInput : BasePageInput
{
    /// <summary>
    ///     名称
    /// </summary>
    public virtual string Name { get; set; }

    /// <summary>
    ///     编码
    /// </summary>
    public virtual string Code { get; set; }
}

public class AddRoleInput : SysRole
{
    /// <summary>
    ///     名称
    /// </summary>
    [Required(ErrorMessage = "角色名称不能为空")]
    public override string Name { get; set; }
}

public class UpdateRoleInput : AddRoleInput
{
}

public class DeleteRoleInput : BaseId
{
}