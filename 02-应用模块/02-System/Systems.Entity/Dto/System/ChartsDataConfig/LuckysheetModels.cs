using Microsoft.AspNetCore.Http;
using System.Collections.Generic;
using Newtonsoft.Json;

namespace Systems.Entity.Dto;

/// <summary>
/// 文件解析为Luckysheet输入模型
/// </summary>
public class ParseFileToLuckysheetInput
{
    /// <summary>
    /// 上传的文件
    /// </summary>
    public IFormFile File { get; set; }

    /// <summary>
    /// 文件类型
    /// </summary>
    public string Type { get; set; }
}

/// <summary>
/// Luckysheet工作表模型
/// </summary>
public class LuckysheetModel
{
    /// <summary>
    /// 工作表名称
    /// </summary>
    [JsonProperty("name")]
    public string Name { get; set; }

    /// <summary>
    /// 工作表状态
    /// </summary>
    [JsonProperty("status")]
    public int Status { get; set; } = 1;

    /// <summary>
    /// 工作表排序
    /// </summary>
    [JsonProperty("order")]
    public int Order { get; set; } = 0;

    /// <summary>
    /// 工作表索引
    /// </summary>
    [JsonProperty("index")]
    public int Index { get; set; } = 0;

    /// <summary>
    /// 单元格数据
    /// </summary>
    [JsonProperty("data")]
    public List<List<object>> Data { get; set; } = new List<List<object>>();
}