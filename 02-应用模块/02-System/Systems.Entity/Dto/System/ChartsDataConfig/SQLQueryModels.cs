using System.Collections.Generic;

namespace Systems.Entity.Dto;

/// <summary>
/// SQL查询请求输入模型
/// </summary>
public class SQLQueryInput
{
    /// <summary>
    /// 数据库连接ID
    /// </summary>
    public string Id { get; set; }

    /// <summary>
    /// SQL语句
    /// </summary>
    public string Sql { get; set; }

    /// <summary>
    /// 参数配置
    /// </summary>
    public Dictionary<string, object> ParamsConfig { get; set; }
}

/// <summary>
/// SQL查询响应输出模型
/// </summary>
public class SQLQueryOutput
{
    /// <summary>
    /// 列名列表
    /// </summary>
    public List<string> Columns { get; set; } = new List<string>();

    /// <summary>
    /// 数据行
    /// </summary>
    public List<Dictionary<string, object>> Data { get; set; } = new List<Dictionary<string, object>>();
}