using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json.Linq;

namespace Systems.Entity.Dto;



/// <summary>
///     创建数据集基本信息输入
/// </summary>
[SuppressSniffer]
public class ChartsDataCreateInput
{
    /// <summary>
    ///     Id
    /// </summary>
    public string Id { get; set; }

    /// <summary>
    ///     名称
    /// </summary>
    [Required(ErrorMessage = "名称不能为空")]
    public string Name { get; set; }

    /// <summary>
    ///     分组Id
    /// </summary>
    [Required(ErrorMessage = "分组不能为空")]
    public string GroupId { get; set; }

    /// <summary>
    ///     类型
    /// </summary>
    [Required(ErrorMessage = "类型不能为空")]
    public string Type { get; set; }

    /// <summary>
    ///     排序码
    /// </summary>
    public int SortCode { get; set; } = 0;
}

/// <summary>
///     保存数据集配置输入
/// </summary>
[SuppressSniffer]
public class ChartsDataConfigInput
{
    /// <summary>
    ///     数据集Id
    /// </summary>
    [Required(ErrorMessage = "数据集ID不能为空")]
    public string Id { get; set; }

    /// <summary>
    ///     配置内容
    /// </summary>
    public Dictionary<string, object>? Content { get; set; }

    /// <summary>
    ///     字段配置
    /// </summary>
    public List<FieldItem>? Fields { get; set; }

    /// <summary>
    ///     参数配置
    /// </summary>
    public Dictionary<string, object>? Params { get; set; }
}

/// <summary>
///     保存数据集分组输入
/// </summary>
[SuppressSniffer]
public class ChartsDataGroupSaveInput
{
    /// <summary>
    ///     Id
    /// </summary>
    public string Id { get; set; }

    /// <summary>
    ///     名称
    /// </summary>
    [Required(ErrorMessage = "名称不能为空")]
    public string Name { get; set; }

    /// <summary>
    ///     父级Id
    /// </summary>
    public string ParentId { get; set; } = "ROOT";

    /// <summary>
    ///     排序码
    /// </summary>
    public int SortCode { get; set; } = 0;
}
