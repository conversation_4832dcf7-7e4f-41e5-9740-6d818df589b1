using Furion.DataEncryption;
using IotPlatform.Core.Enum;

namespace Systems.Entity.SeedData;

/// <summary>
///     系统用户表种子数据
/// </summary>
public class SysUserSeedData : ISqlSugarEntitySeedData<SysUser>
{
    /// <summary>
    ///     种子数据
    /// </summary>
    /// <returns></returns>
    public IEnumerable<SysUser> HasData()
    {
        string encryptPassword = MD5Encryption.Encrypt("123456");

        return new[]
        {
            new SysUser
            {
                Id = *************, Account = "superAdmin", Password = "c1f8ef9657da79c108ef2376028ff2ab", NickName = "超级管理员", Name = "超级管理员", Phone = "***********", Sex = GenderEnum.Male,
                AdminType = AdminTypeEnum.SuperAdmin, Remark = "超级管理员", CreatedTime = DateTime.Parse("2022-02-10 00:00:00"), OrgId = **************, TenantId = *************
            },
            new SysUser
            {
                Id = *************, Account = "admin", Password = encryptPassword, NickName = "系统管理员", Name = "系统管理员", Phone = "***********", Sex = GenderEnum.Male,
                AdminType = AdminTypeEnum.Admin, Remark = "系统管理员", CreatedTime = DateTime.Parse("2022-02-10 00:00:00"), OrgId = **************, TenantId = *************
            }
        };
    }
}