namespace Systems.Entity.SeedData;

/// <summary>
/// </summary>
public class SysOrgSeedData : ISqlSugarEntitySeedData<SysOrg>
{
    /// <summary>
    ///     种子数据
    /// </summary>
    /// <returns></returns>
    public IEnumerable<SysOrg> HasData()
    {
        yield return new SysOrg { Id = 31519576587015, Pid = 0, OrganizeIdTree = "0", Name = "杭州峰回科技有限公司", Code = "hzfh", Sort = 100, Remark = "", Status = true, TenantId = 1300000000001 };
    }
}