using IotPlatform.Core;
using IotPlatform.Core.Attribute;
using IotPlatform.Core.Enum;
using Newtonsoft.Json;

namespace Systems.Entity;

/// <summary>
///     系统用户表
/// </summary>
[SugarTable(null, "系统用户表")]
[SysTable]
public class SysUser : EntityTenant
{
    /// <summary>
    ///     账号
    /// </summary>
    [SugarColumn(ColumnDescription = "账号", Length = 32)]
    [Required]
    [MaxLength(32)]
    public virtual string Account { get; set; }

    /// <summary>
    ///     密码
    /// </summary>
    [SugarColumn(ColumnDescription = "密码", Length = 512)]
    [JsonIgnore]
    [System.Text.Json.Serialization.JsonIgnore]
    public virtual string Password { get; set; }

    /// <summary>
    ///     真实姓名
    /// </summary>
    [SugarColumn(ColumnDescription = "真实姓名", Length = 32)]
    [MaxLength(32)]
    public virtual string Name { get; set; }

    /// <summary>
    ///     昵称
    /// </summary>
    [SugarColumn(ColumnDescription = "昵称", Length = 32)]
    [MaxLength(32)]
    public string? NickName { get; set; }

    /// <summary>
    ///     头像
    /// </summary>
    [SugarColumn(ColumnDescription = "头像", Length = 512)]
    [MaxLength(512)]
    public string? Avatar { get; set; }

    /// <summary>
    ///     性别-男_1、女_2
    /// </summary>
    [SugarColumn(ColumnDescription = "性别")]
    public GenderEnum Sex { get; set; } = GenderEnum.Male;

    /// <summary>
    ///     手机号码
    /// </summary>
    [SugarColumn(ColumnDescription = "手机号码", Length = 16)]
    [MaxLength(16)]
    public string? Phone { get; set; }

    /// <summary>
    ///     状态
    /// </summary>
    [SugarColumn(ColumnDescription = "状态")]
    public bool Enable { get; set; } = true;

    /// <summary>
    ///     备注
    /// </summary>
    [SugarColumn(ColumnDescription = "备注", Length = 256)]
    [MaxLength(256)]
    public string? Remark { get; set; }

    /// <summary>
    ///     账号类型
    /// </summary>
    [SugarColumn(ColumnDescription = "账号类型")]
    public AdminTypeEnum AdminType { get; set; } = AdminTypeEnum.None;

    /// <summary>
    ///     直属机构Id
    /// </summary>
    [SugarColumn(ColumnDescription = "直属机构Id")]
    public long OrgId { get; set; }

    /// <summary>
    ///     直属机构
    /// </summary>
    [Navigate(NavigateType.OneToOne, nameof(OrgId))]
    public SysOrg SysOrg { get; set; }

    /// <summary>
    ///     工号
    /// </summary>
    [SugarColumn(ColumnDescription = "工号", Length = 32)]
    [MaxLength(32)]
    public string? JobNum { get; set; }

    /// <summary>
    ///     最新登录Ip
    /// </summary>
    [SugarColumn(ColumnDescription = "最新登录Ip", Length = 256)]
    [MaxLength(256)]
    public string? LastLoginIp { get; set; }

    /// <summary>
    ///     最新登录地点
    /// </summary>
    [SugarColumn(ColumnDescription = "最新登录地点", Length = 128)]
    [MaxLength(128)]
    public string? LastLoginAddress { get; set; }

    /// <summary>
    ///     最新登录时间
    /// </summary>
    [SugarColumn(ColumnDescription = "最新登录时间")]
    public System.DateTime? LastLoginTime { get; set; }

    /// <summary>
    ///     最新登录设备
    /// </summary>
    [SugarColumn(ColumnDescription = "最新登录设备", Length = 128)]
    [MaxLength(128)]
    public string? LastLoginDevice { get; set; }
}