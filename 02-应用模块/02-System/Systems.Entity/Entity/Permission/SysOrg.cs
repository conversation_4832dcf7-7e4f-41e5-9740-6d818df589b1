using IotPlatform.Core.Attribute;

namespace Systems.Entity;

/// <summary>
///     系统机构表
/// </summary>
[SugarTable(null, "系统机构表")]
[SysTable]
public class SysOrg : EntityTenant
{
    /// <summary>
    ///     父Id
    /// </summary>
    [SugarColumn(ColumnDescription = "父Id")]
    public long Pid { get; set; }

    /// <summary>
    /// 父级组织.
    /// </summary>
    [SugarColumn(ColumnName = "F_ORGANIZE_ID_TREE")]
    public string? OrganizeIdTree { get; set; }
    
    /// <summary>
    ///     名称
    /// </summary>
    [SugarColumn(ColumnDescription = " 名称", Length = 100)]
    [Required]
    [MaxLength(100)]
    public string Name { get; set; }

    /// <summary>
    ///     编码
    /// </summary>
    [SugarColumn(ColumnDescription = " 编码")]
    [Required]
    [MaxLength(100)]
    public string Code { get; set; }

    /// <summary>
    ///     联系人
    /// </summary>
    [SugarColumn(ColumnDescription = "联系人", Length = 20, IsNullable = true)]
    [MaxLength(20)]
    public string Contacts { get; set; }

    /// <summary>
    ///     电话
    /// </summary>
    [SugarColumn(ColumnDescription = "电话", Length = 20, IsNullable = true)]
    [MaxLength(20)]
    public string Tel { get; set; }

    /// <summary>
    ///     排序
    /// </summary>
    [SugarColumn(ColumnDescription = "排序")]
    public int Sort { get; set; }

    /// <summary>
    ///     备注
    /// </summary>
    [SugarColumn(ColumnDescription = " 备注", Length = 500, IsNullable = true)]
    [MaxLength(500)]
    public string Remark { get; set; }

    /// <summary>
    ///     启用状态
    /// </summary>
    [SugarColumn(ColumnDescription = " 启用状态")]
    public bool Status { get; set; } = true;

    /// <summary>
    /// 机构分类【company-公司、department-部门】.
    /// </summary>
    [SugarColumn(ColumnName = "F_CATEGORY")]
    public string? Category { get; set; }
    
    /// <summary>
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public List<SysOrg> Children { get; set; }
}