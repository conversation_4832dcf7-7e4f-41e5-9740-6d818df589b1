using IotPlatform.Core;
using IotPlatform.Core.Attribute;

namespace Systems.Entity;

/// <summary>
/// 系统用户角色表
/// </summary>
[SugarTable(null, "系统用户角色表")]
[SysTable]
public class SysUserRole : Systems.Entity.EntityBaseId
{
    /// <summary>
    /// 用户Id
    /// </summary>
    [SugarColumn(ColumnDescription = "用户Id")]
    public long SysUserId { get; set; }

    /// <summary>
    /// 用户
    /// </summary>
    [Navigate(NavigateType.OneToOne, nameof(SysUserId))]
    public SysUser SysUser { get; set; }

    /// <summary>
    /// 角色Id
    /// </summary>
    [SugarColumn(ColumnDescription = "角色Id")]
    public long SysRoleId { get; set; }

    /// <summary>
    /// 角色
    /// </summary>
    [Navigate(NavigateType.OneToOne, nameof(SysRoleId))]
    public Systems.Entity.SysRole SysRole { get; set; }
}