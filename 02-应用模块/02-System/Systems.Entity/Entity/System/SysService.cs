namespace Systems.Entity;

/// <summary>
///     系统应用表
/// </summary>
[SugarTable("system_service", "系统应用表")]
public class SysService : EntityTenant
{
    /// <summary>
    ///     名称
    /// </summary>
    [SugarColumn(ColumnDescription = "名称", Length = 50)]
    public string Name { get; set; }

    /// <summary>
    ///     编码
    /// </summary>
    [SugarColumn(ColumnDescription = "编码", Length = 50)]
    public string Code { get; set; }

    /// <summary>
    ///     是否默认激活（Y-是，N-否）,只能有一个系统默认激活
    ///     用户登录后默认展示此系统菜单
    /// </summary>
    [SugarColumn(ColumnDescription = "是否默认激活",IsNullable = true)]
    public string? Active { get; set; }

    /// <summary>
    ///     启用状态
    /// </summary>
    [SugarColumn(ColumnDescription = "启用状态")]
    public bool Status { get; set; }

    /// <summary>
    ///     排序
    /// </summary>
    [SugarColumn(ColumnDescription = "排序")]
    public int Sort { get; set; }

    /// <summary>
    ///     打开方式：1：在新标签中打开；2：在门户内打开
    /// </summary>
    [SugarColumn(ColumnDescription = "打开方式：1：在新标签中打开；2：在门户内打开")]
    public string OpenType { get; set; }

    /// <summary>
    ///     描述
    /// </summary>
    [SugarColumn(ColumnDescription = "描述", ColumnDataType = "longtext,text,clob",IsNullable = true)]
    public string Remark { get; set; }
}

/// <summary>
///     是否枚举
/// </summary>
public enum YesNoEnum
{
    /// <summary>
    ///     是
    /// </summary>
    [Description("是")] Y = 1,

    /// <summary>
    ///     否
    /// </summary>
    [Description("否")] N = 2
}