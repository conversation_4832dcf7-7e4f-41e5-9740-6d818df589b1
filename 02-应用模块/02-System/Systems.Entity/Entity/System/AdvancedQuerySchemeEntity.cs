namespace Systems.Entity;

/// <summary>
///     高级查询方案
/// </summary>
[SugarTable("BASE_ADVANCED_QUERY_SCHEME")]
public class AdvancedQuerySchemeEntity : EntityTenant
{
    /// <summary>
    ///     方案名称.
    /// </summary>
    [SugarColumn(ColumnName = "F_FULL_NAME")]
    public string FullName { get; set; }

    /// <summary>
    ///     匹配逻辑.
    /// </summary>
    [SugarColumn(ColumnName = "F_MATCH_LOGIC", ColumnDataType = StaticConfig.CodeFirst_BigString, IsNullable = true)]
    public string MatchLogic { get; set; }

    /// <summary>
    ///     条件规则Json.
    /// </summary>
    [SugarColumn(ColumnName = "F_CONDITION_JSON", ColumnDataType = StaticConfig.CodeFirst_BigString, IsNullable = true)]
    public string ConditionJson { get; set; }

    /// <summary>
    ///     菜单主键.
    /// </summary>
    [SugarColumn(ColumnName = "F_MODULE_ID")]
    public long ModuleId { get; set; }
}