using IotPlatform.Core;
using IotPlatform.Core.Attribute;

namespace Systems.Entity;

/// <summary>
/// 打印模板
/// </summary>
[SugarTable("business_printTemplate", "打印模板")]
[SysTable]
public class PrintTemplate : EntityBase
{
    /// <summary>
    /// 模板名称
    /// </summary>
    [SugarColumn(ColumnDescription = "模板名称", Length = 100)]
    [MaxLength(100)]
    public string TemplateName { get; set; }

    /// <summary>
    /// 模板内容
    /// </summary>
    [SugarColumn(ColumnDescription = "模板内容", ColumnDataType = "longtext,text,clob")]
    public string TemplateContent { get; set; }

    /// <summary>
    /// 描述
    /// </summary>
    [SugarColumn(ColumnDescription = "描述", Length = 500, IsNullable = true)]
    [MaxLength(500)]
    public string Description { get; set; }

    /// <summary>
    /// 模板变量
    /// </summary>
    [Navigate(NavigateType.OneToMany, nameof(PrintTemplateVariable.TemplateId))]
    public List<PrintTemplateVariable> Variables { get; set; }
}