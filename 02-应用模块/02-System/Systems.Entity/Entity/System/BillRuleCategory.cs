namespace Systems.Entity;

/// <summary>
///     单据规则分类
/// </summary>
[SugarTable("BASE_BILL_RULECategory")]
public class BillRuleCategory : EntityTenant
{
    /// <summary>
    ///     分类名称
    /// </summary>
    [SugarColumn(ColumnDescription = "分类名称")]
    public string Name { get; set; }

    /// <summary>
    ///     父Id
    /// </summary>
    [SugarColumn(ColumnDescription = "父Id")]
    public long Pid { get; set; }

    #region 忽略字段

    /// <summary>
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public List<BillRuleCategory> Children { get; set; }

    #endregion
}