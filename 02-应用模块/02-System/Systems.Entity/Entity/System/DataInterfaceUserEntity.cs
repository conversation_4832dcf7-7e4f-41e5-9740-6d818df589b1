namespace Systems.Entity;

/// <summary>
/// 接口认证用户
/// </summary>
[SugarTable("business_dataInterfaceUser")]
public class DataInterfaceUserEntity : EntityTenant
{
    /// <summary>
    /// 用户主键
    /// </summary>
    [SugarColumn(ColumnDescription = "用户主键")]
    public long? UserId { get; set; }

    /// <summary>
    /// 用户密钥
    /// </summary>
    [SugarColumn(ColumnDescription = "用户密钥")]
    public string? UserKey { get; set; }

    /// <summary>
    /// 接口认证主键
    /// </summary>
    [SugarColumn(ColumnDescription = "接口认证主键")]
    public long? OauthId { get; set; }

    #region 关联表

    /// <summary>
    ///     用户
    /// </summary>
    [Navigate(NavigateType.OneToOne, nameof(UserId))]
    public SysUser SysUser { get; set; }

    #endregion
}
