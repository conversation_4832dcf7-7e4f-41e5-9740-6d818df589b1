using IotPlatform.Core.Attribute;

namespace Systems.Entity;

/// <summary>
///     系统菜单表
/// </summary>
[SugarTable(null, "系统菜单表")]
[SysTable]
public class SysMenu : EntityBase
{
    /// <summary>
    ///     父Id
    /// </summary>
    [SugarColumn(ColumnDescription = "父Id")]
    public long Pid { get; set; }

    /// <summary>
    ///     父Ids
    /// </summary>
    [SugarColumn(ColumnDescription = "父Ids")]
    public string Pids { get; set; }

    /// <summary>
    ///     名称
    /// </summary>
    [SugarColumn(ColumnDescription = "名称")]
    public string Name { get; set; }

    /// <summary>
    ///     编码
    /// </summary>
    [SugarColumn(ColumnDescription = "编码")]
    public string Code { get; set; }

    /// <summary>
    ///     菜单类型（字典 0目录 1菜单 2按钮）
    /// </summary>
    [SugarColumn(ColumnDescription = "菜单类型 字典 0目录 1菜单 2按钮")]
    public MenuEnum Type { get; set; }

    /// <summary>
    ///     图标
    /// </summary>
    [SugarColumn(ColumnDescription = "图标", IsNullable = true)]
    public string Icon { get; set; }

    /// <summary>
    ///     路由地址
    /// </summary>
    [SugarColumn(ColumnDescription = "路由地址")]
    public string Router { get; set; }

    /// <summary>
    ///     组件地址
    /// </summary>
    [SugarColumn(ColumnDescription = "组件地址")]
    public string Component { get; set; }

    /// <summary>
    ///     权限标识
    /// </summary>
    [SugarColumn(ColumnDescription = "权限标识", IsNullable = true)]
    public string Permission { get; set; }

    /// <summary>
    ///     应用分类（应用编码）
    /// </summary>
    [SugarColumn(ColumnDescription = "应用分类应用编码")]
    public string Application { get; set; }

    /// <summary>
    ///     打开方式（字典 0无 1组件 2内链 3外链）
    /// </summary>
    [SugarColumn(ColumnDescription = "打开方式字典 0无 1组件 2内链 3外链")]
    public MenuOpenEnum OpenType { get; set; } = MenuOpenEnum.NONE;

    /// <summary>
    ///     是否可见
    /// </summary>
    [SugarColumn(ColumnDescription = "是否可见")]
    public bool Visible { get; set; } = true;

    /// <summary>
    ///     内链地址
    /// </summary>
    [SugarColumn(ColumnDescription = "内链地址", IsNullable = true)]
    public string Link { get; set; }

    /// <summary>
    ///     重定向地址
    /// </summary>
    [SugarColumn(ColumnDescription = "重定向地址", IsNullable = true)]
    public string Redirect { get; set; }

    /// <summary>
    ///     权重（字典 1系统权重 2业务权重）
    /// </summary>
    [SugarColumn(ColumnDescription = "权重字典 1系统权重 2业务权重")]
    public MenuWeight Weight { get; set; } = MenuWeight.DEFAULT_WEIGHT;

    /// <summary>
    ///     排序
    /// </summary>
    [SugarColumn(ColumnDescription = "排序")]
    public int Sort { get; set; } = 100;

    /// <summary>
    ///     备注
    /// </summary>
    [SugarColumn(ColumnDescription = "备注", IsNullable = true)]
    public string Remark { get; set; }

    /// <summary>
    ///     启用状态
    /// </summary>
    [SugarColumn(ColumnDescription = "启用状态")]
    public bool Status { get; set; } = true;

    /// <summary>
    ///     功能主键.
    /// </summary>
    [SugarColumn(ColumnName = "F_MODULE_ID")]
    public long? ModuleId { get; set; }

    #region 忽略字段

    /// <summary>
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public List<SysMenu> Children { get; set; }

    /// <summary>
    ///     服务名称
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public string ServiceName { get; set; }

    #endregion
}

/// <summary>
///     系统菜单类型枚举
/// </summary>
public enum MenuEnum
{
    /// <summary>
    ///     目录
    /// </summary>
    [Description("目录")] DIR = 0,

    /// <summary>
    ///     菜单
    /// </summary>
    [Description("菜单")] MENU = 1,

    /// <summary>
    ///     按钮
    /// </summary>
    [Description("按钮")] Btn = 2
}

/// <summary>
///     系统菜单类型
/// </summary>
public enum MenuOpenEnum
{
    /// <summary>
    ///     无
    /// </summary>
    [Description("无")] NONE = 0,

    /// <summary>
    ///     组件
    /// </summary>
    [Description("组件")] COMPONENT = 1,

    /// <summary>
    ///     外链
    /// </summary>
    [Description("外链")] OUTER = 3
}

/// <summary>
///     菜单权重
/// </summary>
public enum MenuWeight
{
    /// <summary>
    ///     系统权重
    /// </summary>
    [Description("系统权重")] SUPER_ADMIN_WEIGHT = 1,

    /// <summary>
    ///     业务权重
    /// </summary>
    [Description("业务权重")] DEFAULT_WEIGHT = 2
}