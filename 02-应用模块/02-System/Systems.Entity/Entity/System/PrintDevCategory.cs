namespace Systems.Entity;

/// <summary>
/// 打印模板分类
/// </summary>
[SugarTable("BASE_PRINT_TEMPLATE_CATEGORY")]
public class PrintDevCategory : EntityTenantId
{
    /// <summary>
    ///     分类名称
    /// </summary>
    [SugarColumn(ColumnDescription = "分类名称")]
    public string Name { get; set; }

    /// <summary>
    ///     父Id
    /// </summary>
    [SugarColumn(ColumnDescription = "父Id")]
    public long Pid { get; set; }
    
    #region 忽略字段
    
    /// <summary>
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public List<PrintDevCategory> Children { get; set; }
    
    #endregion
}