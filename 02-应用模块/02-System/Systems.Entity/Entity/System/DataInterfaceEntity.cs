namespace Systems.Entity;

/// <summary>
///     数据接口
/// </summary>
[SugarTable("business_dataInterface")]
public class DataInterfaceEntity : EntityTenant
{
    /// <summary>
    ///     接口名称
    /// </summary>
    [SugarColumn(ColumnDescription = "接口名称")]
    public string FullName { get; set; }

    /// <summary>
    ///     编码
    /// </summary>
    [SugarColumn(ColumnDescription = "编码")]
    public string EnCode { get; set; }

    /// <summary>
    ///     分类Id
    /// </summary>
    [SugarColumn(ColumnDescription = "分类Id")]
    public long CategoryId { get; set; }

    /// <summary>
    ///     描述
    /// </summary>
    [SugarColumn(ColumnDescription = "描述")]
    public string? Description { get; set; }

    /// <summary>
    ///     类型(1-sql，2-静态数据，3-api)
    /// </summary>
    [SugarColumn(ColumnDescription = "类型(1-sql，2-静态数据，3-api)")]
    public int? Type { get; set; }

    /// <summary>
    ///     类型(3-查询)
    /// </summary>
    [SugarColumn(ColumnDescription = "类型(3-查询)")]
    public int? Action { get; set; }

    /// <summary>
    ///     分页(0-禁用，1-启用)
    /// </summary>
    [SugarColumn(ColumnDescription = "分页(0-禁用，1-启用)")]
    public int? HasPage { get; set; }

    /// <summary>
    ///     后置接口(0-禁用，1-启用)
    /// </summary>
    [SugarColumn(ColumnDescription = "后置接口(0-禁用，1-启用)")]
    public int? IsPostposition { get; set; }

    /// <summary>
    ///     数据配置json
    /// </summary>
    [SugarColumn(ColumnDescription = "数据配置json",ColumnDataType = StaticConfig.CodeFirst_BigString)]
    public string? DataConfigJson { get; set; }

    /// <summary>
    ///     数据统计json
    /// </summary>
    [SugarColumn(ColumnDescription = "数据统计json",ColumnDataType = StaticConfig.CodeFirst_BigString)]
    public string? DataCountJson { get; set; }

    /// <summary>
    ///     数据回显json
    /// </summary>
    [SugarColumn(ColumnDescription = "数据回显json",ColumnDataType = StaticConfig.CodeFirst_BigString)]
    public string? DataEchoJson { get; set; }

    /// <summary>
    ///     异常验证json
    /// </summary>
    [SugarColumn(ColumnDescription = "异常验证json",ColumnDataType = StaticConfig.CodeFirst_BigString)]
    public string? DataExceptionJson { get; set; }

    /// <summary>
    ///     数据处理json
    /// </summary>
    [SugarColumn(ColumnDescription = "数据处理json",ColumnDataType = StaticConfig.CodeFirst_BigString)]
    public string? DataJsJson { get; set; }

    /// <summary>
    ///     参数json
    /// </summary>
    [SugarColumn(ColumnDescription = "参数json",ColumnDataType = StaticConfig.CodeFirst_BigString)]
    public string? ParameterJson { get; set; }

    /// <summary>
    ///     字段json
    /// </summary>
    [SugarColumn(ColumnDescription = "字段json",ColumnDataType = StaticConfig.CodeFirst_BigString)]
    public string? FieldJson { get; set; }

    /// <summary>
    ///     排序码
    /// </summary>
    [SugarColumn(ColumnDescription = "排序码")]
    public virtual long? SortCode { get; set; }

    /// <summary>
    ///     获取或设置 启用标识
    ///     0-禁用,1-启用
    /// </summary>
    [SugarColumn(ColumnDescription = "启用标识")]
    public virtual int? EnabledMark { get; set; }
}