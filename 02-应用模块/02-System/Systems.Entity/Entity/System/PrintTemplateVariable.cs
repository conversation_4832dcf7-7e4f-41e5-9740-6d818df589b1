using IotPlatform.Core;
using IotPlatform.Core.Attribute;

namespace Systems.Entity;

/// <summary>
/// 打印模板变量
/// </summary>
[SugarTable("business_printTemplateVariable", "打印模板变量")]
[SysTable]
public class PrintTemplateVariable : EntityBase
{
    /// <summary>
    /// 模板ID
    /// </summary>
    [SugarColumn(ColumnDescription = "模板ID")]
    public long TemplateId { get; set; }

    /// <summary>
    /// 变量名称
    /// </summary>
    [SugarColumn(ColumnDescription = "变量名称", Length = 100)]
    [MaxLength(100)]
    public string VariableName { get; set; }

    /// <summary>
    /// 变量描述
    /// </summary>
    [SugarColumn(ColumnDescription = "变量描述", Length = 200, IsNullable = true)]
    [MaxLength(200)]
    public string Description { get; set; }

    /// <summary>
    /// 所属模板
    /// </summary>
    [Navigate(NavigateType.OneToOne, nameof(TemplateId))]
    public PrintTemplate PrintTemplate { get; set; }
}