namespace Systems.Entity;

/// <summary>
///     数据接口变量
/// </summary>
[SugarTable("business_dataInterfaceVariate")]
public class DataInterfaceVariateEntity : EntityTenant
{
    /// <summary>
    ///     数据接口id.
    /// </summary>
    [SugarColumn(ColumnDescription = "数据接口id")]
    public long InterfaceId { get; set; }

    /// <summary>
    ///     变量名.
    /// </summary>
    [SugarColumn(ColumnDescription = "变量名")]
    public string FullName { get; set; }

    /// <summary>
    ///     表达式.
    /// </summary>
    [SugarColumn(ColumnDescription = "表达式")]
    public string Expression { get; set; }

    /// <summary>
    ///     变量值.
    /// </summary>
    [SugarColumn(ColumnDescription = "变量值")]
    public string? Value { get; set; }

    /// <summary>
    ///     排序码.
    /// </summary>
    [SugarColumn(ColumnDescription = "排序码")]
    public virtual long? SortCode { get; set; }
}