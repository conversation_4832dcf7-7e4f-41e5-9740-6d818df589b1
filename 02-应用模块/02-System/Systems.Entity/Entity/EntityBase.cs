using Newtonsoft.Json;

namespace Systems.Entity;

/// <summary>
///     框架实体基类Id
/// </summary>
public abstract class EntityBaseId
{
    /// <summary>
    ///     雪花Id
    /// </summary>
    [SugarColumn(ColumnName = "Id", ColumnDescription = "主键Id", IsPrimaryKey = true, IsIdentity = false)]
    public virtual long Id { get; set; }
}

/// <summary>
/// 实体类基类
/// </summary>
[SuppressSniffer]
public abstract class EntityBase<TKey> 
{
    /// <summary>
    /// 获取或设置 编号
    /// </summary>  
    [SugarColumn(ColumnName = "F_Id", ColumnDescription = "主键", IsPrimaryKey = true)]
    public TKey Id { get; set; }
}

/// <summary>
///     框架实体基类
/// </summary>
public abstract class EntityBase : EntityBaseId
{
    /// <summary>
    ///     创建时间
    /// </summary>
    [SugarColumn(ColumnDescription = "创建时间", IsOnlyIgnoreUpdate = true)]
    public virtual DateTime? CreatedTime { get; set; }

    /// <summary>
    ///     更新时间
    /// </summary>
    [SugarColumn(ColumnDescription = "更新时间", IsOnlyIgnoreInsert = true)]
    public virtual DateTime? UpdatedTime { get; set; }

    /// <summary>
    ///     创建者Id
    /// </summary>
    [SugarColumn(ColumnDescription = "创建者Id", IsOnlyIgnoreUpdate = true)]
    public virtual long? CreatedUserId { get; set; }

    /// <summary>
    ///     创建者
    /// </summary>
    [JsonIgnore]
    [System.Text.Json.Serialization.JsonIgnore]
    [Navigate(NavigateType.OneToOne, nameof(CreatedUserId))]
    public virtual SysUser CreatedUser { get; set; }

    /// <summary>
    ///     创建者姓名
    /// </summary>
    [SugarColumn(ColumnDescription = "创建者姓名", Length = 64, IsOnlyIgnoreUpdate = true)]
    public virtual string? CreatedUserName { get; set; }

    /// <summary>
    ///     修改者Id
    /// </summary>
    [SugarColumn(ColumnDescription = "修改者Id", IsOnlyIgnoreInsert = true)]
    public virtual long? UpdatedUserId { get; set; }

    /// <summary>
    ///     修改者
    /// </summary>
    [JsonIgnore]
    [System.Text.Json.Serialization.JsonIgnore]
    [Navigate(NavigateType.OneToOne, nameof(UpdatedUserId))]
    public virtual SysUser UpdatedUser { get; set; }

    /// <summary>
    ///     修改者姓名
    /// </summary>
    [SugarColumn(ColumnDescription = "修改者姓名", Length = 64, IsOnlyIgnoreInsert = true)]
    public virtual string? UpdatedUserName { get; set; }
}

/// <summary>
///     租户基类实体
/// </summary>
public abstract class EntityTenant : EntityBase, ITenantIdFilter
{
    /// <summary>
    ///     租户Id
    /// </summary>
    [SugarColumn(ColumnDescription = "租户Id", IsOnlyIgnoreUpdate = true)]
    public virtual long? TenantId { get; set; }
}

/// <summary>
///     租户基类实体Id
/// </summary>
public abstract class EntityTenantId : EntityBaseId, ITenantIdFilter
{
    /// <summary>
    ///     租户Id
    /// </summary>
    [SugarColumn(ColumnDescription = "租户Id", IsOnlyIgnoreUpdate = true)]
    public virtual long? TenantId { get; set; }
}