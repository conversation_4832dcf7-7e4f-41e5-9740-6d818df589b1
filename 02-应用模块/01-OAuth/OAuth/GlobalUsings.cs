global using Furion.DataEncryption;
global using Furion.DependencyInjection;
global using Furion.DynamicApiController;
global using Furion.FriendlyException;
global using Microsoft.AspNetCore.Authorization;
global using Microsoft.AspNetCore.Http;
global using Microsoft.AspNetCore.Mvc;
global using System.ComponentModel;
global using System.ComponentModel.DataAnnotations;
global using IotPlatform.Core.Entity;
global using IotPlatform.Core.Const;