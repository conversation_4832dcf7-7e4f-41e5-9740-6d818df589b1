using System.Runtime.InteropServices;
using IotPlatform.DongleLicense.Models;
using static IotPlatform.DongleLicense.Models.DongleStructures;

namespace IotPlatform.DongleLicense.Services;

/// <summary>
/// 加密锁API服务类
/// </summary>
public class DongleApiService
{
    #region DLL导入声明

    [DllImport("Dongle_d.dll")]
    static extern uint Dongle_Enum(ref DONGLE_INFO pDongleInfo, out ushort pCount);

    [DllImport("Dongle_d.dll")]
    static extern uint Dongle_Open(ref uint phDongle, int nIndex);

    [DllImport("Dongle_d.dll")]
    static extern uint Dongle_Close(uint hDongle);

    [DllImport("Dongle_d.dll")]
    static extern uint Dongle_VerifyPIN(uint hDongle, uint nFlags, byte[] pPIN, out int pRemainCount);

    [DllImport("Dongle_d.dll")]
    static extern uint Dongle_CreateFile(uint hDongle, uint nFileType, ushort wFileID, uint pFileAttr);

    [DllImport("Dongle_d.dll")]
    static extern uint Dongle_WriteFile(uint hDongle, uint nFileType, ushort wFileID, short wOffset, byte[] buffer, int nDataLen);

    [DllImport("Dongle_d.dll")]
    static extern uint Dongle_ReadFile(uint hDongle, short wFileID, short wOffset, byte[] buffer, int nDataLen);

    [DllImport("Dongle_d.dll")]
    static extern uint Dongle_ListFile(uint hDongle, uint nFileType, DATA_FILE_LIST[] pFileList, ref int pDataLen);

    [DllImport("Dongle_d.dll")]
    static extern uint Dongle_DeleteFile(uint hDongle, uint nFileType, short wFileID);

    [DllImport("Dongle_d.dll")]
    static extern uint Dongle_DownloadExeFile(uint hDongle, EXE_FILE_INFO[] pExeFileInfo, int nCount);

    [DllImport("Dongle_d.dll")]
    static extern uint Dongle_RunExeFile(uint hDongle, short wFileID, byte[] pInOutData, short wInOutDataLen, ref int nMainRet);

    [DllImport("Dongle_d.dll")]
    static extern uint Dongle_WriteShareMemory(uint hDongle, byte[] pData, int nDataLen);

    [DllImport("Dongle_d.dll")]
    static extern uint Dongle_ReadShareMemory(uint hDongle, byte[] pData);

    [DllImport("Dongle_d.dll")]
    static extern uint Dongle_WriteData(uint hDongle, int nOffset, byte[] pData, int nDataLen);

    [DllImport("Dongle_d.dll")]
    static extern uint Dongle_ReadData(uint hDongle, int nOffset, byte[] pData, int nDataLen);

    [DllImport("Dongle_d.dll")]
    static extern uint Dongle_LEDControl(uint hDongle, uint nFlag);

    [DllImport("Dongle_d.dll")]
    static extern uint Dongle_SwitchProtocol(uint hDongle, uint nFlag);

    [DllImport("Dongle_d.dll")]
    static extern uint Dongle_GetUTCTime(uint hDongle, ref uint pdwUTCTime);

    [DllImport("Dongle_d.dll")]
    static extern uint Dongle_SetDeadline(uint hDongle, uint dwTime);

    [DllImport("Dongle_d.dll")]
    static extern uint Dongle_GenUniqueKey(uint hDongle, int nSeedLen, byte[] pSeed, byte[] pPIDstr, byte[] pAdminPINstr);

    [DllImport("Dongle_d.dll")]
    static extern uint Dongle_ResetState(uint hDongle);

    [DllImport("Dongle_d.dll")]
    static extern uint Dongle_ChangePIN(uint hDongle, uint nFlags, byte[] pOldPIN, byte[] pNewPIN, int nTryCount);

    [DllImport("Dongle_d.dll")]
    static extern uint Dongle_RFS(uint hDongle);

    [DllImport("Dongle_d.dll")]
    static extern uint Dongle_SetUserID(uint hDongle, uint dwUserID);

    [DllImport("Dongle_d.dll")]
    static extern uint Dongle_ResetUserPIN(uint hDongle, byte[] pAdminPIN);

    [DllImport("Dongle_d.dll")]
    static extern uint Dongle_RsaGenPubPriKey(uint hDongle, ushort wPriFileID, ref RSA_PUBLIC_KEY pPubBakup, ref RSA_PRIVATE_KEY pPriBakup);

    [DllImport("Dongle_d.dll")]
    static extern uint Dongle_RsaPri(uint hDongle, ushort wPriFileID, uint nFlag, byte[] pInData, uint nInDataLen, byte[] pOutData, ref uint pOutDataLen);

    [DllImport("Dongle_d.dll")]
    static extern uint Dongle_RsaPub(uint hDongle, uint nFlag, ref RSA_PUBLIC_KEY pPubKey, byte[] pInData, uint nInDataLen, byte[] pOutData, ref uint pOutDataLen);

    [DllImport("Dongle_d.dll")]
    static extern uint Dongle_TDES(uint hDongle, ushort wKeyFileID, uint nFlag, byte[] pInData, byte[] pOutData, uint nDataLen);

    [DllImport("Dongle_d.dll")]
    static extern uint Dongle_SM4(uint hDongle, ushort wKeyFileID, uint nFlag, byte[] pInData, byte[] pOutData, uint nDataLen);

    [DllImport("Dongle_d.dll")]
    static extern uint Dongle_HASH(uint hDongle, uint nFlag, byte[] pInData, uint nDataLen, byte[] pHash);

    [DllImport("Dongle_d.dll")]
    static extern uint Dongle_LimitSeedCount(uint hDongle, int nCount);

    [DllImport("Dongle_d.dll")]
    static extern uint Dongle_Seed(uint hDongle, byte[] pSeed, uint nSeedLen, byte[] pOutData);

    [DllImport("Dongle_d.dll")]
    static extern uint Dongle_EccGenPubPriKey(uint hDongle, ushort wPriFileID, ref ECCSM2_PUBLIC_KEY vPubBakup, ref ECCSM2_PRIVATE_KEY vPriBakup);

    [DllImport("Dongle_d.dll")]
    static extern uint Dongle_EccSign(uint hDongle, ushort wPriFileID, byte[] pHashData, uint nHashDataLen, byte[] pOutData);

    [DllImport("Dongle_d.dll")]
    static extern uint Dongle_EccVerify(uint hDongle, ref ECCSM2_PUBLIC_KEY pPubKey, byte[] pHashData, uint nHashDataLen, byte[] pSign);

    [DllImport("Dongle_d.dll")]
    static extern uint Dongle_SM2GenPubPriKey(uint hDongle, ushort wPriFileID, ref ECCSM2_PUBLIC_KEY pPubBakup, ref ECCSM2_PRIVATE_KEY pPriBakup);

    [DllImport("Dongle_d.dll")]
    static extern uint Dongle_SM2Sign(uint hDongle, ushort wPriFileID, byte[] pHashData, uint nHashDataLen, byte[] pOutData);

    [DllImport("Dongle_d.dll")]
    static extern uint Dongle_SM2Verify(uint hDongle, ref ECCSM2_PUBLIC_KEY pPubKey, byte[] pHashData, uint nHashDataLen, byte[] pSign);

    #endregion

    /// <summary>
    /// 枚举加密锁设备
    /// </summary>
    /// <returns>返回加密锁信息和数量</returns>
    public (uint result, DONGLE_INFO dongleInfo, ushort count) EnumDongle()
    {
        var pDongleInfo = new DONGLE_INFO();
        ushort pCount = 0;
        uint ret = Dongle_Enum(ref pDongleInfo, out pCount);
        return (ret, pDongleInfo, pCount);
    }

    /// <summary>
    /// 打开加密锁
    /// </summary>
    /// <param name="index">设备索引</param>
    /// <returns>返回结果和句柄</returns>
    public (uint result, uint handle) OpenDongle(int index = 0)
    {
        uint hDongle = 0;
        uint ret = Dongle_Open(ref hDongle, index);
        return (ret, hDongle);
    }

    /// <summary>
    /// 关闭加密锁
    /// </summary>
    /// <param name="handle">设备句柄</param>
    /// <returns>操作结果</returns>
    public uint CloseDongle(uint handle)
    {
        return Dongle_Close(handle);
    }

    /// <summary>
    /// 验证PIN码
    /// </summary>
    /// <param name="handle">设备句柄</param>
    /// <param name="flags">标志</param>
    /// <param name="pin">PIN码</param>
    /// <returns>返回结果和剩余尝试次数</returns>
    public (uint result, int remainCount) VerifyPIN(uint handle, uint flags, byte[] pin)
    {
        int remainCount = 0;
        uint ret = Dongle_VerifyPIN(handle, flags, pin, out remainCount);
        return (ret, remainCount);
    }

    /// <summary>
    /// 读取数据
    /// </summary>
    /// <param name="handle">设备句柄</param>
    /// <param name="offset">偏移量</param>
    /// <param name="buffer">数据缓冲区</param>
    /// <param name="dataLen">数据长度</param>
    /// <returns>操作结果</returns>
    public uint ReadData(uint handle, int offset, byte[] buffer, int dataLen)
    {
        return Dongle_ReadData(handle, offset, buffer, dataLen);
    }

    /// <summary>
    /// 写入数据
    /// </summary>
    /// <param name="handle">设备句柄</param>
    /// <param name="offset">偏移量</param>
    /// <param name="data">要写入的数据</param>
    /// <param name="dataLen">数据长度</param>
    /// <returns>操作结果</returns>
    public uint WriteData(uint handle, int offset, byte[] data, int dataLen)
    {
        return Dongle_WriteData(handle, offset, data, dataLen);
    }

    /// <summary>
    /// 获取UTC时间
    /// </summary>
    /// <param name="handle">设备句柄</param>
    /// <returns>返回结果和UTC时间</returns>
    public (uint result, uint utcTime) GetUTCTime(uint handle)
    {
        uint utcTime = 0;
        uint ret = Dongle_GetUTCTime(handle, ref utcTime);
        return (ret, utcTime);
    }

    /// <summary>
    /// 重置状态
    /// </summary>
    /// <param name="handle">设备句柄</param>
    /// <returns>操作结果</returns>
    public uint ResetState(uint handle)
    {
        return Dongle_ResetState(handle);
    }
}
