using IotPlatform.DongleLicense.Models;
using Microsoft.Extensions.DependencyInjection;

namespace IotPlatform.DongleLicense.Services;

/// <summary>
/// 加密锁触发服务（用于手动触发检查）
/// </summary>
public class DongleTriggerService
{
    private readonly IServiceScopeFactory _serviceScopeFactory;
    private readonly DongleLicenseManager _licenseManager;

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="serviceScopeFactory">服务作用域工厂</param>
    /// <param name="licenseManager">许可证管理器</param>
    public DongleTriggerService(
        IServiceScopeFactory serviceScopeFactory,
        DongleLicenseManager licenseManager)
    {
        _serviceScopeFactory = serviceScopeFactory;
        _licenseManager = licenseManager;
    }

    /// <summary>
    /// 手动触发检查
    /// </summary>
    /// <returns>检查结果</returns>
    public async Task<DongleCheckResult> TriggerCheckAsync()
    {
        try
        {
            // 使用作用域创建服务实例
            using var scope = _serviceScopeFactory.CreateScope();
            var dongleCheckService = scope.ServiceProvider.GetRequiredService<DongleCheckService>();
            
            var result = await dongleCheckService.CheckDongleAsync();
            _licenseManager.UpdateCheckResult(result);
            return result;
        }
        catch (Exception ex)
        {
            var errorResult = new DongleCheckResult
            {
                IsSuccess = false,
                ErrorMessage = $"手动检查异常: {ex.Message}",
                CheckTime = DateTime.Now
            };
            
            _licenseManager.UpdateCheckResult(errorResult);
            return errorResult;
        }
    }
}
