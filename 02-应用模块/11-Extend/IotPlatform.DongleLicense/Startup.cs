using Furion;
using IotPlatform.DongleLicense.HostedService;
using IotPlatform.DongleLicense.Models;
using IotPlatform.DongleLicense.Services;
using Microsoft.Extensions.DependencyInjection;

namespace IotPlatform.DongleLicense;

/// <summary>
/// 加密锁许可证模块启动配置
/// </summary>
[AppStartup(200)]
public class Startup : AppStartup
{
    /// <summary>
    /// 配置服务
    /// </summary>
    /// <param name="services">服务集合</param>
    public void ConfigureServices(IServiceCollection services)
    {
        // 注册配置选项
        services.Configure<DongleCheckConfig>(App.Configuration.GetSection("DongleLicense"));

        // 注册加密锁API服务
        services.AddSingleton<DongleApiService>();

        // 注册许可证管理器（单例）
        services.AddSingleton<DongleLicenseManager>();

        // 注册加密锁检查服务
        services.AddScoped<DongleCheckService>();

        // 注册触发服务（单例）
        services.AddSingleton<DongleTriggerService>();

        // 注册业务服务
        services.AddScoped<DongleLicenseService>();
        services.AddScoped<DongleTestService>();

        // 注册后台托管服务
        services.AddSingleton<DongleLicenseHostedService>();
        services.AddHostedService(provider => provider.GetRequiredService<DongleLicenseHostedService>());
    }
}
