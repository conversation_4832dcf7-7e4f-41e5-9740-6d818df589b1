namespace IotPlatform.Extend;

/// <summary>
///     文件预览
/// </summary>
[ApiDescriptionSettings("系统服务", Order = 600)]
[Route("api/extend/[controller]")]
public class DocumentPreview : IDynamicApiController, ITransient
{
    private readonly IFileManager _fileManager;

    public DocumentPreview(IFileManager fileManager)
    {
        _fileManager = fileManager;
    }

    #region Get

    /// <summary>
    ///     获取文档列表.
    /// </summary>
    /// <param name="input">请求参数.</param>
    /// <returns></returns>
    [HttpGet("/extend/documentPreview/page")]
    public async Task<dynamic> GetList_Api([FromQuery] KeywordInput input)
    {
        string filePath = FileVariable.DocumentPreviewFilePath;
        List<AnnexModel> list = await _fileManager.GetObjList(filePath);
        list = list.FindAll(
            x => "xlsx".Equals(x.FileType) || "xls".Equals(x.FileType) || "docx".Equals(x.FileType) || "doc".Equals(x.FileType) || "pptx".Equals(x.FileType) || "ppt".Equals(x.FileType));
        if (input.keyword.IsNotEmptyOrNull())
        {
            list = list.FindAll(x => x.FileName.Contains(input.keyword));
        }

        return list.OrderByDescending(x => x.FileTime).Adapt<List<DocumentPreviewListOutput>>();
    }

    /// <summary>
    ///     文件在线预览.
    /// </summary>
    /// <param name="fileId"></param>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpGet("/extend/document/{fileId}/Preview")]
    public async Task<dynamic> DocumentPreview_Api(string fileId, [FromQuery] DocumentPreviewPreviewInput input)
    {
        string filePath = FileVariable.DocumentPreviewFilePath;
        List<AnnexModel> files = await _fileManager.GetObjList(filePath);
        AnnexModel? file = files.Find(x => x.FileId == fileId);
        if (file == null)
        {
            throw Oops.Oh(ErrorCode.D8000);
        }

        if (file.IsNotEmptyOrNull())
        {
            string domain = App.GetConfig<AppOptions>("JNPF_App", true).Domain;
            string url = string.Format("{0}/extend/documentPreview/down/{1}", domain, file.FileName);
            if (!input.previewType.Equals("localPreview"))
            {
                url = _fileManager.KkFileUploaderPreview(file.FileName, domain);
            }

            return url;
        }

        throw Oops.Oh(ErrorCode.D8000);
    }

    /// <summary>
    ///     下载.
    /// </summary>
    /// <param name="fileName"></param>
    [HttpGet("/extend/documentPreview/down/{fileName}")]
    [AllowAnonymous]
    public async Task FileDown(string fileName)
    {
        string filePath = Path.Combine(FileVariable.DocumentPreviewFilePath, fileName);
        string systemFilePath = Path.Combine(FileVariable.SystemFilePath, fileName);
        FileStreamResult fileStreamResult = null;
        if (await _fileManager.ExistsFile(filePath))
        {
            fileStreamResult = await _fileManager.DownloadFileByType(filePath, fileName);
        }
        else
        {
            fileStreamResult = await _fileManager.DownloadFileByType(systemFilePath, fileName);
        }

        byte[] bytes = new byte[fileStreamResult.FileStream.Length];

        fileStreamResult.FileStream.Read(bytes, 0, bytes.Length);

        fileStreamResult.FileStream.Close();
        HttpContext? httpContext = App.HttpContext;
        httpContext.Response.ContentType = "application/octet-stream";
        httpContext.Response.Headers.Add("Content-Disposition", "attachment;filename=" + HttpUtility.UrlEncode(fileName, Encoding.UTF8));
        httpContext.Response.Headers.Add("Content-Length", bytes.Length.ToString());
        httpContext.Response.Body.WriteAsync(bytes);
        httpContext.Response.Body.Flush();
        httpContext.Response.Body.Close();
    }

    #endregion
}