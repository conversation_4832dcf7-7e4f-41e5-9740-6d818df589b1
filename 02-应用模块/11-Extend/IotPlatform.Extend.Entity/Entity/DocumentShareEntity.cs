namespace IotPlatform.Extend.Entity;

/// <summary>
///     知识文档共享
/// </summary>
[SugarTable("EXT_DOCUMENT_SHARE")]
public class DocumentShareEntity : EntityTenant
{
    /// <summary>
    ///     文档主键.
    /// </summary>
    [SugarColumn(ColumnName = "F_DOCUMENT_ID")]
    public long? DocumentId { get; set; }

    /// <summary>
    ///     共享人员.
    /// </summary>
    [SugarColumn(ColumnName = "F_SHARE_USER_ID")]
    public long? ShareUserId { get; set; }

    /// <summary>
    ///     共享时间.
    /// </summary>
    [SugarColumn(ColumnName = "F_SHARE_TIME")]
    public DateTime? ShareTime { get; set; }
}