namespace VisualDev.Entity.Dto.Portal;

/// <summary>
/// 门户设计信息输出.
/// </summary>
[SuppressSniffer]
public class PortalInfoOutput
{
    /// <summary>
    /// ID.
    /// </summary>
    public long id { get; set; }

    /// <summary>
    /// 名称.
    /// </summary>
    public string fullName { get; set; }

    /// <summary>
    /// 分类.
    /// </summary>
    public long category { get; set; }

    /// <summary>
    /// 编码.
    /// </summary>
    public string enCode { get; set; }

    /// <summary>
    /// 说明.
    /// </summary>
    public string description { get; set; }

    /// <summary>
    /// 表单JSON.
    /// </summary>
    public string formData { get; set; }

    /// <summary>
    /// 排序.
    /// </summary>
    public long? sortCode { get; set; }

    /// <summary>
    /// 锁定（0-锁定，1-自定义）.
    /// </summary>
    public int? enabledLock { get; set; }

    /// <summary>
    /// 发布选中平台.
    /// </summary>
    public string platformRelease { get; set; }

    /// <summary>
    /// pc门户发布标识.
    /// </summary>
    public int pcPortalIsRelease { get; set; }
 
    /// <summary>
    /// pc已发布门户名称.
    /// </summary>
    public string pcPortalReleaseName { get; set; }

}
