using Common.Security;

namespace VisualDev.Entity.Dto.VisualDev;

/// <summary>
/// 在线开发下拉框输出.
/// </summary>
public class VisualDevSelectorOutput : TreeModel
{
    /// <summary>
    /// 名称.
    /// </summary>
    public string? fullName { get; set; }

    /// <summary>
    /// 排序码.
    /// </summary>
    public long? SortCode { get; set; }

    /// <summary>
    /// 页面类型（1、纯表单，2、表单加列表，3、表单列表工作流）.
    /// </summary>
    public int webType { get; set; }
}
