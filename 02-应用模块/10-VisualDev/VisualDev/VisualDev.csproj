<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <PropertyGroup Condition=" '$(Configuration)' == 'Debug' ">
      <DocumentationFile>bin\Debug\VisualDev.xml</DocumentationFile>
    </PropertyGroup>

    <PropertyGroup Condition=" '$(Configuration)' == 'Release' ">
      <DebugSymbols>true</DebugSymbols>
      <DebugType>none</DebugType>
    </PropertyGroup>

    <ItemGroup>
      <ProjectReference Include="..\..\00-Common\Common.Core\Common.Core.csproj" />
      <ProjectReference Include="..\..\02-System\Systems.Core\Systems.Core.csproj" />
      <ProjectReference Include="..\..\09-Engine\VisualDev.Engine\VisualDev.Engine.csproj" />
      <ProjectReference Include="..\VisualDev.Entity\VisualDev.Entity.csproj" />
      <ProjectReference Include="..\VisualDev.Interface\VisualDev.Interface.csproj" />
    </ItemGroup>

</Project>
