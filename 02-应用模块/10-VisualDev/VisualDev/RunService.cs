using DateTime = System.DateTime;

namespace VisualDev;

/// <summary>
///     在线开发运行服务 .
/// </summary>
public class RunService : IRunService, ITransient, IDisposable
{
    #region 构造

    /// <summary>
    ///     服务提供器.
    /// </summary>
    private readonly IServiceScope _serviceScope;

    /// <summary>
    ///     服务基础仓储.
    /// </summary>
    private readonly ISqlSugarRepository<VisualDevEntity> _visualDevRepository; // 在线开发功能实体

    /// <summary>
    ///     SqlSugarClient客户端.
    /// </summary>
    private SqlSugarScope _sqlSugarClient;

    /// <summary>
    ///     表单数据解析.
    /// </summary>
    private readonly FormDataParsing _formDataParsing;

    /// <summary>
    ///     切库.
    /// </summary>
    private readonly IDataBaseManager _databaseService;

    /// <summary>
    ///     用户管理.
    /// </summary>
    private readonly IUserManager _userManager;

    /// <summary>
    ///     数据连接服务.
    /// </summary>
    private readonly DbLinkService _dbLinkService;

    /// <summary>
    ///     事件总线.
    /// </summary>
    private readonly IEventPublisher _eventPublisher;

    /// <summary>
    ///     缓存管理.
    /// </summary>
    private readonly SysCacheService _cacheManager;

    /// <summary>
    ///     多租户配置选项.
    /// </summary>
    private readonly TenantOptions _tenant;

    /// <summary>
    ///     默认数据库配置.
    /// </summary>
    private readonly DbConnectionConfig defaultConnectionConfig;
    
    /// <summary>
    ///     事务.
    /// </summary>
    private readonly ITenant _db;

    private readonly BillRuleService _billRuleService;
    
    /// <summary>
    /// 数据接口.
    /// </summary>
    private readonly DataInterfaceService _dataInterfaceService;

    /// <summary>
    ///     构造.
    /// </summary>
    public RunService(
        IServiceScopeFactory serviceScopeFactory,
        ISqlSugarRepository<VisualDevEntity> visualDevRepository,
        ISqlSugarClient sqlSugarClient,
        FormDataParsing formDataParsing,
        IOptions<TenantOptions> tenantOptions,
        IUserManager userManager,
        DbLinkService dbLinkService,
        IDataBaseManager databaseService,
        ISqlSugarClient context,
        IEventPublisher eventPublisher,
        IOptions<ConnectionStringsOptions> connectionOptions,
        SysCacheService cacheManager, BillRuleService billRuleService, DataInterfaceService dataInterfaceService)
    {
        _serviceScope = serviceScopeFactory.CreateScope();
        _visualDevRepository = visualDevRepository;
        _sqlSugarClient = (SqlSugarScope)sqlSugarClient;
        _formDataParsing = formDataParsing;
        _userManager = userManager;
        _tenant = tenantOptions.Value;
        _databaseService = databaseService;
        _dbLinkService = dbLinkService;
        _eventPublisher = eventPublisher;
        _db = context.AsTenant();
        _cacheManager = cacheManager;
        _billRuleService = billRuleService;
        _dataInterfaceService = dataInterfaceService;
        defaultConnectionConfig = connectionOptions.Value.DefaultConnectionConfig;
    }

    #endregion

    #region Get

    /// <summary>
    ///     列表数据处理.
    /// </summary>
    /// <param name="entity">功能实体.</param>
    /// <param name="input">查询参数.</param>
    /// <param name="actionType"></param>
    /// <param name="tenantId">租户Id.</param>
    /// <returns></returns>
    public async Task<SqlSugarPagedList<Dictionary<string, object>>> GetListResult(VisualDevEntity entity, VisualDevModelListQueryInput input, string actionType = "List", string? tenantId = null)
    {
        SqlSugarPagedList<Dictionary<string, object>>? realList = new() { Rows = new List<Dictionary<string, object>>() }; // 返回结果集
        TemplateParsingBase templateInfo = new(entity); // 解析模板控件
        if (entity.WebType.Equals(4))
        {
            return await GetDataViewResults(templateInfo, input); // 数据视图
        }

        // 处理查询
        Dictionary<string, object> queryJson = string.IsNullOrEmpty(input.queryJson) ? null : input.queryJson.ToObjectOld<Dictionary<string, object>>();
        if (queryJson != null)
        {
            foreach ((string key, object value) in queryJson)
            {
                queryJson[key] = value.GetJsonElementValue();
            }

            foreach (KeyValuePair<string, object> item in queryJson)
            {
                if (!templateInfo.ColumnData.searchList.Any(it => it.id.Equals(item.Key)) && !item.Key.Equals(JnpfKeyConst.JNPFKEYWORD))
                {
                    FieldsModel? vmodel = templateInfo.AllFieldsModel.Find(it => it.__vModel__.Equals(item.Key));
                    if (templateInfo.ColumnData.searchList.Any(it => it.id.Equals(item.Key)))
                    {
                        vmodel.searchMultiple = templateInfo.ColumnData.searchList.Find(it => it.id.Equals(item.Key)).searchMultiple;
                    }

                    IndexSearchFieldModel searchModel = vmodel.Adapt<IndexSearchFieldModel>();
                    searchModel.options = searchModel.options.ListDicToListDic();
                    searchModel.id = item.Key;
                    templateInfo.ColumnData.searchList.Add(searchModel);
                }

                if (templateInfo.AppColumnData?.searchList != null && !templateInfo.AppColumnData.searchList.Any(it => it.id.Equals(item.Key)) && !item.Key.Equals(JnpfKeyConst.JNPFKEYWORD))
                {
                    FieldsModel? vmodel = templateInfo.AllFieldsModel.Find(it => it.__vModel__.Equals(item.Key));
                    if (templateInfo.AppColumnData.searchList.Any(it => it.id.Equals(item.Key)))
                    {
                        vmodel.searchMultiple = templateInfo.AppColumnData.searchList.Find(it => it.id.Equals(item.Key)).searchMultiple;
                    }

                    IndexSearchFieldModel searchModel = vmodel.Adapt<IndexSearchFieldModel>();
                    searchModel.options = searchModel.options.ListDicToListDic();
                    searchModel.id = item.Key;
                    templateInfo.AppColumnData.searchList.Add(searchModel);
                }
            }
        }

        input.superQueryJson = GetSuperQueryInput(input.superQueryJson);

        string? primaryKey = "f_id"; // 列表主键

        // 获取请求端类型，并对应获取 数据权限
        DbLink link = await GetDbLink(entity.DbLinkId, tenantId);
        templateInfo.DbLink = link;
        await SyncField(templateInfo); // 同步业务字段
        primaryKey = GetPrimary(link, templateInfo.MainTableName);
        bool udp = _userManager.UserOrigin == "pc" ? templateInfo.ColumnData.useDataPermission : templateInfo.AppColumnData.useDataPermission;
        templateInfo.ColumnData = _userManager.UserOrigin == "pc" ? templateInfo.ColumnData : templateInfo.AppColumnData;
        List<IConditionalModel>? pvalue = new();
        if (_userManager.User != null || _userManager.UserId.IsNotEmptyOrNull())
        {
            // pvalue = await _userManager.GetCondition<Dictionary<string, object>>(primaryKey, input.menuId, udp, templateInfo.FormModel.primaryKeyPolicy.Equals(2));
        }

        string pvalueJson = pvalue.ToJson();
        foreach (KeyValuePair<string, string> item in templateInfo.AllTableFields)
        {
            if (pvalueJson.Contains(string.Format("\"FieldName\":\"{0}\",", item.Key)))
            {
                pvalueJson.Replace(string.Format("\"FieldName\":\"{0}\",", item.Value), string.Format("\"FieldName\":\"{0}\",", item.Key));
            }
        }

        pvalue = _visualDevRepository.AsSugarClient().Utilities.JsonToConditionalModels(pvalueJson);
        if (templateInfo.ColumnData.type.Equals(5))
        {
            pvalue.Clear(); // 树形表格 去掉数据权限.
            input.pageSize = 999999;
        }

        // 所有查询条件
        input.dataRuleJson = _userManager.UserOrigin == "pc" ? templateInfo.DataRuleListJson.ToJson() : templateInfo.AppDataRuleListJson.ToJson(); // 数据过滤
        List<IConditionalModel>? dataRuleWhere = new();
        List<IConditionalModel> queryWhere = new();
        List<IConditionalModel> superQueryWhere = new();
        if (input.dataRuleJson.IsNotEmptyOrNull())
        {
            dataRuleWhere = _visualDevRepository.AsSugarClient().Utilities.JsonToConditionalModels(input.dataRuleJson);
        }

        queryWhere = GetQueryJson(input.queryJson, _userManager.UserOrigin == "pc" ? templateInfo.ColumnData : templateInfo.AppColumnData, input.isInteAssisData);
        if (input.superQueryJson.IsNotEmptyOrNull())
        {
            superQueryWhere = GetSuperQueryJson(input.superQueryJson, templateInfo);
        }

        if (templateInfo.ColumnData.type == 4)
        {
            await OptimisticLocking(link, templateInfo); // 开启行编辑 处理 开启并发锁定
        }

        Dictionary<string, string>? tableFieldKeyValue = new(); // 联表查询 表字段名称 对应 前端字段名称 (应对oracle 查询字段长度不能超过30个)
        string? sql = GetListQuerySql(primaryKey, templateInfo, ref input, ref tableFieldKeyValue, pvalue); // 查询sql

        // 未开启分页
        if (!templateInfo.ColumnData.hasPage)
        {
            input.pageSize = 999999;
        }

        // 处理查询
        input.queryJson = GetQueryJson(input.queryJson, templateInfo.ColumnData, input.isInteAssisData).ToJson();
        input.superQueryJson = GetSuperQueryJson(input.superQueryJson, templateInfo).ToJson();

        realList = _databaseService.GetInterFaceData(link, sql, input, templateInfo.ColumnData.Adapt<MainBeltViceQueryModel>(), new List<IConditionalModel>(), tableFieldKeyValue);

        // 显示列有子表字段
        if ((entity.isShortLink || (templateInfo.ColumnData.type != 4 &&
                                    templateInfo.ColumnData.columnList.Any(x => templateInfo.ChildTableFields.ContainsKey(x.__vModel__) || templateInfo.ChildTableFields.ContainsKey(x.prop)))) &&
            realList.Rows.Any())
        {
            realList = await GetListChildTable(templateInfo, primaryKey, queryWhere, dataRuleWhere, superQueryWhere, realList, pvalue);
        }

        // 处理 自增长ID 流程表单 自增长Id转成 流程Id
        if (entity.FlowId.IsNotEmptyOrNull() && entity.EnableFlow.Equals(1) && realList.Rows.Any())
        {
            List<object> ids = realList.Rows.Select(x => x[primaryKey]).ToList();
            Dictionary<string, string> newIds = GetPIdsByFlowIds(link, templateInfo, primaryKey, ids.ToObjectOld<List<string>>(), true);
            realList.Rows.ToList().ForEach(item => item[primaryKey] = newIds.First(x => x.Value.Equals(item[primaryKey].ToString())).Key);
        }

        if (input.sidx.IsNullOrEmpty())
        {
            input.sidx = primaryKey;
        }

        // 增加前端回显字段 : key_name
        string roweditId = YitIdHelper.NextId().ToString();
        if (templateInfo.ColumnData.type.Equals(4) && _userManager.UserOrigin.Equals("pc"))
        {
            realList.Rows.ToList().ForEach(items =>
            {
                Dictionary<string, object> addItem = items.Where(item => item.Key != "RowIndex").ToDictionary(item => item.Key + roweditId, item => item.Value);

                foreach (KeyValuePair<string, object> item in addItem)
                {
                    items.Add(item.Key, item.Value);
                }
            });
        }

        if (realList.Rows.Any())
        {
            // 树形表格
            if (templateInfo.ColumnData.type.Equals(5))
            {
                realList.Rows.ToList().ForEach(item => item[templateInfo.ColumnData.parentField + "_pid"] = item[templateInfo.ColumnData.parentField]);
            }

            // 数据解析
            if (templateInfo.SingleFormData.Any(x => x.__config__.templateJson != null && x.__config__.templateJson.Any()))
            {
                realList.Rows = await _formDataParsing.GetKeyData(templateInfo.SingleFormData.Where(x => x.__config__.templateJson != null && x.__config__.templateJson.Any()).ToList(),
                    (List<Dictionary<string, object>>)realList.Rows,
                    templateInfo.ColumnData, actionType, templateInfo.WebType, primaryKey, entity.isShortLink, input.isConvertData);
            }

            realList.Rows = await _formDataParsing.GetKeyData(templateInfo.SingleFormData.Where(x => x.__config__.templateJson == null || !x.__config__.templateJson.Any()).ToList(),
                (List<Dictionary<string, object>>)realList.Rows,
                templateInfo.ColumnData, actionType, templateInfo.WebType, primaryKey, entity.isShortLink, input.isConvertData);

            // 如果是无表数据并且排序字段不为空，再进行数据排序
            if (!templateInfo.IsHasTable && input.sidx.IsNotEmptyOrNull())
            {
                if (input.sort == "desc")
                {
                    realList.Rows = realList.Rows.OrderByDescending(x =>
                    {
                        IDictionary<string, object> dic = x;
                        dic.GetOrAdd(input.sidx, () => null);
                        return dic[input.sidx];
                    }).ToList();
                }
                else
                {
                    realList.Rows = realList.Rows.OrderBy(x =>
                    {
                        IDictionary<string, object> dic = x;
                        dic.GetOrAdd(input.sidx, () => null);
                        return dic[input.sidx];
                    }).ToList();
                }
            }
        }

        if (input.dataType == "0" || input.dataType == "2")
        {
            if (string.IsNullOrEmpty(entity.Tables) || "[]".Equals(entity.Tables))
            {
                realList.TotalRows = realList.Rows.Count();
                realList.PageSize = input.pageSize;
                realList.PageNo = input.currentPage;
                realList.Rows = realList.Rows.Skip(input.pageSize * (input.currentPage - 1)).Take(input.pageSize).ToList();
            }

            // 分组表格
            if (templateInfo.ColumnData.type == 3 && _userManager.UserOrigin == "pc" && !entity.isShortLink)
            {
                List<IndexGridFieldModel> showFieldList = templateInfo.ColumnData.columnList.FindAll(x => x.__vModel__.ToLower() != templateInfo.ColumnData.groupField.ToLower());
                string groupShowField = showFieldList.Any(it => <EMAIL>("left")) ? showFieldList.First(it => <EMAIL>("left")).__vModel__ : showFieldList.First().__vModel__;
                realList.Rows = CodeGenHelper.GetGroupList((List<Dictionary<string, object>>)realList.Rows, templateInfo.ColumnData.groupField, groupShowField);
            }

            // 树形表格
            if (templateInfo.ColumnData.type.Equals(5))
            {
                realList.Rows = CodeGenHelper.GetTreeList((List<Dictionary<string, object>>)realList.Rows, templateInfo.ColumnData.parentField + "_pid",
                    templateInfo.ColumnData.columnList.Find(x => x.__vModel__.ToLower() != templateInfo.ColumnData.parentField.ToLower()).__vModel__);
            }
        }
        else
        {
            if (string.IsNullOrEmpty(entity.Tables) || "[]".Equals(entity.Tables))
            {
                realList.TotalRows = realList.Rows.Count();
                realList.PageSize = input.pageSize;
                realList.PageNo = input.currentPage;
                realList.Rows = realList.Rows.ToList();
            }

            // 分组表格
            if (templateInfo.ColumnData.type == 3 && _userManager.UserOrigin == "pc")
            {
                List<IndexGridFieldModel> showFieldList = templateInfo.ColumnData.columnList.FindAll(x => x.__vModel__.ToLower() != templateInfo.ColumnData.groupField.ToLower());
                string groupShowField = showFieldList.Any(it => <EMAIL>("left")) ? showFieldList.First(it => <EMAIL>("left")).__vModel__ : showFieldList.First().__vModel__;
                realList.Rows = CodeGenHelper.GetGroupList((List<Dictionary<string, object>>)realList.Rows, templateInfo.ColumnData.groupField, groupShowField);
            }
        }

        // 增加前端回显字段 : key_name
        if (!entity.isShortLink && templateInfo.ColumnData.type.Equals(4) && _userManager.UserOrigin.Equals("pc"))
        {
            List<Dictionary<string, object>> newList = new();
            realList.Rows.ToList().ForEach(items =>
            {
                Dictionary<string, object> newItem = new();
                foreach (KeyValuePair<string, object> item in items)
                {
                    if (item.Key.Contains(roweditId))
                    {
                        if (item.Value.IsNotEmptyOrNull())
                        {
                            object? obj = item.Value;
                            if (obj.ToString().Contains("[["))
                            {
                                obj = item.Value.ToString().ToObjectOld<List<List<object>>>();
                            }
                            else if (obj.ToString().Contains("["))
                            {
                                obj = item.Value.ToString().ToObjectOld<List<object>>();
                            }

                            object? value = items.FirstOrDefault(x => x.Key == item.Key.Replace(roweditId, string.Empty)).Value;
                            if (value.IsNullOrEmpty())
                            {
                                obj = null;
                            }

                            if (!newItem.ContainsKey(item.Key.Replace(roweditId, string.Empty)))
                            {
                                newItem.Add(item.Key.Replace(roweditId, string.Empty), obj);
                            }

                            if (!newItem.ContainsKey(item.Key.Replace(roweditId, string.Empty) + "_name"))
                            {
                                newItem.Add(item.Key.Replace(roweditId, string.Empty) + "_name", value);
                            }
                        }
                        else
                        {
                            if (!newItem.ContainsKey(item.Key.Replace(roweditId, string.Empty)))
                            {
                                newItem.Add(item.Key.Replace(roweditId, string.Empty), null);
                            }

                            if (!newItem.ContainsKey(item.Key.Replace(roweditId, string.Empty) + "_name"))
                            {
                                newItem.Add(item.Key.Replace(roweditId, string.Empty) + "_name", null);
                            }
                        }
                    }

                    if (item.Key.Equals("flowState") || item.Key.Equals("flowState_name") || item.Key.Equals("flowId") || item.Key.Equals("flowId_name"))
                    {
                        newItem.Add(item.Key, item.Value);
                    }

                    if (item.Key.Equals("id") && !newItem.ContainsKey(item.Key))
                    {
                        newItem.Add(item.Key, item.Value);
                    }

                    if (templateInfo.AllFieldsModel.Any(x => x.__vModel__.Equals(item.Key) && (x.__config__.jnpfKey.Equals(JnpfKeyConst.TIME)
                                                                                               || x.__config__.jnpfKey.Equals(JnpfKeyConst.CREATETIME) ||
                                                                                               x.__config__.jnpfKey.Equals(JnpfKeyConst.CREATEUSER) ||
                                                                                               x.__config__.jnpfKey.Equals(JnpfKeyConst.MODIFYTIME)
                                                                                               || x.__config__.jnpfKey.Equals(JnpfKeyConst.MODIFYUSER) ||
                                                                                               x.__config__.jnpfKey.Equals(JnpfKeyConst.CURRORGANIZE))))
                    {
                        newItem[item.Key] = items[item.Key];
                    }
                }

                newList.Add(newItem);
            });
            realList.Rows = newList;
        }

        // 集成助手所需流程表单已审核通过的数据
        List<Dictionary<string, object>> processReviewCompletedDic = new();
        if (input.isProcessReviewCompleted.Equals(1))
        {
            foreach (Dictionary<string, object> item in realList.Rows)
            {
                if (item.ContainsKey("flowState") && item["flowState"].Equals(2))
                {
                    processReviewCompletedDic.Add(item);
                }
            }

            realList.Rows = processReviewCompletedDic;
        }

        // 集成助手所需只有 id 的数据
        List<Dictionary<string, object>> onlyIdDic = new();
        if (input.isOnlyId.Equals(1))
        {
            foreach (Dictionary<string, object> item in realList.Rows)
            {
                Dictionary<string, object> idDic = new();
                if (item.ContainsKey("id"))
                {
                    idDic["id"] = item["id"];
                    onlyIdDic.Add(idDic);
                }
            }

            realList.Rows = onlyIdDic;
        }

        return realList;
    }

    /// <summary>
    ///     关联表单列表数据处理.
    /// </summary>
    /// <param name="entity">功能实体.</param>
    /// <param name="input">查询参数.</param>
    /// <param name="actionType"></param>
    /// <param name="dataFilteringModel">自定义过滤</param>
    /// <returns></returns>
    public async Task<SqlSugarPagedList<Dictionary<string, object>>> GetRelationFormList(VisualDevEntity entity, VisualDevModelListQueryInput input, string actionType = "List"
        ,DataFilteringModel ? dataFilteringModel = null)
    {
        SqlSugarPagedList<Dictionary<string, object>>? realList = new() { Rows = new List<Dictionary<string, object>>() }; // 返回结果集
        TemplateParsingBase? templateInfo = new(entity,dataFilteringModel:dataFilteringModel); // 解析模板控件
        if (entity.WebType.Equals(4))
        {
            return await GetDataViewResults(templateInfo, input); // 数据视图
        }

        string? primaryKey = "f_id"; // 列表主键

        List<IConditionalModel>? pvalue = new(); // 关联表单调用 数据全部放开

        DbLink link = await GetDbLink(entity.DbLinkId);
        templateInfo.DbLink = link;
        await SyncField(templateInfo); // 同步业务字段
        primaryKey = GetPrimary(link, templateInfo.MainTableName);
        Dictionary<string, string>? tableFieldKeyValue = new(); // 联表查询 表字段名称 对应 前端字段名称 (应对oracle 查询字段长度不能超过30个)

        input.dataRuleJson = _userManager.UserOrigin == "pc" ? templateInfo.DataRuleListJson.ToJson() : templateInfo.AppDataRuleListJson.ToJson(); // 数据过滤
        input.dataRuleJsonCustom =  templateInfo.DataRuleJsonCustom.ToJson(); // 数据过滤
        string? queryJson = input.queryJson;
        input.queryJson = string.Empty;

        string? sql = GetListQuerySql(primaryKey, templateInfo, ref input, ref tableFieldKeyValue, pvalue, true); // 查询sql
        realList = _databaseService.GetInterFaceData(link, sql, input, templateInfo.ColumnData.Adapt<MainBeltViceQueryModel>(), pvalue, tableFieldKeyValue);

        input.queryJson = queryJson;

        // 处理 自增长ID 流程表单 自增长Id转成 流程Id
        if (entity.FlowId.IsNotEmptyOrNull() && entity.EnableFlow.Equals(1) && realList.Rows.Any())
        {
            List<object> ids = realList.Rows.Select(x => x[primaryKey]).ToList();
            Dictionary<string, string> newIds = GetPIdsByFlowIds(link, templateInfo, primaryKey, ids.ToObjectOld<List<string>>(), true);
            realList.Rows.ToList().ForEach(item => item[primaryKey] = newIds.First(x => x.Value.Equals(item[primaryKey].ToString())).Key);
        }

        if (input.sidx.IsNullOrEmpty())
        {
            input.sidx = primaryKey;
        }

        if (realList.Rows.Any())
        {
            if (templateInfo.SingleFormData.Any(x => x.__config__.templateJson != null && x.__config__.templateJson.Any()))
            {
                realList.Rows = await _formDataParsing.GetKeyData(templateInfo.SingleFormData.Where(x => x.__config__.templateJson != null && x.__config__.templateJson.Any()).ToList(),
                    (List<Dictionary<string, object>>)realList.Rows,
                    templateInfo.ColumnData, actionType, templateInfo.WebType, primaryKey);
            }

            realList.Rows = await _formDataParsing.GetKeyData(
                templateInfo.SingleFormData.Where(x => !x.__config__.jnpfKey.Equals(JnpfKeyConst.RELATIONFORM) && (x.__config__.templateJson == null || !x.__config__.templateJson.Any())).ToList(),
                (List<Dictionary<string, object>>)realList.Rows, templateInfo.ColumnData, actionType, templateInfo.WebType.ParseToInt(), primaryKey);

            if (input.queryJson.IsNotEmptyOrNull())
            {
                Dictionary<string, string>? search = input.queryJson.ToObjectOld<Dictionary<string, string>>();
                if (search.FirstOrDefault().Value.IsNotEmptyOrNull())
                {
                    string? keyWord = search.FirstOrDefault().Value;
                    List<string> keyWordList = search.Select(it => it.Key).ToList();
                    List<Dictionary<string, object>>? newList = new();
                    List<string>? columnName = templateInfo.ColumnData.columnList.Select(x => x.prop).ToList();
                    realList.Rows.ToList().ForEach(item =>
                    {
                        if (item.Any(x => columnName.Contains(x.Key) && keyWordList.Contains(x.Key) && x.Value != null && x.Value.ToString().Contains(keyWord)))
                        {
                            newList.Add(item);
                        }
                    });

                    realList.Rows = newList;
                }
                // 二开-自定义过滤
                if (dataFilteringModel != null)
                {
                    
                }
            }
            //

            // 排序
            if (input.sidx.IsNotEmptyOrNull())
            {
                List<string> sidx = input.sidx.Split(",").ToList();

                realList.Rows.ToList().Sort((x, y) =>
                {
                    foreach (string item in sidx)
                    {
                        if (item[0].ToString().Equals("-"))
                        {
                            string itemName = item.Remove(0, 1);
                            if (!x[itemName].Equals(y[itemName]))
                            {
                                return y[itemName].ToString().CompareTo(x[itemName].ToString());
                            }
                        }
                        else
                        {
                            if (!x[item].Equals(y[item]))
                            {
                                return x[item].ToString().CompareTo(y[item].ToString());
                            }
                        }
                    }

                    return 0;
                });
            }
        }

        if (string.IsNullOrEmpty(entity.Tables) || "[]".Equals(entity.Tables))
        {
            realList.TotalRows = realList.Rows.Count();
            realList.PageSize = input.pageSize;
            realList.PageNo = input.currentPage;
            realList.Rows = realList.Rows.ToList();
        }

        return realList;
    }

    /// <summary>
    ///     获取有表详情.
    /// </summary>
    /// <param name="id">主键.</param>
    /// <param name="templateEntity">模板实体.</param>
    /// <param name="isInteAssis">是否为集成助手.</param>
    /// <returns></returns>
    public async Task<Dictionary<string, object>> GetHaveTableInfo(string id, VisualDevEntity templateEntity, bool isInteAssis = false)
    {
        TemplateParsingBase templateInfo = new(templateEntity); // 解析模板控件
        DbLink link = await GetDbLink(templateEntity.DbLinkId);
        var mainPrimary = GetPrimaryField(link, templateInfo.MainTableName);
        await OptimisticLocking(link, templateInfo); // 处理 开启 并发锁定
        if (id.Equals("0") || id.IsNullOrWhiteSpace())
        {
            return new Dictionary<string, object>();
        }

        id = GetPIdsByFlowIds(link, templateInfo, mainPrimary.field, new List<string> { id }).First().Value;
        Dictionary<string, string>? tableFieldKeyValue = new(); // 联表查询 表字段 别名
        tableFieldKeyValue[mainPrimary.field.ToUpper()] = mainPrimary.field;
        if (templateInfo.WebType.Equals(3))
        {
            tableFieldKeyValue["f_flow_id".ToUpper()] = "f_flow_id";
        }

        string sql = GetInfoQuerySql(id, mainPrimary, templateInfo, ref tableFieldKeyValue); // 获取查询Sql
        Dictionary<string, object>? data = _databaseService.GetSqlData(link, sql)
            .ToObjectOld<List<Dictionary<string, object>>>().FirstOrDefault();

        foreach ((string key, object value) in data)
        {
            data[key] = value.GetJsonElementValue();
        }

        if (data == null)
        {
            return null;
        }

        // 记录全部数据
        Dictionary<string, object> dataMap = new();

        // 查询别名转换
        if (templateInfo.AuxiliaryTableFieldsModelList.Any())
        {
            foreach (KeyValuePair<string, object> item in data)
            {
                dataMap.Add(tableFieldKeyValue[item.Key.ToUpper()], item.Value);
            }
        }
        else
        {
            dataMap = data;
        }

        Dictionary<string, object> newDataMap = new();

        dataMap = _formDataParsing.GetTableDataInfo(new List<Dictionary<string, object>> { dataMap }, templateInfo.FieldsModelList, "detail").FirstOrDefault();

        // 处理子表数据
        newDataMap = await GetChildTableData(templateInfo, link, dataMap, newDataMap);

        int dicCount = newDataMap.Keys.Count;
        string[] strKey = new string[dicCount];
        newDataMap.Keys.CopyTo(strKey, 0);
        for (int i = 0; i < strKey.Length; i++)
        {
            FieldsModel? model = templateInfo.FieldsModelList.Where(m => m.__vModel__ == strKey[i]).FirstOrDefault();
            if (model != null)
            {
                List<Dictionary<string, object>> tables = newDataMap[strKey[i]].ToObjectOld<List<Dictionary<string, object>>>();
                List<Dictionary<string, object>> newTables = new();
                foreach (Dictionary<string, object>? item in tables)
                {
                    Dictionary<string, object> dic = new();
                    foreach (KeyValuePair<string, object> value in item)
                    {
                        FieldsModel? child = model.__config__.children.Find(c => c.__vModel__ == value.Key);
                        if (child != null || value.Key.Equals("id"))
                        {
                            dic.Add(value.Key, value.Value);
                        }
                    }

                    newTables.Add(dic);
                }

                if (newTables.Count > 0)
                {
                    newDataMap[strKey[i]] = newTables;
                }
            }
        }

        foreach (KeyValuePair<string, object> entryMap in dataMap)
        {
            FieldsModel? model = templateInfo.FieldsModelList.Where(m => m.__vModel__.ToLower() == entryMap.Key.ToLower()).FirstOrDefault();
            if (model != null && entryMap.Key.ToLower().Equals(model.__vModel__.ToLower()))
            {
                newDataMap[entryMap.Key] = entryMap.Value;
            }
        }

        if (!newDataMap.ContainsKey("id"))
        {
            newDataMap.Add("id", data[mainPrimary.field]);
        }

        _formDataParsing.GetBARAndQR(templateInfo.FieldsModelList, newDataMap, dataMap); // 处理 条形码 、 二维码 控件
        if (dataMap.ContainsKey("f_flow_id"))
        {
            newDataMap["flowId"] = dataMap["f_flow_id"];
        }

        if (dataMap.ContainsKey("F_FLOW_ID"))
        {
            newDataMap["flowId"] = dataMap["F_FLOW_ID"];
        }

        // 集成助手不用转换数据
        if (isInteAssis)
        {
            return newDataMap;
        }

        return await _formDataParsing.GetSystemComponentsData(templateInfo.FieldsModelList, newDataMap.ToJson());
    }

    /// <summary>
    ///     获取有表详情转换.
    /// </summary>
    /// <param name="id">主键.</param>
    /// <param name="templateEntity">模板实体.</param>
    /// <param name="isFlowTask"></param>
    /// <param name="tenantId">租户id.</param>
    /// <returns></returns>
    public async Task<string> GetHaveTableInfoDetails(string id, VisualDevEntity templateEntity, bool isFlowTask = false, string? tenantId = null)
    {
        TemplateParsingBase? templateInfo = new(templateEntity, isFlowTask); // 解析模板控件
        DbLink link = await GetDbLink(templateEntity.DbLinkId, tenantId);
        var mainPrimary = GetPrimaryField(link, templateInfo.MainTableName);
        id = GetPIdsByFlowIds(link, templateInfo, mainPrimary.field, new List<string> { id }).First().Value;
        Dictionary<string, string>? tableFieldKeyValue = new(); // 联表查询 表字段 别名
        tableFieldKeyValue[mainPrimary.field.ToUpper()] = mainPrimary.field;
        if (templateInfo.WebType.Equals(3))
        {
            tableFieldKeyValue["f_flow_id".ToUpper()] = "f_flow_id";
        }

        string sql = GetInfoQuerySql(id, mainPrimary, templateInfo, ref tableFieldKeyValue); // 获取查询Sql

        Dictionary<string, object>? data = _databaseService.GetSqlData(link, sql).ToObjectOld<List<Dictionary<string, object>>>().FirstOrDefault();
        if (data == null)
        {
            return id;
        }

        // 记录全部数据
        Dictionary<string, object> dataMap = new();

        // 查询别名转换
        if (templateInfo.AuxiliaryTableFieldsModelList.Any())
        {
            foreach (KeyValuePair<string, object> item in data)
            {
                dataMap.Add(tableFieldKeyValue[item.Key.ToUpper()], item.Value);
            }
        }
        else
        {
            dataMap = data;
        }

        Dictionary<string, object> newDataMap = new();

        // 处理子表数据
        newDataMap = await GetChildTableData(templateInfo, link, dataMap, newDataMap, true);

        int dicCount = newDataMap.Keys.Count;
        string[] strKey = new string[dicCount];
        newDataMap.Keys.CopyTo(strKey, 0);
        for (int i = 0; i < strKey.Length; i++)
        {
            FieldsModel? model = templateInfo.FieldsModelList.Find(m => m.__vModel__ == strKey[i]);
            if (model != null)
            {
                List<Dictionary<string, object>> childModelData = new();
                foreach (Dictionary<string, object>? item in newDataMap[strKey[i]].ToObjectOld<List<Dictionary<string, object>>>())
                {
                    Dictionary<string, object> dic = new();
                    foreach (KeyValuePair<string, object> value in item)
                    {
                        FieldsModel? child = model.__config__.children.Find(c => c.__vModel__ == value.Key);
                        if (child != null && value.Value != null)
                        {
                            if (child.__config__.jnpfKey.Equals(JnpfKeyConst.DATE))
                            {
                                string? keyValue = value.Value.ToString();
                                dic.Add(value.Key,
                                    DateTime.TryParse(keyValue, out DateTime _) ? keyValue.ParseToDateTime().ParseToUnixTime() : value.Value.ToString().ParseToDateTime().ParseToUnixTime());
                            }
                            else
                            {
                                dic.Add(value.Key, value.Value);
                            }
                        }
                        else
                        {
                            dic.Add(value.Key, value.Value);
                        }
                    }

                    dic["JnpfKeyConst_MainData"] = data.ToJson();
                    childModelData.Add(dic);
                }

                if (childModelData.Count > 0)
                {
                    // 将关键字查询传输的id转换成名称
                    if (model.__config__.children.Any(x => x.__config__.templateJson != null && x.__config__.templateJson.Any()))
                    {
                        newDataMap[strKey[i]] = await _formDataParsing.GetKeyData(model.__config__.children.Where(x => x.__config__.templateJson != null && x.__config__.templateJson.Any()).ToList(),
                            childModelData, templateInfo.ColumnData, "List", templateInfo.WebType, mainPrimary.field, templateEntity.isShortLink);
                    }

                    newDataMap[strKey[i]] = await _formDataParsing.GetKeyData(model.__config__.children.Where(x => x.__config__.templateJson == null || !x.__config__.templateJson.Any()).ToList(),
                        childModelData, templateInfo.ColumnData.ToObjectOld<ColumnDesignModel>(), "List", templateInfo.WebType, mainPrimary.field, templateEntity.isShortLink);
                }
            }
        }

        List<Dictionary<string, object>> listEntity = new() { dataMap };

        // 控件联动
        Dictionary<string, object>? tempDataMap = new();
        if (templateInfo.SingleFormData.Any(x => x.__config__.templateJson != null && x.__config__.templateJson.Any()))
        {
            tempDataMap = (await _formDataParsing.GetKeyData(templateInfo.SingleFormData.Where(x => x.__config__.templateJson != null && x.__config__.templateJson.Any()).ToList(), listEntity,
                templateInfo.ColumnData, "List", templateInfo.WebType, mainPrimary.field, templateEntity.isShortLink)).FirstOrDefault();
        }

        tempDataMap = (await _formDataParsing.GetKeyData(templateInfo.SingleFormData.Where(x => x.__config__.templateJson == null || !x.__config__.templateJson.Any()).ToList(), listEntity,
            templateInfo.ColumnData, "List", templateInfo.WebType, mainPrimary.field, templateEntity.isShortLink)).FirstOrDefault();

        // 将关键字查询传输的id转换成名称
        foreach (KeyValuePair<string, object> entryMap in tempDataMap)
        {
            if (entryMap.Value != null)
            {
                FieldsModel? model = templateInfo.FieldsModelList.Where(m => m.__vModel__.Contains(entryMap.Key)).FirstOrDefault();
                if (model != null && entryMap.Key.Equals(model.__vModel__))
                {
                    newDataMap[entryMap.Key] = entryMap.Value;
                }
                else if (templateInfo.FieldsModelList.Where(m => m.__vModel__ == entryMap.Key.Replace("_id", string.Empty)).Any())
                {
                    newDataMap[entryMap.Key] = entryMap.Value;
                }
                else if (templateInfo.FieldsModelList.Where(m =>
                             (m.__config__.jnpfKey.Equals(JnpfKeyConst.POPUPATTR) || m.__config__.jnpfKey.Equals(JnpfKeyConst.RELATIONFORMATTR)) && entryMap.Key.Contains(m.showField)).Any())
                {
                    newDataMap[entryMap.Key] = entryMap.Value;
                }
            }
        }

        _formDataParsing.GetBARAndQR(templateInfo.FieldsModelList, newDataMap, dataMap); // 处理 条形码 、 二维码 控件

        if (!newDataMap.ContainsKey("id"))
        {
            newDataMap.Add("id", id);
        }

        foreach ((string key, object value) in newDataMap)
        {
            newDataMap[key] = value.GetJsonElementValue();
        }

        return newDataMap.ToJson();
    }

    #endregion

    #region Post

    /// <summary>
    ///     创建在线开发功能.
    /// </summary>
    /// <param name="templateEntity">功能模板实体.</param>
    /// <param name="dataInput">数据输入.</param>
    /// <param name="tenantId">租户Id.</param>
    /// <returns></returns>
    public async Task<string> Create(VisualDevEntity templateEntity, VisualDevModelDataCrInput dataInput, string? tenantId = null)
    {
        string? mainId = YitIdHelper.NextId().ToString();
        DbLink link = await GetDbLink(templateEntity.DbLinkId, tenantId);
        Dictionary<string, List<Dictionary<string, object>>> haveTableSql = await CreateHaveTableSql(templateEntity, dataInput, mainId, tenantId);

        // 主表自增长Id.
        if (haveTableSql.ContainsKey("MainTableReturnIdentity"))
        {
            haveTableSql.Remove("MainTableReturnIdentity");
        }

        try
        {
            await _db.BeginTranAsync();
            foreach (KeyValuePair<string, List<Dictionary<string, object>>> item in haveTableSql)
            {
                await _databaseService.ExecuteSql(link, item.Key, item.Value); // 新增功能数据
            }

            // 添加集成助手`事件触发`新增事件
            if (tenantId.IsNotEmptyOrNull())
            {
                dataInput.isInteAssis = false;
            }

            if (dataInput.isInteAssis)
            {
                await _eventPublisher.PublishAsync(new InteEventSource("Inte:CreateInte", _userManager.UserId.ToString(), _userManager.TenantId.ToString(), new InteAssiEventModel
                {
                    ModelId = templateEntity.Id.ToString(),
                    Data = dataInput.data,
                    DataId = mainId,
                    TriggerType = 1
                }));
            }

            await _db.CommitTranAsync();
        }
        catch (Exception e)
        {
            await _db.RollbackTranAsync();
            throw Oops.Oh(ErrorCode.COM1000 + "：" + e.Message);
        }

        return mainId;
    }

    /// <summary>
    ///     创建有表SQL.
    /// </summary>
    /// <param name="templateEntity"></param>
    /// <param name="dataInput"></param>
    /// <param name="mainId"></param>
    /// <param name="tenantId">租户Id.</param>
    /// <returns></returns>
    public async Task<Dictionary<string, List<Dictionary<string, object>>>> CreateHaveTableSql(VisualDevEntity templateEntity, VisualDevModelDataCrInput dataInput, string mainId,
        string? tenantId = null)
    {
        TemplateParsingBase templateInfo = new(templateEntity); // 解析模板控件
        templateInfo.DbLink = await GetDbLink(templateEntity.DbLinkId, tenantId);
        return await GetCreateSqlByTemplate(templateInfo, dataInput, mainId);
    }

    public async Task<Dictionary<string, List<Dictionary<string, object>>>> GetCreateSqlByTemplate(TemplateParsingBase templateInfo, VisualDevModelDataCrInput dataInput, string mainId,
        List<string>? systemControlList = null)
    {
        await SyncField(templateInfo); // 同步业务字段
        Dictionary<string, object>? allDataMap = dataInput.data.ToObjectOld<Dictionary<string, object>>();
        if (!templateInfo.VerifyTemplate())
        {
            throw Oops.Oh(ErrorCode.D1401); // 验证模板
        }

        // 处理系统控件(模板开启行编辑)
        if (templateInfo.ColumnData.type.Equals(4) && _userManager.UserOrigin.Equals("pc"))
        {
            templateInfo.GenerateFields.ForEach(item =>
            {
                if (!allDataMap.ContainsKey(item.__vModel__))
                {
                    allDataMap.Add(item.__vModel__, string.Empty);
                }

                if (item.__config__.jnpfKey.Equals(JnpfKeyConst.CREATETIME) && allDataMap.ContainsKey(item.__vModel__))
                {
                    string? value = allDataMap[item.__vModel__].ToString();
                    allDataMap.Remove(item.__vModel__);
                    allDataMap.Add(item.__vModel__, DateTime.Now.ToString());
                }
            });
        }

        if (templateInfo.visualDevEntity != null && !templateInfo.visualDevEntity.isShortLink)
        {
            allDataMap = await GenerateFeilds(templateInfo.FieldsModelList.ToJson(), allDataMap, true, systemControlList); // 生成系统自动生成字段
        }

        DbLink link = templateInfo.DbLink;

        List<DbTableFieldModel>? tableList = _databaseService.GetFieldList(link, templateInfo.MainTableName); // 获取主表 表结构 信息
        DbTableFieldModel? mainPrimary = tableList.Find(t => t.primaryKey); // 主表主键
        string? dbType = link?.DbType != null ? link.DbType.ToString() : _visualDevRepository.AsSugarClient().CurrentConnectionConfig.DbType.ToString();

        // 验证唯一值
        UniqueVerify(link, templateInfo, allDataMap, mainPrimary?.field, mainId);

        // 新增SQL
        Dictionary<string, List<Dictionary<string, object>>> dictionarySql = new();
        Dictionary<string, object> tableField = new(); // 字段和值
        templateInfo?.MainTableFieldsModelList.ForEach(item =>
        {
            if (allDataMap.ContainsKey(item.__vModel__))
            {
                object? itemData = allDataMap[item.__vModel__];
                if (item.__vModel__.IsNotEmptyOrNull() && itemData != null && !string.IsNullOrEmpty(itemData.ToString()) && itemData.ToString() != "[]")
                {
                    object value = _formDataParsing.InsertValueHandle(dbType, tableList, item.__vModel__, itemData, templateInfo.MainTableFieldsModelList, "create",
                        templateInfo.visualDevEntity != null ? templateInfo.visualDevEntity.isShortLink : false);
                    tableField.Add(item.__vModel__, value);
                }
            }
        });

        if (_tenant.MultiTenancy)
        {
            GlobalTenantCacheModel? tenantCache = _cacheManager.Get<List<GlobalTenantCacheModel>>(CommonConst.GLOBALTENANT).Find(it => it.TenantId.Equals(link.Id));
            if (tenantCache.IsNotEmptyOrNull() && tenantCache.type.Equals(1))
            {
                tableField.Add("f_tenant_id", tenantCache.connectionConfig.IsolationField); // 多租户
            }
        }

        // 集成助手数据标识
        if (allDataMap.ContainsKey("f_inte_assistant") && allDataMap["f_inte_assistant"].IsNotEmptyOrNull())
        {
            tableField.Add("f_inte_assistant", allDataMap["f_inte_assistant"]);
        }

        // 主键策略(雪花Id)
        if (templateInfo.FormModel.primaryKeyPolicy.Equals(1))
        {
            tableField.Add(mainPrimary?.field, mainId);
        }

        // 前端空提交
        if (!tableField.Any())
        {
            tableField.Add(tableList.Where(x => !x.primaryKey).First().field, null);
        }

        // 拼接主表 sql
        dictionarySql.Add(templateInfo.MainTableName, new List<Dictionary<string, object>> { tableField });

        // 流程表单 需要增加字段 f_flow_task_id
        if (templateInfo.visualDevEntity != null && templateInfo.visualDevEntity.EnableFlow.Equals(1))
        {
            if (!tableList.Any(x => SqlFunc.ToLower(x.field) == "f_flow_task_id"))
            {
                List<DbTableFieldModel>? pFieldList = new()
                    { new DbTableFieldModel { field = "f_flow_task_id", fieldName = "流程任务Id", dataType = "varchar", dataLength = "50", allowNull = 1 } };
                _databaseService.AddTableColumn(link, templateInfo.MainTableName, pFieldList);
            }

            if (!tableList.Any(x => SqlFunc.ToLower(x.field) == "f_flow_id"))
            {
                List<DbTableFieldModel> pFieldList = new() { new DbTableFieldModel { field = "f_flow_id", fieldName = "流程引擎Id", dataType = "varchar", dataLength = "50", allowNull = 1 } };
                _databaseService.AddTableColumn(link, templateInfo.MainTableName, pFieldList);
            }

            dictionarySql[templateInfo.MainTableName].First().Add("f_flow_task_id", mainId);
            dictionarySql[templateInfo.MainTableName].First().Add("f_flow_id", allDataMap["flowId"]);
        }

        // 自增长主键 需要返回的自增id
        if (templateInfo.FormModel.primaryKeyPolicy.Equals(2))
        {
            KeyValuePair<string, List<Dictionary<string, object>>> mainSql = dictionarySql.First();
            mainId = _databaseService.ExecuteReturnIdentity(link, mainSql.Key, mainSql.Value).ToString();
            if (mainId.Equals("0"))
            {
                throw Oops.Oh(ErrorCode.D1402);
            }

            tableField.Clear();
            dictionarySql.Clear();
            tableField.Add("ReturnIdentity", mainId);
            dictionarySql.Add("MainTableReturnIdentity", new List<Dictionary<string, object>> { tableField });
        }

        // 拼接副表 sql
        if (templateInfo.AuxiliaryTableFieldsModelList.Any())
        {
            templateInfo.AuxiliaryTableFieldsModelList.Select(x => x.__config__.tableName).Distinct().ToList().ForEach(tbname =>
            {
                tableField = new Dictionary<string, object>();

                // 主键策略(雪花Id)
                if (templateInfo.FormModel.primaryKeyPolicy.Equals(1))
                {
                    tableField.Add(_databaseService.GetFieldList(link, tbname)?.Find(x => x.primaryKey).field, YitIdHelper.NextId().ToString());
                }

                // 外键
                tableField.Add(templateInfo?.AllTable?.Find(t => t.table == tbname).tableField, mainId);

                // 字段
                templateInfo.AuxiliaryTableFieldsModelList.Select(x => x.__vModel__).Where(x => x.Contains("jnpf_" + tbname + "_jnpf_")).ToList().ForEach(item =>
                {
                    object? itemData = allDataMap.Where(x => x.Key == item).Count() > 0 ? allDataMap[item] : null;
                    if (item.IsNotEmptyOrNull() && itemData != null && !string.IsNullOrEmpty(itemData.ToString()) && itemData.ToString() != "[]")
                    {
                        object value = _formDataParsing.InsertValueHandle(dbType, tableList, item, allDataMap[item], templateInfo.FieldsModelList, "create",
                            templateInfo.visualDevEntity != null ? templateInfo.visualDevEntity.isShortLink : false);
                        tableField.Add(item.ReplaceRegex(@"(\w+)_jnpf_", string.Empty), value);
                    }
                });

                dictionarySql.Add(tbname, new List<Dictionary<string, object>> { tableField });
            });
        }

        // 拼接子表 sql
        foreach (string? item in allDataMap.Where(d => d.Key.ToLower().Contains("tablefield")).Select(d => d.Key).ToList())
        {
            if (!templateInfo.AllFieldsModel.Any(x => x.__vModel__.Equals(item)) || !templateInfo.AllFieldsModel.Find(x => x.__vModel__.Equals(item)).__config__.jnpfKey.Equals(JnpfKeyConst.TABLE))
            {
                continue;
            }

            // 查找到该控件数据
            object? objectData = allDataMap[item];
            List<Dictionary<string, object>>? model = objectData.ToObjectOld<List<Dictionary<string, object>>>();
            if (model != null && model.Count > 0)
            {
                // 利用key去找模板
                FieldsModel? fieldsModel = templateInfo.FieldsModelList.Find(f => f.__vModel__ == item);
                TableModel? childTable = templateInfo.AllTable.Find(t => t.table == fieldsModel.__config__.tableName);
                tableList = new List<DbTableFieldModel>();
                tableList = _databaseService.GetFieldList(link, childTable?.table);
                DbTableFieldModel? childPrimary = tableList.Find(t => t.primaryKey);
                foreach (Dictionary<string, object>? data in model)
                {
                    tableField = new Dictionary<string, object>();

                    // 主键策略(雪花Id)
                    if (templateInfo.FormModel.primaryKeyPolicy.Equals(1))
                    {
                        tableField.Add(childPrimary.field, YitIdHelper.NextId().ToString());
                    }

                    // 外键
                    tableField.Add(childTable.tableField, mainId);

                    // 字段
                    foreach (KeyValuePair<string, object> child in data)
                    {
                        if (child.Key.Equals("id") && child.Value.IsNotEmptyOrNull())
                        {
                            tableField[childPrimary.field] = child.Value;
                        }
                        else if (child.Key.IsNotEmptyOrNull() && child.Value.IsNotEmptyOrNull() && child.Value.ToString() != "[]")
                        {
                            object value = _formDataParsing.InsertValueHandle(dbType, tableList, child.Key, child.Value, fieldsModel?.__config__.children, "create",
                                templateInfo.visualDevEntity != null ? templateInfo.visualDevEntity.isShortLink : false);
                            tableField.Add(child.Key, value);
                        }
                    }

                    if (dictionarySql.ContainsKey(fieldsModel.__config__.tableName))
                    {
                        dictionarySql[fieldsModel.__config__.tableName].Add(tableField);
                    }
                    else
                    {
                        dictionarySql.Add(fieldsModel.__config__.tableName, new List<Dictionary<string, object>> { tableField });
                    }
                }
            }
        }

        // 处理 开启 并发锁定
        await OptimisticLocking(link, templateInfo);
        return dictionarySql;
    }

    /// <summary>
    ///     修改在线开发功能.
    /// </summary>
    /// <param name="id">修改ID.</param>
    /// <param name="templateEntity"></param>
    /// <param name="visualdevModelDataUpForm"></param>
    /// <returns></returns>
    public async Task Update(string id, VisualDevEntity templateEntity, VisualDevModelDataUpInput visualdevModelDataUpForm)
    {
        TemplateParsingBase templateInfo = new(templateEntity); // 解析模板控件
        if (templateInfo.ColumnData.type.Equals(4) && _userManager.UserOrigin.Equals("pc"))
        {
            // 剔除 [增加前端回显字段 : key_name]
            Dictionary<string, object> oldDataMap = visualdevModelDataUpForm.data.ToObjectOld<Dictionary<string, object>>();
            Dictionary<string, object> newDataMap = new();
            foreach (KeyValuePair<string, object> item in oldDataMap)
            {
                string key = item.Key.Substring(0, item.Key.LastIndexOf("_name") != -1 ? item.Key.LastIndexOf("_name") : item.Key.Length);
                if (!newDataMap.ContainsKey(key) && oldDataMap.ContainsKey(key))
                {
                    newDataMap.Add(key, oldDataMap[key]);
                }
            }

            if (newDataMap.Any())
            {
                visualdevModelDataUpForm.data = newDataMap.ToJson();
            }
        }

        DbLink link = await GetDbLink(templateEntity.DbLinkId);
        List<string> haveTableSql = await UpdateHaveTableSql(templateEntity, visualdevModelDataUpForm, id);

        try
        {
            await _db.BeginTranAsync();
            foreach (string item in haveTableSql)
            {
                await _databaseService.ExecuteSql(link, item); // 修改功能数据
            }

            // 添加集成助手`事件触发`修改事件
            if (visualdevModelDataUpForm.isInteAssis)
            {
                await _eventPublisher.PublishAsync(new InteEventSource("Inte:CreateInte", _userManager.UserId.ToString(), _userManager.TenantId.ToString(), new InteAssiEventModel
                {
                    ModelId = templateEntity.Id.ToString(),
                    Data = visualdevModelDataUpForm.data,
                    DataId = visualdevModelDataUpForm.id,
                    TriggerType = 2
                }));
            }

            await _db.CommitTranAsync();
        }
        catch (Exception ex)
        {
            await _db.RollbackTranAsync();
            throw Oops.Oh(ErrorCode.COM1001 + "：" + ex.Message);
        }
    }

    /// <summary>
    ///     批量修改在线开发功能（集成助手用）.
    /// </summary>
    /// <param name="ids"></param>
    /// <param name="templateEntity"></param>
    /// <param name="visualdevModelDataUpForm"></param>
    /// <returns></returns>
    public async Task BatchUpdate(List<string>? ids, VisualDevEntity templateEntity, VisualDevModelDataUpInput visualdevModelDataUpForm)
    {
        TemplateParsingBase templateInfo = new(templateEntity); // 解析模板控件
        DbLink link = await GetDbLink(templateEntity.DbLinkId);

        Dictionary<string, object> data = visualdevModelDataUpForm.data.ToObjectOld<Dictionary<string, object>>();
        List<string> updateSql = new();
        string? mainTableName = string.Empty;
        string mainPrimary = GetPrimary(link, templateInfo.MainTableName);
        Dictionary<string, string> mainData = new();
        Dictionary<string, object> viceTableName = new();
        Dictionary<string, string> viceData = new();
        foreach (KeyValuePair<string, object> dataItem in data)
        {
            if (templateInfo.FieldsModelList.Select(it => it.__vModel__).ToList().Contains(dataItem.Key))
            {
                TableModel? mainTable = templateInfo.AllTable.Where(it => it.typeId.Equals("1")).FirstOrDefault();
                mainTableName = mainTable.table;
                if (dataItem.Value.IsNotEmptyOrNull())
                {
                    mainData.Add(dataItem.Key, dataItem.Value.ToString());
                }
                else
                {
                    mainData.Add(dataItem.Key, string.Empty);
                }
            }
            else if (templateInfo.AuxiliaryTableFieldsModelList.Select(it => it.__vModel__).ToList().Contains(dataItem.Key))
            {
                TableModel? viceTable = templateInfo.AllTable.Where(it => dataItem.Key.Contains(it.table)).FirstOrDefault();
                if (!viceTableName.ContainsKey(viceTable.table))
                {
                    viceTableName.Add(viceTable.table, viceTable.tableField);
                }

                if (dataItem.Value.IsNotEmptyOrNull())
                {
                    viceData.Add(dataItem.Key, dataItem.Value.ToString());
                }
                else
                {
                    viceData.Add(dataItem.Key, string.Empty);
                }
            }
        }

        // 主表拼接Sql
        if (mainTableName.IsNotEmptyOrNull() && mainData.Any())
        {
            string dataSql = string.Empty;
            foreach (KeyValuePair<string, string> item in mainData)
            {
                if (item.Equals(mainData.FirstOrDefault()))
                {
                    dataSql = $"{item.Key}='{item.Value}'";
                }
                else
                {
                    dataSql = $"{dataSql},{item.Key}='{item.Value}'";
                }
            }

            if (ids.IsNotEmptyOrNull() && ids.Any())
            {
                updateSql.Add($"update {mainTableName} set {dataSql} where {mainPrimary} in ({string.Join(",", ids)})");
            }
            else
            {
                updateSql.Add($"update {mainTableName} set {dataSql}");
            }
        }

        // 主表拼接Sql
        if (viceTableName.Any() && viceData.Any())
        {
            foreach (KeyValuePair<string, object> tableName in viceTableName)
            {
                string dataSql = string.Empty;
                foreach (KeyValuePair<string, string> item in viceData)
                {
                    if (item.Key.Contains(tableName.Key))
                    {
                        if (item.Equals(viceData.FirstOrDefault()))
                        {
                            dataSql = $"{item.Key.Split("_jnpf_").LastOrDefault()}='{item.Value}'";
                        }
                        else
                        {
                            dataSql = $"{dataSql},{item.Key.Split("_jnpf_").LastOrDefault()}='{item.Value}'";
                        }
                    }
                }

                if (ids.IsNotEmptyOrNull() && ids.Any())
                {
                    updateSql.Add($"update {tableName.Key} set {dataSql} where {tableName.Value} in ({string.Join(",", ids)})");
                }
                else
                {
                    updateSql.Add($"update {tableName.Key} set {dataSql}");
                }
            }
        }

        await _db.BeginTranAsync();
        foreach (string item in updateSql)
        {
            await _databaseService.ExecuteSql(link, item); // 执行修改Sql
        }

        await _db.CommitTranAsync();
    }

    /// <summary>
    ///     修改有表SQL.
    /// </summary>
    /// <param name="templateEntity"></param>
    /// <param name="visualdevModelDataUpForm"></param>
    /// <param name="id"></param>
    /// <returns></returns>
    public async Task<List<string>> UpdateHaveTableSql(VisualDevEntity templateEntity, VisualDevModelDataUpInput visualdevModelDataUpForm, string id)
    {
        TemplateParsingBase templateInfo = new(templateEntity); // 解析模板控件
        templateInfo.DbLink = await GetDbLink(templateEntity.DbLinkId);
        return await GetUpdateSqlByTemplate(templateInfo, visualdevModelDataUpForm, id);
    }

    public async Task<List<string>> GetUpdateSqlByTemplate(TemplateParsingBase templateInfo, VisualDevModelDataUpInput visualdevModelDataUpForm, string id, List<string>? systemControlList = null)
    {
        await SyncField(templateInfo); // 同步业务字段
        Dictionary<string, object>? allDataMap = visualdevModelDataUpForm.data.ToObjectOld<Dictionary<string, object>>();
        if (!templateInfo.VerifyTemplate())
        {
            throw Oops.Oh(ErrorCode.D1401); // 验证模板
        }

        // 处理系统控件(模板开启行编辑)
        if (templateInfo.ColumnData.type.Equals(4) && _userManager.UserOrigin.Equals("pc"))
        {
            // 处理显示列和提交的表单数据匹配(行编辑空数据 前端会过滤该控件)
            templateInfo.ColumnData.columnList.Where(x => !allDataMap.ContainsKey(x.prop) && x.__config__.visibility.Equals("pc")).ToList()
                .ForEach(item => allDataMap.Add(item.prop, string.Empty));

            templateInfo.GenerateFields.ForEach(item =>
            {
                if (!allDataMap.ContainsKey(item.__vModel__))
                {
                    allDataMap.Add(item.__vModel__, string.Empty);
                }

                if (item.__config__.jnpfKey.Equals(JnpfKeyConst.CREATETIME) && allDataMap.ContainsKey(item.__vModel__))
                {
                    string? value = allDataMap[item.__vModel__].ToString();
                    allDataMap.Remove(item.__vModel__);
                    DateTime dtDate;
                    if (DateTime.TryParse(value, out dtDate))
                    {
                        value = string.Format("{0:yyyy-MM-dd HH:mm:ss} ", value);
                    }
                    else
                    {
                        value = string.Format("{0:yyyy-MM-dd HH:mm:ss} ", value.ParseToDateTime());
                    }

                    allDataMap.Add(item.__vModel__, value);
                }
            });
        }

        allDataMap = await GenerateFeilds(templateInfo.FieldsModelList.ToJson(), allDataMap, false, systemControlList); // 生成系统自动生成字段
        DbLink link = templateInfo.DbLink;
        List<DbTableFieldModel>? tableList = _databaseService.GetFieldList(link, templateInfo.MainTableName); // 获取主表 表结构 信息
        DbTableFieldModel? mainPrimary = tableList.Find(t => t.primaryKey); // 主表主键
        string? dbType = link?.DbType != null ? link.DbType.ToString() : _visualDevRepository.AsSugarClient().CurrentConnectionConfig.DbType.ToString();
        id = GetPIdsByFlowIds(link, templateInfo, mainPrimary.field, new List<string> { id }).First().Value;

        // 验证唯一值
        UniqueVerify(link, templateInfo, allDataMap, mainPrimary?.field, id, true);

        // 主表查询语句
        List<string> mainSql = new();
        List<string> fieldSql = new(); // key 字段名, value 修改值

        // 拼接主表 sql
        templateInfo?.MainTableFieldsModelList.ForEach(item =>
        {
            if (item.__vModel__.IsNotEmptyOrNull() && allDataMap.ContainsKey(item.__vModel__))
            {
                fieldSql.Add(string.Format("{0}={1}", item.__vModel__,
                    _formDataParsing.InsertValueHandle(dbType, tableList, item.__vModel__, allDataMap[item.__vModel__], templateInfo.MainTableFieldsModelList, "update")));
            }
        });

        if (allDataMap.ContainsKey("f_flow_id") && allDataMap["f_flow_id"].IsNotEmptyOrNull())
        {
            fieldSql.Add(string.Format("{0}='{1}'", "f_flow_id", allDataMap["f_flow_id"]));
        }

        if (fieldSql.Any())
        {
            mainSql.Add(string.Format("update {0} set {1} where {2}='{3}';", templateInfo?.MainTableName, string.Join(",", fieldSql), mainPrimary?.field, id));
        }

        // 拼接副表 sql
        if (templateInfo.AuxiliaryTableFieldsModelList.Any())
        {
            templateInfo.AuxiliaryTableFieldsModelList.Select(x => x.__config__.tableName).Distinct().ToList().ForEach(tbname =>
            {
                List<DbTableFieldModel>? tableAllField = _databaseService.GetFieldList(link, tbname); // 数据库里获取表的所有字段

                List<string>? tableFieldList = templateInfo.AuxiliaryTableFieldsModelList.Where(x => x.__config__.tableName.Equals(tbname)).Select(x => x.__vModel__).ToList();

                fieldSql.Clear(); // key 字段名, value 修改值
                templateInfo.AuxiliaryTableFieldsModelList.Where(x => x.__config__.tableName.Equals(tbname)).Select(x => x.__vModel__).ToList().ForEach(item =>
                {
                    // 前端未填写数据的字段，默认会找不到字段名，需要验证
                    if (item.IsNotEmptyOrNull() && allDataMap.Where(x => x.Key == item).Count() > 0)
                    {
                        fieldSql.Add(string.Format("{0}={1}", item.ReplaceRegex(@"(\w+)_jnpf_", string.Empty),
                            _formDataParsing.InsertValueHandle(dbType, tableList, item, allDataMap[item], templateInfo.FieldsModelList, "update")));
                    }
                });

                string tableField = templateInfo.AllTable.Find(t => t.table.Equals(tbname)).tableField;
                if (fieldSql.Any())
                {
                    mainSql.Add(string.Format("update {0} set {1} where {2}='{3}';", tbname, string.Join(",", fieldSql), tableField, id));
                }
            });
        }

        // 非行编辑
        if (!templateInfo.ColumnData.type.Equals(4) || !_userManager.UserOrigin.Equals("pc"))
        {
            // 删除子表数据
            if (templateInfo.AllTable.Any(x => x.typeId.Equals("0")))
            {
                // 拼接子表 sql
                foreach (string? item in allDataMap.Where(d => d.Key.ToLower().Contains("tablefield")).Select(d => d.Key).ToList())
                {
                    if (!templateInfo.AllFieldsModel.Any(x => x.__vModel__.Equals(item)) ||
                        !templateInfo.AllFieldsModel.Find(x => x.__vModel__.Equals(item)).__config__.jnpfKey.Equals(JnpfKeyConst.TABLE))
                    {
                        continue;
                    }

                    // 查找到该控件数据
                    List<Dictionary<string, object>>? modelData = allDataMap[item].ToObjectOld<List<Dictionary<string, object>>>();

                    // 利用key去找模板
                    FieldsModel? fieldsModel = templateInfo.FieldsModelList.Find(f => f.__vModel__ == item);
                    ConfigModel? fieldsConfig = fieldsModel?.__config__;
                    List<string>? childColumn = new();
                    List<object>? childValues = new();
                    List<string>? updateFieldSql = new();
                    TableModel? childTable = templateInfo.AllTable.Find(t => t.table == fieldsModel.__config__.tableName && t.table != templateInfo.MainTableName);
                    if (childTable != null)
                    {
                        if (modelData != null && modelData.Count > 0)
                        {
                            if (!modelData.Any(x => x.ContainsKey("id")))
                            {
                                mainSql.Add(string.Format("delete from {0} where {1}='{2}';", childTable?.table, childTable.tableField, id));
                            }
                            else
                            {
                                List<string> ctIdList = modelData.Where(x => x.ContainsKey("id")).Select(x => x["id"]).ToObjectOld<List<string>>();
                                string querStr = string.Format("select {0} id from {1} where {0} in('{2}') ", childTable.fields.First(x => x.PrimaryKey).Field, childTable?.table,
                                    string.Join("','", ctIdList));
                                List<Dictionary<string, string>> res = _databaseService.GetSqlData(link, querStr).ToObjectOld<List<Dictionary<string, string>>>();
                                foreach (Dictionary<string, object> it in modelData.Where(x => x.ContainsKey("id")))
                                {
                                    if (!res.Any(x => x["id"].Equals(it["id"])))
                                    {
                                        it.Remove("id");
                                    }
                                }

                                mainSql.Add(string.Format("delete from {0} where {1} not in ('{2}') and {3}='{4}';", childTable?.table, childTable.fields.First(x => x.PrimaryKey).Field,
                                    string.Join("','", modelData.Where(x => x.ContainsKey("id")).Select(x => x["id"]).ToList()), childTable.tableField, id));
                            }

                            tableList = new List<DbTableFieldModel>();
                            tableList = _databaseService.GetFieldList(link, childTable?.table);
                            DbTableFieldModel? childPrimary = tableList.Find(t => t.primaryKey);
                            foreach (Dictionary<string, object>? data in modelData)
                            {
                                if (data.Count > 0)
                                {
                                    foreach (KeyValuePair<string, object> child in data)
                                    {
                                        if (child.Key.IsNotEmptyOrNull() && child.Key != "id")
                                        {
                                            childColumn.Add(child.Key); // Column部分
                                            object value = _formDataParsing.InsertValueHandle(dbType, tableList, child.Key, child.Value, fieldsConfig.children, "update");
                                            childValues.Add(value); // Values部分
                                            updateFieldSql.Add(string.Format("{0}={1}", child.Key, value));
                                        }
                                    }

                                    if (childColumn.Any())
                                    {
                                        if (data.ContainsKey("id"))
                                        {
                                            if (updateFieldSql.Any())
                                            {
                                                mainSql.Add(string.Format("update {0} set {1} where {2}='{3}';", fieldsModel.__config__.tableName, string.Join(',', updateFieldSql),
                                                    childPrimary.field,
                                                    data["id"]));
                                            }
                                        }
                                        else
                                        {
                                            // 主键策略(雪花Id)
                                            if (templateInfo.FormModel.primaryKeyPolicy.Equals(1))
                                            {
                                                mainSql.Add(string.Format(
                                                    "insert into {0}({6},{4}{1}) values('{3}','{5}'{2});",
                                                    fieldsModel.__config__.tableName,
                                                    childColumn.Any() ? "," + string.Join(",", childColumn) : string.Empty,
                                                    childColumn.Any() ? "," + string.Join(",", childValues) : string.Empty,
                                                    YitIdHelper.NextId().ToString(),
                                                    childTable.tableField,
                                                    id,
                                                    childPrimary.field));
                                            }
                                            else
                                            {
                                                mainSql.Add(string.Format(
                                                    "insert into {0}({1}{2}) values('{3}'{4});",
                                                    fieldsModel.__config__.tableName,
                                                    childTable.tableField,
                                                    childColumn.Any() ? "," + string.Join(",", childColumn) : string.Empty,
                                                    id,
                                                    childColumn.Any() ? "," + string.Join(",", childValues) : string.Empty));
                                            }
                                        }
                                    }

                                    childColumn.Clear();
                                    childValues.Clear();
                                    updateFieldSql.Clear();
                                }
                            }
                        }
                        else
                        {
                            mainSql.Add(string.Format("delete from {0} where {1}='{2}';", childTable?.table, childTable.tableField, id));
                        }
                    }
                }
            }
        }

        // 处理 开启 并发锁定
        await OptimisticLocking(link, templateInfo, mainSql, allDataMap);

        return mainSql;
    }

    #endregion

    #region 公用方法

    /// <summary>
    ///     删除有表信息.
    /// </summary>
    /// <param name="id">主键</param>
    /// <param name="templateEntity">模板实体</param>
    /// <returns></returns>
    public async Task DelHaveTableInfo(string id, VisualDevEntity templateEntity)
    {
        // if (templateEntity.EnableFlow == 1)
        // {
        //     var flowTask = await _visualDevRepository.AsSugarClient().Queryable<FlowTaskEntity>().Where(f => f.Id.Equals(id) && f.Status != 4).FirstAsync();
        //     if (flowTask != null)
        //     {
        //         if (flowTask.ParentId != "0")
        //         {
        //             throw Oops.Oh(ErrorCode.WF0003, flowTask.FullName);
        //         }
        //
        //         throw Oops.Oh(ErrorCode.D1417);
        //     }
        // }

        if (id.IsNotEmptyOrNull())
        {
            TemplateParsingBase templateInfo = new(templateEntity); // 解析模板控件
            DbLink link = await GetDbLink(templateEntity.DbLinkId);
            templateInfo.DbLink = link;
            string? mainPrimary = GetPrimary(link, templateInfo.MainTableName);

            // 集成助手所需数据
            Dictionary<string, object> data = await GetHaveTableInfo(id, templateEntity, true);

            // 树形表格 删除父节点时同时删除子节点数据
            if (templateInfo.ColumnData.type.Equals(5))
            {
                Dictionary<string, string> delIdDic = new();
                List<Dictionary<string, string>> dataList = _databaseService.GetData(link, templateInfo.MainTableName).ToObjectOld<List<Dictionary<string, string>>>();
                dataList.ForEach(item => delIdDic.Add(item[mainPrimary], item[templateInfo.ColumnData.parentField]));
                List<string> delIds = new();
                CodeGenHelper.GetChildIdList(delIdDic, id, delIds);
                await BatchDelHaveTableData(delIds.Distinct().ToList(), templateEntity);
            }
            else
            {
                Dictionary<string, string> resId = GetPIdsByFlowIds(link, templateInfo, mainPrimary, new List<string> { id });
                id = resId.First().Value;

                if (templateInfo.FormModel.logicalDelete)
                {
                    string? dbType = link?.DbType != null ? link.DbType.ToString() : _visualDevRepository.AsSugarClient().CurrentConnectionConfig.DbType.ToString();
                    string sql = string.Empty;
                    if (dbType.Equals("Oracle"))
                    {
                        sql = string.Format("update {0} set f_delete_mark=1,f_delete_user_id='{1}',f_delete_time=to_date('{2}','yyyy-mm-dd HH24/MI/SS') where {3}='{4}'", templateInfo.MainTableName,
                            _userManager.UserId, DateTime.Now, mainPrimary, id);
                    }
                    else
                    {
                        sql = string.Format("update {0} set f_delete_mark=1,f_delete_user_id='{1}',f_delete_time='{2}' where {3}='{4}'", templateInfo.MainTableName, _userManager.UserId, DateTime.Now,
                            mainPrimary, id);
                    }

                    await _databaseService.ExecuteSql(link, sql); // 删除标识

                    // if (templateEntity.EnableFlow == 1)
                    // {
                    //     FlowTaskEntity? entity = _flowTaskRepository.GetTaskFirstOrDefault(resId.First().Key);
                    //     if (entity != null)
                    //     {
                    //         if (!entity.ParentId.Equals("0"))
                    //         {
                    //             throw Oops.Oh(ErrorCode.WF0003, entity.FullName);
                    //         }
                    //
                    //         await _flowTaskRepository.DeleteTask(entity);
                    //     }
                    // }
                }
                else
                {
                    List<string>? allDelSql = new(); // 拼接语句
                    allDelSql.Add(string.Format("delete from {0} where {1} = '{2}';", templateInfo.MainTable.table, mainPrimary, id));
                    if (templateInfo.AllTable.Any(x => x.typeId.Equals("0")))
                    {
                        templateInfo.AllTable.Where(x => x.typeId.Equals("0")).ToList()
                            .ForEach(item => allDelSql.Add(string.Format("delete from {0} where {1}='{2}';", item.table, item.tableField, id))); // 删除所有涉及表数据 sql
                    }

                    foreach (string? item in allDelSql)
                    {
                        await _databaseService.ExecuteSql(link, item); // 删除有表数据
                    }

                    // if (templateEntity.EnableFlow == 1)
                    // {
                    //     FlowTaskEntity? entity = _flowTaskRepository.GetTaskFirstOrDefault(resId.First().Key);
                    //     if (entity != null)
                    //     {
                    //         if (!entity.ParentId.Equals("0"))
                    //         {
                    //             throw Oops.Oh(ErrorCode.WF0003, entity.FullName);
                    //         }
                    //
                    //         await _flowTaskRepository.DeleteTask(entity);
                    //     }
                    // }
                }
            }

            // 添加集成助手`事件触发`删除事件
            await _eventPublisher.PublishAsync(new InteEventSource("Inte:CreateInte", _userManager.UserId.ToString(), _userManager.TenantId.ToString(), new InteAssiEventModel
            {
                ModelId = templateEntity.Id.ToString(),
                DataId = id,
                Data = data.ToJson(),
                TriggerType = 3
            }));
        }
    }

    /// <summary>
    ///     删除集成助手标识数据.
    /// </summary>
    /// <param name="templateEntity">模板实体.</param>
    /// <returns></returns>
    public async Task DelInteAssistant(VisualDevEntity templateEntity)
    {
        TemplateParsingBase templateInfo = new(templateEntity); // 解析模板控件
        DbLink link = await GetDbLink(templateEntity.DbLinkId);

        string? mainPrimary = GetPrimary(link, templateInfo.MainTableName);
        string sql = $"select {mainPrimary} from {templateInfo.MainTableName} where f_inte_assistant=1";
        List<Dictionary<string, object>> data = _databaseService.GetSqlData(link, sql).ToObjectOld<List<Dictionary<string, object>>>();
        List<string> idList = new();
        if (data.IsNotEmptyOrNull() && data.Any())
        {
            foreach (Dictionary<string, object> item in data)
            {
                idList.Add(item.FirstOrDefault().Value.ToString());
            }
        }

        List<string> deleteSql = new(); // 拼接语句

        if (idList.Any())
        {
            deleteSql.Add($"delete from {templateInfo.MainTable.table} where {mainPrimary} in ('{string.Join("','", idList)}');"); // 主表数据

            if (templateInfo.AllTable.Any(x => x.typeId.Equals("0")))
            {
                templateInfo.AllTable.Where(x => x.typeId.Equals("0")).ToList().ForEach(item =>
                {
                    deleteSql.Add($"delete from {item.table} where {item.tableField} in ('{string.Join("','", idList)}');");
                });
            }
        }

        await _db.BeginTranAsync();
        foreach (string item in deleteSql)
        {
            await _databaseService.ExecuteSql(link, item); // 执行删除集成助手数据Sql
        }

        await _db.CommitTranAsync();
    }

    /// <summary>
    ///     批量删除有表数据.
    /// </summary>
    /// <param name="ids">id数组</param>
    /// <param name="templateEntity">模板实体</param>
    /// <param name="visualdevModelDataBatchDeForm"></param>
    /// <returns></returns>
    public async Task BatchDelHaveTableData(List<string>? ids, VisualDevEntity templateEntity, VisualDevModelDataBatchDelInput? visualdevModelDataBatchDeForm = null)
    {
        List<string>? idList = ids.Copy();
        // if (templateEntity.EnableFlow == 1)
        // {
        //     var fList = await _visualDevRepository.AsSugarClient().Queryable<FlowTaskEntity>().Where(f => ids.Contains(f.Id) && f.Status != 4).ToListAsync();
        //     if (fList.Any(x => x.ParentId != "0") && fList.Count(x => x.ParentId != "0").Equals(ids.Count))
        //     {
        //         throw Oops.Oh(ErrorCode.WF0003, fList.First(x => x.ParentId != "0").FullName);
        //     }
        //
        //     if (fList.Count.Equals(ids.Count))
        //     {
        //         throw Oops.Oh(ErrorCode.D1417);
        //     }
        //
        //     ids = ids.Except(fList.Select(x => x.Id)).ToList();
        // }

        TemplateParsingBase templateInfo = new(templateEntity); // 解析模板控件
        DbLink link = await GetDbLink(templateEntity.DbLinkId);

        if (ids.IsNotEmptyOrNull() && ids.Count > 0)
        {
            string? mainPrimary = GetPrimary(link, templateInfo.MainTableName);
            Dictionary<string, string> resIds = GetPIdsByFlowIds(link, templateInfo, mainPrimary, ids);
            ids = resIds.Select(x => x.Value).ToList();

            // 集成助手所需数据
            List<object> allData = new();
            foreach (string id in ids)
            {
                Dictionary<string, object> data = await GetHaveTableInfo(id, templateEntity, true);
                allData.Add(new { id, data });
            }

            if (templateInfo.FormModel.logicalDelete)
            {
                if (visualdevModelDataBatchDeForm.deleteRule.Equals(0))
                {
                    await _databaseService.ExecuteSql(link,
                        string.Format("update {0} set f_delete_mark=1,f_delete_user_id='{1}',f_delete_time='{2}' where {3} not in ('{4}')", templateInfo.MainTableName, _userManager.UserId,
                            DateTime.Now, mainPrimary, string.Join("','", ids))); // 删除标识
                }
                else
                {
                    await _databaseService.ExecuteSql(link,
                        string.Format("update {0} set f_delete_mark=1,f_delete_user_id='{1}',f_delete_time='{2}' where {3} in ('{4}')", templateInfo.MainTableName, _userManager.UserId, DateTime.Now,
                            mainPrimary, string.Join("','", ids))); // 删除标识
                }

                // if (templateEntity.EnableFlow == 1)
                // {
                //     ids = resIds.Select(x => x.Key).ToList();
                //     foreach (string it in ids)
                //     {
                //         FlowTaskEntity? entity = _flowTaskRepository.GetTaskFirstOrDefault(it);
                //         if (entity != null && entity.ParentId.Equals("0"))
                //         {
                //             if (!entity.ParentId.Equals("0"))
                //             {
                //                 throw Oops.Oh(ErrorCode.WF0003, entity.FullName);
                //             }
                //
                //             _flowTaskRepository.DeleteTaskNoAwait(entity);
                //         }
                //     }
                // }
            }
            else
            {
                List<string>? allDelSql = new(); // 拼接语句

                if (visualdevModelDataBatchDeForm.deleteRule.Equals(0))
                {
                    allDelSql.Add(string.Format("delete from {0} where {1} not in ('{2}');", templateInfo.MainTable.table, mainPrimary, string.Join("','", ids))); // 主表数据
                }
                else
                {
                    allDelSql.Add(string.Format("delete from {0} where {1} in ('{2}');", templateInfo.MainTable.table, mainPrimary, string.Join("','", ids))); // 主表数据
                }

                if (templateInfo.AllTable.Any(x => x.typeId.Equals("0")))
                {
                    templateInfo.AllTable.Where(x => x.typeId.Equals("0")).ToList()
                        .ForEach(item =>
                        {
                            if (visualdevModelDataBatchDeForm.deleteRule.Equals(0))
                            {
                                allDelSql.Add(string.Format("delete from {0} where {1} not in ('{2}');", item.table, item.tableField, string.Join("','", ids)));
                            }
                            else
                            {
                                allDelSql.Add(string.Format("delete from {0} where {1} in ('{2}');", item.table, item.tableField, string.Join("','", ids)));
                            }
                        });
                }

                foreach (string? item in allDelSql)
                {
                    await _databaseService.ExecuteSql(link, item); // 删除有表数据
                }

                // if (templateEntity.EnableFlow == 1)
                // {
                //     ids = resIds.Select(x => x.Key).ToList();
                //     foreach (string it in ids)
                //     {
                //         FlowTaskEntity? entity = _flowTaskRepository.GetTaskFirstOrDefault(it);
                //         if (entity != null && entity.ParentId.Equals("0"))
                //         {
                //             if (!entity.ParentId.Equals("0"))
                //             {
                //                 throw Oops.Oh(ErrorCode.WF0003, entity.FullName);
                //             }
                //
                //             _flowTaskRepository.DeleteTaskNoAwait(entity);
                //         }
                //     }
                // }
            }

            // 添加集成助手`事件触发`批量删除事件
            if (visualdevModelDataBatchDeForm.isInteAssis)
            {
                await _eventPublisher.PublishAsync(new InteEventSource("Inte:CreateInte", _userManager.UserId.ToString(), _userManager.TenantId.ToString(), new InteAssiEventModel
                {
                    ModelId = templateEntity.Id.ToString(),
                    Data = allData.ToJson(),
                    TriggerType = 5
                }));
            }
        }
        else
        {
            List<string> deleteSql = new();
            foreach (TableModel item in templateInfo.AllTable)
            {
                string? tableSql = $"delete from {item.table}";
                deleteSql.Add(tableSql);
            }

            await _db.BeginTranAsync();
            foreach (string item in deleteSql)
            {
                await _databaseService.ExecuteSql(link, item); // 执行修改Sql
            }

            await _db.CommitTranAsync();
        }

        // if (templateEntity.EnableFlow == 1 && ids.Count < 1 && !idList.Count.Equals(ids.Count))
        // {
        //     throw Oops.Oh(ErrorCode.D1417);
        // }
    }

    /// <summary>
    ///     生成系统自动生成字段.
    /// </summary>
    /// <param name="fieldsModelListJson">模板数据.</param>
    /// <param name="allDataMap">真实数据.</param>
    /// <param name="IsCreate">创建与修改标识 true创建 false 修改.</param>
    /// <param name="systemControlList">不赋值的系统控件Key.</param>
    /// <returns></returns>
    public async Task<Dictionary<string, object>> GenerateFeilds(string fieldsModelListJson, Dictionary<string, object> allDataMap, bool IsCreate, List<string>? systemControlList = null)
    {
        List<FieldsModel> fieldsModelList = fieldsModelListJson.ToObjectOld<List<FieldsModel>>();
        SysUser? userInfo = _userManager.User;
        int dicCount = allDataMap.Keys.Count;
        string[] strKey = new string[dicCount];

        // 修改时 把 创建用户 和 创建时间 去掉.
        if (!IsCreate)
        {
            fieldsModelList.ForEach(item =>
            {
                switch (item.__config__.jnpfKey)
                {
                    case JnpfKeyConst.CREATETIME:
                    case JnpfKeyConst.CREATEUSER:
                        allDataMap.Remove(item.__vModel__);
                        break;
                    case JnpfKeyConst.CURRPOSITION:
                    case JnpfKeyConst.CURRORGANIZE:
                        if (systemControlList == null)
                        {
                            allDataMap.Remove(item.__vModel__);
                        }

                        break;
                    case JnpfKeyConst.TABLE:
                        List<FieldsModel> fList = item.__config__.children.Where(x => x.__config__.jnpfKey.Equals(JnpfKeyConst.CREATETIME)
                                                                                      || x.__config__.jnpfKey.Equals(JnpfKeyConst.CREATEUSER)
                                                                                      || x.__config__.jnpfKey.Equals(JnpfKeyConst.CURRPOSITION)
                                                                                      || x.__config__.jnpfKey.Equals(JnpfKeyConst.CURRORGANIZE)).ToList();
                        fList.ForEach(child =>
                        {
                            if (allDataMap.ContainsKey(item.__vModel__))
                            {
                                List<Dictionary<string, object>> cDataMap = allDataMap[item.__vModel__].ToObjectOld<List<Dictionary<string, object>>>();
                                cDataMap.ForEach(x => x.Remove(child.__vModel__));
                                allDataMap[item.__vModel__] = cDataMap;
                            }
                        });
                        break;
                }
            });
        }

        bool create = IsCreate;
        if (systemControlList.IsNotEmptyOrNull())
        {
            create = true;
        }

        foreach (var model in fieldsModelList)
        {
            if (model != null && model.__vModel__.IsNotEmptyOrNull())
            {
                // 如果模板jnpfKey为table为子表数据
                if (model.__config__.jnpfKey.Equals(JnpfKeyConst.TABLE) && allDataMap.ContainsKey(model.__vModel__) && allDataMap[model.__vModel__] != null)
                {
                    List<FieldsModel> childFieldsModelList = model.__config__.children;
                    object? objectData = allDataMap[model.__vModel__];
                    List<Dictionary<string, object>> childAllDataMapList = objectData.ToObjectOld<List<Dictionary<string, object>>>();

                    if (childAllDataMapList != null && childAllDataMapList.Count > 0)
                    {
                        List<Dictionary<string, object>> newChildAllDataMapList = new();
                        foreach (Dictionary<string, object>? childmap in childAllDataMapList)
                        {
                            Dictionary<string, object>? newChildData = new();
                            foreach (KeyValuePair<string, object> item in childmap)
                            {
                                if (item.Key.Equals("id"))
                                {
                                    newChildData[item.Key] = childmap[item.Key];
                                }

                                FieldsModel? childFieldsModel = childFieldsModelList.Where(c => c.__vModel__ == item.Key).FirstOrDefault();
                                if (childFieldsModel != null && childFieldsModel.__vModel__.Equals(item.Key))
                                {
                                    switch (childFieldsModel.__config__.jnpfKey)
                                    {
                                        case JnpfKeyConst.BILLRULE:
                                            if (IsCreate || childmap[item.Key].IsNullOrEmpty())
                                            {
                                                string billNumber = await _billRuleService.GetBillNumber(childFieldsModel.__config__.rule,false);
                                                if (!"单据规则不存在".Equals(billNumber))
                                                {
                                                    newChildData[item.Key] = billNumber;
                                                }
                                                else
                                                {
                                                    newChildData[item.Key] = string.Empty;
                                                }
                                            }
                                            else
                                            {
                                                newChildData[item.Key] = childmap[item.Key];
                                            }

                                            break;
                                        case JnpfKeyConst.CREATEUSER:
                                            if (IsCreate)
                                            {
                                                newChildData[item.Key] = userInfo?.Id;
                                            }

                                            break;
                                        case JnpfKeyConst.MODIFYUSER:
                                            if (!IsCreate)
                                            {
                                                newChildData[item.Key] = userInfo?.Id;
                                            }

                                            break;
                                        case JnpfKeyConst.CREATETIME:
                                            if (IsCreate)
                                            {
                                                newChildData[item.Key] = string.Format("{0:yyyy-MM-dd HH:mm:ss}", DateTime.Now);
                                            }

                                            break;
                                        case JnpfKeyConst.MODIFYTIME:
                                            if (!IsCreate)
                                            {
                                                newChildData[item.Key] = string.Format("{0:yyyy-MM-dd HH:mm:ss}", DateTime.Now);
                                            }

                                            break;
                                        case JnpfKeyConst.CURRPOSITION:
                                            if (IsCreate)
                                            {
                                                if (allDataMap.ContainsKey("Jnpf_FlowDelegate_CurrPosition")) // 流程委托 需要指定所属岗位
                                                {
                                                    allDataMap[model.__vModel__] = allDataMap["Jnpf_FlowDelegate_CurrPosition"];
                                                }
                                                else
                                                {
                                                    if (userInfo?.AdminType == AdminTypeEnum.SuperAdmin)
                                                    {
                                                        allDataMap[model.__vModel__] = "超级管理员";
                                                    }
                                                    else
                                                    {
                                                        string? roleName = await _visualDevRepository.AsSugarClient().Queryable<SysUserRole>()
                                                            .Where(w => w.SysUserId ==  userInfo.Id)
                                                            .LeftJoin<SysRole>((u, a) => u.SysRoleId == a.Id)
                                                            .Select(u => u.SysRole.Name)
                                                            .FirstAsync();
                                                        if (roleName.IsNotEmptyOrNull())
                                                        {
                                                            newChildData[item.Key] = roleName;
                                                        }
                                                        else
                                                        {
                                                            newChildData[item.Key] = string.Empty;
                                                        }
                                                    }
                                                }
                                            }

                                            break;
                                        case JnpfKeyConst.CURRORGANIZE:
                                            if (IsCreate)
                                            {
                                                if (allDataMap.ContainsKey("Jnpf_FlowDelegate_CurrOrganize")) // 流程委托 需要指定所属组织
                                                {
                                                    allDataMap[model.__vModel__] = allDataMap["Jnpf_FlowDelegate_CurrOrganize"];
                                                }
                                                else
                                                {
                                                    if (userInfo?.OrgId > 0)
                                                    {
                                                        var organizeTree = await _visualDevRepository.AsSugarClient().Queryable<SysOrg>()
                                                            .Where(it => it.Id == userInfo.OrgId)
                                                            .Select(it => it.OrganizeIdTree)
                                                            .FirstAsync();
                                                        if (organizeTree.IsNotEmptyOrNull())
                                                            newChildData[item.Key] = organizeTree.Split(",").ToJson();
                                                        else
                                                            newChildData[item.Key] = new List<string> {_userManager.OrgName}.ToJson();
                                                    }
                                                    else
                                                    {
                                                        newChildData[item.Key] = string.Empty;
                                                    }
                                                }
                                            }

                                            break;
                                        case JnpfKeyConst.UPLOADFZ: // 文件上传
                                            if (!childmap.ContainsKey(item.Key) || childmap[item.Key].IsNullOrEmpty())
                                            {
                                                newChildData[item.Key] = new string[] { };
                                            }
                                            else
                                            {
                                                newChildData[item.Key] = childmap[item.Key];
                                            }

                                            break;
                                        case JnpfKeyConst.CURRTENANT:
                                            if (IsCreate)
                                            {
                                                if (allDataMap.ContainsKey("Jnpf_FlowDelegate_CurrTenant")) // 流程委托 需要指定所属组织
                                                {
                                                    allDataMap[model.__vModel__] = allDataMap["Jnpf_FlowDelegate_CurrTenant"];
                                                }
                                                else
                                                {
                                                    if (userInfo?.TenantId > 0)
                                                    {
                                                        string? organize = await _visualDevRepository.AsSugarClient().Queryable<SysOrg>()
                                                            .Where(it => it.Pid ==  0 && it.TenantId == _userManager.TenantId)
                                                            .Select(it => it.Name)
                                                            .FirstAsync();
                                                        newChildData[item.Key] = organize;
                                                    }
                                                    else
                                                    {
                                                        newChildData[item.Key] = string.Empty;
                                                    }
                                                }
                                            }

                                            break;
                                        default:
                                            newChildData[item.Key] = childmap[item.Key];
                                            break;
                                    }
                                }
                            }

                            newChildAllDataMapList.Add(newChildData);
                            allDataMap[model.__vModel__] = newChildAllDataMapList;
                        }
                    }
                }
                else
                {
                    if (systemControlList.IsNotEmptyOrNull() && systemControlList.Contains(model.__vModel__))
                    {
                        allDataMap.Remove(model.__vModel__);
                    }
                    else
                    {
                        switch (model.__config__.jnpfKey)
                        {
                            case JnpfKeyConst.BILLRULE:
                                if (IsCreate && allDataMap[model.__vModel__].IsNullOrEmpty())
                                {
                                    string billNumber = await _billRuleService.GetBillNumber(model.__config__.rule,false);
                                    if (!"单据规则不存在".Equals(billNumber))
                                    {
                                        allDataMap[model.__vModel__] = billNumber;
                                    }
                                    else
                                    {
                                        allDataMap[model.__vModel__] = string.Empty;
                                    }
                                }

                                break;
                            case JnpfKeyConst.CREATEUSER:
                                if (IsCreate)
                                {
                                    allDataMap[model.__vModel__] = userInfo?.Id;
                                }

                                break;
                            case JnpfKeyConst.CREATETIME:
                                if (IsCreate)
                                {
                                    allDataMap[model.__vModel__] = string.Format("{0:yyyy-MM-dd HH:mm:ss}", DateTime.Now);
                                }

                                break;
                            case JnpfKeyConst.MODIFYUSER:
                                if (!IsCreate)
                                {
                                    allDataMap[model.__vModel__] = userInfo?.Id;
                                }

                                break;
                            case JnpfKeyConst.MODIFYTIME:
                                if (!IsCreate)
                                {
                                    allDataMap[model.__vModel__] = string.Format("{0:yyyy-MM-dd HH:mm:ss}", DateTime.Now);
                                }

                                break;
                            case JnpfKeyConst.CURRPOSITION:
                                if (create)
                                {
                                    if (allDataMap.ContainsKey("Jnpf_FlowDelegate_CurrPosition")) // 流程委托 需要指定所属岗位
                                    {
                                        allDataMap[model.__vModel__] = allDataMap["Jnpf_FlowDelegate_CurrPosition"];
                                    }
                                    else
                                    {
                                        if (userInfo?.AdminType == AdminTypeEnum.SuperAdmin)
                                        {
                                            allDataMap[model.__vModel__] = "超级管理员";
                                        }
                                        else
                                        {
                                            string? roleName = await _visualDevRepository.AsSugarClient().Queryable<SysUserRole>()
                                                .Where(w => w.SysUserId == userInfo.Id)
                                                .LeftJoin<SysRole>((u, a) => u.SysRoleId == a.Id)
                                                .Select(u => u.SysRole.Name)
                                                .FirstAsync();
                                            if (roleName.IsNotEmptyOrNull())
                                            {
                                                allDataMap[model.__vModel__] = roleName;
                                            }
                                            else
                                            {
                                                allDataMap[model.__vModel__] = string.Empty;
                                            }
                                        }
                                    }
                                }

                                break;
                            case JnpfKeyConst.CURRTENANT:
                                if (create)
                                {
                                    if (allDataMap.ContainsKey("Jnpf_FlowDelegate_CurrTenant")) // 流程委托 需要指定所属组织
                                    {
                                        allDataMap[model.__vModel__] = allDataMap["Jnpf_FlowDelegate_CurrTenant"];
                                    }
                                    else
                                    {
                                        string? organize = await _visualDevRepository.AsSugarClient().Queryable<SysOrg>()
                                            .Where(it => it.Pid ==  0 && it.TenantId == _userManager.TenantId)
                                            .Select(it => it.Name)
                                            .FirstAsync();
                                        allDataMap[model.__vModel__] = organize;
                                    }
                                }

                                break;
                            case JnpfKeyConst.CURRORGANIZE:
                                if (create)
                                {
                                    if (allDataMap.ContainsKey("Jnpf_FlowDelegate_CurrOrganize")) // 流程委托 需要指定所属组织
                                    {
                                        allDataMap[model.__vModel__] = allDataMap["Jnpf_FlowDelegate_CurrOrganize"];
                                    }
                                    else
                                    {
                                        if (model.showLevel != null && model.showLevel.Equals("last"))
                                        {
                                            if (userInfo?.OrgId > 0)
                                            {
                                                string? organizeTree = await _visualDevRepository.AsSugarClient().Queryable<SysOrg>()
                                                    .Where(it => it.Id == userInfo.OrgId && it.Pid != 0)
                                                    .Select(s=> s.OrganizeIdTree)
                                                    .FirstAsync();
                                                if (organizeTree.IsNotEmptyOrNull())
                                                    allDataMap[model.__vModel__] = organizeTree.Split(",").ToJson();
                                                else
                                                    allDataMap[model.__vModel__] = new List<string> {_userManager.OrgName}.ToJson();
                                            }
                                            else
                                            {
                                                allDataMap[model.__vModel__] = string.Empty;
                                            }
                                        }
                                        else
                                        {
                                            if (userInfo?.OrgId > 0)
                                            {
                                                string? organizeTree = await _visualDevRepository.AsSugarClient().Queryable<SysOrg>()
                                                    .Where(it => it.Id == userInfo.OrgId)
                                                    .Select(s=> s.OrganizeIdTree)
                                                    .FirstAsync();
                                                if (organizeTree.IsNotEmptyOrNull())
                                                    allDataMap[model.__vModel__] = organizeTree.Split(",").ToJson();
                                                else
                                                    allDataMap[model.__vModel__] = new List<string> {_userManager.OrgName}.ToJson();
                                            }
                                            else
                                            {
                                                allDataMap[model.__vModel__] = string.Empty;
                                            }
                                        }
                                    }
                                }

                                break;
                            case JnpfKeyConst.UPLOADFZ: // 文件上传
                                if (!allDataMap.ContainsKey(model.__vModel__) || allDataMap[model.__vModel__].IsNullOrEmpty())
                                {
                                    allDataMap[model.__vModel__] = new string[] { };
                                }

                                break;
                        }
                    }
                }
            }
        }

        return allDataMap;
    }

    /// <summary>
    ///     获取数据连接, 根据连接Id.
    /// </summary>
    /// <param name="linkId"></param>
    /// <param name="tenantId">租户Id.</param>
    /// <returns></returns>
    public async Task<DbLink> GetDbLink(long linkId, string? tenantId = null)
    {
        DbLink link = await _dbLinkService.GetInfo(linkId);
        if (link == null)
        {
            if (tenantId.IsNotEmptyOrNull())
            {
                GlobalTenantCacheModel? tenantCache = _cacheManager.Get<List<GlobalTenantCacheModel>>(CommonConst.GLOBALTENANT).Find(it => it.TenantId.Equals(tenantId));
                if (tenantCache.type.Equals(1))
                {
                    link = _databaseService.GetTenantDbLink(tenantCache.TenantId.ParseToLong(), tenantCache.connectionConfig.IsolationField);
                }
                else
                {
                    link = _databaseService.GetTenantDbLink(tenantCache.TenantId.ParseToLong(), tenantCache.connectionConfig.ConfigList.First().ServiceName);
                }
            }
            else
            {
                link = _databaseService.GetTenantDbLink(_userManager.TenantId, _userManager.TenantDbName);
            }
        }

        return link;
    }

    /// <summary>
    ///     同步业务需要的字段.
    /// </summary>
    /// <param name="tInfo"></param>
    /// <returns></returns>
    public async Task SyncField(TemplateParsingBase tInfo)
    {
        if (tInfo.IsHasTable && !tInfo.visualDevEntity.WebType.Equals(4))
        {
            // 是否开启软删除配置 , 开启则增加 删除标识 字段.
            if (tInfo.FormModel.logicalDelete)
            {
                if (!_databaseService.IsAnyColumn(tInfo.DbLink, tInfo.MainTableName, "f_delete_mark"))
                {
                    List<DbTableFieldModel> pFieldList = new()
                        { new DbTableFieldModel { field = "f_delete_mark", fieldName = "删除标识", dataType = "int", dataLength = "1", allowNull = 1 } };
                    _databaseService.AddTableColumn(tInfo.DbLink, tInfo.MainTableName, pFieldList);
                }

                if (!_databaseService.IsAnyColumn(tInfo.DbLink, tInfo.MainTableName, "f_delete_user_id"))
                {
                    List<DbTableFieldModel> pFieldList = new()
                        { new DbTableFieldModel { field = "f_delete_user_id", fieldName = "删除用户", dataType = "varchar", dataLength = "50", allowNull = 1 } };
                    _databaseService.AddTableColumn(tInfo.DbLink, tInfo.MainTableName, pFieldList);
                }

                if (!_databaseService.IsAnyColumn(tInfo.DbLink, tInfo.MainTableName, "f_delete_time"))
                {
                    List<DbTableFieldModel> pFieldList = new()
                        { new DbTableFieldModel { field = "f_delete_time", fieldName = "删除时间", dataType = "datetime", dataLength = "50", allowNull = 1 } };
                    _databaseService.AddTableColumn(tInfo.DbLink, tInfo.MainTableName, pFieldList);
                }
            }

            // 是否开启多租户 字段隔离, 开启则增加 隔离 字段.
            if (_tenant.MultiTenancy)
            {
                GlobalTenantCacheModel? tenantCache = _cacheManager.Get<List<GlobalTenantCacheModel>>(CommonConst.GLOBALTENANT).Find(it => it.TenantId.Equals(tInfo.DbLink.Id));
                if (tenantCache.IsNotEmptyOrNull() && tenantCache.type.Equals(1) && !_databaseService.IsAnyColumn(tInfo.DbLink, tInfo.MainTableName, "f_tenant_id"))
                {
                    List<DbTableFieldModel> pFieldList = new()
                        { new DbTableFieldModel { field = "f_tenant_id", fieldName = "租户Id", dataType = "varchar", dataLength = "50", allowNull = 1 } };
                    _databaseService.AddTableColumn(tInfo.DbLink, tInfo.MainTableName, pFieldList);
                }
            }

            if (tInfo.visualDevEntity.EnableFlow.Equals(1))
            {
                // 流程表单 需要增加字段 f_flow_task_id
                List<DbTableFieldModel>? tableList = _databaseService.GetFieldList(tInfo.DbLink, tInfo.MainTableName); // 获取主表 表结构 信息
                if (!tableList.Any(x => SqlFunc.ToLower(x.field) == "f_flow_task_id"))
                {
                    List<DbTableFieldModel>? pFieldList = new()
                        { new DbTableFieldModel { field = "f_flow_task_id", fieldName = "流程任务Id", dataType = "varchar", dataLength = "50", allowNull = 1 } };
                    _databaseService.AddTableColumn(tInfo.DbLink, tInfo.MainTableName, pFieldList);
                }

                if (!tableList.Any(x => SqlFunc.ToLower(x.field) == "f_flow_id"))
                {
                    List<DbTableFieldModel> pFieldList = new()
                        { new DbTableFieldModel { field = "f_flow_id", fieldName = "流程引擎Id", dataType = "varchar", dataLength = "50", allowNull = 1 } };
                    _databaseService.AddTableColumn(tInfo.DbLink, tInfo.MainTableName, pFieldList);
                }

                // var ffEntity = _visualDevRepository.AsSugarClient().Queryable<FlowFormEntity>().First(x => x.Id.Equals(tInfo.visualDevEntity.Id));
                // if (ffEntity != null)
                // {
                //     var flowId = ffEntity.FlowId;
                //     var flowJsonId = await _visualDevRepository.AsSugarClient().Queryable<FlowTemplateJsonEntity>().Where(x => x.TemplateId == flowId && x.EnabledMark == 1 && x.DeleteMark == null)
                //         .Select(x => x.Id).FirstAsync();
                //     string sql = string.Format("update {0} set f_flow_task_id={1},f_flow_id='{2}' where f_flow_id is null or f_flow_id = '';", tInfo.MainTableName,
                //         tableList.First(x => x.IsPrimarykey).DbColumnName, flowJsonId);
                //     await _databaseService.ExecuteSql(tInfo.DbLink, sql);
                // }
            }

            // 集成助手数据标识
            if (!_databaseService.IsAnyColumn(tInfo.DbLink, tInfo.MainTableName, "f_inte_assistant"))
            {
                List<DbTableFieldModel> pFieldList = new()
                    { new DbTableFieldModel { field = "f_inte_assistant", fieldName = "集成助手数据标识", dataType = "int",  allowNull = 1 } };
                _databaseService.AddTableColumn(tInfo.DbLink, tInfo.MainTableName, pFieldList);
            }
        }
    }

    #endregion

    #region 私有方法

    /// <summary>
    ///     获取数据表主键.
    /// </summary>
    /// <param name="link"></param>
    /// <param name="MainTableName"></param>
    /// <returns></returns>
    private string GetPrimary(DbLink link, string MainTableName)
    {
        List<DbTableFieldModel>? tableList = _databaseService.GetFieldList(link, MainTableName); // 获取主表所有列
        DbTableFieldModel? mainPrimary = tableList.Find(t => t.primaryKey); // 主表主键
        if (mainPrimary == null || mainPrimary.IsNullOrEmpty())
        {
            throw Oops.Oh(ErrorCode.D1402); // 主表未设置主键
        }

        return mainPrimary.field;
    }
    
    /// <summary>
    ///     获取数据表主键.
    /// </summary>
    /// <param name="link"></param>
    /// <param name="mainTableName"></param>
    /// <returns></returns>
    private DbTableFieldModel GetPrimaryField(DbLink link, string mainTableName)
    {
        List<DbTableFieldModel>? tableList = _databaseService.GetFieldList(link, mainTableName); // 获取主表所有列
        DbTableFieldModel? mainPrimary = tableList.Find(t => t.primaryKey); // 主表主键
        if (mainPrimary == null || mainPrimary.IsNullOrEmpty())
        {
            throw Oops.Oh(ErrorCode.D1402); // 主表未设置主键
        }

        return mainPrimary;
    }

    /// <summary>
    ///     根据流程Id 获取 主键 Id.
    /// </summary>
    /// <param name="link">数据库连接.</param>
    /// <param name="templateInfo">模板配置.</param>
    /// <param name="mainPrimary">主表主键名.</param>
    /// <param name="Ids">流程Ids.</param>
    /// <param name="isList">是否列表.</param>
    /// <param name="currIndex">.</param>
    /// <returns>f_flow_task_id, mainPrimary.</returns>
    private Dictionary<string, string> GetPIdsByFlowIds(DbLink link, TemplateParsingBase templateInfo, string mainPrimary, List<string> Ids, bool isList = false, int currIndex = 0)
    {
        Dictionary<string, string> res = new();
        if (templateInfo.visualDevEntity != null && templateInfo.visualDevEntity.EnableFlow.Equals(1) && templateInfo.FormModel.primaryKeyPolicy.Equals(2) && currIndex < 3)
        {
            string sql = string.Format("select {0},f_flow_task_id from {1} where f_flow_task_id in ({2});", mainPrimary, templateInfo.MainTableName, string.Join("','", Ids));
            if (isList)
            {
                sql = string.Format("select {0},f_flow_task_id from {1} where {0} in ({2});", mainPrimary, templateInfo.MainTableName, string.Join("','", Ids));
            }

            List<Dictionary<string, string>> data = _databaseService.GetSqlData(link, sql).ToObjectOld<List<Dictionary<string, string>>>();
            currIndex++;
            if (!data.Any())
            {
                return GetPIdsByFlowIds(link, templateInfo, mainPrimary, Ids, true, currIndex);
            }

            data.ForEach(item => res.Add(item["f_flow_task_id"], item[mainPrimary]));
        }
        else
        {
            Ids.ForEach(item => res.Add(item, item));
        }

        return res;
    }


    /// <summary>
    ///     组装高级查询信息.
    /// </summary>
    /// <param name="superQueryJson">查询条件json.</param>
    private string GetSuperQueryInput(string superQueryJson)
    {
        Dictionary<string, object> result = new();
        Dictionary<string, object>? dic = string.IsNullOrEmpty(superQueryJson) ? null : superQueryJson.ToObjectOld<Dictionary<string, object>>();

        if (dic != null)
        {
            object? matchLogic = dic.FirstOrDefault().Value;
            WhereType whereType = matchLogic.ToString().ToUpper().Equals("AND") ? WhereType.And : WhereType.Or;
            List<Dictionary<string, object>> queryList = new();

            foreach (Dictionary<string, object> dicItem in dic.LastOrDefault().Value.ToObjectOld<List<Dictionary<string, object>>>())
            {
                object? subMatchLogic = dicItem.FirstOrDefault().Value;
                WhereType subWhereType = subMatchLogic.ToString().ToUpper().Equals("AND") ? WhereType.And : WhereType.Or;

                bool firstItem = true;
                List<string>? between = new();
                foreach (Dictionary<string, object> item in dicItem.LastOrDefault().Value.ToObjectOld<List<Dictionary<string, object>>>())
                {
                    Dictionary<string, object> query = new();
                    query.Add("whereType", subWhereType);
                    query.Add("jnpfKey", item["jnpfKey"]);
                    query.Add("field", item["field"].ToString());
                    if (firstItem)
                    {
                        query.Add("where", whereType);
                        firstItem = false;
                    }

                    if (item.ContainsKey("fieldValue") && item["fieldValue"].IsNotEmptyOrNull())
                    {
                        item["fieldValue"] = item["fieldValue"].ToString().Replace("\r\n", string.Empty).Replace(" ", string.Empty);
                    }
                    else
                    {
                        if (item["jnpfKey"].Equals(JnpfKeyConst.CALCULATE) || item["jnpfKey"].Equals(JnpfKeyConst.NUMINPUT) || item["jnpfKey"].Equals(JnpfKeyConst.RATE) ||
                            item["jnpfKey"].Equals(JnpfKeyConst.SLIDER))
                        {
                            item["fieldValue"] = null;
                        }
                        else
                        {
                            item["fieldValue"] = string.Empty;
                        }
                    }

                    if (item["fieldValue"].IsNotEmptyOrNull())
                    {
                        if (item["symbol"].Equals("between"))
                        {
                            between = item["fieldValue"].ToString().ToObjectOld<List<string>>();
                        }

                        switch (item["jnpfKey"])
                        {
                            case JnpfKeyConst.DATE:
                            case JnpfKeyConst.CREATETIME:
                            case JnpfKeyConst.MODIFYTIME:
                                if (item["symbol"].Equals("between"))
                                {
                                    DateTime startTime = between.First().ParseToDateTime();
                                    DateTime endTime = between.Last().ParseToDateTime();
                                    between[0] = startTime.ToString();
                                    between[1] = endTime.ToString();
                                }
                                else
                                {
                                    item["fieldValue"] = item["fieldValue"].ToString().ParseToDateTime().ToString();
                                }

                                query["CSharpTypeName"] = "datetime";
                                break;
                            case JnpfKeyConst.TIME:
                                if (!item["symbol"].Equals("between"))
                                {
                                    item["fieldValue"] = string.Format("{0:" + item["format"] + "}", Convert.ToDateTime(item["fieldValue"]));
                                }

                                break;
                            case JnpfKeyConst.RATE:
                            case JnpfKeyConst.SLIDER:
                                query["CSharpTypeName"] = "decimal";
                                break;
                        }
                    }

                    query.Add("fieldValue", item["fieldValue"]);

                    if ((!item.ContainsKey("fieldValue") || item.ContainsKey("fieldValue").Equals("[]")) && item["symbol"].Equals("=="))
                    {
                        if (item["jnpfKey"].Equals(JnpfKeyConst.CALCULATE) || item["jnpfKey"].Equals(JnpfKeyConst.NUMINPUT))
                        {
                            query.Add("ConditionalType", ConditionalType.EqualNull);
                        }
                        else
                        {
                            query.Add("ConditionalType", ConditionalType.IsNullOrEmpty);
                        }

                        queryList.Add(query);
                        continue;
                    }

                    if ((!item.ContainsKey("fieldValue") || item.ContainsKey("fieldValue").Equals("[]") || item["fieldValue"].IsNullOrEmpty()) && item["symbol"].Equals("<>"))
                    {
                        query.Add("ConditionalType", ConditionalType.IsNot);
                        queryList.Add(query);
                        continue;
                    }

                    switch (item["symbol"])
                    {
                        case ">=":
                            query.Add("ConditionalType", ConditionalType.GreaterThanOrEqual);
                            break;
                        case ">":
                            query.Add("ConditionalType", ConditionalType.GreaterThan);
                            break;
                        case "==":
                            query.Add("ConditionalType", ConditionalType.Equal);
                            break;
                        case "<=":
                            query.Add("ConditionalType", ConditionalType.LessThanOrEqual);
                            break;
                        case "<":
                            query.Add("ConditionalType", ConditionalType.LessThan);
                            break;
                        case "like":
                            query.Add("ConditionalType",
                                item["fieldValue"].IsNotEmptyOrNull() ? ConditionalType.Like :
                                item["jnpfKey"].Equals(JnpfKeyConst.CALCULATE) || item["jnpfKey"].Equals(JnpfKeyConst.NUMINPUT) ? ConditionalType.EqualNull : ConditionalType.IsNullOrEmpty);
                            if (query["fieldValue"] != null && query["fieldValue"].ToString().Contains("["))
                            {
                                query["fieldValue"] = query["fieldValue"].ToString().Replace("[", string.Empty).Replace("]", string.Empty);
                            }

                            break;
                        case "<>":
                            query.Add("ConditionalType", ConditionalType.NoEqual);
                            break;
                        case "notLike":
                            query.Add("ConditionalType", ConditionalType.NoLike);
                            if (query["fieldValue"] != null && query["fieldValue"].ToString().Contains("["))
                            {
                                query["fieldValue"] = query["fieldValue"].ToString().Replace("[", string.Empty).Replace("]", string.Empty);
                            }

                            break;
                        case "in":
                        case "notIn":
                            if (query["fieldValue"] != null && query["fieldValue"].ToString().Contains("["))
                            {
                                bool isListValue = false;
                                if (item["jnpfKey"].Equals(JnpfKeyConst.CHECKBOX) || item["jnpfKey"].Equals(JnpfKeyConst.CASCADER) || item["jnpfKey"].Equals(JnpfKeyConst.ADDRESS))
                                {
                                    isListValue = true;
                                }

                                if (item["jnpfKey"].Equals(JnpfKeyConst.COMSELECT))
                                {
                                    isListValue = false;
                                }

                                List<string>? ids = new();
                                if (query["fieldValue"].ToString().Replace("\r\n", "").Replace(" ", "").Contains("[["))
                                {
                                    if (item["jnpfKey"].Equals(JnpfKeyConst.COMSELECT) || item["jnpfKey"].Equals(JnpfKeyConst.CURRORGANIZE))
                                    {
                                        ids = query["fieldValue"].ToString().ToObjectOld<List<List<string>>>().Select(x => x.Last() + "\"]").ToList();
                                    }
                                    else
                                    {
                                        ids = query["fieldValue"].ToString().ToObjectOld<List<List<string>>>().Select(x => x.Last()).ToList();
                                    }
                                }
                                else
                                {
                                    if (item["jnpfKey"].Equals(JnpfKeyConst.COMSELECT) || item["jnpfKey"].Equals(JnpfKeyConst.CURRORGANIZE))
                                    {
                                        ids = query["fieldValue"].ToString().ToObjectOld<List<string>>().Select(x => x + "\"]").ToList();
                                    }
                                    else
                                    {
                                        ids = query["fieldValue"].ToString().ToObjectOld<List<string>>();
                                    }
                                }

                                for (int i = 0; i < ids.Count; i++)
                                {
                                    string it = ids[i];
                                    WhereType conditionWhereType = WhereType.And;
                                    if (item["symbol"].Equals("in"))
                                    {
                                        conditionWhereType = i.Equals(0) && subWhereType.Equals(WhereType.And) ? WhereType.And : WhereType.Or;
                                    }
                                    else
                                    {
                                        conditionWhereType = i.Equals(0) && subWhereType.Equals(WhereType.Or) ? WhereType.Or : WhereType.And;
                                    }

                                    Dictionary<string, object> newQuery = new();
                                    newQuery.Add("whereType", conditionWhereType);
                                    newQuery.Add("jnpfKey", item["jnpfKey"]);
                                    newQuery.Add("field", item["field"].ToString());
                                    newQuery.Add("fieldValue", isListValue ? it.ToJson() : it);
                                    newQuery.Add("ConditionalType",
                                        item["symbol"].Equals("in") ? item["jnpfKey"].Equals(JnpfKeyConst.TREESELECT) ? ConditionalType.Equal : ConditionalType.Like :
                                        item["jnpfKey"].Equals(JnpfKeyConst.TREESELECT) ? ConditionalType.NoEqual : ConditionalType.NoLike);
                                    if (query.ContainsKey("where") && i.Equals(0))
                                    {
                                        newQuery.Add("where", query["where"]);
                                    }

                                    queryList.Add(newQuery);
                                }

                                if (item["symbol"].Equals("notIn"))
                                {
                                    Dictionary<string, object> nullQuery = new();
                                    nullQuery.Add("whereType", WhereType.And);
                                    nullQuery.Add("jnpfKey", item["jnpfKey"]);
                                    nullQuery.Add("field", item["field"].ToString());
                                    nullQuery.Add("fieldValue", null);
                                    nullQuery.Add("ConditionalType", ConditionalType.IsNot);
                                    queryList.Add(nullQuery);

                                    Dictionary<string, object> emptyQuery = new();
                                    emptyQuery.Add("whereType", WhereType.And);
                                    emptyQuery.Add("jnpfKey", item["jnpfKey"]);
                                    emptyQuery.Add("field", item["field"].ToString());
                                    emptyQuery.Add("fieldValue", string.Empty);
                                    emptyQuery.Add("ConditionalType", ConditionalType.IsNot);
                                    queryList.Add(emptyQuery);
                                }

                                continue;
                            }

                            query.Add("ConditionalType", item["symbol"].Equals("in") ? ConditionalType.In : ConditionalType.NotIn);
                            break;
                        case "null":
                            if (item["jnpfKey"].Equals(JnpfKeyConst.CALCULATE) || item["jnpfKey"].Equals(JnpfKeyConst.NUMINPUT) || item["jnpfKey"].Equals(JnpfKeyConst.RATE) ||
                                item["jnpfKey"].Equals(JnpfKeyConst.SLIDER))
                            {
                                query.Add("ConditionalType", ConditionalType.EqualNull);
                            }
                            else
                            {
                                query.Add("ConditionalType", ConditionalType.IsNullOrEmpty);
                            }

                            break;
                        case "notNull":
                            query.Add("ConditionalType", ConditionalType.IsNot);
                            if (query["fieldValue"].IsNullOrEmpty())
                            {
                                query["fieldValue"] = null;
                            }

                            break;
                        case "between":
                            query.Add("ConditionalType", ConditionalType.GreaterThanOrEqual);
                            query["fieldValue"] = between[0];
                            queryList.Add(query);
                            Dictionary<string, object> queryAnd = new();
                            queryAnd.Add("whereType", WhereType.And);
                            queryAnd.Add("jnpfKey", item["jnpfKey"]);
                            queryAnd.Add("field", item["field"].ToString());
                            queryAnd.Add("ConditionalType", ConditionalType.LessThanOrEqual);
                            queryAnd.Add("fieldValue", between[1]);
                            if (query.ContainsKey("CSharpTypeName"))
                            {
                                queryAnd.Add("CSharpTypeName", query["CSharpTypeName"]);
                            }

                            queryList.Add(queryAnd);
                            continue;
                    }

                    queryList.Add(query);
                }
            }

            return queryList.ToJson();
        }

        return string.Empty;
    }

    /// <summary>
    ///     数据唯一 验证.
    /// </summary>
    /// <param name="link">DbLink.</param>
    /// <param name="templateInfo">模板信息.</param>
    /// <param name="allDataMap">数据.</param>
    /// <param name="mainPrimary">主键名.</param>
    /// <param name="mainId">主键Id.</param>
    /// <param name="isUpdate">是否修改.</param>
    private void UniqueVerify(DbLink link, TemplateParsingBase templateInfo, Dictionary<string, object> allDataMap, string mainPrimary, string mainId, bool isUpdate = false)
    {
        // 单行输入 唯一验证
        if (templateInfo.AllFieldsModel.Any(x => x.__config__.jnpfKey.Equals(JnpfKeyConst.COMINPUT) && x.__config__.unique))
        {
            List<string>? relationKey = new();
            List<string>? auxiliaryFieldList = templateInfo.AuxiliaryTableFieldsModelList.Select(x => x.__config__.tableName).Distinct().ToList();
            auxiliaryFieldList.ForEach(tName =>
            {
                string? tableField = templateInfo.AllTable.Find(tf => tf.table == tName)?.tableField;
                relationKey.Add(templateInfo.MainTableName + "." + mainPrimary + "=" + tName + "." + tableField);
            });

            List<string>? fieldList = new();
            List<IConditionalModel> whereList = new();

            templateInfo.SingleFormData.Where(x => x.__config__.jnpfKey.Equals(JnpfKeyConst.COMINPUT) && x.__config__.unique).ToList().ForEach(item =>
            {
                if (allDataMap.ContainsKey(item.__vModel__) && allDataMap[item.__vModel__].IsNotEmptyOrNull())
                {
                    allDataMap[item.__vModel__] = allDataMap[item.__vModel__].ToString().Trim();
                    fieldList.Add(string.Format("{0}.{1}", item.__config__.tableName, item.__vModel__.Split("_jnpf_").Last()));
                    whereList.Add(new ConditionalCollections
                    {
                        ConditionalList = new List<KeyValuePair<WhereType, ConditionalModel>>
                        {
                            new(WhereType.Or, new ConditionalModel
                            {
                                FieldName = string.Format("{0}.{1}", item.__config__.tableName, item.__vModel__.Split("_jnpf_").Last()),
                                ConditionalType = allDataMap.ContainsKey(item.__vModel__) ? ConditionalType.Equal : ConditionalType.IsNullOrEmpty,
                                FieldValue = allDataMap.ContainsKey(item.__vModel__) ? allDataMap[item.__vModel__].ToString() : string.Empty
                            })
                        }
                    });
                }
            });

            string? itemWhere = _visualDevRepository.AsSugarClient().SqlQueryable<dynamic>("@").Where(whereList).ToSqlString();
            if (!itemWhere.Equals("@"))
            {
                relationKey.Add(itemWhere.Split("WHERE").Last());
                string querStr = string.Format(
                    "select {0} from {1} where ({2}) ",
                    string.Join(",", fieldList),
                    auxiliaryFieldList.Any() ? templateInfo.MainTableName + "," + string.Join(",", auxiliaryFieldList) : templateInfo.MainTableName,
                    string.Join(" and ", relationKey)); // 多表， 联合查询
                if (isUpdate)
                {
                    querStr = string.Format("{0} and {1}<>'{2}'", querStr, templateInfo.MainTableName + "." + mainPrimary, mainId);
                }

                if (templateInfo.FormModel.logicalDelete && _databaseService.IsAnyColumn(templateInfo.DbLink, templateInfo.MainTableName, "f_delete_mark"))
                {
                    querStr = string.Format(" {0} and {1} ", querStr, "f_delete_mark is null");
                }

                List<Dictionary<string, string>> res = _databaseService.GetSqlData(link, querStr).ToObjectOld<List<Dictionary<string, string>>>();

                if (res.Any())
                {
                    List<string> errorList = new();

                    res.ForEach(items =>
                    {
                        foreach (KeyValuePair<string, string> item in items)
                        {
                            errorList.Add(templateInfo.SingleFormData.FirstOrDefault(x => x.__vModel__.Equals(item.Key) || x.__vModel__.Contains("_jnpf_" + item.Key))?.__config__.label);
                        }
                    });

                    throw Oops.Oh(ErrorCode.D1407, string.Join(",", errorList.Distinct()));
                }
            }

            foreach (FieldsModel citem in templateInfo.ChildTableFieldsModelList)
            {
                if (allDataMap.ContainsKey(citem.__vModel__))
                {
                    List<Dictionary<string, object>> childrenValues = allDataMap[citem.__vModel__].ToObjectOld<List<Dictionary<string, object>>>();
                    if (childrenValues.Any())
                    {
                        citem.__config__.children.Where(x => x.__config__.jnpfKey.Equals(JnpfKeyConst.COMINPUT) && x.__config__.unique).ToList().ForEach(item =>
                        {
                            List<Dictionary<string, object>> vList = childrenValues.Where(xx => xx.ContainsKey(item.__vModel__)).ToList();
                            vList.ForEach(vitem =>
                            {
                                if (vitem[item.__vModel__] != null)
                                {
                                    vitem[item.__vModel__] = vitem[item.__vModel__].ToString().Trim();
                                    if (childrenValues.Where(x => x.ContainsKey(item.__vModel__) && x.ContainsValue(vitem[item.__vModel__])).Count() > 1)
                                    {
                                        throw Oops.Oh(ErrorCode.D1407, item.__config__.label);
                                    }
                                }
                            });
                        });
                    }

                    allDataMap[citem.__vModel__] = childrenValues;
                }
            }
        }
    }

    /// <summary>
    ///     组装列表查询sql.
    /// </summary>
    /// <param name="primaryKey">主键.</param>
    /// <param name="templateInfo">模板.</param>
    /// <param name="input">查询输入.</param>
    /// <param name="tableFieldKeyValue">联表查询 表字段名称 对应 前端字段名称 (应对oracle 查询字段长度不能超过30个).</param>
    /// <param name="dataPermissions">数据权限.</param>
    /// <param name="showColumnList">是否只查询显示列.</param>
    /// <returns></returns>
    private string GetListQuerySql(string primaryKey, TemplateParsingBase templateInfo, ref VisualDevModelListQueryInput input, ref Dictionary<string, string> tableFieldKeyValue,
        List<IConditionalModel> dataPermissions, bool showColumnList = false)
    {
        List<string> fields = new();

        string? sql = string.Empty; // 查询sql

        // 显示列和搜索列有子表字段
        if (templateInfo.ChildTableFields.Count > 0 && (templateInfo.ColumnData.columnList.Any(x => templateInfo.ChildTableFields.ContainsKey(x.prop)) ||
                                                        templateInfo.ColumnData.searchList.Any(xx => templateInfo.ChildTableFields.ContainsKey(xx.prop))))
        {
            string queryJson = input.queryJson;
            string superQueryJson = input.superQueryJson;
            foreach (KeyValuePair<string, string> item in templateInfo.AllTableFields)
            {
                if (input.dataRuleJson.IsNotEmptyOrNull() && input.dataRuleJson.Contains(string.Format("\"{0}\"", item.Key)))
                {
                    input.dataRuleJson = input.dataRuleJson.Replace(string.Format("\"{0}\"", item.Key), string.Format("\"{0}\"", item.Value));
                }

                if (queryJson.Contains(string.Format("\"{0}\"", item.Key)))
                {
                    queryJson = queryJson.Replace(string.Format("\"{0}\"", item.Key), string.Format("\"{0}\"", item.Value));
                    IndexSearchFieldModel? vmodel = templateInfo.ColumnData.searchList.FirstOrDefault(x => x != null && x.id != null && x.id.Equals(item.Key));
                    if (vmodel != null)
                    {
                        vmodel.__vModel__ = item.Value;
                        vmodel.prop = item.Value;
                        vmodel.id = item.Value;
                        fields.Add(item.Value);
                    }

                    IndexSearchFieldModel? appVModel = templateInfo.AppColumnData.searchList?.FirstOrDefault(x => x != null && x.id != null && x.id.Equals(item.Key));
                    if (appVModel != null)
                    {
                        appVModel.__vModel__ = item.Value;
                        appVModel.prop = item.Value;
                        appVModel.id = item.Value;
                        fields.Add(item.Value);
                    }
                }

                if (superQueryJson.IsNotEmptyOrNull() && superQueryJson.Contains(string.Format("\"{0}\"", item.Key)))
                {
                    superQueryJson = superQueryJson.Replace(string.Format("\"{0}\"", item.Key), string.Format("\"{0}\"", item.Value));
                }
            }

            List<IConditionalModel>? dataRuleQuerDic = new();
            if (input.dataRuleJson.IsNotEmptyOrNull())
            {
                dataRuleQuerDic = _visualDevRepository.AsSugarClient().Utilities.JsonToConditionalModels(input.dataRuleJson);
            }

            Dictionary<string, object>? querDic = queryJson.IsNullOrEmpty() ? null : queryJson.ToObjectOld<Dictionary<string, object>>();

            List<ConditionalCollections>? superQuerDic = new();
            List<IConditionalModel>? superCond = superQueryJson.IsNullOrEmpty() ? null : GetSuperQueryJson(superQueryJson, templateInfo);
            if (superCond != null)
            {
                superQuerDic = superCond.ToObjectOld<List<ConditionalCollections>>();
            }

            string sqlStr = "select {0} from {1} ";

            // 查询
            List<string> querySqlList = new();
            bool isInteAssistant = false;
            if (querDic != null && querDic.Any())
            {
                foreach (KeyValuePair<string, object> item in querDic)
                {
                    Dictionary<string, object> dic = new();
                    dic.Add(item.Key, item.Value);
                    List<IConditionalModel> where = GetQueryJson(dic.ToJson(), _userManager.UserOrigin == "pc" ? templateInfo.ColumnData : templateInfo.AppColumnData);

                    if (item.Key.Equals(JnpfKeyConst.JNPFKEYWORD))
                    {
                        string keywordSql = string.Empty;
                        foreach (KeyValuePair<WhereType, ConditionalModel> con in where[0].ToObjectOld<ConditionalCollections>().ConditionalList)
                        {
                            ConditionalModel? model = con.Value;
                            if (templateInfo.AllTableFields.ContainsKey(model.FieldName))
                            {
                                model.FieldName = templateInfo.AllTableFields[model.FieldName];
                            }

                            List<IConditionalModel> condition = new() { new ConditionalCollections { ConditionalList = new List<KeyValuePair<WhereType, ConditionalModel>> { con } } };
                            _sqlSugarClient = _databaseService.ChangeDataBase(templateInfo.DbLink);
                            string? itemWhere = _sqlSugarClient.SqlQueryable<object>("@")
                                .Where(condition).ToSqlString();
                            _sqlSugarClient.AsTenant().ChangeDatabase(defaultConnectionConfig.ConfigId);

                            if (itemWhere.Contains("WHERE"))
                            {
                                string? fieldName = model.FieldName.Split(".").FirstOrDefault();
                                string idField = templateInfo.AllTable.Where(x => x.table.Equals(fieldName)).First().tableField;
                                string itemSql = string.Format(sqlStr, idField.IsNullOrEmpty() ? primaryKey : idField, fieldName);
                                itemSql = string.Format("{0} where {1}", itemSql, itemWhere.Split("WHERE").Last());
                                string conditionSql = string.Format("({0} in ({1}))", primaryKey, itemSql);

                                if (keywordSql.IsNotEmptyOrNull())
                                {
                                    keywordSql = string.Format("{0} {1} {2}", keywordSql, con.Key, conditionSql);
                                }
                                else
                                {
                                    keywordSql = conditionSql;
                                }
                            }
                        }

                        if (keywordSql.IsNotEmptyOrNull())
                        {
                            keywordSql = "(" + keywordSql + ")";
                            querySqlList.Add(keywordSql);
                        }
                    }
                    else
                    {
                        string? fieldName = item.Key.Split(".").FirstOrDefault();
                        TableModel table = templateInfo.AllTable.Where(x => x.table.Equals(fieldName)).First();
                        // 去除多余的f_inte_assistant条件
                        if (table.typeId.Equals("1") && !isInteAssistant && where.Count > 1 && where.Last().ToJson().Contains("f_inte_assistant"))
                        {
                            isInteAssistant = true;
                        }
                        else
                        {
                            where.RemoveAt(where.Count - 1);
                        }

                        string itemSql = string.Format(sqlStr, table.tableField.IsNullOrEmpty() ? primaryKey : table.tableField, fieldName);

                        _sqlSugarClient = _databaseService.ChangeDataBase(templateInfo.DbLink);
                        string? itemWhere = _sqlSugarClient.SqlQueryable<object>("@")
                            .Where(where).ToSqlString();
                        _sqlSugarClient.AsTenant().ChangeDatabase(defaultConnectionConfig.ConfigId);
                        if (itemWhere.Contains("WHERE"))
                        {
                            itemSql = string.Format("({0} IN ({1}WHERE))", primaryKey, itemSql);
                            if (querySqlList.Any(it => it.Contains(itemSql.TrimEnd(')'))))
                            {
                                string? oldSql = querySqlList.Find(it => it.Contains(itemSql.TrimEnd(')')));
                                querySqlList.Remove(oldSql);
                                string newSql = string.Format("{0}WHERE{1}and{2}", oldSql.Split("WHERE").FirstOrDefault(), itemWhere.Split("WHERE").LastOrDefault(),
                                    oldSql.Split("WHERE").LastOrDefault());
                                querySqlList.Add(newSql);
                            }
                            else
                            {
                                itemSql = string.Format("{0}WHERE{1}{2}", itemSql.Split("WHERE").FirstOrDefault(), itemWhere.Split("WHERE").LastOrDefault(), itemSql.Split("WHERE").LastOrDefault());
                                querySqlList.Add(itemSql);
                            }
                        }
                    }
                }
            }

            // 高级查询
            string superQuerySqlCondition = string.Empty;
            if (superQuerDic != null && superQuerDic.Any())
            {
                foreach (ConditionalCollections item in superQuerDic)
                {
                    // 拼接分组sql条件
                    if (superQuerySqlCondition.IsNotEmptyOrNull())
                    {
                        superQuerySqlCondition = string.Format(superQuerySqlCondition + item.ConditionalList.FirstOrDefault().Key);
                    }

                    // 分组内的sql
                    string groupDataSql = string.Empty;
                    foreach (KeyValuePair<WhereType, ConditionalModel> subItem in item.ConditionalList)
                    {
                        if (subItem.Value.IsNotEmptyOrNull())
                        {
                            string? fieldName = subItem.Value.FieldName.Split(".").FirstOrDefault();
                            string idField = templateInfo.AllTable.Where(x => x.table.Equals(fieldName)).First().tableField;
                            string itemSql = string.Format(sqlStr, idField.IsNullOrEmpty() ? primaryKey : idField, fieldName);

                            List<IConditionalModel> where = new() { new ConditionalCollections { ConditionalList = new List<KeyValuePair<WhereType, ConditionalModel>> { subItem } } };
                            _sqlSugarClient = _databaseService.ChangeDataBase(templateInfo.DbLink);
                            string? itemWhere = _sqlSugarClient.SqlQueryable<object>("@")
                                .Where(where).ToSqlString();
                            _sqlSugarClient.AsTenant().ChangeDatabase(defaultConnectionConfig.ConfigId);

                            if (itemWhere.Contains("WHERE"))
                            {
                                // 分组内的sql条件
                                string groupDataSqlCondition = subItem.Key.ToString();

                                if (item.ConditionalList.FirstOrDefault().Equals(subItem))
                                {
                                    groupDataSql = string.Format("( " + groupDataSql);
                                    groupDataSqlCondition = string.Empty;
                                }

                                string splitWhere = itemSql + " where";
                                itemSql = splitWhere + itemWhere.Split("WHERE").Last();

                                // 子表字段为空 查询 处理.
                                if (templateInfo.ChildTableFields.Any(x => x.Value.Contains(fieldName + ".")) &&
                                    (subItem.ToJson().Contains("\"ConditionalType\":11") || subItem.ToJson().Contains("\"ConditionalType\":14")))
                                {
                                    groupDataSql = string.Format(groupDataSql + groupDataSqlCondition + " ({0} in ({1}) OR {0} NOT IN ( SELECT {2} FROM {3} ))", primaryKey, itemSql,
                                        templateInfo.AllTable.Where(x => x.table.Equals(fieldName)).First().tableField, fieldName);
                                }
                                else
                                {
                                    if (item.Equals(superQuerDic.FirstOrDefault()))
                                    {
                                        if (groupDataSql.Contains(splitWhere))
                                        {
                                            groupDataSql = string.Format(groupDataSql.Split(splitWhere).FirstOrDefault() + splitWhere + itemWhere.Split("WHERE").Last() + groupDataSqlCondition +
                                                                         groupDataSql.Split(splitWhere).LastOrDefault());
                                        }
                                        else
                                        {
                                            groupDataSql = string.Format(groupDataSql + groupDataSqlCondition + " ({0} in ({1}))", primaryKey, itemSql);
                                        }
                                    }
                                    else
                                    {
                                        groupDataSql = string.Format(groupDataSql + groupDataSqlCondition + " ({0} in ({1}))", primaryKey, itemSql);
                                    }
                                }

                                if (item.ConditionalList.LastOrDefault().Equals(subItem))
                                {
                                    groupDataSql = string.Format(groupDataSql + ")");
                                }
                            }
                        }
                    }

                    // 拼接分组sql
                    superQuerySqlCondition = string.Format(superQuerySqlCondition + groupDataSql);
                    groupDataSql = string.Empty;
                }

                superQuerySqlCondition = string.Format("and ({0})", superQuerySqlCondition);
            }

            // 数据过滤
            string dataRuleSqlCondition = string.Empty;
            if (dataRuleQuerDic != null && dataRuleQuerDic.Count != 0)
            {
                ConditionalTree? dataRule = (ConditionalTree)dataRuleQuerDic.FirstOrDefault();
                foreach (KeyValuePair<WhereType, IConditionalModel> item in dataRule.ConditionalList)
                {
                    // 拼接分组sql条件
                    if (dataRuleSqlCondition.IsNotEmptyOrNull())
                    {
                        dataRuleSqlCondition = string.Format(dataRuleSqlCondition + item.Key);
                    }

                    // 分组内的sql
                    string groupDataSql = string.Empty;

                    ConditionalTree? groupDataValue = (ConditionalTree)item.Value;
                    foreach (KeyValuePair<WhereType, IConditionalModel> subItem in groupDataValue.ConditionalList)
                    {
                        if (subItem.Value.IsNotEmptyOrNull())
                        {
                            KeyValuePair<WhereType, IConditionalModel> field = ((ConditionalTree)subItem.Value).ConditionalList.FirstOrDefault();
                            string? fieldName = ((ConditionalModel)field.Value).FieldName.Split(".").FirstOrDefault();
                            string idField = templateInfo.AllTable.Where(x => x.table.Equals(fieldName)).First().tableField;
                            string itemSql = string.Format(sqlStr, idField.IsNullOrEmpty() ? primaryKey : idField, fieldName);

                            List<IConditionalModel> where = new() { new ConditionalTree { ConditionalList = new List<KeyValuePair<WhereType, IConditionalModel>> { subItem } } };
                            _sqlSugarClient = _databaseService.ChangeDataBase(templateInfo.DbLink);
                            string? itemWhere = _sqlSugarClient.SqlQueryable<object>("@")
                                .Where(where).ToSqlString();
                            _sqlSugarClient.AsTenant().ChangeDatabase(defaultConnectionConfig.ConfigId);

                            if (itemWhere.Contains("WHERE"))
                            {
                                // 分组内的sql条件
                                string groupDataSqlCondition = subItem.Key.ToString();

                                if (groupDataValue.ConditionalList.FirstOrDefault().Equals(subItem))
                                {
                                    groupDataSql = string.Format("( " + groupDataSql);
                                    groupDataSqlCondition = string.Empty;
                                }

                                string splitWhere = itemSql + " where";
                                itemSql = splitWhere + itemWhere.Split("WHERE").Last();

                                // 子表字段为空 查询 处理.
                                if (templateInfo.ChildTableFields.Any(x => x.Value.Contains(fieldName + ".")) &&
                                    (subItem.ToJson().Contains("\"ConditionalType\":11") || subItem.ToJson().Contains("\"ConditionalType\":14")))
                                {
                                    groupDataSql = string.Format(groupDataSql + groupDataSqlCondition + " ({0} in ({1}) OR {0} NOT IN ( SELECT {2} FROM {3} ))", primaryKey, itemSql,
                                        templateInfo.AllTable.Where(x => x.table.Equals(fieldName)).First().tableField, fieldName);
                                }
                                else
                                {
                                    groupDataSql = string.Format(groupDataSql + groupDataSqlCondition + " ({0} in ({1}))", primaryKey, itemSql);
                                }

                                if (groupDataValue.ConditionalList.LastOrDefault().Equals(subItem))
                                {
                                    groupDataSql = string.Format(groupDataSql + ")");
                                }
                            }
                        }
                    }

                    // 拼接分组sql
                    dataRuleSqlCondition = string.Format(dataRuleSqlCondition + groupDataSql);
                    groupDataSql = string.Empty;
                }

                if (dataRuleSqlCondition.IsNotEmptyOrNull())
                {
                    dataRuleSqlCondition = string.Format("and ({0})", dataRuleSqlCondition);
                }
            }

            // 拼接数据权限
            string dataPermissionsSqlCondition = string.Empty;
            if (dataPermissions != null && dataPermissions.Any())
            {
                ConditionalTree? allCondition = (ConditionalTree)dataPermissions.FirstOrDefault();
                foreach (KeyValuePair<WhereType, IConditionalModel> roleCondition in allCondition.ConditionalList)
                {
                    // 拼接多个权限组sql条件
                    if (dataPermissionsSqlCondition.IsNotEmptyOrNull())
                    {
                        dataPermissionsSqlCondition = string.Format("(" + dataPermissionsSqlCondition + ")" + roleCondition.Key);
                    }

                    string? roleConditionSql = string.Empty;
                    if (roleCondition.Value.GetType().Name.Equals("ConditionalModel"))
                    {
                        List<IConditionalModel> where = new() { new ConditionalTree { ConditionalList = new List<KeyValuePair<WhereType, IConditionalModel>> { roleCondition } } };
                        string? itemWhere = _visualDevRepository.AsSugarClient().SqlQueryable<dynamic>("@").Where(where).ToSqlString();
                        roleConditionSql = itemWhere.Split("WHERE").Last();
                    }
                    else
                    {
                        foreach (KeyValuePair<WhereType, IConditionalModel> dpCondition in ((ConditionalTree)roleCondition.Value).ConditionalList)
                        {
                            // 拼接多个权限sql条件
                            if (roleConditionSql.IsNotEmptyOrNull())
                            {
                                roleConditionSql = string.Format("(" + roleConditionSql + ")" + dpCondition.Key);
                            }

                            string dpConditionSql = string.Empty;
                            foreach (KeyValuePair<WhereType, IConditionalModel> groupCondition in ((ConditionalTree)dpCondition.Value).ConditionalList)
                            {
                                // 拼接分组sql条件
                                if (dpConditionSql.IsNotEmptyOrNull())
                                {
                                    dpConditionSql = string.Format(dpConditionSql + groupCondition.Key);
                                }

                                string groupConditionSql = string.Empty;
                                foreach (KeyValuePair<WhereType, IConditionalModel> condition in ((ConditionalTree)groupCondition.Value).ConditionalList)
                                {
                                    string? fieldName = ((ConditionalModel)condition.Value).FieldName.Split(".").FirstOrDefault();
                                    string idField = templateInfo.AllTable.Where(x => x.table.Equals(fieldName)).First().tableField;
                                    string itemSql = string.Format(sqlStr, idField.IsNullOrEmpty() ? primaryKey : idField, fieldName);
                                    List<IConditionalModel> where = new() { new ConditionalTree { ConditionalList = new List<KeyValuePair<WhereType, IConditionalModel>> { condition } } };
                                    string? itemWhere = _visualDevRepository.AsSugarClient().SqlQueryable<dynamic>("@").Where(where).ToSqlString();
                                    if (itemWhere.Contains("WHERE"))
                                    {
                                        // 分组内的sql条件
                                        string conditionWhere = condition.Key.ToString();
                                        if (((ConditionalTree)groupCondition.Value).ConditionalList.FirstOrDefault().Equals(condition))
                                        {
                                            groupConditionSql = string.Format("( " + groupConditionSql);
                                            conditionWhere = string.Empty;
                                        }

                                        string splitWhere = itemSql + " where";
                                        itemSql = splitWhere + itemWhere.Split("WHERE").Last();

                                        // 子表字段为空 查询 处理.
                                        if (templateInfo.ChildTableFields.Any(x => x.Value.Contains(fieldName + ".")) && (condition.ToJson().Contains("\"ConditionalType\":11") ||
                                                                                                                          condition.ToJson().Contains("\"ConditionalType\":14")))
                                        {
                                            groupConditionSql = string.Format(groupConditionSql + conditionWhere + " ({0} in ({1}) OR {0} NOT IN ( SELECT {2} FROM {3} ))", primaryKey, itemSql,
                                                templateInfo.AllTable.Where(x => x.table.Equals(fieldName)).First().tableField, fieldName);
                                        }
                                        else
                                        {
                                            if (groupCondition.Equals(((ConditionalTree)dpCondition.Value).ConditionalList.FirstOrDefault()))
                                            {
                                                if (groupConditionSql.Contains(splitWhere))
                                                {
                                                    groupConditionSql = string.Format(groupConditionSql.Split(splitWhere).FirstOrDefault() + splitWhere + itemWhere.Split("WHERE").Last() +
                                                                                      conditionWhere + groupConditionSql.Split(splitWhere).LastOrDefault());
                                                }
                                                else
                                                {
                                                    groupConditionSql = string.Format(groupConditionSql + conditionWhere + " ({0} in ({1}))", primaryKey, itemSql);
                                                }
                                            }
                                            else
                                            {
                                                groupConditionSql = string.Format(groupConditionSql + conditionWhere + " ({0} in ({1}))", primaryKey, itemSql);
                                            }
                                        }
                                    }

                                    if (((ConditionalTree)groupCondition.Value).ConditionalList.LastOrDefault().Equals(condition))
                                    {
                                        groupConditionSql = string.Format(groupConditionSql + ")");
                                    }
                                }

                                // 拼接分组sql
                                dpConditionSql = string.Format(dpConditionSql + groupConditionSql);
                                groupConditionSql = string.Empty;
                            }

                            // 拼接多个权限sql
                            roleConditionSql = string.Format(roleConditionSql + "(" + dpConditionSql + ")");
                            dpConditionSql = string.Empty;
                        }
                    }

                    // 拼接多个权限sql
                    dataPermissionsSqlCondition = string.Format(dataPermissionsSqlCondition + "(" + roleConditionSql + ")");
                    roleConditionSql = string.Empty;
                }

                dataPermissionsSqlCondition = string.Format("and ({0})", dataPermissionsSqlCondition);
            }

            if (templateInfo.FormModel.logicalDelete && _databaseService.IsAnyColumn(templateInfo.DbLink, templateInfo.MainTableName, "f_delete_mark"))
            {
                querySqlList.Add(string.Format(" ( {0} in ({1}) ) ", primaryKey, string.Format(" select {0} from {1} where f_delete_mark is null ", primaryKey, templateInfo.MainTableName))); // 处理软删除
            }

            // 多租户字段隔离
            if (_tenant.MultiTenancy)
            {
                GlobalTenantCacheModel? tenantCache = _cacheManager.Get<List<GlobalTenantCacheModel>>(CommonConst.GLOBALTENANT).Find(it => it.TenantId.Equals(templateInfo.DbLink.Id));
                if (tenantCache.IsNotEmptyOrNull() && tenantCache.type.Equals(1) && _databaseService.IsAnyColumn(templateInfo.DbLink, templateInfo.MainTableName, "f_tenant_id"))
                {
                    querySqlList.Add(string.Format(" ( {0} in ({1}) ) ", primaryKey,
                        string.Format(" select {0} from {1} where f_tenant_id='{2}'", primaryKey, templateInfo.MainTableName, tenantCache.connectionConfig.IsolationField)));
                }
            }

            // 是否只展示流程数据
            //if (templateInfo.visualDevEntity.EnableFlow.Equals(1))
            //    querySqlList.Add(string.Format(" {0} in ({1}) ", primaryKey, string.Format(" select {0} from {1} where f_flow_id <> '' ", primaryKey, templateInfo.MainTableName)));
            //else
            //    querySqlList.Add(string.Format(" {0} in ({1}) ", primaryKey, string.Format(" select {0} from {1} where f_flow_id is null or f_flow_id = '' ", primaryKey, templateInfo.MainTableName)));

            if (!querySqlList.Any())
            {
                querySqlList.Add(string.Format(" ( {0} in ({1}) ) ", primaryKey, string.Format(sqlStr, primaryKey, templateInfo.MainTableName)));
            }

            Dictionary<string, string> ctFields = templateInfo.ChildTableFields;
            templateInfo.ChildTableFields = new Dictionary<string, string>();
            string strSql = GetListQuerySql(primaryKey, templateInfo, ref input, ref tableFieldKeyValue, new List<IConditionalModel>());
            input.dataRuleJson = string.Empty;
            input.queryJson = string.Empty;
            input.superQueryJson = string.Empty;
            templateInfo.ChildTableFields = ctFields;

            sql = string.Format("select * from ({0}) mt where {1} {2} {3} {4}", strSql, string.Join(" and ", querySqlList), superQuerySqlCondition, dataRuleSqlCondition, dataPermissionsSqlCondition);
        }
        else if (!templateInfo.AuxiliaryTableFieldsModelList.Any())
        {
            fields.Add(primaryKey); // 主键
            fields.Add("f_inte_assistant"); // 集成助手数据标识
            if (templateInfo.WebType.Equals(3))
            {
                fields.Add("f_flow_id");
            }

            // 只查询 要显示的列
            if (showColumnList && (templateInfo.SingleFormData.Count > 0 || templateInfo.ColumnData.columnList.Count > 0))
            {
                templateInfo.ColumnData.columnList.ForEach(item =>
                {
                    if (templateInfo.SingleFormData.Any(x => x.__vModel__.Equals(item.prop)))
                    {
                        fields.Add(item.prop);
                    }
                });
            }
            else
            {
                templateInfo.MainTableFieldsModelList.Where(x => x.__vModel__.IsNotEmptyOrNull()).ToList().ForEach(item => fields.Add(item.__vModel__)); // 字段
            }

            sql = string.Format("select {0} from {1}", string.Join(",", fields), templateInfo.MainTableName);
            if (templateInfo.FormModel.logicalDelete && _databaseService.IsAnyColumn(templateInfo.DbLink, templateInfo.MainTableName, "f_delete_mark"))
            {
                sql += " where f_delete_mark is null "; // 处理软删除
            }

            // 多租户字段隔离
            if (_tenant.MultiTenancy)
            {
                GlobalTenantCacheModel? tenantCache = _cacheManager.Get<List<GlobalTenantCacheModel>>(CommonConst.GLOBALTENANT).Find(it => it.TenantId.Equals(templateInfo.DbLink.Id));
                if (tenantCache.IsNotEmptyOrNull() && tenantCache.type.Equals(1) && _databaseService.IsAnyColumn(templateInfo.DbLink, templateInfo.MainTableName, "f_tenant_id"))
                {
                    sql += string.Format(" {0} f_tenant_id='{1}' ", sql.Contains("where") ? "and" : "where", tenantCache.connectionConfig.IsolationField);
                }
            }

            // 是否只展示流程数据
            //if (templateInfo.visualDevEntity.EnableFlow.Equals(1)) sql += string.Format(" {0} f_flow_id <> '' ", sql.Contains("where") ? "and" : "where");
            //else sql += string.Format(" {0} f_flow_id is null or f_flow_id = '' ", sql.Contains("where") ? "and" : "where");

            // 拼接数据权限
            if (dataPermissions != null && dataPermissions.Any())
            {
                // 替换数据权限字段 别名
                string pvalue = dataPermissions.ToJson();
                foreach (string item in fields)
                {
                    if (pvalue.Contains(templateInfo.MainTableName + "." + item))
                    {
                        pvalue = pvalue.Replace(string.Format("\"FieldName\":\"{0}\",", templateInfo.MainTableName + "." + item), string.Format("\"FieldName\":\"{0}\",", item));
                    }
                }

                List<IConditionalModel>? newPvalue = new();
                if (pvalue.IsNotEmptyOrNull())
                {
                    newPvalue = _visualDevRepository.AsSugarClient().Utilities.JsonToConditionalModels(pvalue);
                }

                sql = _visualDevRepository.AsSugarClient().SqlQueryable<dynamic>(sql).Where(newPvalue).ToSqlString();
            }
        }
        else
        {
            #region 所有主、副表 字段名 和 处理查询、排序字段

            // 所有主、副表 字段名
            fields.Add(templateInfo.MainTableName + "." + primaryKey);
            fields.Add(templateInfo.MainTableName + ".f_inte_assistant"); // 集成助手数据标识
            if (templateInfo.WebType.Equals(3))
            {
                fields.Add(templateInfo.MainTableName + ".f_flow_id");
            }

            tableFieldKeyValue.Add(primaryKey.ToUpper(), primaryKey);
            tableFieldKeyValue.Add("f_flow_id".ToUpper(), "f_flow_id");
            Dictionary<string, object>? inputJson = input.queryJson?.ToObjectOld<Dictionary<string, object>>();
            for (int i = 0; i < templateInfo.SingleFormData.Count; i++)
            {
                string? vmodel = templateInfo.SingleFormData[i].__vModel__.ReplaceRegex(@"(\w+)_jnpf_", string.Empty); // Field

                // 只显示要显示的列
                if (showColumnList && !templateInfo.ColumnData.columnList.Any(x => x.prop == templateInfo.SingleFormData[i].__vModel__))
                {
                    vmodel = string.Empty;
                }

                if (vmodel.IsNotEmptyOrNull())
                {
                    fields.Add(templateInfo.SingleFormData[i].__config__.tableName + "." + vmodel + " FIELD_" + i); // TableName.Field_0
                    tableFieldKeyValue.Add("FIELD_" + i, templateInfo.SingleFormData[i].__vModel__);

                    // 查询字段替换
                    if (inputJson != null && inputJson.Count > 0 && inputJson.ContainsKey(templateInfo.SingleFormData[i].__vModel__))
                    {
                        input.queryJson = input.queryJson.Replace("\"" + templateInfo.SingleFormData[i].__vModel__ + "\":", "\"FIELD_" + i + "\":");
                    }

                    if (input.superQueryJson.IsNotEmptyOrNull())
                    {
                        input.superQueryJson = input.superQueryJson.Replace(string.Format("\"field\":\"{0}\"", templateInfo.SingleFormData[i].__vModel__),
                            string.Format("\"field\":\"{0}\"", "FIELD_" + i));
                    }

                    if (input.dataRuleJson.IsNotEmptyOrNull())
                    {
                        input.dataRuleJson = input.dataRuleJson.Replace(string.Format("\"FieldName\":\"{0}\"", templateInfo.SingleFormData[i].__vModel__),
                            string.Format("\"FieldName\":\"{0}\"", "FIELD_" + i));
                    }

                    templateInfo.ColumnData.searchList.Where(x => x.__vModel__ == templateInfo.SingleFormData[i].__vModel__).ToList().ForEach(item =>
                    {
                        item.id = item.id.Replace(templateInfo.SingleFormData[i].__vModel__, "FIELD_" + i);
                        item.__vModel__ = item.__vModel__.Replace(templateInfo.SingleFormData[i].__vModel__, "FIELD_" + i);
                    });

                    // 排序字段替换
                    if (input.sidx.IsNotEmptyOrNull() && input.sidx.Contains(templateInfo.SingleFormData[i].__vModel__))
                    {
                        input.sidx = input.sidx.Replace(templateInfo.SingleFormData[i].__vModel__, "FIELD_" + i);
                    }
                }
            }

            #endregion

            #region 关联字段

            List<string>? relationKey = new();
            List<string>? auxiliaryFieldList = templateInfo.AuxiliaryTableFieldsModelList.Select(x => x.__config__.tableName).Distinct().ToList();
            auxiliaryFieldList.ForEach(tName =>
            {
                TableModel? tableField = templateInfo.AllTable.Find(tf => tf.table == tName);
                relationKey.Add(templateInfo.MainTableName + "." + tableField.relationField + "=" + tName + "." + tableField.tableField);
            });
            if (templateInfo.FormModel.logicalDelete && _databaseService.IsAnyColumn(templateInfo.DbLink, templateInfo.MainTableName, "f_delete_mark"))
            {
                relationKey.Add(templateInfo.MainTableName + ".f_delete_mark is null "); // 处理软删除
            }

            // 多租户字段隔离
            if (_tenant.MultiTenancy)
            {
                GlobalTenantCacheModel? tenantCache = _cacheManager.Get<List<GlobalTenantCacheModel>>(CommonConst.GLOBALTENANT).Find(it => it.TenantId.Equals(templateInfo.DbLink.Id));
                if (tenantCache.IsNotEmptyOrNull() && tenantCache.type.Equals(1) && _databaseService.IsAnyColumn(templateInfo.DbLink, templateInfo.MainTableName, "f_tenant_id"))
                {
                    relationKey.Add(string.Format(" {0}.f_tenant_id='{1}' ", templateInfo.MainTableName, tenantCache.connectionConfig.IsolationField));
                }
            }

            // 是否只展示流程数据
            //if (templateInfo.visualDevEntity.EnableFlow.Equals(1)) relationKey.Add(templateInfo.MainTableName + ".f_flow_id <> '' ");
            //else relationKey.Add(templateInfo.MainTableName + ".f_flow_id is null or f_flow_id = '' ");

            string? whereStr = string.Join(" and ", relationKey);

            #endregion

            sql = string.Format("select {0} from {1} where {2}", string.Join(",", fields), templateInfo.MainTableName + "," + string.Join(",", auxiliaryFieldList), whereStr); // 多表， 联合查询

            // 拼接数据权限
            if (dataPermissions != null && dataPermissions.Any())
            {
                // 替换数据权限字段 别名
                string pvalue = dataPermissions.ToJson();
                foreach (KeyValuePair<string, string> item in tableFieldKeyValue)
                {
                    string? newValue = item.Value;
                    if (templateInfo.AllTableFields.ContainsKey(item.Value))
                    {
                        newValue = templateInfo.AllTableFields[item.Value];
                    }

                    if (pvalue.Contains(newValue))
                    {
                        pvalue = pvalue.Replace(string.Format("\"FieldName\":\"{0}\",", newValue), string.Format("\"FieldName\":\"{0}\",", item.Key));
                    }
                    else
                    {
                        if (newValue.Contains(templateInfo.MainTableName))
                        {
                            newValue = newValue.Replace(templateInfo.MainTableName + ".", string.Empty);
                        }

                        if (pvalue.Contains(newValue))
                        {
                            pvalue = pvalue.Replace(string.Format("\"FieldName\":\"{0}\",", newValue), string.Format("\"FieldName\":\"{0}\",", item.Key));
                        }
                    }
                }

                List<IConditionalModel>? newPvalue = new();
                if (pvalue.IsNotEmptyOrNull())
                {
                    newPvalue = _visualDevRepository.AsSugarClient().Utilities.JsonToConditionalModels(pvalue);
                }

                sql = _visualDevRepository.AsSugarClient().SqlQueryable<dynamic>(sql).Where(newPvalue).ToSqlString();
            }
        }

        return sql;
    }

    private List<IConditionalModel> GetIConditionalModelListByTableName(List<IConditionalModel> cList, string tableName)
    {
        for (int i = 0; i < cList.Count; i++)
        {
            if (cList[i] is ConditionalTree)
            {
                ConditionalTree newItem = (ConditionalTree)cList[i];
                for (int j = 0; j < newItem.ConditionalList.Count; j++)
                {
                    List<IConditionalModel>? value = GetIConditionalModelListByTableName(new List<IConditionalModel> { newItem.ConditionalList[j].Value }, tableName);
                    if (value != null && value.Any())
                    {
                        if (newItem.ConditionalList[j].Equals(newItem.ConditionalList.FirstOrDefault()))
                        {
                            newItem.ConditionalList[j] = new KeyValuePair<WhereType, IConditionalModel>(WhereType.Null, value.First());
                        }
                        else
                        {
                            newItem.ConditionalList[j] = new KeyValuePair<WhereType, IConditionalModel>(newItem.ConditionalList[j].Key, value.First());
                        }
                    }
                    else
                    {
                        newItem.ConditionalList.RemoveAt(j);
                        j--;
                    }
                }

                if (newItem.ConditionalList.Any())
                {
                    cList[i] = newItem;
                }
                else
                {
                    cList.RemoveAt(i);
                    i--;
                }
            }
            else if (cList[i] is ConditionalModel)
            {
                ConditionalModel newItem = (ConditionalModel)cList[i];
                if (!newItem.FieldName.Contains(tableName))
                {
                    cList.RemoveAt(i);
                }
            }
        }

        return cList;
    }

    /// <summary>
    ///     组装单条信息查询sql.
    /// </summary>
    /// <param name="id">id.</param>
    /// <param name="mainPrimary">主键.</param>
    /// <param name="templateInfo">模板.</param>
    /// <param name="tableFieldKeyValue">联表查询 表字段名称 对应 前端字段名称 (应对oracle 查询字段长度不能超过30个).</param>
    /// <returns></returns>
    private string GetInfoQuerySql(string id, DbTableFieldModel mainPrimary, TemplateParsingBase templateInfo, ref Dictionary<string, string> tableFieldKeyValue)
    {
        List<string> fields = new();
        string? sql = string.Empty; // 查询sql
        if (mainPrimary.dataType == "varchar")
            id = "'" + id + "'";
        // 没有副表,只查询主表
        if (!templateInfo.AuxiliaryTableFieldsModelList.Any())
        {
            fields.Add(mainPrimary.field); // 主表主键
            if (templateInfo.WebType.Equals(3))
            {
                fields.Add("f_flow_id");
            }

            templateInfo.MainTableFieldsModelList.Where(x => x.__vModel__.IsNotEmptyOrNull()).ToList().ForEach(item => fields.Add(item.__vModel__)); // 主表列名
           
            sql = $"select {string.Join(",", fields)} from {templateInfo.MainTableName} where {mainPrimary.field}={id}";
        }
        else
        {
            #region 所有主表、副表 字段名

            fields.Add(templateInfo.MainTableName + "." + mainPrimary.field); // 主表主键
            if (templateInfo.WebType.Equals(3))
            {
                fields.Add(templateInfo.MainTableName + ".f_flow_id");
            }

            for (int i = 0; i < templateInfo.SingleFormData.Count; i++)
            {
                string? vmodel = templateInfo.SingleFormData[i].__vModel__.ReplaceRegex(@"(\w+)_jnpf_", ""); // Field
                if (vmodel.IsNotEmptyOrNull())
                {
                    fields.Add(templateInfo.SingleFormData[i].__config__.tableName + "." + vmodel + " FIELD" + i); // TableName.Field_0
                    tableFieldKeyValue.Add("FIELD" + i, templateInfo.SingleFormData[i].__vModel__);
                }
            }

            #endregion

            #region 所有副表 关联字段

            List<string>? ctNameList = templateInfo.AuxiliaryTableFieldsModelList.Select(x => x.__config__.tableName).Distinct().ToList();
            List<string>? relationKey = new();
            relationKey.Add(string.Format(" {0}.{1}={2} ", templateInfo.MainTableName, mainPrimary.field, id)); // 主表ID
            ctNameList.ForEach(tName =>
            {
                string? tableField = templateInfo.AllTable.Find(tf => tf.table == tName)?.tableField;
                relationKey.Add(string.Format(" {0}.{1}={2}.{3} ", templateInfo.MainTableName, mainPrimary.field, tName, tableField));
            });
            string? whereStr = string.Join(" and ", relationKey);

            #endregion

            sql = $"select {string.Join(",", fields)} from {templateInfo.MainTableName + "," + string.Join(",", ctNameList)} where {whereStr}"; // 多表， 联合查询
        }

        return sql;
    }

    /// <summary>
    ///     组装 查询 json.
    /// </summary>
    /// <param name="queryJson"></param>
    /// <param name="columnDesign"></param>
    /// <param name="isInteAssisData">是否为集成助手数据</param>
    /// <returns></returns>
    private List<IConditionalModel> GetQueryJson(string queryJson, ColumnDesignModel columnDesign, int isInteAssisData = 0)
    {
        // 将查询的关键字json转成Dictionary
        Dictionary<string, object> keywordJsonDic = string.IsNullOrEmpty(queryJson) ? null : queryJson.ToObjectOld<Dictionary<string, object>>();
        List<IConditionalModel> conModels = new();
        if (keywordJsonDic != null)
        {
            foreach ((string key, object value) in keywordJsonDic)
            {
                keywordJsonDic[key] = value.GetJsonElementValue();
            }

            foreach (KeyValuePair<string, object> item in keywordJsonDic)
            {
                if (item.Key.Equals(JnpfKeyConst.JNPFKEYWORD) && columnDesign.searchList.Any(it => it.isKeyword))
                {
                    ConditionalCollections con = new() { ConditionalList = new List<KeyValuePair<WhereType, ConditionalModel>>() };
                    foreach (IndexSearchFieldModel model in columnDesign.searchList.FindAll(it => it.isKeyword))
                    {
                        KeyValuePair<WhereType, ConditionalModel> conditional = new(WhereType.Or, new ConditionalModel
                        {
                            FieldName = model.id,
                            ConditionalType = ConditionalType.Like,
                            FieldValue = item.Value.ToString()
                        });
                        con.ConditionalList.Add(conditional);
                    }

                    conModels.Add(con);
                }
                else
                {
                    IndexSearchFieldModel? model = columnDesign.searchList.Find(it => it.id.Equals(item.Key));
                    if (model.IsNullOrEmpty())
                    {
                        model = columnDesign.searchList.Find(it => it.__vModel__.Equals(item.Key));
                    }

                    switch (model.__config__.jnpfKey)
                    {
                        case JnpfKeyConst.DATE:
                        case JnpfKeyConst.CREATETIME:
                        case JnpfKeyConst.MODIFYTIME:
                        {
                            List<string> timeRange = item.Value.ToObjectOld<List<string>>();
                            DateTime startTime = timeRange.First().TimeStampToDateTime();
                            DateTime endTime = timeRange.Last().TimeStampToDateTime();

                            conModels.Add(new ConditionalCollections
                            {
                                ConditionalList = new List<KeyValuePair<WhereType, ConditionalModel>>
                                {
                                    new(WhereType.And, new ConditionalModel
                                    {
                                        FieldName = item.Key,
                                        ConditionalType = ConditionalType.GreaterThanOrEqual,
                                        FieldValue = new DateTime(startTime.Year, startTime.Month, startTime.Day, startTime.Hour, startTime.Minute, startTime.Second, 0).ToString(),
                                        CSharpTypeName = "datetime",
                                        FieldValueConvertFunc = it => Convert.ToDateTime(it)
                                    }),
                                    new(WhereType.And, new ConditionalModel
                                    {
                                        FieldName = item.Key,
                                        ConditionalType = ConditionalType.LessThanOrEqual,
                                        FieldValue = new DateTime(endTime.Year, endTime.Month, endTime.Day, endTime.Hour, endTime.Minute, endTime.Second, 999).ToString(),
                                        CSharpTypeName = "datetime",
                                        FieldValueConvertFunc = it => Convert.ToDateTime(it)
                                    })
                                }
                            });
                        }

                            break;
                        case JnpfKeyConst.TIME:
                        {
                            List<string> timeRange = item.Value.ToObjectOld<List<string>>();
                            string startTime = string.Format("{0:" + model.format + "}", Convert.ToDateTime(timeRange.First()));
                            string endTime = string.Format("{0:" + model.format + "}", Convert.ToDateTime(timeRange.Last()));
                            conModels.Add(new ConditionalCollections
                            {
                                ConditionalList = new List<KeyValuePair<WhereType, ConditionalModel>>
                                {
                                    new(WhereType.And, new ConditionalModel
                                    {
                                        FieldName = item.Key,
                                        ConditionalType = ConditionalType.GreaterThanOrEqual,
                                        FieldValue = startTime
                                    }),
                                    new(WhereType.And, new ConditionalModel
                                    {
                                        FieldName = item.Key,
                                        ConditionalType = ConditionalType.LessThanOrEqual,
                                        FieldValue = endTime
                                    })
                                }
                            });
                        }

                            break;
                        case JnpfKeyConst.NUMINPUT:
                        case JnpfKeyConst.CALCULATE:
                        {
                            List<string> numArray = item.Value.ToObjectOld<List<string>>();
                            decimal startNum = numArray.First().ParseToDecimal();
                            decimal endNum = numArray.Last() == null ? decimal.MaxValue : numArray.Last().ParseToDecimal();
                            conModels.Add(new ConditionalCollections
                            {
                                ConditionalList = new List<KeyValuePair<WhereType, ConditionalModel>>
                                {
                                    new(WhereType.And, new ConditionalModel
                                    {
                                        CSharpTypeName = "decimal",
                                        FieldName = item.Key,
                                        ConditionalType = ConditionalType.GreaterThanOrEqual,
                                        FieldValue = startNum.ToString()
                                    }),
                                    new(WhereType.And, new ConditionalModel
                                    {
                                        CSharpTypeName = "decimal",
                                        FieldName = item.Key,
                                        ConditionalType = ConditionalType.LessThanOrEqual,
                                        FieldValue = endNum.ToString()
                                    })
                                }
                            });
                        }

                            break;
                        case JnpfKeyConst.CHECKBOX:
                        {
                            //if (model.searchType.Equals(1))
                            //    conModels.Add(new ConditionalModel { FieldName = item.Key, ConditionalType = ConditionalType.Equal, FieldValue = item.Value.ToString() });
                            //else
                            conModels.Add(new ConditionalCollections
                            {
                                ConditionalList = new List<KeyValuePair<WhereType, ConditionalModel>>
                                {
                                    new(WhereType.And, new ConditionalModel
                                    {
                                        FieldName = item.Key,
                                        ConditionalType = ConditionalType.Like,
                                        FieldValue = item.Value.ToJson()
                                    })
                                }
                            });
                        }

                            break;
                        case JnpfKeyConst.ROLESELECT:
                        // case JnpfKeyConst.GROUPSELECT:
                        // case JnpfKeyConst.POSSELECT:
                        case JnpfKeyConst.USERSELECT:
                            // case JnpfKeyConst.DEPSELECT:
                        {
                            // 多选时为模糊查询
                            if (model.multiple || model.searchMultiple)
                            {
                                List<object>? value = item.Value.ToString().Contains("[") ? item.Value.ToObjectOld<List<object>>() : new List<object> { item.Value.ToString() };
                                List<KeyValuePair<WhereType, ConditionalModel>> addItems = new();
                                for (int i = 0; i < value.Count; i++)
                                {
                                    KeyValuePair<WhereType, ConditionalModel> add = new(i == 0 ? WhereType.And : WhereType.Or, new ConditionalModel
                                    {
                                        FieldName = item.Key,
                                        ConditionalType = model.multiple ? ConditionalType.Like : ConditionalType.Equal,
                                        FieldValue = model.multiple ? value[i].ToJson() : value[i].ToString()
                                    });
                                    addItems.Add(add);
                                }

                                conModels.Add(new ConditionalCollections { ConditionalList = addItems });
                            }
                            else
                            {
                                string? value = item.Value.ToString().Contains("[") ? item.Value.ToObjectOld<List<string>>().FirstOrDefault() : item.Value.ToString();
                                conModels.Add(new ConditionalCollections
                                {
                                    ConditionalList = new List<KeyValuePair<WhereType, ConditionalModel>>
                                    {
                                        new(WhereType.And, new ConditionalModel
                                        {
                                            FieldName = item.Key,
                                            ConditionalType = ConditionalType.Equal,
                                            FieldValue = value
                                        })
                                    }
                                });
                            }
                        }

                            break;
                        case JnpfKeyConst.USERSSELECT:
                        {
                            if (item.Value != null)
                            {
                                if (model.multiple || model.searchMultiple)
                                {
                                    List<string>? objIdList = new();
                                    if (item.Value.ToString().Contains("["))
                                    {
                                        objIdList = item.Value.ToObjectOld<List<string>>();
                                    }
                                    else
                                    {
                                        objIdList.Add(item.Value.ToString());
                                    }

                                    // var rIdList = _visualDevRepository.AsSugarClient().Queryable<UserRelationEntity>()
                                    //     .Where(x => objIdList.Select(xx => xx.Replace("--user", string.Empty)).Contains(x.UserId)).Select(x => new {x.ObjectId, x.ObjectType}).ToList();
                                    // rIdList.ForEach(x =>
                                    // {
                                    //     if (x.ObjectType.Equals("Organize"))
                                    //     {
                                    //         objIdList.Add(x.ObjectId + "--company");
                                    //         objIdList.Add(x.ObjectId + "--department");
                                    //     }
                                    //     else
                                    //     {
                                    //         objIdList.Add(x.ObjectId + "--" + x.ObjectType.ToLower());
                                    //     }
                                    // });

                                    List<KeyValuePair<WhereType, ConditionalModel>> whereList = new();
                                    for (int i = 0; i < objIdList.Count(); i++)
                                    {
                                        if (i == 0)
                                        {
                                            whereList.Add(new KeyValuePair<WhereType, ConditionalModel>(WhereType.And, new ConditionalModel
                                            {
                                                FieldName = item.Key,
                                                ConditionalType = ConditionalType.Like,
                                                FieldValue = objIdList[i]
                                            }));
                                        }
                                        else
                                        {
                                            whereList.Add(new KeyValuePair<WhereType, ConditionalModel>(WhereType.Or, new ConditionalModel
                                            {
                                                FieldName = item.Key,
                                                ConditionalType = ConditionalType.Like,
                                                FieldValue = objIdList[i]
                                            }));
                                        }
                                    }

                                    conModels.Add(new ConditionalCollections { ConditionalList = whereList });
                                }
                                else
                                {
                                    conModels.Add(new ConditionalCollections
                                    {
                                        ConditionalList = new List<KeyValuePair<WhereType, ConditionalModel>>
                                        {
                                            new(WhereType.And, new ConditionalModel
                                            {
                                                FieldName = item.Key,
                                                ConditionalType = ConditionalType.Equal,
                                                FieldValue = item.Value.ToString()
                                            })
                                        }
                                    });
                                }
                            }
                        }

                            break;
                        case JnpfKeyConst.TREESELECT:
                        {
                            if (item.Value.IsNotEmptyOrNull() && item.Value.ToString().Contains("["))
                            {
                                List<string> value = item.Value.ToObjectOld<List<string>>();

                                conModels.Add(new ConditionalCollections
                                {
                                    ConditionalList = new List<KeyValuePair<WhereType, ConditionalModel>>
                                    {
                                        new(WhereType.And, new ConditionalModel
                                        {
                                            FieldName = item.Key,
                                            ConditionalType = ConditionalType.Like,
                                            FieldValue = value.LastOrDefault()
                                        })
                                    }
                                });
                            }
                            else
                            {
                                // 多选时为模糊查询
                                if (model.multiple)
                                {
                                    conModels.Add(new ConditionalCollections
                                    {
                                        ConditionalList = new List<KeyValuePair<WhereType, ConditionalModel>>
                                        {
                                            new(WhereType.And, new ConditionalModel
                                            {
                                                FieldName = item.Key,
                                                ConditionalType = ConditionalType.Like,
                                                FieldValue = item.Value.ToString()
                                            })
                                        }
                                    });
                                }
                                else
                                {
                                    conModels.Add(new ConditionalCollections
                                    {
                                        ConditionalList = new List<KeyValuePair<WhereType, ConditionalModel>>
                                        {
                                            new(WhereType.And, new ConditionalModel
                                            {
                                                FieldName = item.Key,
                                                ConditionalType = ConditionalType.Equal,
                                                FieldValue = item.Value.ToString()
                                            })
                                        }
                                    });
                                }
                            }
                        }

                            break;
                        case JnpfKeyConst.CURRORGANIZE:
                        {
                            string? itemValue = item.Value.ToString().Contains("[") ? item.Value?.ToString().ToObjectOld<List<string>>().ToJson() : item.Value.ToString();
                            conModels.Add(new ConditionalCollections
                            {
                                ConditionalList = new List<KeyValuePair<WhereType, ConditionalModel>>
                                {
                                    new(WhereType.And, new ConditionalModel
                                    {
                                        FieldName = item.Key,
                                        ConditionalType = ConditionalType.Equal,
                                        FieldValue = itemValue
                                    })
                                }
                            });
                        }

                            break;
                        case JnpfKeyConst.CASCADER:
                        {
                            string? itemValue = item.Value.ToString().Contains("[") ? item.Value?.ToString().ToObjectOld<List<string>>().ToJson() : item.Value.ToString();
                            conModels.Add(new ConditionalCollections
                            {
                                ConditionalList = new List<KeyValuePair<WhereType, ConditionalModel>>
                                {
                                    new(WhereType.And, new ConditionalModel
                                    {
                                        FieldName = item.Key,
                                        ConditionalType = ConditionalType.Like,
                                        FieldValue = itemValue.Replace("[", string.Empty).Replace("]", string.Empty)
                                    })
                                }
                            });
                        }
                            break;
                        case JnpfKeyConst.ADDRESS:
                        case JnpfKeyConst.COMSELECT:
                        {
                            // 多选时为模糊查询
                            if (model.multiple || model.searchMultiple)
                            {
                                List<object>? value = item.Value?.ToString().ToObjectOld<List<object>>();
                                if (value.Any())
                                {
                                    List<KeyValuePair<WhereType, ConditionalModel>> addItems = new();
                                    for (int i = 0; i < value.Count; i++)
                                    {
                                        KeyValuePair<WhereType, ConditionalModel> add = new(i == 0 ? WhereType.And : WhereType.Or, new ConditionalModel
                                        {
                                            FieldName = item.Key,
                                            ConditionalType = ConditionalType.Like,
                                            FieldValue = value[i].ToJson().Contains("[")
                                                ? value[i].ToJson().Replace("[", string.Empty)
                                                : item.Value?.ToString().Replace("[", string.Empty).Replace("\r\n", string.Empty).Replace(" ", string.Empty)
                                        });
                                        addItems.Add(add);
                                    }

                                    conModels.Add(new ConditionalCollections { ConditionalList = addItems });
                                }
                            }
                            else
                            {
                                string? itemValue = item.Value.ToString().Contains("[") ? item.Value.ToJson() : item.Value.ToString();
                                if (itemValue.Contains("[["))
                                {
                                    itemValue = itemValue.ToObjectOld<List<List<object>>>().FirstOrDefault().ToJson();
                                }

                                conModels.Add(new ConditionalCollections
                                {
                                    ConditionalList = new List<KeyValuePair<WhereType, ConditionalModel>>
                                    {
                                        new(WhereType.And, new ConditionalModel
                                        {
                                            FieldName = item.Key,
                                            ConditionalType = ConditionalType.Equal,
                                            FieldValue = itemValue
                                        })
                                    }
                                });
                            }
                        }

                            break;
                        case JnpfKeyConst.SELECT:
                        case JnpfKeyConst.POPUPSELECT:
                        {
                            string? itemValue = item.Value.ToString().Contains("[") ? item.Value.ToJson() : item.Value.ToString();

                            // 多选时为模糊查询
                            if (model.multiple || model.searchMultiple)
                            {
                                List<object>? value = item.Value.ToString().Contains("[") ? item.Value.ToObjectOld<List<object>>() : new List<object> { item.Value.ToString() };
                                List<KeyValuePair<WhereType, ConditionalModel>> addItems = new();
                                for (int i = 0; i < value.Count; i++)
                                {
                                    KeyValuePair<WhereType, ConditionalModel> add = new(i == 0 ? WhereType.And : WhereType.Or, new ConditionalModel
                                    {
                                        FieldName = item.Key,
                                        ConditionalType = model.multiple ? ConditionalType.Like : ConditionalType.Equal,
                                        FieldValue = model is { multiple: true, __config__.jnpfKey: JnpfKeyConst.SELECT } ? value[i].ToJson() : value[i].ToString()
                                    });
                                    addItems.Add(add);
                                }

                                conModels.Add(new ConditionalCollections { ConditionalList = addItems });
                            }
                            else
                            {
                                conModels.Add(new ConditionalCollections
                                {
                                    ConditionalList = new List<KeyValuePair<WhereType, ConditionalModel>>
                                    {
                                        new(WhereType.And, new ConditionalModel
                                        {
                                            FieldName = item.Key,
                                            ConditionalType = ConditionalType.Equal,
                                            FieldValue = itemValue
                                        })
                                    }
                                });
                            }
                        }

                            break;
                        case JnpfKeyConst.RATE:
                        case JnpfKeyConst.SLIDER:
                        {
                            List<string> rateRange = item.Value.ToObjectOld<List<string>>();
                            conModels.Add(new ConditionalCollections
                            {
                                ConditionalList = new List<KeyValuePair<WhereType, ConditionalModel>>
                                {
                                    new(WhereType.And, new ConditionalModel
                                    {
                                        FieldName = item.Key,
                                        ConditionalType = ConditionalType.GreaterThanOrEqual,
                                        FieldValue = rateRange.First(),
                                        CSharpTypeName = "decimal"
                                    }),
                                    new(WhereType.And, new ConditionalModel
                                    {
                                        FieldName = item.Key,
                                        ConditionalType = ConditionalType.LessThanOrEqual,
                                        FieldValue = rateRange.Last(),
                                        CSharpTypeName = "decimal"
                                    })
                                }
                            });
                        }

                            break;
                        default:
                        {
                            string? itemValue = item.Value.ToString().Contains("[") ? item.Value.ToJson() : item.Value.ToString();

                            if (model.searchType == 1)
                            {
                                conModels.Add(new ConditionalCollections
                                {
                                    ConditionalList = new List<KeyValuePair<WhereType, ConditionalModel>>
                                    {
                                        new(WhereType.And, new ConditionalModel
                                        {
                                            FieldName = item.Key,
                                            ConditionalType = ConditionalType.Equal,
                                            FieldValue = itemValue
                                        })
                                    }
                                });
                            }
                            else
                            {
                                conModels.Add(new ConditionalCollections
                                {
                                    ConditionalList = new List<KeyValuePair<WhereType, ConditionalModel>>
                                    {
                                        new(WhereType.And, new ConditionalModel
                                        {
                                            FieldName = item.Key,
                                            ConditionalType = ConditionalType.Like,
                                            FieldValue = itemValue
                                        })
                                    }
                                });
                            }
                        }

                            break;
                    }
                }
            }
        }

        if (isInteAssisData.Equals(1))
        {
            conModels.Add(new ConditionalCollections
            {
                ConditionalList = new List<KeyValuePair<WhereType, ConditionalModel>>
                {
                    new(WhereType.And, new ConditionalModel
                    {
                        FieldName = "f_inte_assistant",
                        ConditionalType = ConditionalType.Equal,
                        FieldValue = "1"
                    })
                }
            });
        }
        else
        {
            conModels.Add(new ConditionalCollections
            {
                ConditionalList = new List<KeyValuePair<WhereType, ConditionalModel>>
                {
                    new(WhereType.And, new ConditionalModel
                    {
                        FieldName = "f_inte_assistant",
                        ConditionalType = ConditionalType.EqualNull
                    })
                }
            });
        }

        return conModels;
    }

    /// <summary>
    ///     组装高级查询条件.
    /// </summary>
    /// <param name="superQueryJson"></param>
    /// <returns></returns>
    private List<IConditionalModel> GetSuperQueryJson(string superQueryJson, TemplateParsingBase tInfo)
    {
        List<IConditionalModel> conModels = new();
        if (superQueryJson.IsNotEmptyOrNull())
        {
            List<Dictionary<string, object>> querList = superQueryJson.ToObjectOld<List<Dictionary<string, object>>>();
            List<KeyValuePair<WhereType, ConditionalModel>> whereTypeList = new();
            foreach (Dictionary<string, object> item in querList)
            {
                WhereType whereType = new();

                // 判断是否为新分组
                if (item.ContainsKey("where"))
                {
                    if (item.Equals(querList.First()))
                    {
                        item["where"] = "0";
                    }

                    if (whereTypeList.Count > 0)
                    {
                        conModels.Add(new ConditionalCollections { ConditionalList = whereTypeList });
                        whereTypeList = new List<KeyValuePair<WhereType, ConditionalModel>>();
                    }

                    whereType = item["where"].ToString().ToObjectOld<WhereType>();
                }
                else
                {
                    whereType = item["whereType"].ToString().ToObjectOld<WhereType>();
                }

                ConditionalType conditionalType = item["ConditionalType"].ToString().ToObjectOld<ConditionalType>();
                string _CSharpTypeName = item.ContainsKey("CSharpTypeName") ? item["CSharpTypeName"].ToString() : null;
                whereTypeList.Add(new KeyValuePair<WhereType, ConditionalModel>(whereType, new ConditionalModel
                {
                    CSharpTypeName = _CSharpTypeName,
                    FieldName = item["field"].ToString(),
                    ConditionalType = conditionalType,
                    FieldValue = item["fieldValue"] == null ? null : item["fieldValue"].ToString()
                }));

                if (item.Equals(querList.Last()))
                {
                    conModels.Add(new ConditionalCollections { ConditionalList = whereTypeList });
                }
            }
        }

        return conModels;
    }

    /// <summary>
    ///     显示列有子表字段,根据主键查询所有子表.
    /// </summary>
    /// <param name="templateInfo"></param>
    /// <param name="primaryKey"></param>
    /// <param name="querList"></param>
    /// <param name="dataRuleList"></param>
    /// <param name="superQuerList"></param>
    /// <param name="result"></param>
    /// <param name="dataPermissions"></param>
    /// <returns></returns>
    private async Task<SqlSugarPagedList<Dictionary<string, object>>> GetListChildTable(
        TemplateParsingBase templateInfo,
        string primaryKey,
        List<IConditionalModel> querList,
        List<IConditionalModel> dataRuleList,
        List<IConditionalModel> superQuerList,
        SqlSugarPagedList<Dictionary<string, object>> result,
        List<IConditionalModel> dataPermissions)
    {
        List<object> ids = new();
        result.Rows.ToList().ForEach(item => ids.Add(item[primaryKey]));

        Dictionary<string, List<string>> childTableList = new();

        templateInfo.AllFieldsModel.Where(x => x.__config__.jnpfKey.Equals(JnpfKeyConst.TABLE)).ToList().ForEach(ctitem =>
        {
            templateInfo.AllFieldsModel.Where(x => x.__vModel__.Contains(ctitem.__vModel__ + "-")).ToList().ForEach(item =>
            {
                string value = item.__vModel__.Split("-").Last();
                if (value.IsNotEmptyOrNull())
                {
                    if (childTableList.ContainsKey(ctitem.__config__.tableName))
                    {
                        childTableList[ctitem.__config__.tableName].Add(value);
                    }
                    else
                    {
                        childTableList.Add(ctitem.__config__.tableName, new List<string> { value });
                    }
                }
            });
        });

        Dictionary<string, string> relationField = new();
        templateInfo.ChildTableFieldsModelList.ForEach(item =>
        {
            string? tableField = templateInfo.AllTable.Find(tf => tf.table == item.__config__.tableName)?.tableField;
            if (!relationField.ContainsKey(item.__config__.tableName))
            {
                relationField.Add(item.__config__.tableName, tableField);
            }
        });

        string dataRuleJson = dataRuleList.ToJson();
        foreach (KeyValuePair<string, string> item in templateInfo.AllTableFields)
        {
            if (dataRuleJson.IsNotEmptyOrNull() && dataRuleJson.Contains(string.Format("\"{0}\"", item.Key)))
            {
                dataRuleJson = dataRuleJson.Replace(string.Format("\"{0}\"", item.Key), string.Format("\"{0}\"", item.Value));
            }
        }

        // 捞取 所有子表查询条件 <tableName , where>
        Dictionary<string, List<IConditionalModel>> childTableQuery = new();
        List<IConditionalModel>? dataRule = _visualDevRepository.AsSugarClient().Utilities.JsonToConditionalModels(dataRuleJson);
        List<ConditionalCollections> query = querList.ToObjectOld<List<ConditionalCollections>>();
        foreach (KeyValuePair<string, string> item in templateInfo.ChildTableFields)
        {
            string? tableName = item.Value.Split(".").FirstOrDefault();
            List<IConditionalModel> dataRuleConList = GetIConditionalModelListByTableName(dataRuleList, tableName);
            if (dataRuleConList.Any())
            {
                //foreach (var it in dataRuleConList) it.ConditionalList.ForEach(x => x.Value.FieldName = item.Value);
                if (!childTableQuery.ContainsKey(tableName))
                {
                    childTableQuery.Add(tableName, new List<IConditionalModel>());
                }

                childTableQuery[tableName].AddRange(dataRuleConList);
            }

            List<ConditionalCollections> conList = query.Where(x => x.ConditionalList.Any(xx => xx.Value.FieldName.Equals(item.Key))).ToList();
            if (conList.Any())
            {
                foreach (ConditionalCollections it in conList)
                {
                    it.ConditionalList.ForEach(x =>
                    {
                        if (templateInfo.ChildTableFields.ContainsKey(x.Value.FieldName))
                        {
                            x.Value.FieldName = templateInfo.ChildTableFields[x.Value.FieldName];
                        }
                    });
                }

                if (!childTableQuery.ContainsKey(tableName))
                {
                    childTableQuery.Add(tableName, new List<IConditionalModel>());
                }

                childTableQuery[tableName].AddRange(conList);
            }
        }

        // 处理高级查询值名称
        foreach (ConditionalCollections item in superQuerList)
        {
            foreach (KeyValuePair<WhereType, ConditionalModel> sitem in item.ConditionalList)
            {
                if (templateInfo.ChildTableFields.ContainsKey(sitem.Value.FieldName))
                {
                    sitem.Value.FieldName = templateInfo.ChildTableFields[sitem.Value.FieldName];
                }
            }
        }

        foreach (KeyValuePair<string, List<string>> item in childTableList)
        {
            item.Value.Add(relationField[item.Key]);
            string? sql = string.Format("select {0} from {1} where {2} in('{3}')", string.Join(",", item.Value), item.Key, relationField[item.Key], string.Join("','", ids));
            if (childTableQuery.ContainsKey(item.Key)) // 子表查询条件
            {
                string? itemWhere = _visualDevRepository.AsSugarClient().SqlQueryable<dynamic>("@").Where(childTableQuery[item.Key]).ToSqlString();
                if (itemWhere.Contains("WHERE"))
                {
                    sql = string.Format(" {0} and {1} ", sql, itemWhere.Split("WHERE").Last());
                }
            }

            // 拼接高级查询
            List<IConditionalModel>? superQueryConList = new();
            if (superQuerList != null && superQuerList.Any())
            {
                List<object> sList = new();
                List<object> allSuperQuery = superQuerList.ToObjectOld<List<object>>();
                allSuperQuery.ForEach(it =>
                {
                    if (it.ToJson().Contains(item.Key + "."))
                    {
                        sList.Add(it);
                    }
                });
                if (sList.Any())
                {
                    superQueryConList = _visualDevRepository.AsSugarClient().Utilities.JsonToConditionalModels(sList.ToJson());
                    superQueryConList = GetIConditionalModelListByTableName(superQueryConList, item.Key);
                    string json = superQueryConList.ToJson().Replace(item.Key + ".", string.Empty);
                    superQueryConList = _visualDevRepository.AsSugarClient().Utilities.JsonToConditionalModels(json);
                }
            }

            // 拼接数据权限
            List<IConditionalModel>? dataPermissionsList = new();
            if (dataPermissions != null && dataPermissions.Any())
            {
                List<object> pList = new();
                List<object> allPersissions = dataPermissions.ToObjectOld<List<object>>();
                allPersissions.ForEach(it =>
                {
                    if (it.ToJson().Contains(item.Key + "."))
                    {
                        pList.Add(it);
                    }
                });
                if (pList.Any())
                {
                    dataPermissionsList = _visualDevRepository.AsSugarClient().Utilities.JsonToConditionalModels(pList.ToJson());
                    dataPermissionsList = GetIConditionalModelListByTableName(dataPermissionsList, item.Key);
                    string json = dataPermissionsList.ToJson().Replace(item.Key + ".", string.Empty);
                    dataPermissionsList = _visualDevRepository.AsSugarClient().Utilities.JsonToConditionalModels(json);
                }
            }

            // 数据过滤
            List<IConditionalModel>? dataRuleConditionalList = new();
            if (dataRule != null && dataRule.Any())
            {
                List<object> pList = new();
                List<object> allPersissions = dataRule.ToObjectOld<List<object>>();
                allPersissions.ForEach(it =>
                {
                    if (it.ToJson().Contains(item.Key + "."))
                    {
                        pList.Add(it);
                    }
                });
                if (pList.Any())
                {
                    dataRuleConditionalList = _visualDevRepository.AsSugarClient().Utilities.JsonToConditionalModels(pList.ToJson());
                    dataRuleConditionalList = GetIConditionalModelListByTableName(dataRuleConditionalList, item.Key);
                    string json = dataRuleConditionalList.ToJson().Replace(item.Key + ".", string.Empty);
                    dataRuleConditionalList = _visualDevRepository.AsSugarClient().Utilities.JsonToConditionalModels(json);
                }
            }

            sql = _visualDevRepository.AsSugarClient().SqlQueryable<dynamic>(sql).Where(superQueryConList).Where(dataPermissionsList).Where(dataRuleConditionalList).ToSqlString();

            List<Dictionary<string, object>> dt = _databaseService.GetSqlData(templateInfo.DbLink, sql).ToObjectOld<List<Dictionary<string, object>>>();
            string? vModel = templateInfo.AllFieldsModel.Find(x => x.__config__.tableName == item.Key)?.__vModel__;

            if (vModel.IsNotEmptyOrNull())
            {
                foreach (Dictionary<string, object> it in result.Rows)
                {
                    List<Dictionary<string, object>> rows = dt.Where(x => x[relationField[item.Key]].ToString().Equals(it[primaryKey].ToString())).ToList();
                    foreach (Dictionary<string, object> row in rows)
                    {
                        row["JnpfKeyConst_MainData"] = it.ToJson();
                    }

                    FieldsModel childTableModel = templateInfo.ChildTableFieldsModelList.First(x => x.__vModel__.Equals(vModel));

                    List<Dictionary<string, object>>? datas = new();
                    if (childTableModel.__config__.children.Any(x => x.__config__.templateJson != null && x.__config__.templateJson.Any()))
                    {
                        datas = await _formDataParsing.GetKeyData(childTableModel.__config__.children.Where(x => x.__config__.templateJson != null && x.__config__.templateJson.Any()).ToList(), rows,
                            templateInfo.ColumnData, "List", templateInfo.WebType, primaryKey, templateInfo.visualDevEntity.isShortLink);
                    }

                    datas = await _formDataParsing.GetKeyData(childTableModel.__config__.children.Where(x => x.__config__.templateJson == null || !x.__config__.templateJson.Any()).ToList(), rows,
                        templateInfo.ColumnData, "List", templateInfo.WebType, primaryKey, templateInfo.visualDevEntity.isShortLink);

                    List<Dictionary<string, object>> newDatas = new();
                    foreach (Dictionary<string, object> data in datas)
                    {
                        Dictionary<string, object>? newData = data.Copy();
                        if (newData.ContainsKey("JnpfKeyConst_MainData"))
                        {
                            newData.Remove("JnpfKeyConst_MainData");
                        }

                        newData.Remove(relationField[item.Key]);
                        newDatas.Add(newData);
                    }

                    it.Add(vModel, newDatas);
                }
            }
        }

        return result;
    }

    /// <summary>
    ///     获取处理子表数据.
    /// </summary>
    /// <param name="templateInfo">模板信息.</param>
    /// <param name="link">数据库连接.</param>
    /// <param name="dataMap">全部数据.</param>
    /// <param name="newDataMap">新数据.</param>
    /// <param name="isDetail">是否详情转换.</param>
    /// <returns></returns>
    private async Task<Dictionary<string, object>> GetChildTableData(TemplateParsingBase templateInfo, DbLink? link, Dictionary<string, object> dataMap, Dictionary<string, object> newDataMap,
        bool isDetail = false)
    {
        foreach (FieldsModel model in templateInfo.ChildTableFieldsModelList)
        {
            if (!string.IsNullOrEmpty(model.__vModel__))
            {
                if (model.__config__.jnpfKey.Equals(JnpfKeyConst.TABLE))
                {
                    List<string> feilds = new();
                    string ctPrimaryKey = templateInfo.AllTable.Find(x => x.table.Equals(model.__config__.tableName)).fields.Find(x => x.PrimaryKey).Field;
                    feilds.Add(ctPrimaryKey + " id "); // 子表主键
                    foreach (FieldsModel? childModel in model.__config__.children)
                    {
                        if (!string.IsNullOrEmpty(childModel.__vModel__))
                        {
                            feilds.Add(childModel.__vModel__); // 拼接查询字段
                        }
                    }

                    string relationMainFeildValue = string.Empty;
                    string childSql = string.Format("select {0} from {1} where 1=1 ", string.Join(",", feilds), model.__config__.tableName); // 查询子表数据
                    foreach (TableModel? tableMap in templateInfo.AllTable.Where(x => !x.table.Equals(templateInfo.MainTableName)).ToList())
                    {
                        if (tableMap.table.Equals(model.__config__.tableName))
                        {
                            if (dataMap.ContainsKey(tableMap.relationField))
                            {
                                childSql += string.Format(" And {0}='{1}'", tableMap.tableField, dataMap[tableMap.relationField]); // 外键
                            }

                            if (dataMap.ContainsKey(tableMap.relationField.ToUpper()))
                            {
                                childSql += string.Format(" And {0}='{1}'", tableMap.tableField, dataMap[tableMap.relationField.ToUpper()]); // 外键
                            }

                            if (dataMap.ContainsKey(tableMap.relationField.ToLower()))
                            {
                                childSql += string.Format(" And {0}='{1}'", tableMap.tableField, dataMap[tableMap.relationField.ToLower()]); // 外键
                            }

                            List<Dictionary<string, object>>? childTableData = _databaseService.GetSqlData(link, childSql).ToObjectOld<List<Dictionary<string, object>>>();
                            if (!isDetail)
                            {
                                List<Dictionary<string, object>>? childData = _databaseService.GetSqlData(link, childSql).ToObjectOld<List<Dictionary<string, object>>>();
                                childTableData = _formDataParsing.GetTableDataInfo(childData, model.__config__.children, "detail");
                            }

                            #region 获取关联表单属性 和 弹窗选择属性

                            foreach (FieldsModel item in model.__config__.children.Where(x => x.__config__.jnpfKey == JnpfKeyConst.RELATIONFORM).ToList())
                            {
                                foreach (Dictionary<string, object> dataItem in childTableData)
                                {
                                    if (item.__vModel__.IsNotEmptyOrNull() && dataItem.ContainsKey(item.__vModel__) && dataItem[item.__vModel__] != null)
                                    {
                                        string? relationValueId = dataItem[item.__vModel__].ToString(); // 获取关联表单id
                                        VisualDevReleaseEntity? relationReleaseInfo =
                                            await _visualDevRepository.AsSugarClient().Queryable<VisualDevReleaseEntity>().FirstAsync(x => x.Id == item.modelId.ParseToLong()); // 获取 关联表单 转换后的数据
                                        VisualDevEntity relationInfo = relationReleaseInfo.Adapt<VisualDevEntity>();
                                        string relationValueStr = string.Empty;
                                        relationValueStr = await GetHaveTableInfoDetails(relationValueId, relationInfo);

                                        if (!relationValueStr.IsNullOrEmpty() && !relationValueStr.Equals(relationValueId))
                                        {
                                            Dictionary<string, object> relationValue = relationValueStr.ToObjectOld<Dictionary<string, object>>();

                                            // 添加到 子表 列
                                            model.__config__.children.Where(x => x.relationField.ReplaceRegex(@"_jnpfTable_(\w+)", string.Empty) == item.__vModel__).ToList().ForEach(citem =>
                                            {
                                                citem.__vModel__ = item.__vModel__ + "_" + citem.showField;
                                                if (relationValue.ContainsKey(citem.showField))
                                                {
                                                    dataItem[item.__vModel__ + "_" + citem.showField] = relationValue[citem.showField];
                                                }
                                                else
                                                {
                                                    dataItem[item.__vModel__ + "_" + citem.showField] = string.Empty;
                                                }
                                            });
                                        }
                                    }
                                }
                            }

                            if (model.__config__.children.Where(x => x.__config__.jnpfKey == JnpfKeyConst.POPUPATTR).Any())
                            {
                                foreach (FieldsModel item in model.__config__.children.Where(x => x.__config__.jnpfKey == JnpfKeyConst.POPUPSELECT).ToList())
                                {
                                    List<Dictionary<string, string>>? pDataList = await _formDataParsing.GetPopupSelectDataList(item.interfaceId, item); // 获取接口数据列表
                                    foreach (Dictionary<string, object> dataItem in childTableData)
                                    {
                                        if (!string.IsNullOrWhiteSpace(item.__vModel__) && dataItem.ContainsKey(item.__vModel__) && dataItem[item.__vModel__] != null)
                                        {
                                            string? relationValueId = dataItem[item.__vModel__].ToString(); // 获取关联表单id

                                            // 添加到 子表 列
                                            model.__config__.children.Where(x => x.relationField.ReplaceRegex(@"_jnpfTable_(\w+)", string.Empty) == item.__vModel__).ToList().ForEach(citem =>
                                            {
                                                citem.__vModel__ = item.__vModel__ + "_" + citem.showField;
                                                Dictionary<string, string>? value = pDataList.Where(x => x.Values.Contains(dataItem[item.__vModel__].ToString())).FirstOrDefault();
                                                if (value != null && value.ContainsKey(citem.showField))
                                                {
                                                    dataItem[item.__vModel__ + "_" + citem.showField] = value[citem.showField];
                                                }
                                            });
                                        }
                                    }
                                }
                            }

                            #endregion

                            if (childTableData.Count > 0)
                            {
                                newDataMap[model.__vModel__] = childTableData;
                            }
                            else
                            {
                                newDataMap[model.__vModel__] = new List<Dictionary<string, object>>();
                            }
                        }
                    }
                }
            }
        }

        return newDataMap;
    }

    /// <summary>
    ///     处理并发锁定(乐观锁).
    /// </summary>
    /// <param name="link">数据库连接.</param>
    /// <param name="templateInfo">模板信息.</param>
    /// <param name="updateSqlList">修改Sql集合(提交修改时接入).</param>
    /// <param name="allDataMap">前端提交的数据(提交修改时接入).</param>
    private async Task OptimisticLocking(DbLink? link, TemplateParsingBase templateInfo, List<string>? updateSqlList = null, Dictionary<string, object>? allDataMap = null)
    {
        if (templateInfo.FormModel.concurrencyLock)
        {
            try
            {
                // 主表修改语句, 如果有修改语句 获取执行结果.
                // 不是修改模式, 增加并发锁定字段 f_version.
                if (updateSqlList != null && updateSqlList.Any())
                {
                    string? mainTableUpdateSql = updateSqlList.Find(x => x.Contains(templateInfo.MainTableName));
                    object versoin = allDataMap.ContainsKey("f_version") && allDataMap["f_version"] != null ? allDataMap["f_version"] : "-1";

                    // 并发乐观锁 字段 拼接条件
                    mainTableUpdateSql = string.Format("{0} and f_version={1};", mainTableUpdateSql.Replace(";", string.Empty), versoin);
                    int res = await _databaseService.ExecuteSql(link, mainTableUpdateSql);
                    if (res.Equals(0) && !allDataMap.ContainsKey("jnpf_resurgence"))
                    {
                        throw Oops.Oh(ErrorCode.D1408); // 该条数据已经被修改过
                    }

                    // f_version +1
                    string? sql = string.Format("update {0} set {1}={2};", templateInfo.MainTableName, "f_version", versoin.ParseToInt() + 1);
                    await _databaseService.ExecuteSql(link, sql);
                }
                else
                {
                    List<DbTableFieldModel>? fieldList = _databaseService.GetFieldList(link, templateInfo.MainTableName); // 获取主表所有列

                    if (!fieldList.Any(x => SqlFunc.ToLower(x.field) == "f_version"))
                    {
                        List<DbTableFieldModel>? newField = new()
                            { new DbTableFieldModel { field = "f_version", fieldName = "并发锁定字段", dataType = "int", dataLength = "50", allowNull = 1 } };
                        _databaseService.AddTableColumn(link, templateInfo.MainTableName, newField);
                    }

                    // f_version 赋予默认值 0
                    string? sql = string.Format("update {0} set {1}={2} where f_version IS NULL ;", templateInfo.MainTableName, "f_version", "0");
                    await _databaseService.ExecuteSql(link, sql);

                    FieldsModel newVModel = new()
                    {
                        __vModel__ = "f_version", __config__ = new ConfigModel { jnpfKey = JnpfKeyConst.COMINPUT, relationTable = templateInfo.MainTableName, tableName = templateInfo.MainTableName }
                    };
                    templateInfo.SingleFormData.Add(newVModel);
                    templateInfo.MainTableFieldsModelList.Add(newVModel);
                    templateInfo.FieldsModelList.Add(newVModel);
                }
            }
            catch (Exception ex)
            {
                if (ex.Message.Contains("[D1408]"))
                {
                    throw Oops.Oh(ErrorCode.D1408);
                }

                throw Oops.Oh(ErrorCode.COM1008);
            }
        }
    }

    /// <summary>
    ///     数据是否可以传递.
    /// </summary>
    /// <param name="oldModel">原控件模型.</param>
    /// <param name="newModel">新控件模型.</param>
    /// <returns>true 可以传递, false 不可以</returns>
    private bool DataTransferVerify(FieldsModel oldModel, FieldsModel newModel)
    {
        switch (oldModel.__config__.jnpfKey)
        {
            case JnpfKeyConst.COMINPUT:
            case JnpfKeyConst.TEXTAREA:
            case JnpfKeyConst.RADIO:
            case JnpfKeyConst.EDITOR:
                if (!(newModel.__config__.jnpfKey.Equals(JnpfKeyConst.COMINPUT) ||
                      newModel.__config__.jnpfKey.Equals(JnpfKeyConst.TEXTAREA) ||
                      newModel.__config__.jnpfKey.Equals(JnpfKeyConst.RADIO) ||
                      (newModel.__config__.jnpfKey.Equals(JnpfKeyConst.SELECT) && !newModel.multiple) ||
                      newModel.__config__.jnpfKey.Equals(JnpfKeyConst.EDITOR)))
                {
                    return false;
                }

                break;
            case JnpfKeyConst.CHECKBOX:
                if (!((newModel.__config__.jnpfKey.Equals(JnpfKeyConst.POPUPTABLESELECT) && newModel.multiple) ||
                      (newModel.__config__.jnpfKey.Equals(JnpfKeyConst.SELECT) && newModel.multiple) ||
                      (newModel.__config__.jnpfKey.Equals(JnpfKeyConst.TREESELECT) && newModel.multiple) ||
                      newModel.__config__.jnpfKey.Equals(JnpfKeyConst.CHECKBOX) ||
                      newModel.__config__.jnpfKey.Equals(JnpfKeyConst.CASCADER)))
                {
                    return false;
                }

                break;
            case JnpfKeyConst.NUMINPUT:
            case JnpfKeyConst.DATE:
            case JnpfKeyConst.TIME:
            case JnpfKeyConst.UPLOADFZ:
            case JnpfKeyConst.UPLOADIMG:
            case JnpfKeyConst.COLORPICKER:
            case JnpfKeyConst.RATE:
            case JnpfKeyConst.SLIDER:
                if (!oldModel.__config__.jnpfKey.Equals(newModel.__config__.jnpfKey))
                {
                    return false;
                }

                break;
            case JnpfKeyConst.COMSELECT:
            // case JnpfKeyConst.DEPSELECT:
            // case JnpfKeyConst.POSSELECT:
            case JnpfKeyConst.USERSELECT:
            case JnpfKeyConst.ROLESELECT:
            // case JnpfKeyConst.GROUPSELECT:
            case JnpfKeyConst.ADDRESS:
                if (!(oldModel.__config__.jnpfKey.Equals(newModel.__config__.jnpfKey) && oldModel.multiple.Equals(newModel.multiple)))
                {
                    return false;
                }

                break;
            case JnpfKeyConst.TREESELECT:
                if (oldModel.multiple)
                {
                    if (!((newModel.__config__.jnpfKey.Equals(JnpfKeyConst.POPUPTABLESELECT) && newModel.multiple) ||
                          (newModel.__config__.jnpfKey.Equals(JnpfKeyConst.SELECT) && newModel.multiple) ||
                          (newModel.__config__.jnpfKey.Equals(JnpfKeyConst.TREESELECT) && newModel.multiple) ||
                          newModel.__config__.jnpfKey.Equals(JnpfKeyConst.CASCADER)))
                    {
                        return false;
                    }
                }
                else
                {
                    if (!(newModel.__config__.jnpfKey.Equals(JnpfKeyConst.COMINPUT) ||
                          newModel.__config__.jnpfKey.Equals(JnpfKeyConst.TEXTAREA) ||
                          newModel.__config__.jnpfKey.Equals(JnpfKeyConst.RADIO) ||
                          (newModel.__config__.jnpfKey.Equals(JnpfKeyConst.SELECT) && !newModel.multiple) ||
                          (newModel.__config__.jnpfKey.Equals(JnpfKeyConst.TREESELECT) && !newModel.multiple) ||
                          newModel.__config__.jnpfKey.Equals(JnpfKeyConst.EDITOR)))
                    {
                        return false;
                    }
                }

                break;
            case JnpfKeyConst.POPUPTABLESELECT:
                if (oldModel.multiple)
                {
                    if (!((newModel.__config__.jnpfKey.Equals(JnpfKeyConst.POPUPTABLESELECT) && newModel.multiple) ||
                          (newModel.__config__.jnpfKey.Equals(JnpfKeyConst.SELECT) && newModel.multiple) ||
                          (newModel.__config__.jnpfKey.Equals(JnpfKeyConst.TREESELECT) && newModel.multiple) ||
                          newModel.__config__.jnpfKey.Equals(JnpfKeyConst.CASCADER)))
                    {
                        return false;
                    }
                }
                else
                {
                    if (!((newModel.__config__.jnpfKey.Equals(JnpfKeyConst.POPUPTABLESELECT) && !newModel.multiple) ||
                          newModel.__config__.jnpfKey.Equals(JnpfKeyConst.RELATIONFORM) ||
                          newModel.__config__.jnpfKey.Equals(JnpfKeyConst.POPUPSELECT)))
                    {
                        return false;
                    }
                }

                break;
            case JnpfKeyConst.POPUPSELECT:
            case JnpfKeyConst.RELATIONFORM:
                if (!(newModel.__config__.jnpfKey.Equals(JnpfKeyConst.RELATIONFORM) ||
                      newModel.__config__.jnpfKey.Equals(JnpfKeyConst.POPUPSELECT) ||
                      (newModel.__config__.jnpfKey.Equals(JnpfKeyConst.POPUPTABLESELECT) && !newModel.multiple)))
                {
                    return false;
                }

                break;
        }

        return true;
    }

    /// <summary>
    ///     处理数据视图.
    /// </summary>
    /// <param name="templateInfo"></param>
    /// <param name="input"></param>
    /// <returns></returns>
    private async Task<SqlSugarPagedList<Dictionary<string, object>>> GetDataViewResults(TemplateParsingBase templateInfo, VisualDevModelListQueryInput input)
    {
        List<IndexSearchFieldModel>? searchList = _userManager.UserOrigin.Equals("pc") ? templateInfo.ColumnData.searchList.Copy() : templateInfo.AppColumnData.searchList.Copy();
        SqlSugarPagedList<Dictionary<string, object>>? realList = new() { Rows = new List<Dictionary<string, object>>() }; // 返回结果集
        DataInterfacePreviewInput par = input.Adapt<DataInterfacePreviewInput>();
        par.paramList = templateInfo.visualDevEntity.InterfaceParam.ToObjectOld<List<DataInterfaceParameter>>();
        if (par.queryJson.IsNotEmptyOrNull())
        {
            Dictionary<string, object> newList = new Dictionary<string, object>();
            foreach (KeyValuePair<string, object> item in par.queryJson.ToObjectOld<Dictionary<string, object>>())
            {
                if (par.paramList.Any(it => it.field.Equals(item.Key)))
                {
                    par.paramList.Find(it => it.field.Equals(item.Key)).defaultValue = item.Value;
                }
                else
                {
                    newList.Add(item.Key, item.Value);
                }
            }

            input.queryJson = newList.ToJson();
        }

        long interfaceId = Convert.ToInt64(templateInfo.visualDevEntity.InterfaceId);
        // 数据
        DataInterfaceEntity? dataInterface = await _visualDevRepository.AsSugarClient().Queryable<DataInterfaceEntity>().FirstAsync(x => x.Id == interfaceId);
        if (templateInfo.ColumnData.hasPage)
        {
            par.currentPage = 1;
            par.pageSize = 999999;
        }

        par.tenantId = _userManager.TenantId;
        object res = await _dataInterfaceService.GetResponseByType(interfaceId, 2, par);
        if (dataInterface.HasPage.Equals(1))
        {
            if (!res.ToJson().Equals("[]") && res.ToJson() != string.Empty)
            {
                realList = res.ToObjectOld<SqlSugarPagedList<Dictionary<string, object>>>();
            }
        }
        else
        {
            List<Dictionary<string, object>>? resList = new List<Dictionary<string, object>>();

            if (res.ToJson().Contains("["))
            {
                try
                {
                    resList = res.ToObjectOld<List<Dictionary<string, object>>>();
                }
                catch
                {
                    resList = res.ToObjectOld<SqlSugarPagedList<Dictionary<string, object>>>().Rows.ToList();
                }
            }
            else
            {
                resList.Add(res.ToObjectOld<Dictionary<string, object>>());
            }

            realList.Rows = resList.IsNotEmptyOrNull() ? resList : new List<Dictionary<string, object>>();
        }

        // 查询
        if (input.queryJson.IsNotEmptyOrNull())
        {
            foreach (var item in input.queryJson.ToObjectOld<Dictionary<string, object>>())
            {
                realList.Rows = await GetDataViewQuery(realList.Rows.ToList(), searchList, item);
            }
        }

        // 分页
        realList.TotalRows = realList.Rows.ToList().Count;
        realList.PageSize = input.pageSize;
        realList.PageNo = input.currentPage;
        if (templateInfo.ColumnData.hasPage)
        {
            List<Dictionary<string, object>> dt = GetPageToDataTable(realList.Rows.ToList(), input.currentPage, input.pageSize);
            realList.Rows = dt.ToObjectOld<List<Dictionary<string, object>>>();
        }

        // 排序
        if (input.sidx.IsNotEmptyOrNull())
        {
            List<string> sidx = input.sidx.Split(",").ToList();

            realList.Rows.ToList().Sort((Dictionary<string, object> x, Dictionary<string, object> y) =>
            {
                foreach (string item in sidx)
                {
                    if (item[0].ToString().Equals("-"))
                    {
                        string itemName = item.Remove(0, 1);
                        if (!x[itemName].Equals(y[itemName]))
                        {
                            return y[itemName].ToString().CompareTo(x[itemName].ToString());
                        }
                    }
                    else
                    {
                        if (!x[item].Equals(y[item]))
                        {
                            return x[item].ToString().CompareTo(y[item].ToString());
                        }
                    }
                }

                return 0;
            });
        }

        // 递归给数据添加id
        AddDataViewId(realList.Rows.ToList());

        // 分组表格
        if (templateInfo.ColumnData.type == 3 && _userManager.UserOrigin == "pc")
        {
            realList.Rows = CodeGenHelper.GetGroupList(realList.Rows.ToList(), templateInfo.ColumnData.groupField,
                templateInfo.ColumnData.columnList.Find(x => x.__vModel__.ToLower() != templateInfo.ColumnData.groupField.ToLower()).__vModel__);
        }

        return realList;
    }
    
    /// <summary>
    ///     静态数据分页.
    /// </summary>
    /// <param name="dt">数据源.</param>
    /// <param name="PageIndex">第几页.</param>
    /// <param name="PageSize">每页多少条.</param>
    /// <returns></returns>
    private List<Dictionary<string, object>> GetPageToDataTable(List<Dictionary<string, object>> dt, int PageIndex, int PageSize)
    {
        if (PageIndex == 0)
        {
            return dt; // 0页代表每页数据，直接返回
        }

        if (dt == null)
        {
            return new List<Dictionary<string, object>>();
        }

        List<Dictionary<string, object>> newdt = new();
        int rowbegin = (PageIndex - 1) * PageSize;
        int rowend = PageIndex * PageSize; // 要展示的数据条数
        if (rowbegin >= dt.Count)
        {
            return dt; // 源数据记录数小于等于要显示的记录，直接返回dt
        }

        if (rowend > dt.Count)
        {
            rowend = dt.Count;
        }

        for (int i = rowbegin; i <= rowend - 1; i++)
        {
            newdt.Add(dt[i]);
        }

        return newdt;
    }

    /// <summary>
    ///     数据视图列表递归添加id.
    /// </summary>
    /// <param name="list"></param>
    private void AddDataViewId(List<Dictionary<string, object>> list)
    {
        foreach (Dictionary<string, object> item in list)
        {
            if (!item.ContainsKey("id"))
            {
                item.Add("id", YitIdHelper.NextId().ToString());
            }

            if (item.ContainsKey("children"))
            {
                List<Dictionary<string, object>> fmList = item["children"].ToObjectOld<List<Dictionary<string, object>>>();
                AddDataViewId(fmList);
                item["children"] = fmList;
            }
        }
    }

    /// <summary>
    ///     处理数据视图查询.
    /// </summary>
    /// <param name="list">数据.</param>
    /// <param name="searchList">查询列.</param>
    /// <param name="item">查询值</param>
    /// <returns></returns>
    private async Task<List<Dictionary<string, object>>> GetDataViewQuery(List<Dictionary<string, object>> list, List<IndexSearchFieldModel> searchList, KeyValuePair<string, object> item)
    {
        IndexSearchFieldModel? searchInfo = searchList.Find(x => x.__vModel__.Equals(item.Key));
        if (searchInfo.IsNotEmptyOrNull())
        {
            switch (searchInfo.searchType)
            {
                case 1: // 等于查询
                    List<Dictionary<string, object>> newList = new();
                    if (searchInfo.searchMultiple)
                    {
                        foreach (object data in item.Value.ToObjectOld<List<object>>())
                        {
                            if (searchInfo.isIncludeSubordinate)
                            {
                                switch (searchInfo.jnpfKey)
                                {
                                    case JnpfKeyConst.COMSELECT:
                                        string orgId = data.ToObjectOld<List<string>>().Last();
                                        List<string>? orgChildIds = await _visualDevRepository.AsSugarClient().Queryable<SysOrg>()
                                            .Where(it => it.Status == true && it.OrganizeIdTree.Contains(orgId)).Select(it => it.OrganizeIdTree).ToListAsync();
                                        foreach (string? child in orgChildIds)
                                        {
                                            newList.AddRange(list.Where(x => x.Any(xx => xx.Key.Equals(item.Key) && xx.Value.IsNotEmptyOrNull() && xx.Value.ToString().Contains(child))).ToList());
                                        }

                                        break;
                                    // case JnpfKeyConst.DEPSELECT:
                                    //     var depChildIds = await _visualDevRepository.AsSugarClient().Queryable<OrganizeEntity>().Where(it =>
                                    //             it.DeleteMark == null && it.EnabledMark == 1 && it.Category.Equals("department") && it.OrganizeIdTree.Contains(data.ToString())).Select(it => it.Id)
                                    //         .ToListAsync();
                                    //     foreach (var child in depChildIds)
                                    //     {
                                    //         newList.AddRange(list.Where(x => x.Any(xx => xx.Key.Equals(item.Key) && xx.Value.IsNotEmptyOrNull() && xx.Value.ToString().Contains(child))).ToList());
                                    //     }
                                    //
                                    //     break;
                                    case JnpfKeyConst.USERSELECT:
                                        List<long>? userChildIds = await _visualDevRepository.AsSugarClient().Queryable<SysUser>()
                                            .Where(it => it.Enable && it.Id.Equals(data.ToString())).Select(it => it.Id).ToListAsync();
                                        foreach (long child in userChildIds)
                                        {
                                            newList.AddRange(list.Where(x => x.Any(xx => xx.Key.Equals(item.Key) && xx.Value.IsNotEmptyOrNull() && xx.Value.ToString().Contains(child.ToString())))
                                                .ToList());
                                        }

                                        break;
                                }
                            }

                            newList.AddRange(list.Where(x => x.Any(xx => xx.Key.Equals(item.Key) && xx.Value.IsNotEmptyOrNull() && xx.Value.ToString().Contains(data.ToString()))).ToList());
                        }
                    }
                    else
                    {
                        if (searchInfo.isIncludeSubordinate)
                        {
                            switch (searchInfo.jnpfKey)
                            {
                                case JnpfKeyConst.COMSELECT:
                                    string orgId = item.Value.ToObjectOld<List<string>>().Last();
                                    List<string>? orgChildIds = await _visualDevRepository.AsSugarClient().Queryable<SysOrg>()
                                        .Where(it => it.Status == true && it.OrganizeIdTree.Contains(orgId)).Select(it => it.OrganizeIdTree).ToListAsync();
                                    foreach (string? child in orgChildIds)
                                    {
                                        newList.AddRange(list.Where(x => x.Any(xx => xx.Key.Equals(item.Key) && xx.Value.IsNotEmptyOrNull() && xx.Value.ToString().Equals(child))).ToList());
                                    }

                                    break;
                                // case JnpfKeyConst.DEPSELECT:
                                //     var depChildIds = await _visualDevRepository.AsSugarClient().Queryable<OrganizeEntity>().Where(it =>
                                //             it.DeleteMark == null && it.EnabledMark == 1 && it.Category.Equals("department") && it.OrganizeIdTree.Contains(item.Value.ToString())).Select(it => it.Id)
                                //         .ToListAsync();
                                //     foreach (var child in depChildIds)
                                //     {
                                //         newList.AddRange(list.Where(x => x.Any(xx => xx.Key.Equals(item.Key) && xx.Value.IsNotEmptyOrNull() && xx.Value.ToString().Equals(child))).ToList());
                                //     }
                                //
                                //     break;
                                case JnpfKeyConst.USERSELECT:
                                    List<long>? userChildIds = await _visualDevRepository.AsSugarClient().Queryable<SysUser>()
                                        .Where(it => it.Enable == true && it.Id.Equals(item.Value.ToString())).Select(it => it.Id).ToListAsync();
                                    foreach (long child in userChildIds)
                                    {
                                        newList.AddRange(list.Where(x => x.Any(xx => xx.Key.Equals(item.Key) && xx.Value.IsNotEmptyOrNull() && xx.Value.ToString().Equals(child))).ToList());
                                    }

                                    break;
                            }
                        }

                        newList.AddRange(list.Where(x => x.Any(xx => xx.Key.Equals(item.Key) && xx.Value.IsNotEmptyOrNull() && xx.Value.ToString().Equals(item.Value.ToString()))).ToList());
                    }

                    list = newList.Distinct().ToList();
                    break;
                case 2: // 模糊查询
                    list = list.Where(x => x.Any(xx => xx.Key.Equals(item.Key) && xx.Value.IsNotEmptyOrNull() && xx.Value.ToString().Contains(item.Value.ToString()))).ToList();
                    break;
                case 3: // 范围查询
                    List<object> between = item.Value.ToObjectOld<List<object>>();
                    switch (searchInfo.jnpfKey)
                    {
                        case JnpfKeyConst.NUMINPUT:
                        {
                            decimal start = between.First().ParseToDecimal();
                            decimal end = between.Last().ParseToDecimal();
                            list = list.Where(x => x.Any(xx => xx.Key.Equals(item.Key) && xx.Value.IsNotEmptyOrNull() && xx.Value.ParseToDecimal() >= start && xx.Value.ParseToDecimal() <= end))
                                .ToList();
                        }
                            break;
                        case JnpfKeyConst.DATE:
                        {
                            var start = IotPlatform.Core.Extension.DateTime.ToTime(between.First());
                            var end = IotPlatform.Core.Extension.DateTime.ToTime(between.Last());
                            list = list.Where(x => x.Any(xx =>
                                xx.Key.Equals(item.Key) && xx.Value.IsNotEmptyOrNull() && Convert.ToDateTime(xx.Value) >= start && Convert.ToDateTime(xx.Value) <= end)).ToList();
                        }
                            break;
                        case JnpfKeyConst.TIME:
                        {
                            DateTime start = Convert.ToDateTime(between.First());
                            DateTime end = Convert.ToDateTime(between.Last());
                            list = list.Where(x => x.Any(xx => xx.Key.Equals(item.Key) && xx.Value.IsNotEmptyOrNull() && Convert.ToDateTime(xx.Value) >= start && Convert.ToDateTime(xx.Value) <= end))
                                .ToList();
                        }
                            break;
                    }

                    break;
            }
        }

        return list;
    }

    public void Dispose()
    {
        _serviceScope.Dispose();
    }

    #endregion
}