using VisualDev.Entity.Dto.VisualPersonal;

namespace VisualDev;

/// <summary>
///     列表个性视图.
/// </summary>
[ApiDescriptionSettings("功能设计",Tag = "VisualDev", Name = "Personal", Order = 177)]
public class VisualPersonalService : IDynamicApiController, ITransient
{
    /// <summary>
    ///     服务基础仓储.
    /// </summary>
    private readonly ISqlSugarRepository<VisualPersonalEntity> _repository;

    /// <summary>
    ///     用户管理.
    /// </summary>
    private readonly IUserManager _userManager;

    /// <summary>
    ///     初始化一个<see cref="VisualPersonalService" />类型的新实例.
    /// </summary>
    public VisualPersonalService(ISqlSugarRepository<VisualPersonalEntity> repository, IUserManager userManager)
    {
        _repository = repository;
        _userManager = userManager;
    }

    #region Get

    /// <summary>
    ///     列表.
    /// </summary>
    /// <param name="input">请求参数.</param>
    /// <returns>返回列表.</returns>
    [HttpGet("/visualdev/personal/list")]
    public async Task<dynamic> GetList([FromQuery] VisualPersonalListInput input)
    {
        List<VisualPersonalListOutput>? list = await _repository.AsQueryable()
            .Where(it => it.MenuId == input.menuId && it.CreatedUserId == _userManager.UserId)
            .OrderBy(it => it.CreatedTime, OrderByType.Desc)
            .Select(it => new VisualPersonalListOutput
            {
                id = it.Id,
                fullName = it.FullName,
                type = it.Type,
                status = it.Status,
                searchList = it.SearchList,
                columnList = it.ColumnList
            })
            .ToListAsync();

        VisualPersonalEntity defaultData = new()
        {
            Id = 1300000000000,
            FullName = "系统视图",
            Type = 0,
            Status = list.Any(x => x.status.Equals(1)) ? 0 : 1
        };

        list.Insert(0, defaultData.Adapt<VisualPersonalListOutput>());

        return list;
    }

    #endregion

    #region POST

    /// <summary>
    ///     新建.
    /// </summary>
    /// <param name="input">实体对象.</param>
    /// <returns></returns>
    [HttpPost("/visualdev/personal/add")]
    public async Task Create([FromBody] VisualPersonalCrInput input)
    {
        List<VisualPersonalEntity>? dataList = await _repository.AsQueryable().Where(x => x.MenuId == input.menuId && x.CreatedUserId == _userManager.UserId).ToListAsync();
        if (dataList.Count >= 5)
        {
            throw Oops.Oh(ErrorCode.D3201);
        }

        if (input.fullName == "系统视图" || dataList.Any(x => x.FullName == input.fullName))
        {
            throw Oops.Oh(ErrorCode.COM1032);
        }

        VisualPersonalEntity entity = input.Adapt<VisualPersonalEntity>();
        entity.Status = 0;
        entity.Type = 1;
        int isOk = await _repository.AsInsertable(entity).IgnoreColumns(true).ExecuteCommandAsync();
        if (isOk < 1)
        {
            throw Oops.Oh(ErrorCode.COM1000);
        }
    }

    /// <summary>
    ///     修改.
    /// </summary>
    /// <param name="id">主键值.</param>
    /// <param name="input">实体对象.</param>
    /// <returns></returns>
    [HttpPost("/visualdev/personal/{id}/update")]
    public async Task Update(long id, [FromBody] VisualPersonalUpInput input)
    {
        if (input.fullName == "系统视图" || await _repository.IsAnyAsync(x => x.Id != id && x.FullName == input.fullName && x.MenuId == input.menuId && x.CreatedUserId == _userManager.UserId))
        {
            throw Oops.Oh(ErrorCode.COM1032);
        }

        VisualPersonalEntity entity = input.Adapt<VisualPersonalEntity>();
        entity.Status = 0;
        entity.Type = 1;
        bool isOk = await _repository.AsUpdateable(entity).IgnoreColumns(true).ExecuteCommandHasChangeAsync();
        if (!isOk)
        {
            throw Oops.Oh(ErrorCode.COM1001);
        }
    }

    /// <summary>
    ///     修改.
    /// </summary>
    /// <param name="id"></param>
    /// <param name="menuId"></param>
    /// <returns></returns>
    [HttpPost("/visualdev/personal/{id}/setDefault")]
    [UnitOfWork]
    public async Task SetDefault(long id, long menuId)
    {
        await _repository.AsUpdateable().SetColumns(it => it.Status == 0).Where(it => it.MenuId == menuId && it.CreatedUserId == _userManager.UserId && it.Status == 1).ExecuteCommandAsync();

        if (id != 1300000000000)
        {
            await _repository.AsUpdateable().SetColumns(it => it.Status == 1).Where(it => it.Id == id).ExecuteCommandAsync();
        }
    }

    /// <summary>
    ///     删除.
    /// </summary>
    /// <param name="id">主键.</param>
    /// <returns></returns>
    [HttpPost("/visualdev/personal/{id}/delete")]
    public async Task Delete(long id)
    {
        await _repository.DeleteByIdAsync(id);
    }

    #endregion
}