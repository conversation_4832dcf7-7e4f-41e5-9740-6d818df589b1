using Systems.Entity;

namespace Common.Core.Manager.User;

/// <summary>
///     用户管理抽象.
/// </summary>
public interface IUserManager
{
    /// <summary>
    ///     组织Id
    /// </summary>
    public long OrgId { get; }
    
    /// <summary>
    ///     组织名称
    /// </summary>
    public string OrgName { get; }

    /// <summary>
    ///     用户编号.
    /// </summary>
    long UserId { get; }

    /// <summary>
    ///     租户ID.
    /// </summary>
    long TenantId { get; }

    /// <summary>
    ///     租户数据库名称.
    /// </summary>
    string TenantDbName { get; }

    /// <summary>
    ///     用户账号.
    /// </summary>
    string Account { get; }

    /// <summary>
    ///     用户昵称.
    /// </summary>
    string RealName { get; }

    /// <summary>
    ///     当前用户 ToKen.
    /// </summary>
    string ToKen { get; }

    /// <summary>
    ///     是否管理员.
    /// </summary>
    bool IsAdministrator { get; }

    /// <summary>
    ///     获取请求端类型 pc 、 app.
    /// </summary>
    string UserOrigin { get; }

    /// <summary>
    ///     用户信息.
    /// </summary>
    SysUser User { get; }
}