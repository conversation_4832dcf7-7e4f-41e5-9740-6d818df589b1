using Extras.DatabaseAccessor.SqlSugar.Repositories;
using Systems.Entity;

namespace Common.Core.Job;

/// <summary>
///     清理日志作业任务
/// </summary>
[JobDetail("clearOpLogs", Description = "清理操作日志", GroupName = "日志清理", Concurrent = false)]
[Daily(TriggerId = "clearOpLogs", Description = "清理操作日志")]
public class LogJob : IJob
{
    private readonly IServiceScopeFactory _scopeFactory;

    public LogJob(IServiceScopeFactory scopeFactory)
    {
        _scopeFactory = scopeFactory;
    }

    public async Task ExecuteAsync(JobExecutingContext context, CancellationToken stoppingToken)
    {
        using IServiceScope serviceScope = _scopeFactory.CreateScope();
        ISqlSugarRepository<SysLogVis>? logVisRep = serviceScope.ServiceProvider.GetService<ISqlSugarRepository<SysLogVis>>();
        ISqlSugarRepository<SysLogOp>? logOpRep = serviceScope.ServiceProvider.GetService<ISqlSugarRepository<SysLogOp>>();
        ISqlSugarRepository<SysLogDiff>? logDiffRep = serviceScope.ServiceProvider.GetService<ISqlSugarRepository<SysLogDiff>>();
        int daysAgo = 30; // 删除30天以前
        await logVisRep?.CopyNew().AsDeleteable().Where(u => (DateTime) u.CreatedTime < DateTime.Now.AddDays(-daysAgo)).ExecuteCommandAsync(stoppingToken); // 删除访问日志
        await logOpRep?.CopyNew().AsDeleteable().Where(u => (DateTime) u.CreatedTime < DateTime.Now.AddDays(-daysAgo)).ExecuteCommandAsync(stoppingToken); // 删除操作日志
        await logDiffRep?.CopyNew().AsDeleteable().Where(u => (DateTime) u.CreatedTime < DateTime.Now.AddDays(-daysAgo)).ExecuteCommandAsync(stoppingToken); // 删除差异日志
    }
}