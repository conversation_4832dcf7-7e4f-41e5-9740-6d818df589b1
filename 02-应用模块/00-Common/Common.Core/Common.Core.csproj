<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <PropertyGroup Condition=" '$(Configuration)' == 'Release' ">
      <DebugSymbols>true</DebugSymbols>
      <DebugType>none</DebugType>
    </PropertyGroup>

    <ItemGroup>
        <ProjectReference Include="..\..\..\01-架构核心\IotPlatform.Core\IotPlatform.Core.csproj"/>
        <ProjectReference Include="..\..\02-System\Systems.Entity\Systems.Entity.csproj"/>
        <ProjectReference Include="..\..\10-VisualDev\VisualDev.Entity\VisualDev.Entity.csproj" />
        <ProjectReference Include="..\Common\Common.csproj"/>
    </ItemGroup>

</Project>
