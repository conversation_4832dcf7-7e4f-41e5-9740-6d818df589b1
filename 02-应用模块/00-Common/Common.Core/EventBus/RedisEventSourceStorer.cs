using System.Threading.Channels;
using Common.Options;
using NewLife.Caching;

namespace Common.Core.EventBus;

/// <summary>
///     Redis自定义事件源存储器
/// </summary>
/// <remarks>
/// </remarks>
public sealed class RedisEventSourceStorer : IEventSourceStorer, IDisposable
{
    /// <summary>
    ///     消费者
    /// </summary>
    private readonly EventConsumer<ChannelEventSource> _eventConsumer;

    /// <summary>
    ///     内存通道事件源存储器
    /// </summary>
    private readonly Channel<IEventSource> _channel;

    private readonly IProducerConsumer<ChannelEventSource> _queueSingle;

    private readonly ILogger<RedisEventSourceStorer> _logger;

    /// <summary>
    ///     构造函数
    /// </summary>
    /// <param name="cacheProvider">Redis 连接对象</param>
    /// <param name="eventBus">事件配置</param>
    public RedisEventSourceStorer(ICacheProvider cacheProvider, EventBusOptions eventBus)
    {
        _logger = App.GetRequiredService<ILogger<RedisEventSourceStorer>>();

        // 配置通道，设置超出默认容量后进入等待
        BoundedChannelOptions boundedChannelOptions = new(eventBus.Capacity)
        {
            FullMode = eventBus.FullMode
        };

        // 创建有限容量通道
        _channel = Channel.CreateBounded<IEventSource>(boundedChannelOptions);

        // （用来发布重启、Reload配置等消息）
        FullRedis redis = (FullRedis)cacheProvider.Cache;
        // 创建队列消息订阅者，只要有一个服务节点消费了消息即可
        _queueSingle = redis.GetQueue<ChannelEventSource>(eventBus.ChannelKey + ":single");
        _eventConsumer = new EventConsumer<ChannelEventSource>(_queueSingle);

        // 订阅消息写入 Channel
        _eventConsumer.Received += async (send, cr) =>
        {
            try
            {
                ChannelEventSource ces = (ChannelEventSource)cr;
                await ConsumeChannelEventSourceAsync(ces, ces.CancellationToken);
            }
            catch (Exception e)
            {
                _logger.LogError(e, "处理Received中的消息产生错误！");
            }
        };
        _eventConsumer.Start();
    }

    private async Task ConsumeChannelEventSourceAsync(ChannelEventSource ces, CancellationToken cancel = default)
    {
        // 打印测试事件
        if (ces.EventId != null && ces.EventId.IndexOf(":Test") > 0)
        {
            ConsoleColor oriColor = Console.ForegroundColor;
            Console.ForegroundColor = ConsoleColor.Green;
            Console.WriteLine($"有消息要处理{ces.EventId},{ces.Payload}");
            Console.ForegroundColor = oriColor;
        }

        await _channel.Writer.WriteAsync(ces, cancel);
    }

    /// <summary>
    ///     将事件源写入存储器
    /// </summary>
    /// <param name="eventSource">事件源对象</param>
    /// <param name="cancellationToken">取消任务 Token</param>
    /// <returns>
    ///     <see cref="ValueTask" />
    /// </returns>
    public async ValueTask WriteAsync(IEventSource eventSource, CancellationToken cancellationToken)
    {
        // 空检查
        if (eventSource == default)
        {
            throw new ArgumentNullException(nameof(eventSource));
        }

        // 这里判断是否是 ChannelEventSource 或者 自定义的 EventSource
        if (eventSource is ChannelEventSource source)
        {
            // 异步发布
            await Task.Factory.StartNew(() => { _queueSingle.Add(source); }, cancellationToken, TaskCreationOptions.LongRunning, TaskScheduler.Default);
        }
        else
        {
            // 处理动态订阅问题
            await _channel.Writer.WriteAsync(eventSource, cancellationToken);
        }
    }

    /// <summary>
    ///     从存储器中读取一条事件源
    /// </summary>
    /// <param name="cancellationToken">取消任务 Token</param>
    /// <returns>事件源对象</returns>
    public async ValueTask<IEventSource> ReadAsync(CancellationToken cancellationToken)
    {
        // 读取一条事件源
        IEventSource eventSource = await _channel.Reader.ReadAsync(cancellationToken);
        return eventSource;
    }

    /// <summary>
    ///     释放非托管资源
    /// </summary>
    public async void Dispose()
    {
        await _eventConsumer.Stop();
        GC.SuppressFinalize(this);
    }
}