using Extras.DatabaseAccessor.SqlSugar.Repositories;
using Furion.LinqBuilder;
using IPTools.Core;
using Systems.Entity;

namespace Common.Core.Logging;

/// <summary>
///     数据库日志写入器
/// </summary>
public class DatabaseLoggingWriter : IDatabaseLoggingWriter, IDisposable
{
    private readonly IServiceScope _serviceScope;
    private readonly ISqlSugarRepository<SysLogVis> _sysLogVisRep; // 访问日志
    private readonly ISqlSugarRepository<SysLogOp> _sysLogOpRep; // 操作日志
    private readonly ISqlSugarRepository<SysLogEx> _sysLogExRep; // 异常日志

    public DatabaseLoggingWriter(IServiceScopeFactory scopeFactory)
    {
        _serviceScope = scopeFactory.CreateScope();
        _sysLogVisRep = _serviceScope.ServiceProvider.GetRequiredService<ISqlSugarRepository<SysLogVis>>();
        _sysLogOpRep = _serviceScope.ServiceProvider.GetRequiredService<ISqlSugarRepository<SysLogOp>>();
        _sysLogExRep = _serviceScope.ServiceProvider.GetRequiredService<ISqlSugarRepository<SysLogEx>>();
    }

    public async Task WriteAsync(LogMessage logMsg, bool flush)
    {
        var jsonStr = logMsg.Context?.Get("loggingMonitor")?.ToString();
        if (string.IsNullOrWhiteSpace(jsonStr))
        {
            await _sysLogOpRep.InsertAsync(new SysLogOp
            {
                DisplayTitle = "自定义操作日志",
                LogDateTime = logMsg.LogDateTime,
                EventId = logMsg.EventId.Id,
                ThreadId = logMsg.ThreadId,
                TraceId = logMsg.TraceId,
                Exception = logMsg.Exception == null ? null : JSON.Serialize(logMsg.Exception),
                Message = logMsg.Message,
                LogLevel = logMsg.LogLevel,
                Status = "200",
            });
            return;
        }
        dynamic? loggingMonitor = JSON.Deserialize<dynamic>(jsonStr);

        // 不记录数据校验日志
        if (loggingMonitor.validation != null)
        {
            return;
        }

        // 获取当前操作者
        string account = "", realName = "", userId = "", tenantId = "";
        if (loggingMonitor.authorizationClaims != null)
        {
            foreach (dynamic? item in loggingMonitor.authorizationClaims)
            {
                if (item.type == ClaimConst.Account)
                {
                    account = item.value;
                }

                if (item.type == ClaimConst.RealName)
                {
                    realName = item.value;
                }

                if (item.type == ClaimConst.TenantId)
                {
                    tenantId = item.value;
                }

                if (item.type == ClaimConst.UserId)
                {
                    userId = item.value;
                }
            }
        }

        string remoteIPv4 = loggingMonitor.remoteIPv4;
        (string ipLocation, double? longitude, double? latitude) = GetIpAddress(remoteIPv4);

        dynamic? client = Parser.GetDefault().Parse(loggingMonitor.userAgent.ToString());
        string browser = $"{client.UA.Family} {client.UA.Major}.{client.UA.Minor} / {client.Device.Family}";
        string os = $"{client.OS.Family} {client.OS.Major} {client.OS.Minor}";

        // 记录异常日志并发送邮件
        if (logMsg.Exception != null || loggingMonitor.exception != null)
        {
            await _sysLogExRep.InsertAsync(new SysLogEx
            {
                ControllerName = loggingMonitor.controllerName,
                ActionName = loggingMonitor.actionTypeName,
                DisplayTitle = loggingMonitor.displayTitle,
                Status = loggingMonitor.returnInformation?.httpStatusCode,
                RemoteIp = remoteIPv4,
                Location = ipLocation,
                Longitude = longitude,
                Latitude = latitude,
                Browser = browser, // loggingMonitor.userAgent,
                Os = os, // loggingMonitor.osDescription + " " + loggingMonitor.osArchitecture,
                Elapsed = loggingMonitor.timeOperationElapsedMilliseconds,
                LogDateTime = logMsg.LogDateTime,
                Account = account,
                RealName = realName,
                HttpMethod = loggingMonitor.httpMethod,
                RequestUrl = loggingMonitor.requestUrl,
                RequestParam = loggingMonitor.parameters == null || loggingMonitor.parameters.Count == 0 ? null : JSON.Serialize(loggingMonitor.parameters[0].value),
                ReturnResult = loggingMonitor.returnInformation == null ? null : JSON.Serialize(loggingMonitor.returnInformation),
                EventId = logMsg.EventId.Id,
                ThreadId = logMsg.ThreadId,
                TraceId = logMsg.TraceId,
                Exception = JSON.Serialize(loggingMonitor.exception),
                Message = logMsg.Message,
                CreatedUserId = string.IsNullOrWhiteSpace(userId) ? 0 : long.Parse(userId),
                TenantId = string.IsNullOrWhiteSpace(tenantId) ? 0 : long.Parse(tenantId),
                LogLevel = logMsg.LogLevel
            });

            // 将异常日志发送到邮件
            await App.GetRequiredService<IEventPublisher>().PublishAsync("Send:ErrorMail", loggingMonitor.exception.ToString());

            return;
        }

        // 记录访问日志-登录登出
        if (loggingMonitor.actionName == "Login" || loggingMonitor.actionName == "Logout")
        {
            if (account.IsNullOrEmpty())
            {
                account = loggingMonitor.parameters[0]?.value?.account;
            }

            await _sysLogVisRep.InsertAsync(new SysLogVis
            {
                ControllerName = loggingMonitor.controllerName,
                ActionName = loggingMonitor.actionTypeName,
                DisplayTitle = loggingMonitor.displayTitle,
                Status = loggingMonitor.returnInformation?.httpStatusCode,
                RemoteIp = remoteIPv4,
                Location = ipLocation,
                Longitude = longitude,
                Latitude = latitude,
                Browser = browser, // loggingMonitor.userAgent,
                Os = os, // loggingMonitor.osDescription + " " + loggingMonitor.osArchitecture,
                Elapsed = loggingMonitor.timeOperationElapsedMilliseconds,
                LogDateTime = logMsg.LogDateTime,
                Account = account,
                RealName = realName,
                CreatedUserId = string.IsNullOrWhiteSpace(userId) ? 0 : long.Parse(userId),
                TenantId = string.IsNullOrWhiteSpace(tenantId) ? 0 : long.Parse(tenantId),
                LogLevel = logMsg.LogLevel
            });

            return;
        }

        // 记录操作日志
        await _sysLogOpRep.InsertAsync(new SysLogOp
        {
            ControllerName = loggingMonitor.controllerName,
            ActionName = loggingMonitor.actionTypeName,
            DisplayTitle = loggingMonitor.displayTitle,
            Status = loggingMonitor.returnInformation?.httpStatusCode,
            RemoteIp = remoteIPv4,
            Location = ipLocation,
            Longitude = longitude,
            Latitude = latitude,
            Browser = browser, // loggingMonitor.userAgent,
            Os = os, // loggingMonitor.osDescription + " " + loggingMonitor.osArchitecture,
            Elapsed = loggingMonitor.timeOperationElapsedMilliseconds,
            LogDateTime = logMsg.LogDateTime,
            Account = account,
            RealName = realName,
            HttpMethod = loggingMonitor.httpMethod,
            RequestUrl = loggingMonitor.requestUrl,
            RequestParam = loggingMonitor.parameters == null || loggingMonitor.parameters.Count == 0 ? null : JSON.Serialize(loggingMonitor.parameters[0].value),
            ReturnResult = loggingMonitor.returnInformation == null ? null : JSON.Serialize(loggingMonitor.returnInformation),
            EventId = logMsg.EventId.Id,
            ThreadId = logMsg.ThreadId,
            TraceId = logMsg.TraceId,
            Exception = loggingMonitor.exception == null ? null : JSON.Serialize(loggingMonitor.exception),
            Message = logMsg.Message,
            CreatedUserId = string.IsNullOrWhiteSpace(userId) ? 0 : long.Parse(userId),
            TenantId = string.IsNullOrWhiteSpace(tenantId) ? 0 : long.Parse(tenantId),
            LogLevel = logMsg.LogLevel
        });
    }

    /// <summary>
    ///     解析IP地址
    /// </summary>
    /// <param name="ip"></param>
    /// <returns></returns>
    internal static (string ipLocation, double? longitude, double? latitude) GetIpAddress(string ip)
    {
        try
        {
            IpInfo? ipInfo = IpTool.Search(ip);
            List<string> addressList = new() {ipInfo.Country, ipInfo.Province, ipInfo.City, ipInfo.NetworkOperator};
            return (string.Join("|", addressList.Where(it => it != "0").ToList()), ipInfo.Longitude, ipInfo.Latitude); // 去掉0并用|连接
        }
        catch
        {
        }

        return ("未知", 0, 0);
    }

    /// <summary>
    ///     释放服务作用域
    /// </summary>
    public void Dispose()
    {
        _serviceScope.Dispose();
    }
}