using Microsoft.AspNetCore.SignalR;

namespace Common.Hubs;

/// <summary>
///     发布消息
/// </summary>
public class SocketSingleton : ISingleton
{
    private IHubCallerClients _clients = null!;

    /// <summary>
    ///     初始化
    /// </summary>
    /// <param name="clients"></param>
    public void Init(IHubCallerClients clients)
    {
        _clients = clients;
    }

    /// <summary>
    ///     消息发送
    /// </summary>
    /// <param name="context"></param>
    /// <param name="topic"></param>
    /// <param name="debug"></param>
    /// <returns></returns>
    public async Task Send(string context, string topic = "DeviceResult", bool debug = false)
    {
        try
        {
            if (debug)
            {
                Log.Information(context);
            }

            if (_clients == null)
            {
                return;
            }

            await _clients.Group(topic).SendAsync(topic, context);
        }
        catch (Exception ex)
        {
            Log.Error($"消息推送失败,错误:{ex.Message}！");
        }
    }

    /// <summary>
    ///     错误消息发送
    /// </summary>
    /// <param name="context"></param>
    /// <param name="topic"></param>
    public async Task Error(string context, string topic = "Error")
    {
        try
        {
            if (_clients == null)
            {
                return;
            }

            await _clients.Group(topic).SendAsync(topic, context);
            // _clients.All.SendAsync(topic, context);
        }
        catch (Exception ex)
        {
            Log.Error($"Error消息推送失败,错误:{ex.Message}！");
        }
    }
}