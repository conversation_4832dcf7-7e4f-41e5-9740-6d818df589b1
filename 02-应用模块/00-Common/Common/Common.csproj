<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <PropertyGroup Condition=" '$(Configuration)' == 'Release' ">
      <DebugSymbols>true</DebugSymbols>
      <DebugType>none</DebugType>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="NPOI" Version="2.5.5" />
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\..\..\01-架构核心\Extras.DatabaseAccessor.SqlSugar\Extras.DatabaseAccessor.SqlSugar.csproj"/>
        <ProjectReference Include="..\..\..\01-架构核心\IotPlatform.Core\IotPlatform.Core.csproj"/>
        <ProjectReference Include="..\..\09-Engine\Engine.Entity\Engine.Entity.csproj" />
    </ItemGroup>

</Project>
