using System.ComponentModel.DataAnnotations;
using Furion.DataValidation;

namespace Common.Models;

/// <summary>
///     主键Id输入参数
/// </summary>
public class BaseId
{
    /// <summary>
    ///     主键Id
    /// </summary>
    [Required(ErrorMessage = "Id不能为空")]
    [DataValidation(ValidationTypes.Numeric)]
    public virtual long Id { get; set; }
}

/// <summary>
///     主键Id映射DTO
/// </summary>
public class BaseId<T>
{
    /// <summary>
    ///     主键Id
    /// </summary>
    [Required(ErrorMessage = "Id不能为空")]
    public T Id { get; set; }
}

/// <summary>
/// </summary>
/// <typeparam name="T"></typeparam>
public class EnableInput<T> : BaseId<T>
{
    /// <summary>
    ///     是否启用
    /// </summary>
    public bool Enable { get; set; }
}

/// <summary>
/// </summary>
public class BaseReName : BaseId
{
    /// <summary>
    /// </summary>
    [Required]
    public string Name { get; set; }
}