using Common.Configuration;
using IotPlatform.Core.Enum;
using IotPlatform.Core.Extension;

namespace IotPlatform.VisualData.Services;

/// <summary>
///     可视化管理-大屏设计
///     版 本:V5.0.0
///     版 权:杭州峰回科技有限公司
///     作 者:蒋状
///     日 期:2023-07-05
/// </summary>
[ApiDescriptionSettings("可视化管理")]
public class ScreenService : IDynamicApiController, ITransient
{
    private readonly ISqlSugarRepository<VisualCategory> _visualCategoryRepository;
    private readonly ISqlSugarRepository<VisualConfig> _visualConfigRepository;
    private readonly ISqlSugarRepository<Visual> _visualRepository;

    /// <summary>
    ///     初始化一个<see cref="ScreenService" />类型的新实例
    /// </summary>
    public ScreenService(ISqlSugarRepository<Visual> visualRepository,
        ISqlSugarRepository<VisualConfig> visualConfigRepository,
        ISqlSugarRepository<VisualCategory> visualCategoryRepository)
    {
        _visualRepository = visualRepository;
        _visualConfigRepository = visualConfigRepository;
        _visualCategoryRepository = visualCategoryRepository;
    }

    #region PrivateMethod

    /// <summary>
    ///     允许文件类型
    /// </summary>
    /// <param name="fileExtension">文件后缀名</param>
    /// <returns></returns>
    private bool AllowImageType(string fileExtension)
    {
        List<string>? allowExtension = KeyVariable.AllowImageType;
        string? isExist = allowExtension.Find(a => a == fileExtension.ToLower());
        return !string.IsNullOrEmpty(isExist);
    }

    #endregion

    #region Get

    /// <summary>
    ///     分页
    /// </summary>
    /// <returns></returns>
    [HttpGet("/blade-visual/visual/page")]
    public async Task<SqlSugarPagedList<ScreenListOutput>> GetList([FromQuery] ScreenListQueryInput input)
    {
        SqlSugarPagedList<ScreenListOutput>? data = await _visualRepository.AsQueryable()
            .WhereIF(input.Category > 0 ,v => v.Category == input.Category)
            .WhereIF(input.SearchValue.IsNotEmptyOrNull(), w => w.Title.Contains(input.SearchValue))
            .Select<ScreenListOutput>()
            .ToPagedListAsync(input.PageNo, input.PageSize);
        return data;
    }

    /// <summary>
    ///     详情
    /// </summary>
    /// <returns></returns>
    [HttpGet("/blade-visual/visual/detail")]
    [AllowAnonymous]
    public async Task<dynamic> GetInfo([FromQuery] BaseId input)
    {
        Visual? entity = await _visualRepository.GetFirstAsync(v => v.Id == input.Id);
        VisualConfig? configEntity = await _visualConfigRepository.GetFirstAsync(v => v.VisualId == input.Id);
        return new { Config = configEntity.Adapt<ScreenConfigInfoOutput>(), Visual = entity.Adapt<ScreenInfoOutput>() };
    }

    /// <summary>
    ///     获取类型
    /// </summary>
    /// <returns></returns>
    [HttpGet("/blade-visual/visual/category")]
    public async Task<dynamic> GetCategoryList()
    {
        List<VisualCategory>? entity = await _visualCategoryRepository.GetListAsync();
        return entity.Adapt<ScreenCategoryListOutput>();
    }

    /// <summary>
    ///     获取图片列表
    /// </summary>
    /// <returns></returns>
    [HttpGet("/blade-visual/visual/{type}")]
    public dynamic GetImgFileList(string type)
    {
        List<ScreenImgFileOutput> list = new();
        Dictionary<int, string>? typeEnum = typeof(ScreenImgEnum).GetEnumDescDictionary();
        KeyValuePair<int, string> imgEnum = typeEnum.Where(t => t.Value.Equals(type)).FirstOrDefault();
        if (imgEnum.Value != null)
        {
            string path = Path.Combine(Path.Combine(KeyVariable.SystemPath, "BiVisualPath"), imgEnum.Value);
            List<FileInfo> fileinfos = FileHelper.GetAllFiles(path);
            foreach (FileInfo item in fileinfos)
            {
                list.Add(new ScreenImgFileOutput
                {
                    Link = string.Format(@"/api/file/VisusalImg/{0}/{1}", type, item.Name),
                    OriginalName = item.Name
                });
            }
        }

        return list;
    }

    /// <summary>
    ///     查看图片
    /// </summary>
    /// <returns></returns>
    [HttpGet("/blade-visual/visual/{type}/{fileName}")]
    [AllowAnonymous]
    public dynamic GetImgFile(string type, string fileName)
    {
        Dictionary<int, string>? typeEnum = typeof(ScreenImgEnum).GetEnumDescDictionary();
        (_, string? value) = typeEnum.FirstOrDefault(t => t.Value.Equals(type));
        if (value == null)
        {
            return Task.FromResult(false);
        }

        string path = Path.Combine(Path.Combine(KeyVariable.SystemPath, "BiVisualPath"), value, fileName);
        return new FileStreamResult(new FileStream(path, FileMode.Open), "application/octet-stream") { FileDownloadName = fileName };
    }

    /// <summary>
    ///     大屏下拉框
    /// </summary>
    /// <returns></returns>
    [HttpGet("/blade-visual/visual/selector")]
    public async Task<dynamic> GetSelector()
    {
        List<ScreenSelectorOutput>? screenList = await _visualRepository.AsQueryable()
            .Select(v => new ScreenSelectorOutput
            {
                id = v.Id.ToString(),
                parentId = SqlFunc.ToString(v.Category),
                FullName = v.Title
            }).ToListAsync();
        List<ScreenSelectorOutput>? categortList = await _visualCategoryRepository.AsQueryable()
            .Select(v => new ScreenSelectorOutput
            {
                id = v.CategoryValue,
                parentId = "0",
                FullName = v.CategoryKey
            }).ToListAsync();
        return new { list = categortList.Union(screenList).ToList().ToTree() };
    }

    #endregion

    #region Post

    /// <summary>
    ///     新增
    /// </summary>
    /// <returns></returns>
    [HttpPost("/blade-visual/visual/add")]
    [UnitOfWork]
    public async Task<dynamic> Save([FromBody] ScreenCrInput input)
    {
        Visual entity = input.Visual.Adapt<Visual>();
        VisualConfig configEntity = input.Config.Adapt<VisualConfig>();
        try
        {
            Visual? newEntity = await _visualRepository.AsInsertable(entity).ExecuteReturnEntityAsync();
            configEntity.VisualId = newEntity.Id;
            await _visualConfigRepository.AsInsertable(configEntity).ExecuteReturnEntityAsync();

            return new { id = newEntity.Id };
        }
        catch (Exception ex)
        {
            throw Oops.Oh(ErrorCode.COM1000);
        }
    }

    /// <summary>
    ///     修改
    /// </summary>
    /// <returns></returns>
    [HttpPost("/blade-visual/visual/update")]
    [UnitOfWork]
    public async Task Update([FromBody] ScreenUpInput input)
    {
        Visual? entity = new();
        if (input.Visual.BackgroundUrl != null)
        {
            entity = await _visualRepository.GetFirstAsync(v => v.Id == input.Visual.Id);
            entity.BackgroundUrl = input.Visual.BackgroundUrl;
        }
        else
        {
            entity = input.Visual.Adapt<Visual>();
        }

        VisualConfig? configEntity = input.Config.Adapt<VisualConfig>();
        try
        {
            await _visualRepository.AsUpdateable(entity).IgnoreColumns(ignoreAllNullColumns: true).ExecuteCommandAsync();
            if (configEntity != null)
            {
                configEntity.VisualId = entity.Id;
                await _visualConfigRepository.AsUpdateable(configEntity).IgnoreColumns(ignoreAllNullColumns: true).ExecuteCommandAsync();
            }
        }
        catch (Exception)
        {
            throw Oops.Oh(ErrorCode.COM1001);
        }
    }

    /// <summary>
    ///     逻辑删除
    /// </summary>
    /// <returns></returns>
    [HttpPost("/blade-visual/visual/delete")]
    public async Task Remove(BaseId<string> input)
    {
        string[] ids = input.Id.Split(",").ToArray();
        Visual? entity = await _visualRepository.GetFirstAsync(v => ids.Contains(v.Id.ToString()));
        await _visualRepository.DeleteAsync(entity);
    }

    /// <summary>
    ///     复制
    /// </summary>
    /// <returns></returns>
    [HttpPost("/blade-visual/visual/copy")]
    [UnitOfWork]
    public async Task<dynamic> Copy(BaseId input)
    {
        Visual? entity = await _visualRepository.GetFirstAsync(v => v.Id == input.Id);
        VisualConfig? configEntity = await _visualConfigRepository.GetFirstAsync(v => v.VisualId == input.Id);
        try
        {
            entity.Id = YitIdHelper.NextId();
            Visual? newEntity = await _visualRepository.AsInsertable(entity).ExecuteReturnEntityAsync();
            configEntity.Id = YitIdHelper.NextId();
            configEntity.VisualId = entity.Id;
            await _visualConfigRepository.AsInsertable(configEntity).ExecuteReturnEntityAsync();

            return new { id = newEntity.Id };
        }
        catch (Exception)
        {
            throw Oops.Oh(ErrorCode.COM1000);
        }
    }
    
    /// <summary>
    /// 上传文件.
    /// </summary>
    /// <returns></returns>
    [HttpPost("/put-file/{type}")]
    [AllowAnonymous]
    public async Task<dynamic> SaveFile(string type, IFormFile file)
    {
        var typeEnum = typeof(ScreenImgEnum).GetEnumDescDictionary();
        var imgEnum = typeEnum.FirstOrDefault(t => t.Value.Equals(type));
        if (imgEnum.Value != null)
        {
            string? imgType = Path.GetExtension(file.FileName).Replace(".", string.Empty);
            //if (!this.AllowImageType(ImgType))
            //    throw Oops.Oh(ErrorCode.D5013);
            var path = imgEnum.Value;
            string? filePath = Path.Combine(FileVariable.BiVisualPath, path);
            if (!Directory.Exists(filePath))
                Directory.CreateDirectory(filePath);
            string? fileName = YitIdHelper.NextId() + "." + imgType;
            await using (FileStream stream = File.Create(Path.Combine(filePath, fileName)))
            {
                await file.CopyToAsync(stream);
            }
            var fielNameUrl = string.Format("/{0}/{1}/{2}/{3}/{4}/{5}", "api", "file", "VisusalImg", "BiVisualPath", path, fileName);
            return new { name = fielNameUrl, link = fielNameUrl, originalName = file.FileName };
        }

        return Task.FromResult(false);
    }

    #endregion
}