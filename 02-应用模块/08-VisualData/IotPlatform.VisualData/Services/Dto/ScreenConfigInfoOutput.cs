namespace IotPlatform.VisualData.Services.Dto;

/// <summary>
///     大屏配置详情输出
/// </summary>
public class ScreenConfigInfoOutput
{
    /// <summary>
    ///     组件json
    /// </summary>
    public string Component { get; set; }

    /// <summary>
    ///     配置json
    /// </summary>
    public string Detail { get; set; }

    /// <summary>
    ///     主键
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    ///     可视化表主键
    /// </summary>
    public long VisualId { get; set; }
}