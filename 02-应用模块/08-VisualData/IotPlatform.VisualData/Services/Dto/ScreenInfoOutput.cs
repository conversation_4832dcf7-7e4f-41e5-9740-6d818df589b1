namespace IotPlatform.VisualData.Services.Dto;

/// <summary>
///     大屏信息输出
/// </summary>
public class ScreenInfoOutput
{
    /// <summary>
    ///     大屏背景
    /// </summary>
    public string BackgroundUrl { get; set; }

    /// <summary>
    ///     大屏类型
    /// </summary>
    public int Category { get; set; }

    /// <summary>
    ///     创建部门
    /// </summary>
    public string CreateDept { get; set; }

    /// <summary>
    ///     创建时间
    /// </summary>
    public DateTime CreatedTime { get; set; }

    /// <summary>
    ///     创建人
    /// </summary>
    public long CreatedUserId { get; set; }

    /// <summary>
    ///     主键ID
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    ///     发布密码
    /// </summary>
    public string Password { get; set; }

    /// <summary>
    ///     业务状态
    /// </summary>
    public int Status { get; set; }

    /// <summary>
    ///     大屏标题
    /// </summary>
    public string Title { get; set; }

    /// <summary>
    ///     更新时间
    /// </summary>
    public DateTime? UpdatedTime { get; set; }

    /// <summary>
    ///     更新人
    /// </summary>
    public long UpdatedUserId { get; set; }
    
    /// <summary>
    ///     外连配置
    /// </summary>
    [SugarColumn(ColumnDescription = "外连配置",IsJson = true,IsNullable = true)]
    public VisualLinkModel? VisualLink { get; set; }
}