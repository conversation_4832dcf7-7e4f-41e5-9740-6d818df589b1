namespace IotPlatform.VisualData.Services.Dto;

/// <summary>
/// </summary>
public class ScreenCrInput
{
    /// <summary>
    /// </summary>
    public ScreenConfigCrInput Config { get; set; }

    /// <summary>
    /// </summary>
    public ScreenEntityCrInput Visual { get; set; }
}

/// <summary>
/// </summary>
public class ScreenEntityCrInput
{
    /// <summary>
    ///     大屏类型
    /// </summary>
    public long Category { get; set; }

    /// <summary>
    ///     创建部门
    /// </summary>
    public string CreateDept { get; set; }

    /// <summary>
    ///     发布密码
    /// </summary>
    public string Password { get; set; }

    /// <summary>
    ///     大屏标题
    /// </summary>
    public string Title { get; set; }
    
    /// <summary>
    ///     外连配置
    /// </summary>
    [SugarColumn(ColumnDescription = "外连配置",IsJson = true,IsNullable = true)]
    public VisualLinkModel? VisualLink { get; set; }
}