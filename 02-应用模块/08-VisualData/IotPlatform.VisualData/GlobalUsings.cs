global using Furion.DatabaseAccessor;
global using Furion.DependencyInjection;
global using Furion.DynamicApiController;
global using Furion.FriendlyException;
global using IotPlatform.VisualData.Entity;
global using IotPlatform.VisualData.Services.Dto;
global using Mapster;
global using Microsoft.AspNetCore.Authorization;
global using Microsoft.AspNetCore.Http;
global using Microsoft.AspNetCore.Mvc;
global using SqlSugar;
global using System;
global using Yitter.IdGenerator;
global using System.ComponentModel;
global using IotPlatform.Core.Entity;
global using Systems.Entity;
global using Common.Models;
global using Common.Security;
global using Extras.DatabaseAccessor.SqlSugar.Extensions;
global using Extras.DatabaseAccessor.SqlSugar.Internal;
global using Extras.DatabaseAccessor.SqlSugar.Repositories;
global using System.Collections.Generic;
global using System.Linq;
global using System.Threading.Tasks;