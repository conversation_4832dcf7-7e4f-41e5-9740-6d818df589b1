namespace IotPlatform.VisualData.Entity;

/// <summary>
///     可视化配置表
/// </summary>
[SugarTable("business_visualConfig", "可视化配置表")]
public class VisualConfig : EntityTenantId
{
    /// <summary>
    ///     可视化表主键
    /// </summary>
    [SugarColumn(ColumnDescription = "可视化表主键")]
    public long VisualId { get; set; }

    /// <summary>
    ///     配置json
    /// </summary>
    [SugarColumn(ColumnDescription = "配置json", ColumnDataType = "longtext,text,clob",IsNullable = true)]
    public string? Detail { get; set; }

    /// <summary>
    ///     组件json
    /// </summary>
    [SugarColumn(ColumnDescription = "组件json", ColumnDataType = "longtext,text,clob",IsNullable = true)]
    public string? Component { get; set; }
}