namespace IotPlatform.VisualData.Entity;

/// <summary>
///     可视化地图配置表
/// </summary>
[SugarTable("business_visualMap", "可视化地图配置表")]
public class VisualMap : EntityTenantId
{
    /// <summary>
    ///     名称
    /// </summary>
    [SugarColumn(ColumnDescription = "名称")]
    public string Name { get; set; }

    /// <summary>
    ///     地图数据
    /// </summary>
    [SugarColumn(ColumnDescription = "地图数据", ColumnDataType = "longtext,text,clob")]
    public string Data { get; set; }
}