<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <PropertyGroup Condition=" '$(Configuration)' == 'Release' ">
      <DebugType>none</DebugType>
    </PropertyGroup>

    <ItemGroup>
      <ProjectReference Include="..\..\..\01-架构核心\IotPlatform.Core\IotPlatform.Core.csproj" />
      <ProjectReference Include="..\..\00-Common\Common\Common.csproj" />
      <ProjectReference Include="..\..\07-Thing\03-RemoteControl\IotPlatform.Thing.RemoteControl.Entity\IotPlatform.Thing.RemoteControl.Entity.csproj" />
      
    </ItemGroup>

</Project>
