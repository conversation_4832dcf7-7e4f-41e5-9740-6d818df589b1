using JsScript.Engine.Attributes;
using Common.Hubs;
using System;
using System.Threading.Tasks;

namespace JsScript.Engine.EngineMethods;

/// <summary>
///     Socket消息发送类
///     用于通过Socket向指定主题发送消息
/// </summary>
[Engine]
public class PluginSocket : ISingleton
{
    /// <summary>
    ///     Socket推送
    /// </summary>
    private readonly SocketSingleton _socketService;

    /// <summary>
    ///     构造函数
    /// </summary>
    /// <param name="socketService">Socket服务</param>
    public PluginSocket(SocketSingleton socketService)
    {
        _socketService = socketService;
    }

    /// <summary>
    ///     发送Socket消息
    /// </summary>
    /// <param name="content">消息内容</param>
    /// <param name="topic">消息主题</param>
    /// <param name="debug">是否输出调试信息</param>
    /// <returns>发送是否成功</returns>
    [EngineMethod("socket.Send('消息内容', '主题', false)", "发送Socket消息", "// 方法:socket.Send()\n// 参数一: '消息内容' 要发送的消息内容\n// 参数二: '主题' 消息主题，默认为'DeviceResult'\n// 参数三: false 是否输出调试信息(非必填,默认false)\n// 返回类型:bool\n// 描述:该方法发送Socket消息到指定主题\nvar content = '温度过高警报';\nvar topic = 'alarm';\nvar success = socket.Send(content, topic);\nreturn success;\n\n// 示例返回结果\ntrue", "消息操作")]
    public bool Send(string content, string topic = "DeviceResult", bool debug = false)
    {
        if (string.IsNullOrEmpty(content))
        {
            return false;
        }

        try
        {
            // 使用同步方式调用异步方法
            _socketService.Send(content, topic, debug).Wait();
            return true;
        }
        catch (Exception ex)
        {
            Log.Error($"Socket消息发送失败,错误:{ex.Message}！");
            return false;
        }
    }
}