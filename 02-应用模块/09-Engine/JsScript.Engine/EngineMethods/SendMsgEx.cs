using JsScript.Engine.Attributes;
using MQTTnet.Client;

namespace JsScript.Engine.EngineMethods;

/// <summary>
///     消息发送
/// </summary>
[Engine]
public class SendMsgEx : IDisposable
{
    /// <summary>
    ///     mqtt对象
    /// </summary>
    public IMqttClient? Client { get; set; }

    /// <summary>
    ///     连接MQTT客户端
    /// </summary>
    /// <param name="client">MQTT客户端对象</param>
    /// <returns></returns>
    [EngineMethod("mqtt.Connect(client)", "连接MQTT客户端", "// 方法:mqtt.Connect()\n// 参数一: client MQTT客户端对象\n// 返回类型:bool\n// 描述:该方法连接到已存在的MQTT客户端\nvar success = mqtt.Connect(client);\nreturn success;\n\n// 示例返回结果\ntrue", "消息操作")]
    public bool Connect(IMqttClient? client)
    {
        Client = client;
        return true;
    }

    /// <summary>
    ///     连接MQTT服务器
    /// </summary>
    /// <param name="ip">服务器地址</param>
    /// <param name="port">服务器端口</param>
    /// <param name="clientId">客户端ID</param>
    /// <param name="userName">用户名</param>
    /// <param name="password">密码</param>
    /// <returns></returns>
    [EngineMethod("mqtt.Connect('broker.example.com', 1883, 'client001', 'username', 'password')", "连接MQTT服务器", "// 方法:mqtt.Connect()\n// 参数一: 'broker.example.com' MQTT服务器地址\n// 参数二: 1883 MQTT服务器端口\n// 参数三: 'client001' 客户端ID(非必填)\n// 参数四: 'username' 用户名(非必填)\n// 参数五: 'password' 密码(非必填)\n// 返回类型:bool\n// 描述:该方法连接到MQTT服务器\nvar success = mqtt.Connect('broker.example.com', 1883, 'client001', 'username', 'password');\nreturn success;\n\n// 示例返回结果\ntrue", "消息操作")]
    public bool Connect(string ip, int port, string? clientId = null, string userName = "", string password = "")
    {
        Client = new MqttFactory().CreateMqttClient();
        var clientOptions = new MqttClientOptionsBuilder()
            .WithClientId(clientId ?? Guid.NewGuid().ToString("N"))
            .WithTcpServer(ip, port)
            .WithCredentials(userName, password)
            .WithTimeout(TimeSpan.FromSeconds(5))
            .WithKeepAlivePeriod(TimeSpan.FromSeconds(60))
            .WithCleanSession()
            .Build();
        try
        {
            Client.ConnectAsync(clientOptions, CancellationToken.None).GetAwaiter().GetResult();
        }
        catch (Exception e)
        {
            return false;
        }

        return Client.IsConnected;
    }

    /// <summary>
    ///     发送MQTT消息
    /// </summary>
    /// <param name="topic">消息主题</param>
    /// <param name="message">消息内容</param>
    /// <param name="level">消息质量</param>
    /// <param name="retain">保留消息</param>
    /// <returns></returns>
    /// <exception cref="AppFriendlyException"></exception>
    [EngineMethod("mqtt.Publish('sensors/temperature', '{\"temp\":25.6}', 1, false)", "发布MQTT消息", "// 方法:mqtt.Publish()\n// 参数一: 'sensors/temperature' 消息主题\n// 参数二: '{\"temp\":25.6}' 消息内容\n// 参数三: 1 消息质量等级(0,1,2)(非必填,默认1)\n// 参数四: false 是否保留消息(非必填,默认false)\n// 返回类型:object\n// 描述:该方法发布MQTT消息\nvar topic = 'sensors/temperature';\nvar message = JSON.stringify({temp: 25.6});\nvar result = mqtt.Publish(topic, message);\nreturn result;\n\n// 示例返回结果\n{success: true, message: '上送Topic:sensors/temperature 成功'}", "消息操作")]
    public object Publish(string topic, string message, MqttQualityOfServiceLevel level = MqttQualityOfServiceLevel.AtLeastOnce, bool retain = false)
    {
        try
        {
            if (Client == null || Client.IsConnected == false)
                return new { success = false, message = "MQTT服务连接已断开!" };

            var resp = Client.PublishAsync(new MqttApplicationMessage
            {
                Topic = topic,
                PayloadSegment = Encoding.UTF8.GetBytes(message),
                QualityOfServiceLevel = level,
                Retain = retain
            }).Result;
            return resp.ReasonCode != MqttClientPublishReasonCode.Success
                ? new { success = false, message = $"上送请求Topic:{topic} 失败,返回结果:{JSON.Serialize(resp)}" }
                : new { success = true, message = $"上送Topic:{topic} 成功" };
        }
        catch (Exception e)
        {
            Log.Error($"【MQTT Publish】 Error:{e.Message}");
            return new { success = false, message = $"【MQTT Publish】 消息发送异常:{e.Message}" };
        }
    }

    /// <summary>
    ///     断开MQTT连接
    /// </summary>
    [EngineMethod("mqtt.DisConnect()", "断开MQTT连接", "// 方法:mqtt.DisConnect()\n// 参数: 无\n// 返回类型:void\n// 描述:该方法断开MQTT连接\nmqtt.DisConnect();\nreturn '已断开连接';\n\n// 示例返回结果\n已断开连接", "消息操作")]
    public void DisConnect()
    {
        Client?.DisconnectAsync();
    }

    public void Dispose()
    {
        Client?.DisconnectAsync();
        Client?.Dispose();
    }
}