using System.Security.Cryptography;
using JsScript.Engine.Attributes;
using Yitter.IdGenerator;

namespace JsScript.Engine.EngineMethods;

/// <summary>
///     系统方法
/// </summary>
[Engine]
public class SystemEngine : ISingleton
{
    /// <summary>
    ///     Md5加密
    /// </summary>
    /// <param name="text"></param>
    /// <param name="uppercase"></param>
    /// <param name="is16"></param>
    /// <returns></returns>
    [EngineMethod("system.Md5Encryption('text', false, false)", "MD5加密", "// 方法:system.Md5Encryption()\n// 参数一: 'text' 需要加密的文本\n// 参数二: false 是否大写(非必填,默认false)\n// 参数三: false 是否16位MD5(非必填,默认false)\n// 返回类型:string\n// 描述:该方法对文本进行MD5加密\nvar text = 'Hello World';\nvar md5 = system.Md5Encryption(text);\nreturn md5;\n\n// 示例返回结果\nb10a8db164e0754105b7a99be72e3fe5", "系统操作")]
    public string Md5Encryption(string text, bool uppercase = false, bool is16 = false)
    {
        //  MD5 加密
        string? md5Hash = MD5Encryption.Encrypt(text, uppercase, is16); // 加密
        return md5Hash;
    }

    /// <summary>
    ///     HMAC-SHA256算法加密
    /// </summary>
    /// <param name="signingString"></param>
    /// <param name="secretKey"></param>
    /// <param name="toLower"></param>
    /// <returns></returns>
    [EngineMethod("system.HmacSha256('data', 'secret', true)", "HMAC-SHA256加密", "// 方法:system.HmacSha256()\n// 参数一: 'data' 需要加密的数据\n// 参数二: 'secret' 密钥\n// 参数三: true 是否小写(非必填,默认true)\n// 返回类型:string\n// 描述:该方法使用HMAC-SHA256算法对数据进行加密\nvar data = 'Hello World';\nvar secret = 'mySecretKey';\nvar hash = system.HmacSha256(data, secret);\nreturn hash;\n\n// 示例返回结果\n0a128eb4a4f8c11646f957f6f3c4b5eb3ad5cb9f4fed3efd7acd7f78a2cdcd7c", "系统操作")]
    public string HmacSha256(string signingString, string secretKey, bool toLower = true)
    {
        var keyBytes = Encoding.UTF8.GetBytes(secretKey);
        var dataBytes = Encoding.UTF8.GetBytes(signingString);
        using var hmac = new HMACSHA256(keyBytes);
        var hashBytes = hmac.ComputeHash(dataBytes);
        var hexString = BitConverter.ToString(hashBytes).Replace("-", "");
        return toLower ? hexString.ToLower() : hexString;
    }

    /// <summary>
    ///     休眠
    /// </summary>
    /// <param name="millisecondsTimeout"></param>
    /// <returns></returns>
    [EngineMethod("system.Sleep(1000)", "线程休眠", "// 方法:system.Sleep()\n// 参数一: 1000 休眠毫秒数\n// 返回类型:void\n// 描述:该方法使当前线程休眠指定的毫秒数\nsystem.Sleep(1000); // 休眠1秒\nreturn '休眠完成';\n\n// 示例返回结果\n休眠完成", "系统操作")]
    public void Sleep(int millisecondsTimeout)
    {
        Thread.Sleep(millisecondsTimeout);
    }

    /// <summary>
    ///     生成Guid
    /// </summary>
    /// <param name="toString">format格式</param>
    /// <returns></returns>
    [EngineMethod("system.Guid('')", "生成GUID", "// 方法:system.Guid()\n// 参数一: '' 格式化字符串(非必填)\n// 返回类型:string\n// 描述:该方法生成一个新的GUID\nvar guid = system.Guid();\nreturn guid;\n\n// 示例返回结果\n3f2504e0-4f89-11d3-9a0c-0305e82c3301", "系统操作")]
    public string Guid(string? toString = "")
    {
        return toString != null ? System.Guid.NewGuid().ToString(toString) : System.Guid.NewGuid().ToString();
    }

    /// <summary>
    ///     生成雪花Id
    /// </summary>
    /// <returns></returns>
    [EngineMethod("system.SnowId()", "生成雪花ID", "// 方法:system.SnowId()\n// 参数: 无\n// 返回类型:long\n// 描述:该方法生成一个雪花算法的分布式ID\nvar id = system.SnowId();\nreturn id;\n\n// 示例返回结果\n986301066474631168", "系统操作")]
    public long SnowId()
    {
        return YitIdHelper.NextId();
    }
}