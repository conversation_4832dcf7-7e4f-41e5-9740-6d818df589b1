using JsScript.Engine.Attributes;
using DbType = SqlSugar.DbType;
using Extensions = IotPlatform.Core.Extension.Extensions;
using System.Text.Json;

namespace JsScript.Engine.EngineMethods;

/// <summary>
/// 数据库执行结果
/// </summary>
public class DbExecutionResult
{
    /// <summary>
    /// 执行的SQL
    /// </summary>
    public string Sql { get; set; } = "";
    /// <summary>
    /// 影响行数
    /// </summary>
    public int AffectedRows { get; set; } = 0;
    /// <summary>
    /// 是否成功
    /// </summary>
    public bool Success { get; set; } = false;
    /// <summary>
    /// 错误信息
    /// </summary>
    public string ErrorMessage { get; set; } = "";
}

/// <summary>
///     本地数据库相关脚本
/// </summary>
[Engine]
public class InternalDatabaseEngine : ITransient, IDisposable
{
    private readonly SqlSugarScope _db;

    /// <summary>
    /// </summary>
    public InternalDatabaseEngine(ISqlSugarClient context)
    {
        _db = (SqlSugarScope)context;
    }

    /// <summary>
    ///     执行查询sql语句
    /// </summary>
    /// <param name="sql"></param>
    /// <returns></returns>
    [EngineMethod("db.current().select('SELECT * FROM users WHERE age > 18')", "执行查询SQL", "// 方法:db.current().select()\n// 参数一: 'SELECT * FROM users WHERE age > 18' SQL查询语句\n// 返回类型:string\n// 描述:该方法在内部数据库执行SQL查询并返回结果\nvar sql = 'SELECT * FROM users WHERE age > 18';\nvar result = db.current().select(sql);\nreturn result;\n\n// 示例返回结果\n[{\"id\":1,\"name\":\"John\",\"age\":25},{\"id\":2,\"name\":\"Jane\",\"age\":30}]", "内部数据库")]
    public string Select(string sql)
    {
        List<dynamic> list = _db.Ado.SqlQuery<dynamic>(sql).ToList();
        string? resultData = JSON.Serialize(list);
        return resultData;
    }

    /// <summary>
    ///     执行sql语句
    /// </summary>
    /// <param name="sql"></param>
    /// <returns></returns>
    [EngineMethod("db.current().execute('UPDATE users SET age = 26 WHERE id = 1')", "执行SQL", "// 方法:db.current().execute()\n// 参数一: 'UPDATE users SET age = 26 WHERE id = 1' SQL执行语句\n// 返回类型:int\n// 描述:该方法在内部数据库执行SQL语句并返回影响行数\nvar sql = 'UPDATE users SET age = 26 WHERE id = 1';\nvar affectedRows = db.current().execute(sql);\nreturn affectedRows;\n\n// 示例返回结果\n1", "内部数据库")]
    public int Execute(string sql)
    {
        return _db.Ado.ExecuteCommandAsync(sql).GetAwaiter().GetResult();
    }

    /// <summary>
    ///     执行查询sql语句，返回单行数据
    /// </summary>
    /// <param name="sql"></param>
    /// <returns></returns>
    [EngineMethod("db.current().select_single('SELECT * FROM users WHERE id = 1')", "执行查询SQL(单行)", "// 方法:db.current().select_single()\n// 参数一: 'SELECT * FROM users WHERE id = 1' SQL查询语句\n// 返回类型:string\n// 描述:该方法在内部数据库执行SQL查询并返回单行结果\nvar sql = 'SELECT * FROM users WHERE id = 1';\nvar result = db.current().select_single(sql);\nreturn result;\n\n// 示例返回结果\n{\"id\":1,\"name\":\"John\",\"age\":25}", "内部数据库")]
    public string SelectSingleLine(string sql)
    {
        dynamic? list = _db.Ado.SqlQuerySingle<dynamic>(sql);
        dynamic? resultData = JSON.Serialize(list);
        return resultData;
    }

    /// <summary>
    ///     Insert
    /// </summary>
    /// <param name="table"></param>
    /// <param name="dictionary"></param>
    /// <returns></returns>
    [EngineMethod("db.current().insert('users', '{\"name\":\"John\",\"age\":25}')", "插入数据", "// 方法:db.current().insert()\n// 参数一: 'users' 表名\n// 参数二: '{\"name\":\"John\",\"age\":25}' 插入数据(JSON格式)\n// 返回类型:object\n// 描述:该方法向内部数据库表中插入数据\nvar table = 'users';\nvar data = '{\"name\":\"John\",\"age\":25}';\nvar result = db.current().insert(table, data);\nreturn result;\n\n// 示例返回结果\n{\"Sql\":\"INSERT INTO users (`name`, `age`)  Values ('John', 25);\",\"AffectedRows\":1,\"Success\":true,\"ErrorMessage\":\"\"}", "内部数据库")]
    public string Insert(string table, string dictionary)
    {
        var execResult = new DbExecutionResult();
        try
        {
            if (Extensions.IsNullOrEmpty(table))
            {
                throw new Exception("Insert传入参数是空！");
            }

            Dictionary<string, object>? setDict = JSON.Deserialize<Dictionary<string, object>>(dictionary);
            if (!setDict.Any())
            {
                throw new Exception("Insert传入Key-Value解析失败！");
            }

            string insertSql = $"INSERT INTO {table} ";
            string keys = "(";
            string values = "(";
            int count = 1;
            foreach ((string s, object o) in setDict)
            {
                string key = s.StartsWith("@")
                    ? "`" + s.Replace("@", "") + "`"
                    : s;

                string finalValue = GetSqlValue(o);

                if (count == setDict.Count())
                {
                    keys += key + ") ";
                    values += finalValue + ");";
                }
                else
                {
                    keys += key + ",";
                    values += finalValue + ",";
                }

                count++;
            }

            insertSql += keys + " Values " + values;
            execResult.Sql = insertSql;
            int result = _db.Ado.ExecuteCommand(insertSql);
            execResult.AffectedRows = result;
            execResult.Success = result > 0;
        }
        catch (Exception e)
        {
            execResult.Success = false;
            execResult.ErrorMessage = e.Message;
        }
        return JSON.Serialize(execResult);
    }

    /// <summary>
    ///     Update
    /// </summary>
    /// <param name="table">table</param>
    /// <param name="dictionary">set</param>
    /// <param name="whereDictionary">条件</param>
    /// <returns></returns>
    [EngineMethod("db.current().update('users', '{\"age\":26}', '{\"id\":1}')", "更新数据", "// 方法:db.current().update()\n// 参数一: 'users' 表名\n// 参数二: '{\"age\":26}' 更新数据(JSON格式)\n// 参数三: '{\"id\":1}' 条件(JSON格式)\n// 返回类型:object\n// 描述:该方法更新内部数据库表中的数据\nvar table = 'users';\nvar data = '{\"age\":26}';\nvar where = '{\"id\":1}';\nvar result = db.current().update(table, data, where);\nreturn result;\n\n// 示例返回结果\n{\"Sql\":\"UPDATE users SET `age`='26'  Where `id`=1;\",\"AffectedRows\":1,\"Success\":true,\"ErrorMessage\":\"\"}", "内部数据库")]
    public string Update(string table, string dictionary, string whereDictionary)
    {
        var execResult = new DbExecutionResult();
        try
        {
            if (Extensions.IsNullOrEmpty(table) || Extensions.IsNullOrEmpty(dictionary) || Extensions.IsNullOrEmpty(whereDictionary))
            {
                throw new Exception("Update传入参数是空！");
            }

            Dictionary<string, object>? setDict = JSON.Deserialize<Dictionary<string, object>>(dictionary);
            if (!setDict.Any())
            {
                throw new Exception("Update传入Set对象Key-Value解析失败！");
            }

            Dictionary<string, object>? whereDict = JSON.Deserialize<Dictionary<string, object>>(whereDictionary);
            if (!whereDict.Any())
            {
                throw new Exception("Update传入Where对象Key-Value解析失败！");
            }

            string updateSql = $"UPDATE {table} SET ";
            string sets = "";
            string wheres = "";
            int count = 1;
            foreach ((string key, object o) in setDict)
            {
                string setKey = key.StartsWith("@")
                    ? "`" + key.Replace("@", "") + "`"
                    : key;
                string finalValue = GetSqlValue(o);
                if (count == setDict.Count())
                {
                    sets += setKey + "=" + finalValue;
                }
                else
                {
                    sets += setKey + "=" + finalValue + ",";
                }

                count++;
            }

            count = 1;
            foreach ((string key, object o) in whereDict)
            {
                string whereKey = key.StartsWith("@")
                    ? "`" + key.Replace("@", "") + "`"
                    : key;

                string finalValue = GetSqlValue(o);

                if (count == whereDict.Count())
                {
                    wheres += whereKey + "=" + finalValue + ";";
                }
                else
                {
                    wheres += whereKey + "=" + finalValue + " AND ";
                }

                count++;
            }

            updateSql += sets + " Where " + wheres;
            execResult.Sql = updateSql;
            int result = _db.Ado.ExecuteCommand(updateSql);
            execResult.AffectedRows = result;
            execResult.Success = result > 0;
        }
        catch (Exception e)
        {
            execResult.Success = false;
            execResult.ErrorMessage = e.Message;
        }
        return JSON.Serialize(execResult);
    }

    /// <summary>
    ///     Delete
    /// </summary>
    /// <param name="table"></param>
    /// <param name="dictionary"></param>
    /// <returns></returns>
    [EngineMethod("db.current().delete('users', '{\"id\":1}')", "删除数据", "// 方法:db.current().delete()\n// 参数一: 'users' 表名\n// 参数二: '{\"id\":1}' 条件(JSON格式)\n// 返回类型:object\n// 描述:该方法删除内部数据库表中的数据\nvar table = 'users';\nvar where = '{\"id\":1}';\nvar result = db.current().delete(table, where);\nreturn result;\n\n// 示例返回结果\n{\"Sql\":\"DELETE FROM users WHERE `id`=1;\",\"AffectedRows\":1,\"Success\":true,\"ErrorMessage\":\"\"}", "内部数据库")]
    public string Delete(string table, string dictionary)
    {
        var execResult = new DbExecutionResult();
        try
        {
            if (Extensions.IsNullOrEmpty(table) || Extensions.IsNullOrEmpty(dictionary))
            {
                throw new Exception("Delete传入参数是空！");
            }

            Dictionary<string, object>? dict = JSON.Deserialize<Dictionary<string, object>>(dictionary);
            if (!dict.Any())
            {
                throw new Exception("Delete传入Key-Value解析失败！");
            }

            string deleteSql = $"DELETE FROM {table} ";
            string wheres = "";
            int count = 1;
            foreach (KeyValuePair<string, object> item in dict)
            {
                string whereKey = item.Key.StartsWith("@")
                    ? "`" + item.Key.Replace("@", "") + "`"
                    : item.Key;

                string finalValue = GetSqlValue(item.Value);

                if (count == dict.Count())
                {
                    wheres += whereKey + "=" + finalValue + ";";
                }
                else
                {
                    wheres += whereKey + "=" + finalValue + " AND ";
                }

                count++;
            }

            deleteSql += "WHERE " + wheres;
            execResult.Sql = deleteSql;
            int result = _db.Ado.ExecuteCommand(deleteSql);
            execResult.AffectedRows = result;
            execResult.Success = result > 0;
        }
        catch (Exception e)
        {
            execResult.Success = false;
            execResult.ErrorMessage = e.Message;
        }

        return JSON.Serialize(execResult);
    }

    /// <summary>
    /// </summary>
    /// <param name="dbOption"></param>
    /// <returns></returns>
    private IDbConnection Connectoin(ExternalDatabaseEngine.DbOption dbOption)
    {
        IDbConnection db;
        switch (dbOption.DbType)
        {
            case DbType.MySql:
                db = new MySqlConnection(dbOption.ConnectionString);
                //查看当前连接状态
                if (db.State == ConnectionState.Closed)
                {
                    db.Open();
                }

                return db;
            case DbType.SqlServer:
                db = new SqlConnection(dbOption.ConnectionString);
                //查看当前连接状态
                if (db.State == ConnectionState.Closed)
                {
                    db.Open();
                }

                return db;
            default:
                throw new Exception("异常数据库连接类型");
        }
    }

    public void Dispose()
    {
        _db.Dispose();
    }

    private string GetSqlValue(object o)
    {
        if (o == null) return "NULL";

        if (o is JsonElement el)
        {
            switch (el.ValueKind)
            {
                case JsonValueKind.Undefined:
                case JsonValueKind.Null:
                    return "NULL";
                case JsonValueKind.Number:
                    return el.GetRawText();
                case JsonValueKind.String:
                    var s = el.GetString();
                    if (s.Length > 5 && s.Length < 40 && (s.Contains('-') || s.Contains(':')) && DateTime.TryParse(s, out var dt))
                    {
                        return "'" + dt.ToString("yyyy-MM-dd HH:mm:ss") + "'";
                    }
                    return "'" + s.Replace("'", "''") + "'";
                case JsonValueKind.True:
                    return "true";
                case JsonValueKind.False:
                    return "false";
                case JsonValueKind.Object:
                case JsonValueKind.Array:
                    return "'" + el.GetRawText().Replace("'", "''") + "'";
                default:
                    return "'" + el.GetRawText().Replace("'", "''") + "'";
            }
        }

        if (o is int || o is long || o is decimal || o is double || o is float)
        {
            return Convert.ToString(o, System.Globalization.CultureInfo.InvariantCulture);
        }
        if (o is DateTime time)
        {
            return "'" + time.ToString("yyyy-MM-dd HH:mm:ss") + "'";
        }

        var strVal = o.ToString();
        if (strVal.Length > 5 && strVal.Length < 40 && (strVal.Contains('-') || strVal.Contains(':')) && DateTime.TryParse(strVal, out var parsedDt))
        {
            return "'" + parsedDt.ToString("yyyy-MM-dd HH:mm:ss") + "'";
        }

        return "'" + strVal.Replace("'", "''") + "'";
    }
}