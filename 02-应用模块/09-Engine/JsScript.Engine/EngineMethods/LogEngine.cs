using JsScript.Engine.Attributes;

namespace JsScript.Engine.EngineMethods;

/// <summary>
///     日志打印
/// </summary>
[Engine]
public class LogEngine
{
    /// <summary>
    ///     日志打印
    /// </summary>
    public List<object?> Logs { get; set; } = new();

    /// <summary>
    ///     打印日志
    /// </summary>
    /// <returns></returns>
    [EngineMethod("log.Write('message', false)", "打印日志", "// 方法:log.Write()\n// 参数一: 'message' 日志内容\n// 参数二: false 是否保存到系统日志(非必填,默认false)\n// 返回类型:void\n// 描述:该方法用于记录日志信息\nvar data = { name: 'test', value: 123 };\nlog.Write(data);\nreturn '日志已记录';\n\n// 示例返回结果\n日志已记录", "日志操作")]
    public void Write(object? val, bool save = false)
    {
        try
        {
            if (val == null)
            {
                return;
            }

            if (save)
            {
                Log.Information(JSON.Serialize(val));
            }

            Logs.Add(val.GetJsonElementValue());
        }
        catch (Exception ex)
        {
            throw Oops.Oh(ex.Message);
        }
    }

    /// <summary>
    /// 清理所有日志
    /// </summary>
    public void Clear()
    {
        Logs.Clear();
    }
}