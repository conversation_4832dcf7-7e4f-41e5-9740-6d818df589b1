using System.Collections.Concurrent;
using System.Globalization;
using JsScript.Engine.Attributes;
using Timer = System.Threading.Timer;

namespace JsScript.Engine.EngineMethods;

/// <summary>
///     脚本执行-操作变量
/// </summary>
[Engine]
public class Share : ISingleton, IDisposable
{
    /// <summary>
    ///     服务提供器.
    /// </summary>
    private readonly IServiceScope _serviceScope;

    private readonly SysCacheService _cacheService;

    /// <summary>
    ///     存储外部保存的数据
    /// </summary>
    private readonly ConcurrentDictionary<string, object> _shareDictionary = new();

    private static Timer _timer;

    public Share(IServiceScopeFactory scopeFactory, SysCacheService cacheService)
    {
        _cacheService = cacheService;
        _serviceScope = scopeFactory.CreateScope();

        // 设置过期时间为5秒
        int ttl = 5000;
        // 启动定时器
        _timer = new Timer(OnTimedEvent, null, ttl, Timeout.Infinite);
    }

    #region 私有方法

    private void OnTimedEvent(object state)
    {
        // 遍历所有键值对，删除过期的数据
        DateTime now = DateTime.Now;
        foreach (KeyValuePair<string, object> item in _shareDictionary)
        {
            try
            {
                if (now >= ((dynamic)item.Value).Expires)
                {
                    _shareDictionary.TryRemove(item.Key, out _);
                }
            }
            catch (Exception e)
            {
                Log.Error($"删除失败:{e.Message}");
            }
        }

        // 重新启动定时器
        _timer.Change(5000, Timeout.Infinite);
    }

    /// <summary>
    ///     计算距离指定时间还有多长时间
    /// </summary>
    /// <param name="expire"></param>
    /// <returns></returns>
    /// <exception cref="AppFriendlyException"></exception>
    private TimeSpan TimeToExecute(string expire)
    {
        // 获取当前时间和指定执行时间
        string timeString = DateTime.Now.ToString("yyyy-MM-dd") + " " + expire; // 将 expire 转换为日期时间字符串
        if (!DateTime.TryParse(timeString, out DateTime executeTime))
        {
            throw Oops.Oh("错误时间格式");
        }

        DateTime now = DateTime.Now;
        // 如果当前时间已经超过了指定执行时间，则将执行时间加上一天
        if (now > executeTime)
        {
            executeTime = executeTime.AddDays(1);
        }

        // 计算距离指定时间还有多长时间
        TimeSpan timeToExecute = executeTime - now;
        return timeToExecute;
    }

    #endregion

    #region 保存临时变量（持久化）

    /// <summary>
    ///     保存临时变量（持久化）
    /// </summary>
    /// <param name="identifier"></param>
    /// <param name="value"></param>
    /// <returns></returns>
    [EngineMethod("share.Save('variableName', 'value')", "持久化保存变量", "// 方法:share.Save()\n// 参数一: 'variableName' 变量名\n// 参数二: 'value' 变量值\n// 返回类型:bool\n// 描述:该方法持久化保存变量，不设过期时间\nvar name = 'globalTemperature';\nvar value = '25.5';\nvar success = share.Save(name, value);\nreturn success;\n\n// 示例返回结果\ntrue", "数据共享")]
    public bool Save(string identifier, object value)
    {
        return SaveCache(identifier, value, 0);
    }

    /// <summary>
    ///     保存临时变量（持久化）
    /// </summary>
    /// <param name="identifier"></param>
    /// <param name="value"></param>
    /// <param name="expire">到期时间</param>
    /// <returns></returns>
    [EngineMethod("share.Save('variableName', 'value', 300)", "持久化保存变量(过期秒数)", "// 方法:share.Save()\n// 参数一: 'variableName' 变量名\n// 参数二: 'value' 变量值\n// 参数三: 300 过期秒数\n// 返回类型:bool\n// 描述:该方法持久化保存变量，设置过期秒数\nvar name = 'globalTemperature';\nvar value = '25.5';\nvar expireSeconds = 300;\nvar success = share.Save(name, value, expireSeconds);\nreturn success;\n\n// 示例返回结果\ntrue", "数据共享")]
    public bool Save(string identifier, object? value, int expire)
    {
        return SaveCache(identifier, value, Convert.ToDouble(expire));
    }

    /// <summary>
    ///     保存临时变量（持久化）
    /// </summary>
    /// <param name="identifier"></param>
    /// <param name="value"></param>
    /// <param name="expire">到期时间</param>
    /// <returns></returns>
    [EngineMethod("share.Save('variableName', 'value', '18:30:00')", "持久化保存变量(过期时间)", "// 方法:share.Save()\n// 参数一: 'variableName' 变量名\n// 参数二: 'value' 变量值\n// 参数三: '18:30:00' 过期时间\n// 返回类型:bool\n// 描述:该方法持久化保存变量，设置指定过期时间\nvar name = 'globalTemperature';\nvar value = '25.5';\nvar expireTime = '18:30:00';\nvar success = share.Save(name, value, expireTime);\nreturn success;\n\n// 示例返回结果\ntrue", "数据共享")]
    public bool Save(string identifier, object? value, string expire)
    {
        return SaveCache(identifier, value, Convert.ToDouble(expire));
    }

    private bool SaveCache(string identifier, object? value, double expire)
    {
        if (value == null)
        {
            return false;
        }

        if (expire > 0)
        {
            _cacheService.Set(identifier, (DateTime.TryParseExact(value.ToString(), "yyyy-MM-dd HH:mm:ss", CultureInfo.InvariantCulture, DateTimeStyles.None, out DateTime time)
                ? time.ToString("yyyy-MM-dd HH:mm:ss")
                : value.ToString()) ?? string.Empty, TimeSpan.FromSeconds(expire));
        }
        else
        {
            _cacheService.Set(identifier, (DateTime.TryParseExact(value.ToString(), "yyyy-MM-dd HH:mm:ss", CultureInfo.InvariantCulture, DateTimeStyles.None, out DateTime time)
                ? time.ToString("yyyy-MM-dd HH:mm:ss")
                : value.ToString()) ?? string.Empty);
        }

        MessageCenter.PublishAsync(identifier, identifier);
        return true;
    }

    #endregion

    #region 保存临时变量

    /// <summary>
    ///     保存临时变量
    /// </summary>
    /// <param name="identifier"></param>
    /// <param name="value"></param>
    /// <returns></returns>
    [EngineMethod("share.Set('variableName', 'value')", "保存临时变量", "// 方法:share.Set()\n// 参数一: 'variableName' 变量名\n// 参数二: 'value' 变量值\n// 返回类型:bool\n// 描述:该方法保存临时变量，不设过期时间\nvar name = 'sessionTemp';\nvar value = '25.5';\nvar success = share.Set(name, value);\nreturn success;\n\n// 示例返回结果\ntrue", "数据共享")]
    public bool Set(string identifier, object? value)
    {
        if (value == null)
        {
            return false;
        }

        _shareDictionary.AddOrUpdate(identifier, new { Value = value, Expires = DateTime.Now.AddDays(999) },
            (_, _) => new { Value = value, Expires = DateTime.Now.AddDays(999) });
        MessageCenter.PublishAsync(identifier, identifier);
        return true;
    }

    /// <summary>
    ///     保存临时变量
    /// </summary>
    /// <param name="identifier"></param>
    /// <param name="value"></param>
    /// <param name="expire">到期时间</param>
    /// <returns></returns>
    [EngineMethod("share.Set('variableName', 'value', 300)", "保存临时变量(过期秒数)", "// 方法:share.Set()\n// 参数一: 'variableName' 变量名\n// 参数二: 'value' 变量值\n// 参数三: 300 过期秒数\n// 返回类型:bool\n// 描述:该方法保存临时变量，设置过期秒数\nvar name = 'sessionTemp';\nvar value = '25.5';\nvar expireSeconds = 300;\nvar success = share.Set(name, value, expireSeconds);\nreturn success;\n\n// 示例返回结果\ntrue", "数据共享")]
    public bool Set(string identifier, object? value, int expire)
    {
        if (value == null)
        {
            return false;
        }

        // 存储数据
        _shareDictionary.AddOrUpdate(identifier, new { Value = value, Expires = DateTime.Now.AddSeconds(expire) },
            (_, _) => new { Value = value, Expires = DateTime.Now.AddMilliseconds(expire * 1000) });
        MessageCenter.PublishAsync(identifier, identifier);
        return true;
    }

    /// <summary>
    ///     保存临时变量
    /// </summary>
    /// <param name="identifier"></param>
    /// <param name="value"></param>
    /// <param name="expire">到期时间</param>
    /// <returns></returns>
    [EngineMethod("share.Set('variableName', 'value', '18:30:00')", "保存临时变量(过期时间)", "// 方法:share.Set()\n// 参数一: 'variableName' 变量名\n// 参数二: 'value' 变量值\n// 参数三: '18:30:00' 过期时间\n// 返回类型:bool\n// 描述:该方法保存临时变量，设置指定过期时间\nvar name = 'sessionTemp';\nvar value = '25.5';\nvar expireTime = '18:30:00';\nvar success = share.Set(name, value, expireTime);\nreturn success;\n\n// 示例返回结果\ntrue", "数据共享")]
    public bool Set(string identifier, object? value, string expire)
    {
        if (value == null)
        {
            return false;
        }

        TimeSpan timeSp = TimeToExecute(expire);
        // 存储数据
        _shareDictionary.AddOrUpdate(identifier, new { Value = value, Expires = DateTime.Now.AddMilliseconds(timeSp.TotalMilliseconds) },
            (_, _) => new { Value = value, Expires = DateTime.Now.AddMilliseconds(timeSp.TotalMilliseconds) });
        MessageCenter.PublishAsync(identifier, identifier);
        return true;
    }

    #endregion

    /// <summary>
    ///     获取临时变量
    /// </summary>
    /// <param name="identifier"></param>
    /// <returns></returns>
    [EngineMethod("share.Get('variableName')", "获取变量值", "// 方法:share.Get()\n// 参数一: 'variableName' 变量名\n// 返回类型:object\n// 描述:该方法获取已保存的变量值\nvar name = 'globalTemperature';\nvar value = share.Get(name);\nreturn value;\n\n// 示例返回结果\n25.5", "数据共享")]
    public object Get(string identifier)
    {
        if (identifier == null)
        {
            return false;
        }

        object val = _shareDictionary.ContainsKey(identifier)
            ? ((dynamic)_shareDictionary[identifier]).Value
            : _cacheService.Get<string>(identifier);

        return val;
    }

    /// <summary>
    ///     删除临时变量
    /// </summary>
    /// <param name="identifier"></param>
    /// <returns></returns>
    /// <exception cref="AppFriendlyException"></exception>
    [EngineMethod("share.Remove('variableName')", "删除变量", "// 方法:share.Remove()\n// 参数一: 'variableName' 变量名\n// 返回类型:bool\n// 描述:该方法删除已保存的变量\nvar name = 'globalTemperature';\nvar success = share.Remove(name);\nreturn success;\n\n// 示例返回结果\ntrue", "数据共享")]
    public bool Remove(string identifier)
    {
        if (identifier == null)
        {
            return false;
        }

        _shareDictionary.Remove(identifier, out _);
        _cacheService.Remove(identifier);
        return true;
    }

    public void Dispose()
    {
        _serviceScope?.Dispose();
        _timer?.Dispose();
    }
}