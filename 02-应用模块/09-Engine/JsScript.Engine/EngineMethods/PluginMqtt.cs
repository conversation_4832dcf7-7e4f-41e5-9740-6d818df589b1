using JsScript.Engine.Attributes;
using Mqtt.Engine;
using System.Diagnostics;

namespace JsScript.Engine.EngineMethods;

/// <summary>
///     MQTT下发指令
/// </summary>
[Engine]
public class PluginMqtt : ISingleton
{
    private readonly MasterClient _mqttServer;

    /// <summary>
    /// </summary>
    /// <param name="mqttServer"></param>
    public PluginMqtt(MasterClient mqttServer)
    {
        _mqttServer = mqttServer;
    }

    /// <summary>
    ///     下写数据
    /// </summary>
    /// <param name="name">下写设备属性名称</param>
    /// <param name="value">值</param>
    /// <returns></returns>
    /// <exception cref="AppFriendlyException"></exception>
    [Obsolete]
    [EngineMethod("mqtt.Write('gateway/device/attribute', 'value')", "下发单个属性值（已过时）", "// 方法:mqtt.Write()\n// 参数一: 'gateway/device/attribute' 网关/设备/属性标识\n// 参数二: 'value' 属性值\n// 返回类型:bool\n// 描述:该方法用于下发单个设备属性值(已过时)\nvar path = 'gateway1/device1/temperature';\nvar value = '25.5';\nvar result = mqtt.Write(path, value);\nreturn result;\n\n// 示例返回结果\ntrue", "指令下发")]
    public bool Write(string name, string value)
    {
        Stopwatch stopwatch = Stopwatch.StartNew();
        DateTime startTime = DateTime.Now;
        Log.Information($"MQTT下发开始 - 方法:Write, 参数:name='{name}',value='{value}', 开始时间:{startTime:yyyy-MM-dd HH:mm:ss.fff}");

        string[] iden = name.Split("/");
        if (iden.Length < 2)
        {
            stopwatch.Stop();
            DateTime endTime = DateTime.Now;
            Log.Information($"MQTT下发完成 - 方法:Write, 结果:失败(解析错误), 结束时间:{endTime:yyyy-MM-dd HH:mm:ss.fff}, 耗时:{stopwatch.ElapsedMilliseconds}ms");
            throw Oops.Oh($"下写传入变量：{name},解析属性名称[{name}]错误,不符合规范，退出!");
        }

        string topic = $"{iden[0]}/v1/p/write";
        MqttApplicationMessage message = new()
        {
            PayloadSegment = Encoding.UTF8.GetBytes(JSON.Serialize(new { DeviceName = iden[1], Params = new Dictionary<string, string> { { iden[2], value } } })),
            Retain = false,
            Topic = topic,
            QualityOfServiceLevel = MqttQualityOfServiceLevel.AtLeastOnce
        };
        bool output = _mqttServer.PublishAsync(message).GetAwaiter().GetResult();
        stopwatch.Stop();
        DateTime endTime2 = DateTime.Now;
        Log.Information($"MQTT下发完成 - 方法:Write, 结果:{(output ? "成功" : "失败")}, 结束时间:{endTime2:yyyy-MM-dd HH:mm:ss.fff}, 耗时:{stopwatch.ElapsedMilliseconds}ms");
        return output;
    }

    /// <summary>
    ///     下写数据
    /// </summary>
    /// <param name="name">网关转发标识</param>
    /// <param name="value">值</param>
    /// <returns></returns>
    /// <exception cref="AppFriendlyException"></exception>
    [EngineMethod("prop.Write('gateway/device', {param1: 'value1', param2: 'value2'})", "下发属性值", "// 方法:mqtt.Write()\n// 参数一: 'gateway/device' 网关/设备标识\n// 参数二: {param1: 'value1', param2: 'value2'} 属性值对象\n// 返回类型:bool\n// 描述:该方法用于下发设备属性值\nvar path = 'gateway1/device1';\nvar params = {temperature: 25.5, humidity: 60};\nvar result = mqtt.Write(path, params);\nreturn result;\n\n// 示例返回结果\ntrue", "指令下发")]
    public bool Write(string name, object value)
    {
        Stopwatch stopwatch = Stopwatch.StartNew();
        DateTime startTime = DateTime.Now;
        Log.Information($"MQTT下发开始 - 方法:Write, 参数:name='{name}',value='{JSON.Serialize(value)}', 开始时间:{startTime:yyyy-MM-dd HH:mm:ss.fff}");

        if (value == null)
        {
            stopwatch.Stop();
            DateTime endTime = DateTime.Now;
            Log.Information($"MQTT下发完成 - 方法:Write, 结果:失败(value为null), 结束时间:{endTime:yyyy-MM-dd HH:mm:ss.fff}, 耗时:{stopwatch.ElapsedMilliseconds}ms");
            return false;
        }

        string[] iden = name.Split("/");
        if (iden.Length < 2)
        {
            stopwatch.Stop();
            DateTime endTime = DateTime.Now;
            Log.Information($"MQTT下发完成 - 方法:Write, 结果:失败(解析错误), 结束时间:{endTime:yyyy-MM-dd HH:mm:ss.fff}, 耗时:{stopwatch.ElapsedMilliseconds}ms");
            throw Oops.Oh($"下写传入变量：{name},解析属性名称[{name}]错误,不符合规范，退出!");
        }

        string gatewayName = iden[0];
        string deviceName = iden[1];
        IDictionary<string, object?> dicValue = DictionaryExtensions.ToDictionary(value);
        Dictionary<string, string> dicParams = dicValue.ToDictionary(
            kvp => kvp.Key,
            kvp => kvp.Value.ToString()
        );
        string topic = $"{gatewayName}/v1/p/write";
        MqttApplicationMessage message = new()
        {
            PayloadSegment = Encoding.UTF8.GetBytes(JSON.Serialize(new { DeviceName = deviceName, Params = dicParams })),
            Retain = false,
            Topic = topic,
            QualityOfServiceLevel = MqttQualityOfServiceLevel.AtLeastOnce
        };
        bool output = _mqttServer.PublishAsync(message).GetAwaiter().GetResult();

        stopwatch.Stop();
        DateTime endTime2 = DateTime.Now;
        Log.Information($"MQTT下发完成 - 方法:Write, 结果:{(output ? "成功" : "失败")}, 结束时间:{endTime2:yyyy-MM-dd HH:mm:ss.fff}, 耗时:{stopwatch.ElapsedMilliseconds}ms");
        return output;
    }

    /// <summary>
    ///     下写数据
    /// </summary>
    /// <param name="gatewayName">网关转发标识</param>
    /// <param name="deviceName">设备名称</param>
    /// <param name="value">值</param>
    /// <returns></returns>
    /// <exception cref="AppFriendlyException"></exception>
    [EngineMethod("prop.Write('gateway', 'device', {param1: 'value1', param2: 'value2'})", "下发属性值(指定网关和设备)", "// 方法:mqtt.Write()\n// 参数一: 'gateway' 网关标识\n// 参数二: 'device' 设备标识\n// 参数三: {param1: 'value1', param2: 'value2'} 属性值对象\n// 返回类型:bool\n// 描述:该方法用于下发设备属性值\nvar gateway = 'gateway1';\nvar device = 'device1';\nvar params = {temperature: 25.5, humidity: 60};\nvar result = mqtt.Write(gateway, device, params);\nreturn result;\n\n// 示例返回结果\ntrue", "指令下发")]
    public bool Write(string gatewayName, string deviceName, object value)
    {
        Stopwatch stopwatch = Stopwatch.StartNew();
        DateTime startTime = DateTime.Now;
        Log.Information($"MQTT下发开始 - 方法:Write(重载), 参数:gatewayName='{gatewayName}',deviceName='{deviceName}',value='{JSON.Serialize(value)}', 开始时间:{startTime:yyyy-MM-dd HH:mm:ss.fff}");

        if (value == null)
        {
            stopwatch.Stop();
            DateTime endTime = DateTime.Now;
            Log.Information($"MQTT下发完成 - 方法:Write(重载), 结果:失败(value为null), 结束时间:{endTime:yyyy-MM-dd HH:mm:ss.fff}, 耗时:{stopwatch.ElapsedMilliseconds}ms");
            return false;
        }

        IDictionary<string, object?> dicValue = DictionaryExtensions.ToDictionary(value);
        Dictionary<string, string> dicParams = dicValue.ToDictionary(
            kvp => kvp.Key,
            kvp => kvp.Value.ToString()
        );
        string topic = $"{gatewayName}/v1/p/write";
        MqttApplicationMessage message = new()
        {
            PayloadSegment = Encoding.UTF8.GetBytes(JSON.Serialize(new { DeviceName = deviceName, Params = dicParams })),
            Retain = false,
            Topic = topic,
            QualityOfServiceLevel = MqttQualityOfServiceLevel.AtLeastOnce
        };
        bool output = _mqttServer.PublishAsync(message).GetAwaiter().GetResult();

        stopwatch.Stop();
        DateTime endTime2 = DateTime.Now;
        Log.Information($"MQTT下发完成 - 方法:Write(重载), 结果:{(output ? "成功" : "失败")}, 结束时间:{endTime2:yyyy-MM-dd HH:mm:ss.fff}, 耗时:{stopwatch.ElapsedMilliseconds}ms");
        return output;
    }

    /// <summary>
    ///     下写共享变量数据
    /// </summary>
    /// <param name="gateway">网关名称</param>
    /// <param name="data">下写设备属性名称</param>
    /// <param name="value">值</param>
    /// <returns></returns>
    /// <exception cref="AppFriendlyException"></exception>
    [EngineMethod("prop.Share('gateway', 'variable', 'value')", "下发共享变量", "// 方法:mqtt.Share()\n// 参数一: 'gateway' 网关标识\n// 参数二: 'variable' 共享变量名称\n// 参数三: 'value' 变量值\n// 返回类型:bool\n// 描述:该方法用于下发共享变量数据\nvar gateway = 'gateway1';\nvar variable = 'globalTemp';\nvar value = '25.5';\nvar result = mqtt.Share(gateway, variable, value);\nreturn result;\n\n// 示例返回结果\ntrue", "指令下发")]
    public bool Share(string gateway, string data, object value)
    {
        Stopwatch stopwatch = Stopwatch.StartNew();
        DateTime startTime = DateTime.Now;
        Log.Information($"MQTT下发开始 - 方法:Share, 参数:gateway='{gateway}',data='{data}',value='{value}', 开始时间:{startTime:yyyy-MM-dd HH:mm:ss.fff}");

        string topic = $"{gateway}/v1/p/share";
        MqttApplicationMessage message = new()
        {
            PayloadSegment = Encoding.UTF8.GetBytes(JSON.Serialize(new { DeviceName = "", Params = new Dictionary<string, string> { { data, value.ToString() ?? "" } } })),
            Retain = false,
            Topic = topic,
            QualityOfServiceLevel = MqttQualityOfServiceLevel.AtLeastOnce
        };
        bool output = _mqttServer.PublishAsync(message).GetAwaiter().GetResult();

        stopwatch.Stop();
        DateTime endTime = DateTime.Now;
        Log.Information($"MQTT下发完成 - 方法:Share, 结果:{(output ? "成功" : "失败")}, 结束时间:{endTime:yyyy-MM-dd HH:mm:ss.fff}, 耗时:{stopwatch.ElapsedMilliseconds}ms");
        return output;
    }

    /// <summary>
    ///     指令下发
    /// </summary>
    /// <param name="gateway">网关名称</param>
    /// <param name="cmd">指令</param>
    /// <returns></returns>
    /// <exception cref="AppFriendlyException"></exception>
    [EngineMethod("prop.Cmd('gateway', '{\"command\":\"restart\"}')", "下发指令", "// 方法:mqtt.Cmd()\n// 参数一: 'gateway' 网关标识\n// 参数二: '{\"command\":\"restart\"}' 指令内容\n// 返回类型:bool\n// 描述:该方法用于下发原始指令\nvar gateway = 'gateway1';\nvar command = '{\"command\":\"restart\"}';\nvar result = mqtt.Cmd(gateway, command);\nreturn result;\n\n// 示例返回结果\ntrue", "指令下发")]
    public bool Cmd(string gateway, object cmd)
    {
        Stopwatch stopwatch = Stopwatch.StartNew();
        DateTime startTime = DateTime.Now;
        Log.Information($"MQTT下发开始 - 方法:Cmd, 参数:gateway='{gateway}',cmd='{cmd}', 开始时间:{startTime:yyyy-MM-dd HH:mm:ss.fff}");

        string topic = $"{gateway}/v1/p/cmd";
        MqttApplicationMessage message = new()
        {
            PayloadSegment = Encoding.UTF8.GetBytes(cmd.ToString() ?? ""),
            Retain = false,
            Topic = topic,
            QualityOfServiceLevel = MqttQualityOfServiceLevel.AtLeastOnce
        };
        bool output = _mqttServer.PublishAsync(message).GetAwaiter().GetResult();

        stopwatch.Stop();
        DateTime endTime = DateTime.Now;
        Log.Information($"MQTT下发完成 - 方法:Cmd, 结果:{(output ? "成功" : "失败")}, 结束时间:{endTime:yyyy-MM-dd HH:mm:ss.fff}, 耗时:{stopwatch.ElapsedMilliseconds}ms");
        return output;
    }
}