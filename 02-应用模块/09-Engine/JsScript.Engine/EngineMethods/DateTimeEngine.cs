using JsScript.Engine.Attributes;

namespace JsScript.Engine.EngineMethods;

/// <summary>
///     时间类扩展
/// </summary>
[Engine]
public class DateTimeEngine : ISingleton
{
    /// <summary>
    ///     当前时间
    /// </summary>
    /// <returns></returns>
    [EngineMethod("dateTime.Now('utc')", "获取当前时间", "// 方法:dateTime.Format()\n// 参数一: 'time'  时间支持DateTime和字符串格式\n//参数二: 'yyyy-MM-dd HH:mm:ss' 非必填,默认转换格式,支持自定义如:'yyyy-MM-dd' or 'yyyy-MM-dd HH:mm' \n// 返回类型:string\n// 描述:该方法格式化时间,返回字符串\nvar time = '2023-04-04 08:31:03';\nvar status = dateTime.Format(time,'yyyy-MM-dd HH:mm:ss');\nreturn status;\n\n// 示例返回结果\n2023-04-04 08:31", "时间操作")]
    public string Now(string convert = "utc")
    {
        try
        {
            if (convert != "cst")
            {
                return DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
            }

            // 设置时区为 Asia/Shanghai
            TimeZoneInfo timeZone = RuntimeInformation.IsOSPlatform(OSPlatform.Windows)
                ? TimeZoneInfo.FindSystemTimeZoneById("China Standard Time")
                : TimeZoneInfo.FindSystemTimeZoneById("Asia/Shanghai");
            // 获取当前时间的 UTC 时间
            DateTime utcTime = DateTime.UtcNow;
            // 将 UTC 时间转换为指定时区的本地时间
            return TimeZoneInfo.ConvertTimeFromUtc(utcTime, timeZone).ToString("yyyy-MM-dd HH:mm:ss");
        }
        catch (Exception e)
        {
            return DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
        }
    }

    /// <summary>
    ///     北京时间
    /// </summary>
    /// <returns></returns>
    public string ShangHai()
    {
        try
        {
            // 设置时区为 Asia/Shanghai
            TimeZoneInfo timeZone = RuntimeInformation.IsOSPlatform(OSPlatform.Windows)
                ? TimeZoneInfo.FindSystemTimeZoneById("China Standard Time")
                : TimeZoneInfo.FindSystemTimeZoneById("Asia/Shanghai");
            // 获取当前时间的 UTC 时间
            DateTime utcTime = DateTime.UtcNow;
            // 将 UTC 时间转换为指定时区的本地时间
            return TimeZoneInfo.ConvertTimeFromUtc(utcTime, timeZone).ToString("yyyy-MM-dd HH:mm:ss");
        }
        catch
        {
            return DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
        }
    }

    /// <summary>
    ///     格式化时间
    /// </summary>
    /// <returns></returns>
    [EngineMethod("dateTime.Format(DateTime, 'yyyy-MM-dd HH:mm:ss')", "格式化时间", "// 方法:dateTime.Format()\n// 参数一: 时间\n//参数二: 'yyyy-MM-dd HH:mm:ss' 非必填,默认转换格式,支持自定义如:'yyyy-MM-dd' or 'yyyy-MM-dd HH:mm' \n// 返回类型:string\n// 描述:该方法格式化DateTime时间,返回字符串\nvar now = new Date();\nvar formattedTime = dateTime.Format(now,'yyyy-MM-dd');\nreturn formattedTime;\n\n// 示例返回结果\n2023-04-04", "时间操作")]
    public string Format(DateTime time, string str = "yyyy-MM-dd HH:mm:ss")
    {
        return time.ToString(str);
    }

    /// <summary>
    ///     格式化时间
    /// </summary>
    /// <returns></returns>
    public string Format(string time, string str = "yyyy-MM-dd HH:mm:ss")
    {
        return Convert.ToDateTime(time).ToString(str);
    }

    /// <summary>
    ///     时间差
    /// </summary>
    /// <returns></returns>
    [EngineMethod("dateTime.DiffDate(startDateTime, endDateTime, 0)", "计算时间差", "// 方法:dateTime.DiffDate()\n// 参数一: startDateTime 开始时间(DateTime类型)\n// 参数二: endDateTime 结束时间(DateTime类型)\n// 参数三: 0 偏移小时数(整数, 非必填)\n// 返回类型:double\n// 描述:该方法计算两个DateTime时间的差值,返回毫秒数\nvar start = new Date('2023-04-04 08:00:00');\nvar end = new Date('2023-04-04 09:30:00');\nvar diff = dateTime.DiffDate(start, end);\nreturn diff;\n\n// 示例返回结果\n5400000", "时间操作")]
    public double DiffDate(DateTime startTime, DateTime endTime, int hour = 0)
    {
        if (hour > 0)
        {
            endTime = endTime.AddHours(hour);
        }

        double time = (endTime - startTime).TotalMilliseconds;
        return time;
    }

    /// <summary>
    ///     时间差,偏移量
    /// </summary>
    /// <returns></returns>
    public double DiffDate(string startTimeStr, string endTimeStr, int hour = 0)
    {
        DateTime starTime = Convert.ToDateTime(startTimeStr);
        DateTime endTime = Convert.ToDateTime(endTimeStr);
        if (hour > 0)
        {
            endTime = endTime.AddHours(hour);
        }

        return (endTime - starTime).TotalMilliseconds;
    }

    /// <summary>
    ///     增减时间
    /// </summary>
    /// <returns></returns>
    [EngineMethod("dateTime.AddDate(nowDateTime, 'day', 1)", "增减时间", "// 方法:dateTime.AddDate()\n// 参数一: nowDateTime 当前时间(DateTime类型)\n// 参数二: 'day' 时间类型 ('year','month','day','hour','min','sec')\n// 参数三: 1 增减数值(整数,可为负数)\n// 返回类型:string\n// 描述:该方法对DateTime时间进行增减操作,返回格式化后的时间字符串\nvar now = new Date();\nvar tomorrow = dateTime.AddDate(now, 'day', 1);\nreturn tomorrow;\n\n// 示例返回结果\n2023-04-05 08:31:03", "时间操作")]
    public string AddDate(DateTime nowTime, string type, int value)
    {
        return type switch
        {
            "year" => nowTime.AddYears(value).ToString("yyyy-MM-dd HH:mm:ss"),
            "month" => nowTime.AddMonths(value).ToString("yyyy-MM-dd HH:mm:ss"),
            "day" => nowTime.AddDays(value).ToString("yyyy-MM-dd HH:mm:ss"),
            "hour" => nowTime.AddHours(value).ToString("yyyy-MM-dd HH:mm:ss"),
            "min" => nowTime.AddMinutes(value).ToString("yyyy-MM-dd HH:mm:ss"),
            "sec" => nowTime.AddSeconds(value).ToString("yyyy-MM-dd HH:mm:ss"),
            _ => throw new Exception("加减类型错误！")
        };
    }

    /// <summary>
    ///     增减时间
    /// </summary>
    /// <returns></returns>
    public string AddDate(string time, string type, int value)
    {
        DateTime nowTime = Convert.ToDateTime(time);
        return type switch
        {
            "year" => nowTime.AddYears(value).ToString("yyyy-MM-dd HH:mm:ss"),
            "month" => nowTime.AddMonths(value).ToString("yyyy-MM-dd HH:mm:ss"),
            "day" => nowTime.AddDays(value).ToString("yyyy-MM-dd HH:mm:ss"),
            "hour" => nowTime.AddHours(value).ToString("yyyy-MM-dd HH:mm:ss"),
            "min" => nowTime.AddMinutes(value).ToString("yyyy-MM-dd HH:mm:ss"),
            "sec" => nowTime.AddSeconds(value).ToString("yyyy-MM-dd HH:mm:ss"),
            _ => throw new Exception("加减类型错误！")
        };
    }

    /// <summary>
    ///     秒级时间戳
    /// </summary>
    /// <returns>Unix时间戳格式</returns>
    [EngineMethod("dateTime.ToSeconds()", "获取秒级时间戳", "// 方法:dateTime.ToSeconds()\n// 参数: 无\n// 返回类型:long\n// 描述:该方法获取当前时间的秒级时间戳\nvar timestamp = dateTime.ToSeconds();\nreturn timestamp;\n\n// 示例返回结果\n1680595863", "时间操作")]
    public static long ToSeconds()
    {
        TimeSpan delta = DateTime.Now - new DateTime(1970, 1, 1);
        return Convert.ToInt64(delta.TotalSeconds);
    }

    /// <summary>
    ///     秒级时间戳
    /// </summary>
    /// <param name="time"> DateTime时间格式</param>
    /// <returns>Unix时间戳格式</returns>
    public static long ToSeconds(DateTime time)
    {
        TimeSpan delta = time - new DateTime(1970, 1, 1);
        return Convert.ToInt64(delta.TotalSeconds);
    }

    /// <summary>
    ///     秒级时间戳
    /// </summary>
    /// <param name="time"> 时间</param>
    /// <returns>Unix时间戳格式</returns>
    public static long ToSeconds(string time)
    {
        TimeSpan delta = Convert.ToDateTime(time) - new DateTime(1970, 1, 1);
        return Convert.ToInt64(delta.TotalSeconds);
    }

    /// <summary>
    ///     毫秒级时间戳
    /// </summary>
    /// <param name="time"></param>
    /// <returns></returns>
    [EngineMethod("dateTime.ToMilliseconds('2023-04-04 08:31:03')", "转毫秒级时间戳", "// 方法:dateTime.ToMilliseconds()\n// 参数: '2023-04-04 08:31:03' 字符串格式时间\n// 返回类型:string\n// 描述:该方法将字符串时间转换为毫秒级时间戳字符串\nvar timeStr = '2023-04-04 08:31:03';\nvar timestamp = dateTime.ToMilliseconds(timeStr);\nreturn timestamp;\n\n// 示例返回结果\n1680595863000", "时间操作")]
    public static string ToMilliseconds(string time)
    {
        TimeSpan delta = Convert.ToDateTime(time) - new DateTime(1970, 1, 1);
        return Convert.ToInt64(delta.TotalMilliseconds).ToString();
    }

    /// <summary>
    ///     毫秒级时间戳返回int64类型
    /// </summary>
    /// <returns>Unix时间戳格式</returns>
    public static long ToMilliseconds(DateTime time)
    {
        TimeSpan delta = time - new DateTime(1970, 1, 1);
        return Convert.ToInt64(delta.TotalMilliseconds);
    }

    /// <summary>
    ///     毫秒级时间戳返回int64类型
    /// </summary>
    /// <returns>Unix时间戳格式</returns>
    [EngineMethod("dateTime.ToMilliseconds()", "获取毫秒级时间戳", "// 方法:dateTime.ToMilliseconds()\n// 参数: 无\n// 返回类型:long\n// 描述:该方法获取当前时间的毫秒级时间戳\nvar timestamp = dateTime.ToMilliseconds();\nreturn timestamp;\n\n// 示例返回结果\n1680595863000", "时间操作")]
    public static long ToMilliseconds()
    {
        TimeSpan delta = DateTime.Now - new DateTime(1970, 1, 1);
        return Convert.ToInt64(delta.TotalMilliseconds);
    }

    /// <summary>
    ///     毫秒时间戳转为北京时间
    /// </summary>
    /// <returns>C#格式时间</returns>
    [EngineMethod("dateTime.ToLocalTime(1680595863000)", "毫秒时间戳转北京时间", "// 方法:dateTime.ToLocalTime()\n// 参数: 1680595863000 毫秒级时间戳(long类型)\n// 返回类型:string\n// 描述:该方法将毫秒级时间戳转换为北京时间\nvar timestamp = 1680595863000;\nvar localTime = dateTime.ToLocalTime(timestamp);\nreturn localTime;\n\n// 示例返回结果\n2023-04-04 16:31:03", "时间操作")]
    public static string ToLocalTime(long unixTimeStamp)
    {
        try
        {
            // 将毫秒时间戳转换为时间
            DateTimeOffset dateTimeOffset = DateTimeOffset.FromUnixTimeMilliseconds(unixTimeStamp);
            // 将 DateTimeOffset 对象转换为本地时间
            DateTimeOffset beijingOffset = RuntimeInformation.IsOSPlatform(OSPlatform.Windows)
                ? TimeZoneInfo.ConvertTime(dateTimeOffset, TimeZoneInfo.FindSystemTimeZoneById("China Standard Time"))
                : TimeZoneInfo.ConvertTime(dateTimeOffset, TimeZoneInfo.FindSystemTimeZoneById("Asia/Shanghai"));
            DateTimeOffset localTime = beijingOffset.ToOffset(TimeSpan.FromHours(8));
            return localTime.ToString("yyyy-MM-dd HH:mm:ss");
        }
        catch
        {
            DateTime dtDateTime = new(1970, 1, 1);
            dtDateTime = dtDateTime.AddMilliseconds(Convert.ToInt64(unixTimeStamp)).ToLocalTime();
            return dtDateTime.ToString("yyyy-MM-dd HH:mm:ss");
        }
    }

    /// <summary>
    ///     毫秒时间戳转为北京时间
    /// </summary>
    /// <returns>C#格式时间</returns>
    public static string ToLocalTime(string unixTimeStamp)
    {
        try
        {
            // 将毫秒时间戳转换为时间
            DateTimeOffset dateTimeOffset = DateTimeOffset.FromUnixTimeMilliseconds(Convert.ToInt64(unixTimeStamp));
            // 将 DateTimeOffset 对象转换为本地时间
            DateTimeOffset beijingOffset = RuntimeInformation.IsOSPlatform(OSPlatform.Windows)
                ? TimeZoneInfo.ConvertTime(dateTimeOffset, TimeZoneInfo.FindSystemTimeZoneById("China Standard Time"))
                : TimeZoneInfo.ConvertTime(dateTimeOffset, TimeZoneInfo.FindSystemTimeZoneById("Asia/Shanghai"));
            DateTimeOffset localTime = beijingOffset.ToOffset(TimeSpan.FromHours(8));
            return localTime.ToString("yyyy-MM-dd HH:mm:ss");
        }
        catch
        {
            DateTime dtDateTime = new(1970, 1, 1);
            dtDateTime = dtDateTime.AddMilliseconds(Convert.ToInt64(unixTimeStamp)).ToLocalTime();
            return dtDateTime.ToString("yyyy-MM-dd HH:mm:ss");
        }
    }
}