using System;
using System.IO;
using System.Text;
using System.Threading.Tasks;
using System.Collections.Generic;
using System.Linq;
using JsScript.Engine.Attributes;

namespace IotGateway.Application.Exec;

/// <summary>
/// 文件操作扩展类，提供基本的文件读写、删除、复制等功能
/// </summary>
[Engine]
public class FileOperationEx : IDisposable
{
    private bool _disposed = false;

    /// <summary>
    /// 根据字符串获取对应的编码对象
    /// </summary>
    /// <param name="encodingStr">编码字符串，如"utf-8"、"gbk"等</param>
    /// <returns>编码对象</returns>
    private Encoding GetEncoding(string? encodingStr)
    {
        if (string.IsNullOrEmpty(encodingStr))
            return Encoding.UTF8;

        try
        {
            return encodingStr.ToLower() switch
            {
                "utf8" or "utf-8" => Encoding.UTF8,
                "ascii" => Encoding.ASCII,
                "unicode" => Encoding.Unicode,
                "utf32" or "utf-32" => Encoding.UTF32,
                "gbk" or "gb2312" => Encoding.GetEncoding("GBK"),
                _ => Encoding.GetEncoding(encodingStr)
            };
        }
        catch
        {
            return Encoding.UTF8;
        }
    }

    /// <summary>
    /// 读取文本文件内容
    /// </summary>
    /// <param name="filePath">文件路径</param>
    /// <param name="encodingStr">编码方式字符串，如"utf-8"、"gbk"等，默认为"utf-8"</param>
    /// <returns>文件内容</returns>
    [EngineMethod("file.ReadText('/path/to/file.txt', 'utf-8')", "读取文本文件", "// 方法:file.ReadText()\n// 参数一: '/path/to/file.txt' 文件路径\n// 参数二: 'utf-8' 文件编码(非必填,默认utf-8)\n// 返回类型:string\n// 描述:该方法读取文本文件内容\nvar path = '/data/config.json';\nvar content = file.ReadText(path);\nreturn content;\n\n// 示例返回结果\n{\"name\":\"config\",\"version\":\"1.0\"}", "文件操作")]
    public string ReadText(string filePath, string? encodingStr = null)
    {
        try
        {
            if (!File.Exists(filePath))
                return string.Empty;

            Encoding encoding = GetEncoding(encodingStr);
            return File.ReadAllText(filePath, encoding);
        }
        catch (Exception ex)
        {
            Log.Error($"【文件读取】 Error:{ex.Message}");
            return string.Empty;
        }
    }

    /// <summary>
    /// 写入文本到文件
    /// </summary>
    /// <param name="filePath">文件路径</param>
    /// <param name="content">要写入的内容</param>
    /// <param name="encodingStr">编码方式字符串，如"utf-8"、"gbk"等，默认为"utf-8"</param>
    /// <returns>是否写入成功</returns>
    [EngineMethod("file.WriteText('/path/to/file.txt', 'content', 'utf-8')", "写入文本文件", "// 方法:file.WriteText()\n// 参数一: '/path/to/file.txt' 文件路径\n// 参数二: 'content' 文件内容\n// 参数三: 'utf-8' 文件编码(非必填,默认utf-8)\n// 返回类型:bool\n// 描述:该方法写入文本到文件\nvar path = '/data/config.json';\nvar content = '{\"name\":\"config\",\"version\":\"1.0\"}';\nvar success = file.WriteText(path, content);\nreturn success;\n\n// 示例返回结果\ntrue", "文件操作")]
    public bool WriteText(string filePath, string content, string? encodingStr = null)
    {
        try
        {
            Encoding encoding = GetEncoding(encodingStr);

            // 确保目录存在
            string? directory = Path.GetDirectoryName(filePath);
            if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory);
            }

            File.WriteAllText(filePath, content, encoding);
            return true;
        }
        catch (Exception ex)
        {
            Log.Error($"【文件写入】 Error:{ex.Message}");
            return false;
        }
    }

    /// <summary>
    /// 追加文本到文件
    /// </summary>
    /// <param name="filePath">文件路径</param>
    /// <param name="content">要追加的内容</param>
    /// <param name="encodingStr">编码方式字符串，如"utf-8"、"gbk"等，默认为"utf-8"</param>
    /// <returns>是否追加成功</returns>
    [EngineMethod("file.AppendText('/path/to/file.txt', 'content', 'utf-8')", "追加文本到文件", "// 方法:file.AppendText()\n// 参数一: '/path/to/file.txt' 文件路径\n// 参数二: 'content' 追加内容\n// 参数三: 'utf-8' 文件编码(非必填,默认utf-8)\n// 返回类型:bool\n// 描述:该方法追加文本到文件末尾\nvar path = '/data/log.txt';\nvar content = 'New log entry';\nvar success = file.AppendText(path, content);\nreturn success;\n\n// 示例返回结果\ntrue", "文件操作")]
    public bool AppendText(string filePath, string content, string? encodingStr = null)
    {
        try
        {
            Encoding encoding = GetEncoding(encodingStr);

            // 确保目录存在
            string? directory = Path.GetDirectoryName(filePath);
            if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory);
            }

            File.AppendAllText(filePath, content, encoding);
            return true;
        }
        catch (Exception ex)
        {
            Log.Error($"【文件追加】 Error:{ex.Message}");
            return false;
        }
    }

    /// <summary>
    /// 复制文件
    /// </summary>
    /// <param name="sourceFilePath">源文件路径</param>
    /// <param name="destFilePath">目标文件路径</param>
    /// <param name="overwrite">是否覆盖已存在的文件</param>
    /// <returns>是否复制成功</returns>
    [EngineMethod("file.Copy('/path/to/source.txt', '/path/to/dest.txt', true)", "复制文件", "// 方法:file.Copy()\n// 参数一: '/path/to/source.txt' 源文件路径\n// 参数二: '/path/to/dest.txt' 目标文件路径\n// 参数三: true 是否覆盖已存在文件(非必填,默认true)\n// 返回类型:bool\n// 描述:该方法复制文件\nvar source = '/data/config.old.json';\nvar dest = '/data/config.new.json';\nvar success = file.Copy(source, dest);\nreturn success;\n\n// 示例返回结果\ntrue", "文件操作")]
    public bool Copy(string sourceFilePath, string destFilePath, bool overwrite = true)
    {
        try
        {
            if (!File.Exists(sourceFilePath))
                return false;

            // 确保目标目录存在
            string? directory = Path.GetDirectoryName(destFilePath);
            if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory);
            }

            File.Copy(sourceFilePath, destFilePath, overwrite);
            return true;
        }
        catch (Exception ex)
        {
            Log.Error($"【文件复制】 Error:{ex.Message}");
            return false;
        }
    }

    /// <summary>
    /// 移动文件
    /// </summary>
    /// <param name="sourceFilePath">源文件路径</param>
    /// <param name="destFilePath">目标文件路径</param>
    /// <param name="overwrite">是否覆盖已存在的文件</param>
    /// <returns>是否移动成功</returns>
    [EngineMethod("file.Move('/path/to/source.txt', '/path/to/dest.txt', true)", "移动文件", "// 方法:file.Move()\n// 参数一: '/path/to/source.txt' 源文件路径\n// 参数二: '/path/to/dest.txt' 目标文件路径\n// 参数三: true 是否覆盖已存在文件(非必填,默认true)\n// 返回类型:bool\n// 描述:该方法移动文件\nvar source = '/temp/file.txt';\nvar dest = '/data/file.txt';\nvar success = file.Move(source, dest);\nreturn success;\n\n// 示例返回结果\ntrue", "文件操作")]
    public bool Move(string sourceFilePath, string destFilePath, bool overwrite = true)
    {
        try
        {
            if (!File.Exists(sourceFilePath))
                return false;

            // 确保目标目录存在
            string? directory = Path.GetDirectoryName(destFilePath);
            if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory);
            }

            // 如果目标文件已存在且允许覆盖，则先删除目标文件
            if (File.Exists(destFilePath) && overwrite)
            {
                File.Delete(destFilePath);
            }

            File.Move(sourceFilePath, destFilePath);
            return true;
        }
        catch (Exception ex)
        {
            Log.Error($"【文件移动】 Error:{ex.Message}");
            return false;
        }
    }

    /// <summary>
    /// 删除文件
    /// </summary>
    /// <param name="filePath">文件路径</param>
    /// <returns>是否删除成功</returns>
    [EngineMethod("file.Delete('/path/to/file.txt')", "删除文件", "// 方法:file.Delete()\n// 参数一: '/path/to/file.txt' 文件路径\n// 返回类型:bool\n// 描述:该方法删除文件\nvar path = '/temp/temp.txt';\nvar success = file.Delete(path);\nreturn success;\n\n// 示例返回结果\ntrue", "文件操作")]
    public bool Delete(string filePath)
    {
        try
        {
            if (!File.Exists(filePath))
                return true;

            File.Delete(filePath);
            return true;
        }
        catch (Exception ex)
        {
            Log.Error($"【文件删除】 Error:{ex.Message}");
            return false;
        }
    }

    /// <summary>
    /// 检查文件是否存在
    /// </summary>
    /// <param name="filePath">文件路径</param>
    /// <returns>文件是否存在</returns>
    [EngineMethod("file.Exists('/path/to/file.txt')", "检查文件是否存在", "// 方法:file.Exists()\n// 参数一: '/path/to/file.txt' 文件路径\n// 返回类型:bool\n// 描述:该方法检查文件是否存在\nvar path = '/data/config.json';\nvar exists = file.Exists(path);\nreturn exists;\n\n// 示例返回结果\ntrue", "文件操作")]
    public bool Exists(string filePath)
    {
        return File.Exists(filePath);
    }

    /// <summary>
    /// 获取文件信息
    /// </summary>
    /// <param name="filePath">文件路径</param>
    /// <returns>文件信息对象</returns>
    public FileInfo? GetInfo(string filePath)
    {
        try
        {
            if (!File.Exists(filePath))
                return null;

            return new FileInfo(filePath);
        }
        catch (Exception ex)
        {
            Log.Error($"【获取文件信息】 Error:{ex.Message}");
            return null;
        }
    }

    /// <summary>
    /// 获取目录下所有文件列表
    /// </summary>
    /// <param name="directoryPath">目录路径</param>
    /// <param name="searchPattern">搜索模式，例如"*.txt"，默认为所有文件"*.*"</param>
    /// <param name="recursive">是否递归查找子目录</param>
    /// <returns>文件路径列表</returns>
    public List<string> GetFiles(string directoryPath, string searchPattern = "*.*", bool recursive = false)
    {
        try
        {
            if (!Directory.Exists(directoryPath))
                return new List<string>();

            SearchOption option = recursive ? SearchOption.AllDirectories : SearchOption.TopDirectoryOnly;
            return Directory.GetFiles(directoryPath, searchPattern, option).ToList();
        }
        catch (Exception ex)
        {
            Log.Error($"【获取文件列表】 Error:{ex.Message}");
            return new List<string>();
        }
    }

    /// <summary>
    /// 获取目录下文件信息列表
    /// </summary>
    /// <param name="directoryPath">目录路径</param>
    /// <param name="searchPattern">搜索模式，例如"*.txt"，默认为所有文件"*.*"</param>
    /// <param name="recursive">是否递归查找子目录</param>
    /// <returns>文件信息列表</returns>
    public List<FileInfo> GetFileInfos(string directoryPath, string searchPattern = "*.*", bool recursive = false)
    {
        try
        {
            if (!Directory.Exists(directoryPath))
                return new List<FileInfo>();

            SearchOption option = recursive ? SearchOption.AllDirectories : SearchOption.TopDirectoryOnly;
            DirectoryInfo dirInfo = new DirectoryInfo(directoryPath);
            return dirInfo.GetFiles(searchPattern, option).ToList();
        }
        catch (Exception ex)
        {
            Log.Error($"【获取文件信息列表】 Error:{ex.Message}");
            return new List<FileInfo>();
        }
    }

    /// <summary>
    /// 读取文件指定行
    /// </summary>
    /// <param name="filePath">文件路径</param>
    /// <param name="lineNumber">行号（从0开始）</param>
    /// <param name="encodingStr">编码方式字符串，如"utf-8"、"gbk"等，默认为"utf-8"</param>
    /// <returns>指定行的内容，如果行号不存在则返回空字符串</returns>
    public string ReadLine(string filePath, int lineNumber, string? encodingStr = null)
    {
        try
        {
            if (!File.Exists(filePath) || lineNumber < 0)
                return string.Empty;

            Encoding encoding = GetEncoding(encodingStr);

            using (StreamReader reader = new StreamReader(filePath, encoding))
            {
                int currentLine = 0;
                while (!reader.EndOfStream)
                {
                    string line = reader.ReadLine() ?? string.Empty;
                    if (currentLine == lineNumber)
                        return line;

                    currentLine++;
                }
            }

            return string.Empty; // 如果行号超出范围，返回空字符串
        }
        catch (Exception ex)
        {
            Log.Error($"【读取文件指定行】 Error:{ex.Message}");
            return string.Empty;
        }
    }

    /// <summary>
    /// 读取文件的多行
    /// </summary>
    /// <param name="filePath">文件路径</param>
    /// <param name="startLine">起始行号（从0开始）</param>
    /// <param name="endLine">结束行号（包含，从0开始，默认-1表示读取到文件末尾）</param>
    /// <param name="encodingStr">编码方式字符串，如"utf-8"、"gbk"等，默认为"utf-8"</param>
    /// <returns>指定范围内的行列表</returns>
    public List<string> ReadLines(string filePath, int startLine, int endLine = -1, string? encodingStr = null)
    {
        try
        {
            List<string> lines = new List<string>();

            if (!File.Exists(filePath) || startLine < 0 || (endLine != -1 && endLine < startLine))
                return lines;

            Encoding encoding = GetEncoding(encodingStr);

            using (StreamReader reader = new StreamReader(filePath, encoding))
            {
                int currentLine = 0;
                while (!reader.EndOfStream && (endLine == -1 || currentLine <= endLine))
                {
                    string line = reader.ReadLine() ?? string.Empty;
                    if (currentLine >= startLine)
                        lines.Add(line);

                    currentLine++;
                }
            }

            return lines;
        }
        catch (Exception ex)
        {
            Log.Error($"【读取文件多行】 Error:{ex.Message}");
            return new List<string>();
        }
    }

    /// <summary>
    /// 读取文件的所有行
    /// </summary>
    /// <param name="filePath">文件路径</param>
    /// <param name="encodingStr">编码方式字符串，如"utf-8"、"gbk"等，默认为"utf-8"</param>
    /// <returns>文件的所有行</returns>
    public List<string> ReadAllLines(string filePath, string? encodingStr = null)
    {
        try
        {
            if (!File.Exists(filePath))
                return new List<string>();

            Encoding encoding = GetEncoding(encodingStr);
            return File.ReadAllLines(filePath, encoding).ToList();
        }
        catch (Exception ex)
        {
            Log.Error($"【读取文件所有行】 Error:{ex.Message}");
            return new List<string>();
        }
    }

    /// <summary>
    /// 统计文件行数
    /// </summary>
    /// <param name="filePath">文件路径</param>
    /// <returns>文件的总行数</returns>
    public int CountLines(string filePath)
    {
        try
        {
            if (!File.Exists(filePath))
                return 0;

            int count = 0;
            using (StreamReader reader = new StreamReader(filePath))
            {
                while (!reader.EndOfStream)
                {
                    reader.ReadLine();
                    count++;
                }
            }

            return count;
        }
        catch (Exception ex)
        {
            Log.Error($"【统计文件行数】 Error:{ex.Message}");
            return 0;
        }
    }

    /// <summary>
    /// 获取目录信息
    /// </summary>
    /// <param name="directoryPath">目录路径</param>
    /// <returns>目录信息对象</returns>
    public DirectoryInfo? GetDirInfo(string directoryPath)
    {
        try
        {
            if (!Directory.Exists(directoryPath))
                return null;

            return new DirectoryInfo(directoryPath);
        }
        catch (Exception ex)
        {
            Log.Error($"【获取目录信息】 Error:{ex.Message}");
            return null;
        }
    }

    /// <summary>
    /// 创建目录
    /// </summary>
    /// <param name="directoryPath">目录路径</param>
    /// <returns>是否创建成功</returns>
    [EngineMethod("file.CreateDir('/path/to/directory')", "创建目录", "// 方法:file.CreateDir()\n// 参数一: '/path/to/directory' 目录路径\n// 返回类型:bool\n// 描述:该方法创建目录\nvar path = '/data/backup';\nvar success = file.CreateDir(path);\nreturn success;\n\n// 示例返回结果\ntrue", "文件操作")]
    public bool CreateDir(string directoryPath)
    {
        try
        {
            if (Directory.Exists(directoryPath))
                return true;

            Directory.CreateDirectory(directoryPath);
            return true;
        }
        catch (Exception ex)
        {
            Log.Error($"【创建目录】 Error:{ex.Message}");
            return false;
        }
    }

    /// <summary>
    /// 删除目录
    /// </summary>
    /// <param name="directoryPath">目录路径</param>
    /// <param name="recursive">是否递归删除子目录和文件</param>
    /// <returns>是否删除成功</returns>
    [EngineMethod("file.DeleteDir('/path/to/directory', true)", "删除目录", "// 方法:file.DeleteDir()\n// 参数一: '/path/to/directory' 目录路径\n// 参数二: true 是否递归删除(非必填,默认false)\n// 返回类型:bool\n// 描述:该方法删除目录\nvar path = '/temp/old';\nvar recursive = true;\nvar success = file.DeleteDir(path, recursive);\nreturn success;\n\n// 示例返回结果\ntrue", "文件操作")]
    public bool DeleteDir(string directoryPath, bool recursive = false)
    {
        try
        {
            if (!Directory.Exists(directoryPath))
                return true;

            Directory.Delete(directoryPath, recursive);
            return true;
        }
        catch (Exception ex)
        {
            Log.Error($"【删除目录】 Error:{ex.Message}");
            return false;
        }
    }

    /// <summary>
    /// 资源释放
    /// </summary>
    public void Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }

    /// <summary>
    /// 资源释放
    /// </summary>
    /// <param name="disposing"></param>
    protected virtual void Dispose(bool disposing)
    {
        if (!_disposed)
        {
            if (disposing)
            {
                // 释放托管资源
            }

            // 释放非托管资源
            _disposed = true;
        }
    }
}