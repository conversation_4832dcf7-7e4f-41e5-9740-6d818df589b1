using Extras.MQTT;
using Furion.Schedule;
using IotPlatform.Thing.RemoteControl.Entity;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using SqlSugar;
using DateTime = System.DateTime;

namespace IotPlatform.Thing.RemoteControl.Job;

/// <summary>
///     ota任务
/// </summary>
public class ExecuteScriptJob : IJob
{
    private readonly ILogger<ExecuteScriptJob> _logger;
    private readonly ISqlSugarClient _db;
    private readonly MqttService _mqttService;

    /// <summary>
    /// </summary>
    /// <param name="logger"></param>
    /// <param name="mqttService"></param>
    /// <param name="db"></param>
    public ExecuteScriptJob(ILogger<ExecuteScriptJob> logger, ISqlSugarClient db, MqttService mqttService)
    {
        _logger = logger;
        _db = db;
        _mqttService = mqttService;
    }

    /// <summary>
    /// </summary>
    /// <param name="context"></param>
    /// <param name="stoppingToken"></param>
    public async Task ExecuteAsync(JobExecutingContext context, CancellationToken stoppingToken)
    {
        try
        {
            Dictionary<string, object>? properties = context.JobDetail.GetProperties();
            FirmwareManageUpdateRecord record = null;

            if (properties.TryGetValue("recordId", out object? property))
            {
                long recordId = Convert.ToInt64(property);
                record = await _db.Queryable<FirmwareManageUpdateRecord>().Where(w => w.Id == recordId)
                    .Includes(w => w.ModelThing)
                    .Includes(w => w.FirmwareManageDetail, w => w.FirmwareManage)
                    .FirstAsync(stoppingToken);
            }

            if (record == null)
            {
                return;
            }

            if (record.Status == "升级指令已下发")
            {
                return;
            }

            try
            {
                bool result = Convert.ToBoolean(await _mqttService.PublishRpc("ota", JsonConvert.SerializeObject(new
                {
                    record.FirmwareManageDetail.FirmwareManage.Name,
                    record.FirmwareManageDetail.FirmwareManage.Version,
                    CreateTime = DateTime.Now,
                    RecordId = record.Id,
                }), record.ModelThing.GatewayExampleModel?.Sn, record.FirmwareManageDetail.TimeOut));

                if (result)
                {
                    record.Logs.Add(DateTime.Now, "升级指令已下发");
                    record.Status = "升级指令已下发";
                    record.StatusUpdateTime = DateTime.Now;
                }
                else
                {
                    record.Status = "升级指令下发失败";
                    record.StatusUpdateTime = DateTime.Now;
                    record.Logs.Add(DateTime.Now, "升级指令下发失败");
                }
            }
            catch (Exception ex)
            {
                record.Status = "升级指令下发失败";
                record.StatusUpdateTime = DateTime.Now;
                record.Logs.Add(DateTime.Now, "升级指令下发失败：" + ex.Message);
            }

            // 插入执行日志
            await _db.CopyNew().Updateable(record).UpdateColumns(w => new { w.StatusUpdateTime, w.Status, w.Logs }).ExecuteCommandAsync(stoppingToken);
            context.Result = record.Status;
        }
        catch (Exception ex)
        {
            _logger.LogError($"【固件升级-定时任务】 Error:【{ex.Message}】");
        }
    }
}