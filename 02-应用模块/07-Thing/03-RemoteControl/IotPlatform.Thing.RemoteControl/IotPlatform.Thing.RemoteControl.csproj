<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <PropertyGroup Condition=" '$(Configuration)' == 'Release' ">
      <DebugType>none</DebugType>
    </PropertyGroup>

    <ItemGroup>
      <ProjectReference Include="..\..\..\..\01-架构核心\Extras.MQTT\Extras.MQTT.csproj" />
      <ProjectReference Include="..\..\..\02-System\Systems.Core\Systems.Core.csproj" />
      <ProjectReference Include="..\..\..\09-Engine\Mqtt.Engine\Mqtt.Engine.csproj" />
      <ProjectReference Include="..\IotPlatform.Thing.RemoteControl.Entity\IotPlatform.Thing.RemoteControl.Entity.csproj" />
    </ItemGroup>

</Project>
