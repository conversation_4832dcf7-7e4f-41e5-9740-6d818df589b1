namespace IotPlatform.Thing.RemoteControl.Entity;

/// <summary>
///     远程控制-固件管理
/// </summary>
[SugarTable("business_firmwareManage", "远程控制-固件管理")]
public class FirmwareManage : EntityTenant
{
    /// <summary>
    ///     物模型Id
    /// </summary>
    [SugarColumn(ColumnDescription = "物模型Id")]
    public long ModelId { get; set; }

    /// <summary>
    ///     固件名称
    /// </summary>
    [SugarColumn(ColumnDescription = "固件名称")]
    public string Name { get; set; }

    /// <summary>
    ///     固件版本
    /// </summary>
    [SugarColumn(ColumnDescription = "固件版本")]
    public string Version { get; set; }

    /// <summary>
    ///     签名算法
    /// </summary>
    [SugarColumn(ColumnDescription = "签名算法")]
    public string SignType { get; set; }

    /// <summary>
    ///     固件Id
    /// </summary>
    [SugarColumn(ColumnDescription = "固件Id")]
    public long FileId { get; set; }
    
    /// <summary>
    /// 磁盘建议大小（M）
    /// </summary>
    [SugarColumn(ColumnDescription = "磁盘建议大小（M）")]
    public long NeedSize { get; set; }
    
    /// <summary>
    ///     描述
    /// </summary>
    [SugarColumn(ColumnDescription = "描述", ColumnDataType = "longtext,text,clob", IsNullable = true)]
    public string? Remark { get; set; }

    #region 关联表

    /// <summary>
    ///     固件Id
    /// </summary>
    [Navigate(NavigateType.OneToOne, nameof(FileId))]
    public SysFile? SysFile { get; set; }

    /// <summary>
    ///     物模型
    /// </summary>
    [Navigate(NavigateType.OneToOne, nameof(ModelId))]
    public Model? Model { get; set; }

    /// <summary>
    ///     远程控制-固件管理-升级
    /// </summary>
    [Navigate(NavigateType.OneToMany, nameof(FirmwareManageDetail.FirmwareManageId))]
    [SugarColumn(IsIgnore = true)]
    public List<FirmwareManageDetail> FirmwareManageDetails { get; set; }

    #endregion
}