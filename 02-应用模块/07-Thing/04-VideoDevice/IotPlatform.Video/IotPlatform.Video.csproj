<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <PropertyGroup Condition=" '$(Configuration)' == 'Release' ">
      <DebugType>none</DebugType>
    </PropertyGroup>

    <ItemGroup>
      <ProjectReference Include="..\IotPlatform.Video.Entity\IotPlatform.Video.Entity.csproj" />
      <ProjectReference Include="..\SharpRTSPtoWebRTC\SharpRTSPtoWebRTC.csproj" />
    </ItemGroup>

</Project>
