namespace IotPlatform.Thing.Warning.Entity;

/// <summary>
///     报警管理-报警类型-条件表
/// </summary>
[SugarTable("business_alarmCondition", "报警管理-报警类型-条件表")]
public class AlarmCondition : EntityTenantId
{
    /// <summary>
    ///     所属报警类型Id
    /// </summary>
    [SugarColumn(ColumnDescription = "所属报警类型Id")]
    public long AlarmTypeId { get; set; }

    /// <summary>
    ///     所属报警类型
    /// </summary>
    [Navigate(NavigateType.OneToOne, nameof(AlarmTypeId))]
    public AlarmType AlarmType { get; set; }

    /// <summary>
    ///     报警来源：1接入与建模
    /// </summary>
    [SugarColumn(ColumnDescription = "报警来源：1接入与建模")]
    public AlarmSourceEnum AlarmSource { get; set; }

    /// <summary>
    ///     报警来源：1接入与建模
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public string AlarmSourceName => AlarmSource.GetDescription();

    /// <summary>
    ///     物模型Id
    /// </summary>
    [SugarColumn(ColumnDescription = "物模型Id")]
    public long ModelId { get; set; }

    /// <summary>
    ///     物模型名称
    /// </summary>
    [SugarColumn(ColumnDescription = "物模型名称")]
    public string ModelName { get; set; }

    /// <summary>
    ///     物实例集合
    /// </summary>
    [SugarColumn(ColumnDescription = "物实例集合", IsJson = true)]
    public List<SourceThing> Things { get; set; }

    /// <summary>
    ///     报警集合
    /// </summary>
    [SugarColumn(ColumnDescription = "报警集合", IsJson = true)]
    public List<SourceAlarm> SourceAlarms { get; set; }
}

/// <summary>
///     报警条件-选中的物实例
/// </summary>
public class SourceThing
{
    /// <summary>
    ///     物实例标识
    /// </summary>
    public string ThingId { get; set; }

    /// <summary>
    ///     物实例名称
    /// </summary>
    public string ThingName { get; set; }
}

/// <summary>
///     报警条件-选中的报警
/// </summary>
public class SourceAlarm
{
    /// <summary>
    ///     报警Id
    /// </summary>
    public string AlarmId { get; set; }

    /// <summary>
    ///     报警名称
    /// </summary>
    public string AlarmName { get; set; }
}

/// <summary>
///     报警来源：1接入与建模
/// </summary>
public enum AlarmSourceEnum
{
    /// <summary>
    ///     接入与建模
    /// </summary>
    [Description("接入与建模")] AccessAndModeling = 1
}