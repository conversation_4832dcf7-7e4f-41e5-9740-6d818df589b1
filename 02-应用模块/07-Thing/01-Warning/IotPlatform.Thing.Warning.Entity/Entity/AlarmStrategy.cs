using IotPlatform.Thing.Entity;

namespace IotPlatform.Thing.Warning.Entity;

/// <summary>
///     报警类型-报警策略
/// </summary>
[SugarTable("business_alarmStrategy", "报警类型-报警策略表")]
public class AlarmStrategy : EntityTenantId
{
    /// <summary>
    ///     所属报警类型Id
    /// </summary>
    [SugarColumn(ColumnDescription = "所属报警类型Id")]
    public long AlarmTypeId { get; set; }

    /// <summary>
    ///     所属报警类型
    /// </summary>
    [Navigate(NavigateType.OneToOne, nameof(AlarmTypeId))]
    public AlarmType AlarmType { get; set; }

    /// <summary>
    ///     名称
    /// </summary>
    [SugarColumn(ColumnDescription = "名称", Length = 64)]
    public string Name { get; set; }

    /// <summary>
    ///     标签
    /// </summary>
    [SugarColumn(ColumnDescription = "标签", IsJson = true)]
    public List<string> Tags { get; set; }

    /// <summary>
    ///     是否通知指定时间段
    /// </summary>
    [SugarColumn(ColumnDescription = "是否通知指定时间段")]
    public bool IsSpecifiedPeriod { get; set; }

    /// <summary>
    ///     通知指定时间段
    /// </summary>
    [SugarColumn(ColumnDescription = "通知指定时间段", IsJson = true, IsNullable = true)]
    public SpecifiedNotificationPeriodModel SpecifiedPeriod { get; set; }

    /// <summary>
    ///     按警报级别设置策略:1：全部级别；2：部分级别
    /// </summary>
    [SugarColumn(ColumnDescription = "按警报级别设置策略:1：全部级别；2：部分级别")]
    public SetPoliciesByAlertLevelEnum AlertLevel { get; set; } = SetPoliciesByAlertLevelEnum.All;

    /// <summary>
    ///     全部级别配置
    /// </summary>
    [SugarColumn(ColumnDescription = "全部级别配置", IsJson = true, IsNullable = true)]
    public LevelAll LevelAll { get; set; }

    /// <summary>
    ///     部分级别配置
    /// </summary>
    [SugarColumn(ColumnDescription = "部分级别配置", IsJson = true, IsNullable = true)]
    public List<LevelPart> LevelPart { get; set; }
}

/// <summary>
///     通知方式完整配置 -全部级别
/// </summary>
public class LevelAll
{
    /// <summary>
    ///     报警通知设置方式：1：使用发送策略；2：自行设置
    /// </summary>
    [SugarColumn(ColumnDescription = "报警通知设置方式：1：使用发送策略；2：自行设置")]
    public AlarmNotificationSettingMethodEnum Method { get; set; }

    /// <summary>
    ///     报警通知时间;1:立即通知;2:延迟通知
    /// </summary>
    [SugarColumn(ColumnDescription = "报警通知时间;1:立即通知;2:延迟通知")]
    public AlarmNotificationTimeTypeEnum TimeType { get; set; }

    /// <summary>
    ///     报警通知设置方式-使用发送策略配置
    /// </summary>
    [SugarColumn(ColumnDescription = "报警通知设置方式-使用发送策略配置", IsJson = true)]
    public List<AlarmNotificationSettingMethodByStrategy?> Strategy { get; set; }

    /// <summary>
    ///     报警通知设置方式-自行设置
    /// </summary>
    [SugarColumn(ColumnDescription = "报警通知设置方式-自行设置", IsJson = true)]
    public List<AlarmNotificationSettingMethodBySetting?> Setting { get; set; }

    /// <summary>
    ///     回调地址
    /// </summary>
    [SugarColumn(ColumnDescription = "回调地址")]
    public string BackUrl { get; set; }
}

/// <summary>
///     通知方式完整配置 -部分级别
/// </summary>
public class LevelPart : LevelAll
{
    /// <summary>
    ///     报警级别：1：紧急；2：重要；3：警告；4：一般；5：不确定
    /// </summary>
    [SugarColumn(ColumnDescription = "报警级别：1：紧急；2：重要；3：警告；4：一般；5：不确定")]
    public AlarmLevelEnum AlarmLevel { get; set; }
}

/// <summary>
///     报警通知设置方式-自行设置-通知到人
/// </summary>
public class AlarmNotificationSettingMethodBySetting
{
    /// <summary>
    ///     报警通知-1：通知到人；2通知到群
    /// </summary>
    public AlarmNotificationBySettingTypeEnum AlarmNotificationBySettingType { get; set; }

    /// <summary>
    ///     发送通知人Id
    /// </summary>
    public List<long>? UserIds { get; set; }

    /// <summary>
    ///     企业微信群机器人webhook地址
    /// </summary>
    public List<string>? WebHooks { get; set; }

    /// <summary>
    ///     通知渠道：1：微信
    /// </summary>
    public List<string> NotificationChannel { get; set; }

    /// <summary>
    ///     报警内容
    /// </summary>
    public string Content { get; set; }

    /// <summary>
    ///     时间
    /// </summary>
    public short TimeOfNumber { get; set; }

    /// <summary>
    ///     时间单位：分钟；小时;天
    /// </summary>
    public string TimeUnit { get; set; }

    /// <summary>
    ///     重复通知
    /// </summary>
    public bool DuplicateNotification { get; set; }

    /// <summary>
    ///     重复通知-时间
    /// </summary>
    public short? DuplicateTimeOfNumber { get; set; }

    /// <summary>
    ///     重复通知-时间单位：分钟；小时;天
    /// </summary>
    public string? DuplicateTimeUnit { get; set; }

    /// <summary>
    ///     重复通知-次数
    /// </summary>
    public short DuplicateCount { get; set; }

    /// <summary>
    ///     通知顺序号
    /// </summary>
    public short OrderNum { get; set; }
}

/// <summary>
///     报警通知设置方式-使用发送策略
/// </summary>
public class AlarmNotificationSettingMethodByStrategy
{
    /// <summary>
    ///     消息策略Id
    /// </summary>
    public long SendStrategyId { get; set; }

    /// <summary>
    ///     时间
    /// </summary>
    public short TimeOfNumber { get; set; }

    /// <summary>
    ///     时间单位：分钟；小时;天
    /// </summary>
    public string TimeUnit { get; set; }

    /// <summary>
    ///     重复通知
    /// </summary>
    public bool DuplicateNotification { get; set; }

    /// <summary>
    ///     重复通知-时间
    /// </summary>
    public short DuplicateTimeOfNumber { get; set; }

    /// <summary>
    ///     重复通知-时间单位：分钟；小时;天
    /// </summary>
    public string? DuplicateTimeUnit { get; set; }

    /// <summary>
    ///     重复通知-次数
    /// </summary>
    public short DuplicateCount { get; set; }

    /// <summary>
    ///     通知顺序号
    /// </summary>
    public short OrderNum { get; set; }
}

/// <summary>
///     通知指定时间段配置
/// </summary>
public class SpecifiedNotificationPeriodModel
{
    /// <summary>
    ///     通知指定时间段:1:每周；2每天；3工作日
    /// </summary>
    public SpecifiedNotificationPeriodEnum SpecifiedNotificationPeriodType { get; set; }

    /// <summary>
    ///     每周选中日期
    /// </summary>
    public List<int> Week { get; set; } = new();

    /// <summary>
    ///     开始时间
    /// </summary>
    public string StartTime { get; set; }

    /// <summary>
    ///     截止时间
    /// </summary>
    public string EndTime { get; set; }

    /// <summary>
    ///     是否是今天
    /// </summary>
    public bool IsToDay { get; set; } = true;
}

/// <summary>
///     报警通知时间;1:立即通知;2:延迟通知
/// </summary>
public enum AlarmNotificationTimeTypeEnum
{
    /// <summary>
    ///     立即通知
    /// </summary>
    [Description("立即通知")] Now = 1,

    /// <summary>
    ///     延迟通知
    /// </summary>
    [Description("延迟通知")] Delay = 2
}

/// <summary>
///     报警通知-1：通知到人；2通知到群
/// </summary>
public enum AlarmNotificationBySettingTypeEnum
{
    /// <summary>
    ///     报警通知到人
    /// </summary>
    [Description("报警通知到人")] User = 1,

    /// <summary>
    ///     报警通知到群
    /// </summary>
    [Description("报警通知到群")] Group = 2
}

/// <summary>
///     报警通知设置方式：1：使用发送策略；2：自行设置
/// </summary>
public enum AlarmNotificationSettingMethodEnum
{
    /// <summary>
    ///     使用发送策略
    /// </summary>
    [Description("使用发送策略")] Strategy = 1,

    /// <summary>
    ///     自行设置
    /// </summary>
    [Description("自行设置")] Setting = 2
}

/// <summary>
///     按警报级别设置策略:1：全部级别；2：部分级别
/// </summary>
public enum SetPoliciesByAlertLevelEnum
{
    /// <summary>
    ///     全部级别
    /// </summary>
    [Description("全部级别")] All = 1,

    /// <summary>
    ///     部分级别
    /// </summary>
    [Description("部分级别")] Part = 2
}

/// <summary>
///     通知指定时间段:1:每周；2每天；3工作日
/// </summary>
public enum SpecifiedNotificationPeriodEnum
{
    /// <summary>
    ///     每周
    /// </summary>
    [Description("每周")] Week = 1,

    /// <summary>
    ///     每天
    /// </summary>
    [Description("每天")] Day = 2,

    /// <summary>
    ///     工作日
    /// </summary>
    [Description("工作日")] Work = 3
}