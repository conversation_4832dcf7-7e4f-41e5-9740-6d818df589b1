using DateTime = System.DateTime;

namespace IotPlatform.Thing.Warning.Entity;

/// <summary>
///     报警抑制记录表
/// </summary>
[SugarTable("business_modelAlarmRestrainRecord", "物模型-报警抑制记录表")]
public class ModelAlarmRestrainRecord : EntityTenantId
{
    /// <summary>
    ///     物实例Id
    /// </summary>
    [SugarColumn(ColumnDescription = "物实例Id")]
    public long ThingId { get; set; }

    /// <summary>
    ///     所属物实例
    /// </summary>
    [Navigate(NavigateType.OneToOne, nameof(ThingId))]
    public ModelThing ModelThing { get; set; }

    /// <summary>
    ///     物模型Id
    /// </summary>
    [SugarColumn(ColumnDescription = "物模型Id")]
    public long ModelId { get; set; }

    /// <summary>
    ///     物模型
    /// </summary>
    [Navigate(NavigateType.OneToOne, nameof(ModelId))]
    public Model Model { get; set; }

    /// <summary>
    ///     发生时间
    /// </summary>
    [SugarColumn(ColumnDescription = "发生时间")]
    public DateTime CreatedTime { get; set; }

    /// <summary>
    ///     结束时间
    /// </summary>
    [SugarColumn(ColumnDescription = "结束时间")]
    public DateTime EndTime { get; set; }

    /// <summary>
    ///     解除时间
    /// </summary>
    [SugarColumn(ColumnDescription = "解除时间", IsNullable = true)]
    public DateTime? CloseTime { get; set; }
}