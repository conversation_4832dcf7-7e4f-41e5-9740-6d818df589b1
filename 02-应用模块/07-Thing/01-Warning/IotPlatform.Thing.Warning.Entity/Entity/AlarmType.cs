namespace IotPlatform.Thing.Warning.Entity;

/// <summary>
///     报警管理-报警类型
/// </summary>
[SugarTable("business_alarmType", "报警管理-报警类型表")]
public class AlarmType : EntityTenant
{
    /// <summary>
    ///     名称
    /// </summary>
    [SugarColumn(ColumnDescription = "名称", Length = 64)]
    public string Name { get; set; }

    /// <summary>
    ///     报警条件
    /// </summary>
    [Navigate(NavigateType.OneToMany, nameof(AlarmCondition.AlarmTypeId))]
    [SugarColumn(IsIgnore = true)]
    public List<AlarmCondition> AlarmTypeCondition { get; set; }

    /// <summary>
    ///     报警策略
    /// </summary>
    [Navigate(NavigateType.OneToMany, nameof(Thing.Warning.Entity.AlarmStrategy.AlarmTypeId))]
    [SugarColumn(IsIgnore = true)]
    public List<AlarmStrategy> AlarmStrategy { get; set; }
}