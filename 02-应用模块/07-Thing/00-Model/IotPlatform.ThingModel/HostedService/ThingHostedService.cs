using Microsoft.Extensions.Hosting;
using Timer = System.Threading.Timer;

namespace IotPlatform.ThingModel.HostedService;

/// <summary>
///     物实例初始化
/// </summary>
public class ThingHostedService : IHostedService, IDisposable
{
    /// <summary>
    ///     定时任务库
    /// </summary>
    private readonly ISchedulerFactory _schedulerFactory;

    /// <summary>
    ///     运行中的物实例
    /// </summary>
    public readonly ConcurrentDictionary<string, CustomThing> DeviceThreads = new();

    /// <summary>
    ///     物实例报警
    /// </summary>
    public readonly ConcurrentDictionary<string, CustomThingWarning> WarningThreads = new();

    /// <summary>
    /// </summary>
    private readonly ISqlSugarClient _db;

    /// <summary>
    ///     事件
    /// </summary>
    private readonly IEventBusFactory _eventBusFactory;

    /// <summary>
    ///     缓存
    /// </summary>
    private readonly SysCacheService? _cacheService;

    /// <summary>
    ///     定时任务
    /// </summary>
    private Timer _timer;

    /// <summary>
    /// </summary>
    /// <param name="db"></param>
    /// <param name="schedulerFactory"></param>
    /// <param name="eventBusFactory"></param>
    /// <param name="scopeFactory"></param>
    public ThingHostedService(ISqlSugarClient db, ISchedulerFactory schedulerFactory, IEventBusFactory eventBusFactory, IServiceScopeFactory scopeFactory)
    {
        _db = db;
        _schedulerFactory = schedulerFactory;
        _eventBusFactory = eventBusFactory;
        _cacheService = scopeFactory.CreateScope().ServiceProvider.GetService<SysCacheService>();
    }

    /// <summary>
    /// </summary>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    public Task StartAsync(CancellationToken cancellationToken)
    {
        _timer = new Timer(DoWork, null, TimeSpan.Zero, TimeSpan.FromMinutes(15)); // 每15分钟执行一次
        return Task.CompletedTask;
    }

    /// <summary>
    /// </summary>
    /// <param name="state"></param>
    private void DoWork(object? state)
    {
        try
        {
            // 初始化物实例
            List<ModelThing>? thingList = _db.Queryable<ModelThing>().Where(w => w.Enable)
                .Includes(w => w.Model, w => w.ThingAttributes)
                .Includes(w => w.Model, w => w.ThingAlarm.Where(thingAlarm => thingAlarm.Enable == true).ToList())
                .Includes(w => w.Model, w => w.ThingLinkVariable)
                .ToList();
            // 创建设备列表
            foreach (ModelThing thing in thingList.Where(thing => !DeviceThreads.ContainsKey(thing.Identification)))
            {
                try
                {
                    CustomThing deviceThread = new(_db, thing, _eventBusFactory, _cacheService);
                    DeviceThreads.TryAdd(thing.Identification, deviceThread);
                    Console.WriteLine($"··························[物实例]启动物实例:{thing.Identification}································");
                    ThreadPool.QueueUserWorkItem(_ => deviceThread.Start());
                }
                catch (Exception e)
                {
                    Log.Error($"【物实例】 启动失败:{e.Message}");
                }

                try
                {
                    if (thing.Model.ThingAlarm.Count == 0)
                    {
                        continue;
                    }

                    CustomThingWarning warningThread = new(_db, thing, _schedulerFactory, _eventBusFactory, _cacheService);
                    WarningThreads.TryAdd(thing.Identification, warningThread);
                    Console.WriteLine($"·························[物实例-报警]启动:{thing.Identification}································");
                    ThreadPool.QueueUserWorkItem(_ => warningThread.Start());
                }
                catch (Exception e)
                {
                    Log.Error($"【物实例-报警】 启动失败:{e.Message}");
                }
            }
        }
        catch (Exception e)
        {
            Log.Error($"[物实例]：严重错误：{e.Message}");
        }
    }

    /// <summary>
    ///     新增物实例设备
    /// </summary>
    public async Task AddDevice(ModelThing thing)
    {
        await Stop(thing.Identification);

        CustomThing customThing = new(_db, thing, _eventBusFactory, _cacheService);
        DeviceThreads.TryAdd(thing.Identification, customThing);
        await Task.Run(() => customThing.Start());

        if (thing.Model.ThingAlarm.Count != 0)
        {
            CustomThingWarning warningThread = new(_db, thing, _schedulerFactory, _eventBusFactory, _cacheService);
            WarningThreads.TryAdd(thing.Identification, warningThread);
            await Task.Run(() => warningThread.Start());
        }
    }

    /// <summary>
    ///     停止线程
    /// </summary>
    /// <param name="identification"></param>
    /// <returns></returns>
    public Task Stop(string identification)
    {
        if (!DeviceThreads.TryGetValue(identification, out CustomThing? thread))
        {
            return Task.CompletedTask;
        }

        thread.Dispose();
        DeviceThreads.TryRemove(identification, out _);
        Log.Warning($"物实例：{identification}，停止任务");
        if (!WarningThreads.TryGetValue(identification, out CustomThingWarning? warningThread))
        {
            return Task.CompletedTask;
        }

        warningThread.Dispose();
        WarningThreads.TryRemove(identification, out _);
        Log.Warning($"物实例：{identification}，停止报警任务");
        return Task.CompletedTask;
    }

    /// <summary>
    /// </summary>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    public Task StopAsync(CancellationToken cancellationToken)
    {
        _timer?.Change(Timeout.Infinite, 0);
        return Task.CompletedTask;
    }

    /// <summary>
    /// </summary>
    public void Dispose()
    {
        _timer?.Dispose();
    }
}