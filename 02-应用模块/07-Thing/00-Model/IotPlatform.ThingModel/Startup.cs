using IotPlatform.ThingModel.Event;

namespace IotPlatform.ThingModel;

/// <summary>
/// </summary>
public class Startup : AppStartup
{
    /// <summary>
    /// </summary>
    /// <param name="services"></param>
    public void ConfigureServices(IServiceCollection services)
    {
        services.AddSingleton<ThingHostedService>(); // 注册为单例服务
        services.AddHostedService(provider => provider.GetRequiredService<ThingHostedService>());
        
        // 注册物实例事件
        services.AddEventBus(builder => { builder.AddSubscriber<ThingEventSubscriber>(); });
    }
}