global using Furion.DependencyInjection;
global using Furion.EventBus;
global using Furion.FriendlyException;
global using Furion.JsonSerialization;
global using Microsoft.Extensions.DependencyInjection;
global using SqlSugar;
global using System;
global using System.Collections.Generic;
global using System.IO;
global using System.Linq;
global using System.Text;
global using System.Threading.Tasks;
global using Furion;
global using Mapster;
global using System.Collections.Concurrent;
global using IotPlatform.Core.Const;
global using System.Timers;
global using DynamicExpresso;
global using Furion.Logging;
global using Furion.Schedule;
global using Jint;
global using Yitter.IdGenerator;
global using Furion.DynamicApiController;
global using Microsoft.AspNetCore.Mvc;
global using Furion.TimeCrontab;
global using Microsoft.Extensions.Logging;
global using IotPlatform.ThingModel.Entity.Dto;
global using Microsoft.AspNetCore.Authorization;
global using MiniExcelLibs;
global using Furion.DatabaseAccessor;
global using Extras.DatabaseAccessor.SqlSugar.Internal;
global using Extras.DatabaseAccessor.SqlSugar.Repositories;
global using MiniExcelLibs.Attributes;
global using MiniExcelLibs.OpenXml;
global using IotPlatform.Core;
global using Common.Models;
global using DateTime = System.DateTime;
global using System.Threading;
global using Common.Hubs;
global using Extras.TDengine.TDengIne;
global using IotPlatform.Core.Enum;
global using IotPlatform.Core.Extension;
global using System.Linq.Expressions;
global using IotPlatform.Thing.Entity;
global using Extras.TDengine.Model;
global using IotPlatform.Thing.Warning.Entity;
global using IotPlatform.ThingModel.Custom;
global using Common.Core.Manager.User;
global using IotPlatform.ThingModel.HostedService;