using IotPlatform.Thing.StatisticalRule.Entity;

namespace IotPlatform.ThingModel.Services;

/// <summary>
/// 系统列定义类，集中管理所有系统列信息
/// </summary>
public static class SystemColumns
{
    /// <summary>
    /// 所有系统列名称（小写形式，用于比较）
    /// </summary>
    public static readonly HashSet<string> All = new HashSet<string>(StringComparer.OrdinalIgnoreCase)
    {
        "ts",
        "cloudtime",
        "cloudTime",
        "devicename",
        "deviceName"
    };

    /// <summary>
    /// 检查列名是否是系统列
    /// </summary>
    /// <param name="columnName">列名</param>
    /// <returns>是否是系统列</returns>
    public static bool IsSystemColumn(string columnName)
    {
        return !string.IsNullOrEmpty(columnName) && All.Contains(columnName.ToLower());
    }
}

/// <summary>
///     物模型
///     版 本:V5.0.0
///     版 权:杭州峰回科技有限公司
///     作 者:蒋状
///     日 期:2023-07-07
/// </summary>
[ApiDescriptionSettings("物模型")]
public class ModelService : IDynamicApiController, ITransient
{
    private readonly ExecuteService _executeService;
    private readonly ISqlSugarRepository<Model> _model;

    /// <summary>
    ///     定时任务
    /// </summary>
    private readonly ISchedulerFactory _schedulerFactory;

    /// <summary>
    /// </summary>
    /// <param name="model"></param>
    /// <param name="executeService"></param>
    /// <param name="schedulerFactory"></param>
    public ModelService(ISqlSugarRepository<Model> model, ExecuteService executeService, ISchedulerFactory schedulerFactory)
    {
        _model = model;
        _executeService = executeService;
        _schedulerFactory = schedulerFactory;
    }

    /// <summary>
    ///     物模型-个性树-拼接物实例
    /// </summary>
    /// <returns></returns>
    /// <exception cref="AppFriendlyException"></exception>
    [HttpGet("/trendAnalysis/tree")]
    public async Task<List<dynamic>> TrendAnalysisTree()
    {
        List<dynamic> resultData = new();
        // 查到符合条件的全部物模型
        List<Model>? modelList = await _model.AsQueryable().Where(w => w.Enable == true && w.IsTemplate == false)
            .Includes(w => w.ThingExample)
            .Includes(w => w.ThingAttributes)
            .ToListAsync();
        resultData.Add(new
        {
            ModelType = ModelTypeEnum.Device,
            ModelTypeName = ModelTypeEnum.Device.GetDescription(),
            ThingTree = ThingTree(modelList, ModelTypeEnum.Device)
        });

        resultData.Add(new
        {
            ModelType = ModelTypeEnum.Gateway,
            ModelTypeName = ModelTypeEnum.Gateway.GetDescription(),
            ThingTree = ThingTree(modelList, ModelTypeEnum.Gateway)
        });

        resultData.Add(new
        {
            ModelType = ModelTypeEnum.Compound,
            ModelTypeName = ModelTypeEnum.Compound.GetDescription(),
            ThingTree = ThingTree(modelList, ModelTypeEnum.Compound)
        });
        return resultData;
    }

    /// <summary>
    ///     根据modelType过滤数据
    /// </summary>
    /// <param name="input"></param>
    /// <param name="modelTypeEnum"></param>
    /// <returns></returns>
    private dynamic ThingTree(List<Model> input, ModelTypeEnum modelTypeEnum)
    {
        var modelList = input.Where(w => w.ModelType == modelTypeEnum)
            .Select(s => new
            {
                s.Id,
                s.Name,
                s.Uuid,
                ThingExample = s.ThingExample.Select(c => new
                {
                    c.Id,
                    c.Name,
                    c.Identification,
                    ModelIdentification = c.DeviceExampleModel is { ExampleConnectType: ExampleConnectTypeEnum.Gateway } ? c.DeviceExampleModel.DeviceExampleGatewayModel?.ThingExampleIdentification : ""
                }),
                ThingAttribute = s.ThingAttributes.Select(a => new
                {
                    a.Id,
                    a.Name,
                    a.Identification,
                    a.DataType
                })
            })
            .ToList();
        return modelList;
    }

    /// <summary>
    ///     物模型-列表
    /// </summary>
    /// <returns></returns>
    [HttpGet("/model/page")]
    public async Task<SqlSugarPagedList<ModelPageOutput>> ModelPage([FromQuery] ModelPageInput input)
    {
        SqlSugarPagedList<ModelPageOutput>? modelPageOutput = await _model.AsQueryable()
            .Where(w => w.IsTemplate == false)
            .Where(w => w.ModelType == input.ModelType)
            .Where(w => w.Enable == input.Enable)
            .WhereIF(input.SearchValue.IsNotEmptyOrNull() && input.PreciseSearch == false, u => u.Name.Contains(input.SearchValue) || u.Uuid.ToString().Contains(input.SearchValue))
            .WhereIF(input.SearchValue.IsNotEmptyOrNull() && input.PreciseSearch, u => u.Name == input.SearchValue || u.Uuid.ToString() == input.SearchValue)
            .Includes(w => w.ModelGroup)
            .WhereIF(input.ThingGroupId > 0, w => w.ThingGroupId == input.ThingGroupId || w.ModelGroup.ParentId == input.ThingGroupId)
            .OrderByIF(!string.IsNullOrEmpty(input.Field), input.Field + " " + input.Order)
            .Select<ModelPageOutput>()
            .ToPagedListAsync(input.PageNo, input.PageSize);

        return modelPageOutput;
    }

    /// <summary>
    ///     物模型-下拉
    /// </summary>
    /// <returns></returns>
    [HttpGet("/model/select")]
    public async Task<List<ModelSelectOutput>> ModelSelect([FromQuery] ModelSelectInput input)
    {
        List<ModelSelectOutput>? modelPageOutput = await _model.AsQueryable()
            .Where(w => w.IsTemplate == false)
            .WhereIF(input.ModelType > 0, w => w.ModelType == (ModelTypeEnum)input.ModelType)
            .Where(w => w.Enable == true)
            .Select<ModelSelectOutput>()
            .ToListAsync();
        return modelPageOutput;
    }

    /// <summary>
    ///     物模型-详情
    /// </summary>
    /// <returns></returns>
    [HttpGet("/model/detail")]
    public async Task<Model> ModelDetail([FromQuery] BaseId input)
    {
        Model? model = await _model.AsQueryable()
            .Where(f => f.Id == input.Id)
            .Includes(w => w.ModelGroup)
            .Includes(w => w.ThingExample)
            .Includes(w => w.ModelClassify)
            .FirstAsync();

        var releaseAll = await _model.AsSugarClient().Queryable<ModelAttributes>().AnyAsync(w => w.ModelId == input.Id && w.Release == false);
        model.ReleaseAll = !releaseAll;
        if (model == null)
        {
            throw Oops.Oh(ErrorCode.D1002);
        }

        return model;
    }

    /// <summary>
    ///     物模型-新增
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/model/add")]
    public async Task ModelAdd(ModelAddInput input)
    {
        bool isExist = await _model.IsAnyAsync(u => u.Name == input.Name && u.IsTemplate == false);
        if (isExist)
        {
            throw Oops.Oh(ErrorCode.Com1004);
        }

        Model model = input.Adapt<Model>();
        model.Id = YitIdHelper.NextId();
        model.Uuid = RandomExtensions.GetRandomString(10, false);
        //初始化默认数据
        if (model.ModelType is ModelTypeEnum.Device or ModelTypeEnum.Gateway)
        {
            model = CreateModelAttributes(model);
        }

        //默认未分组
        model.ThingGroupId = 390350585901254;
        await _model.AsSugarClient().InsertNav(model)
            .Include(w => w.ThingLinkVariable)
            .ThenInclude(w => w.ModelAttributes)
            .ExecuteCommandAsync();
    }

    /// <summary>
    ///     创建网关系统默认属性
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    private Model CreateModelAttributes(Model model)
    {
        //默认模型数据
        model.ThingAttributes = new List<ModelAttributes>();
        model.ThingLinkVariable = new List<ModelLinkVariable>
        {
            new()
            {
                Id = YitIdHelper.NextId(),
                ModelId = model.Id,
                Desc = "",
                Name = "online_",
                ModelAttributes = new ModelAttributes
                {
                    Id = YitIdHelper.NextId(),
                    Identification = "online_",
                    Name = "在线状态",
                    ModelId = model.Id,
                    DataType = DataTypeEnum.Bool,
                    HistoricalData = HistoricalDataEnum.Change,
                    AttributesValueModel = new AttributesValueModel {ThingLinkVariableName = "online_"},
                    ThingAttributeFilter = new ThingAttributeFilter()
                }
            }
        };
        if (model.ModelType is ModelTypeEnum.Device)
        {
            return model;
        }

        model.ThingLinkVariable.AddRange(new[]
        {
            // 软件版本
            new ModelLinkVariable
            {
                Id = YitIdHelper.NextId(),
                ModelId = model.Id,
                Name = "SoftwareVer",
                ModelAttributes = new ModelAttributes
                {
                    Id = YitIdHelper.NextId(),
                    Identification = "SoftwareVer",
                    Name = "软件版本",
                    ModelId = model.Id,
                    AttributesValueModel = new AttributesValueModel {ThingLinkVariableName = "SoftwareVer"},
                    ThingAttributeFilter = new ThingAttributeFilter()
                }
            },
            // 型号
            new ModelLinkVariable
            {
                Id = YitIdHelper.NextId(),
                ModelId = model.Id,
                Name = "Dto",
                ModelAttributes = new ModelAttributes
                {
                    Id = YitIdHelper.NextId(),
                    Identification = "Dto",
                    Name = "型号",
                    ModelId = model.Id,
                    AttributesValueModel = new AttributesValueModel {ThingLinkVariableName = "Dto"},
                    ThingAttributeFilter = new ThingAttributeFilter()
                }
            },
            // Ip地址
            new ModelLinkVariable
            {
                Id = YitIdHelper.NextId(),
                ModelId = model.Id,
                Name = "Network",
                ModelAttributes = new ModelAttributes
                {
                    Id = YitIdHelper.NextId(),
                    Identification = "Network",
                    Name = "IP地址",
                    Length = 200,
                    ModelId = model.Id,
                    AttributesValueModel = new AttributesValueModel {ThingLinkVariableName = "Network"},
                    ThingAttributeFilter = new ThingAttributeFilter()
                }
            },
            // Cpu使用率
            new ModelLinkVariable
            {
                Id = YitIdHelper.NextId(),
                ModelId = model.Id,
                Name = "CpuRate",
                ModelAttributes = new ModelAttributes
                {
                    Id = YitIdHelper.NextId(),
                    Identification = "CpuRate",
                    Name = "Cpu使用率",
                    ModelId = model.Id,
                    DataType = DataTypeEnum.Number,
                    AttributesValueModel = new AttributesValueModel {ThingLinkVariableName = "CpuRate", Magnification = 1},
                    ThingAttributeFilter = new ThingAttributeFilter()
                }
            },
            // 总内存大小
            new ModelLinkVariable
            {
                Id = YitIdHelper.NextId(),
                ModelId = model.Id,
                Name = "MemTotal",
                ModelAttributes = new ModelAttributes
                {
                    Id = YitIdHelper.NextId(),
                    Identification = "MemTotal",
                    Name = "总内存字节数",
                    ModelId = model.Id,
                    AttributesValueModel = new AttributesValueModel {ThingLinkVariableName = "MemTotal"},
                    ThingAttributeFilter = new ThingAttributeFilter()
                }
            },
            // 内存已使用大小
            new ModelLinkVariable
            {
                Id = YitIdHelper.NextId(),
                ModelId = model.Id,
                Name = "MemUsage",
                ModelAttributes = new ModelAttributes
                {
                    Id = YitIdHelper.NextId(),
                    Identification = "MemUsage",
                    Name = "总已使用内存字节数",
                    ModelId = model.Id,
                    AttributesValueModel = new AttributesValueModel {ThingLinkVariableName = "MemUsage"},
                    ThingAttributeFilter = new ThingAttributeFilter()
                }
            },
            // 内存可用大小
            new ModelLinkVariable
            {
                Id = YitIdHelper.NextId(),
                ModelId = model.Id,
                Name = "MemAvailable",
                ModelAttributes = new ModelAttributes
                {
                    Id = YitIdHelper.NextId(),
                    Identification = "MemAvailable",
                    Name = "空闲内存字节数",
                    ModelId = model.Id,
                    AttributesValueModel = new AttributesValueModel {ThingLinkVariableName = "MemAvailable"},
                    ThingAttributeFilter = new ThingAttributeFilter()
                }
            },
            // 内存使用率
            new ModelLinkVariable
            {
                Id = YitIdHelper.NextId(),
                ModelId = model.Id,
                Name = "MemRate",
                ModelAttributes = new ModelAttributes
                {
                    Id = YitIdHelper.NextId(),
                    Identification = "MemRate",
                    Name = "内存使用率",
                    ModelId = model.Id,
                    DataType = DataTypeEnum.Number,
                    AttributesValueModel = new AttributesValueModel {ThingLinkVariableName = "MemRate"},
                    ThingAttributeFilter = new ThingAttributeFilter()
                }
            },
            // 总磁盘大小
            new ModelLinkVariable
            {
                Id = YitIdHelper.NextId(),
                ModelId = model.Id,
                Name = "DiskSize",
                ModelAttributes = new ModelAttributes
                {
                    Id = YitIdHelper.NextId(),
                    Identification = "DiskSize",
                    Name = "分区字节数",
                    ModelId = model.Id,
                    AttributesValueModel = new AttributesValueModel {ThingLinkVariableName = "DiskSize"},
                    ThingAttributeFilter = new ThingAttributeFilter()
                }
            },
            // 磁盘已使用大小
            new ModelLinkVariable
            {
                Id = YitIdHelper.NextId(),
                ModelId = model.Id,
                Name = "DiskUsed",
                ModelAttributes = new ModelAttributes
                {
                    Id = YitIdHelper.NextId(),
                    Identification = "DiskUsed",
                    Name = "分区已使用字节数",
                    ModelId = model.Id,
                    AttributesValueModel = new AttributesValueModel {ThingLinkVariableName = "DiskUsed"},
                    ThingAttributeFilter = new ThingAttributeFilter()
                }
            },
            // 磁盘剩余可用大小
            new ModelLinkVariable
            {
                Id = YitIdHelper.NextId(),
                ModelId = model.Id,
                Name = "DiskAvailable",
                ModelAttributes = new ModelAttributes
                {
                    Id = YitIdHelper.NextId(),
                    Identification = "DiskAvailable",
                    Name = "分区可用字节数",
                    ModelId = model.Id,
                    AttributesValueModel = new AttributesValueModel {ThingLinkVariableName = "DiskAvailable"},
                    ThingAttributeFilter = new ThingAttributeFilter()
                }
            },
            // 磁盘使用率
            new ModelLinkVariable
            {
                Id = YitIdHelper.NextId(),
                ModelId = model.Id,
                Name = "DiskRate",
                ModelAttributes = new ModelAttributes
                {
                    Id = YitIdHelper.NextId(),
                    Identification = "DiskRate",
                    Name = "分区使用率",
                    ModelId = model.Id,
                    AttributesValueModel = new AttributesValueModel {ThingLinkVariableName = "DiskRate"},
                    ThingAttributeFilter = new ThingAttributeFilter()
                }
            }
        });

        return model;
    }

    /// <summary>
    ///     物模型-模板新增
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/model/template/add")]
    public async Task<long> ModelTemplateAdd(ModelTemplateAddInput input)
    {
        bool isExist = await _model.IsAnyAsync(u => u.Name == input.Name && u.IsTemplate == true);
        if (isExist)
        {
            throw Oops.Oh(ErrorCode.Com1004);
        }

        Model? model = await _model.AsQueryable().Where(f => f.Id == input.Id && f.IsTemplate == true)
            .Includes(w => w.ThingAttributes)
            .Includes(w => w.ThingLinkVariable)
            .Includes(w => w.ThingAlarm)
            .FirstAsync();
        if (model == null)
        {
            throw Oops.Oh("模板已经被删除!");
        }

        model.Uuid = RandomExtensions.GetRandomString(10, false);
        model.Id = YitIdHelper.NextId();
        model.Name = input.Name;
        model.ThingClassifyId = input.ThingClassifyId;
        model.IsTemplate = false;
        // 默认未分组
        model.ThingGroupId = 390350585901254;

        foreach (ModelLinkVariable thingLinkVariable in model.ThingLinkVariable)
        {
            thingLinkVariable.Id = YitIdHelper.NextId();
            thingLinkVariable.ModelId = model.Id;
        }

        foreach (ModelAttributes thingAttributes in model.ThingAttributes)
        {
            thingAttributes.Id = YitIdHelper.NextId();
            thingAttributes.ModelId = model.Id;
        }

        await _model.AsSugarClient().InsertNav(model).Include(w => w.ThingAttributes).Include(w => w.ThingLinkVariable).ExecuteCommandAsync();
        return model.Id;
    }

    /// <summary>
    ///     物模型-修改
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/model/update")]
    public async Task ModelUpdate(Model input)
    {
        bool isExist = await _model.IsAnyAsync(u => u.Name == input.Name && u.Id != input.Id && u.IsTemplate == false);
        if (isExist)
        {
            throw Oops.Oh(ErrorCode.Com1004);
        }

        Model? model = await _model.AsQueryable().Where(f => f.Id == input.Id)
            .Includes(w => w.ThingExample)
            .FirstAsync();
        if (model == null)
        {
            throw Oops.Oh(ErrorCode.D1002);
        }


        model = input.Adapt<Model>();
        // 修改物实例的thingInfo
        if (model.ThingInfo != input.ThingInfo)
        {
            foreach (ModelThing thing in model.ThingExample)
            {
                thing.ThingInfo ??= new Dictionary<string, string>();
                thing.ThingInfo = input.ThingInfo;
            }
        }

        // 修改物实例的thingInfoExtensions
        if (model.ThingInfoExtensions != input.ThingInfoExtensions)
        {
            foreach (ModelThing thing in model.ThingExample)
            {
                thing.ThingInfoExtensions ??= new List<ThingInfoExtension>();
                thing.ThingInfoExtensions = input.ThingInfoExtensions;
            }
        }

        model.ThingInfo = input.ThingInfo;
        model.ThingInfoExtensions = input.ThingInfoExtensions;
        await _model.AsSugarClient().UpdateNav(model, new UpdateNavRootOptions
        {
            IgnoreColumns = new[] { "CreatedTime", "CreatedUserId", "CreatedUserName", "ThingGroupId", "Uuid" }
        })
            .Include(w => w.ThingExample)
            .ExecuteCommandAsync();
    }

    /// <summary>
    ///     物模型-分组-修改
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/model/group/update")]
    public async Task ModelUpdateGroup(ModelUpdateGroupInput input)
    {
        List<Model>? modelList = await _model.AsQueryable().Where(w => input.ModelId.Contains(w.Id)).ToListAsync();
        if (!modelList.Any())
        {
            return;
        }

        foreach (Model? thing in modelList)
        {
            thing.ThingGroupId = input.ThingGroupId;
        }

        await _model.AsSugarClient().Updateable(modelList).UpdateColumns(w => w.ThingGroupId).ExecuteCommandAsync();
    }

    /// <summary>
    ///     物模型-删除
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/model/delete")]
    [UnitOfWork]
    public async Task ModelDelete(BaseId input)
    {
        bool isExist = await _model.AsSugarClient().Queryable<ModelThing>().AnyAsync(u => u.ModelId == input.Id);
        if (isExist)
        {
            throw Oops.Oh(ErrorCode.D1007);
        }

        Model? model = await _model.AsQueryable().Where(f => f.Id == input.Id).FirstAsync();
        if (model == null)
        {
            throw Oops.Oh(ErrorCode.D1002);
        }

        // 只有已发布的模型才需要删除超级表
        if (model.Enable)
        {
            // 删除超级表
            DeleteSTable(model.Uuid);
        }

        // 停止定时任务
        List<StatisticalRuleConf>? statisticalRuleList = await _model.AsSugarClient().Queryable<StatisticalRuleConf>().Where(w => w.ModelId == model.Id).ToListAsync();
        foreach (StatisticalRuleConf statisticalRule in statisticalRuleList)
        {
            _schedulerFactory.RemoveJob(statisticalRule.Id.ToString());
        }

        bool isDel = await _model.AsSugarClient().DeleteNav(model)
            .Include(w => w.ThingAlarm)
            .Include(w => w.ThingAttributes)
            .ExecuteCommandAsync();
        if (isDel)
        {
            // 删除统计规则
            await _model.AsSugarClient().Deleteable<StatisticalRuleConf>().Where(w => w.ModelId == model.Id).ExecuteCommandAsync();
        }
    }

    /// <summary>
    ///     物模型-批量删除
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/model/batchDelete")]
    [UnitOfWork]
    public async Task ModelBatchDelete(BaseId<List<long>> input)
    {
        List<Model> modelList = await _model.AsQueryable().Where(f => input.Id.Contains(f.Id))
            .ToListAsync();
        foreach (Model model in modelList)
        {
            bool isExist = await _model.AsSugarClient().Queryable<ModelThing>().AnyAsync(u => u.ModelId == model.Id);
            if (isExist)
            {
                throw Oops.Oh($"【{model.Name}】:含有关联引用，禁止删除");
            }
        }

        foreach (Model? model in modelList)
        {
            // 只有已发布的模型才需要删除超级表
            if (model.Enable)
            {
                DeleteSTable(model.Uuid);
            }
            
            List<StatisticalRuleConf>? statisticalRuleList = await _model.AsSugarClient().Queryable<StatisticalRuleConf>().Where(w => w.ModelId == model.Id).ToListAsync();
            foreach (StatisticalRuleConf statisticalRule in statisticalRuleList)
            {
                _schedulerFactory.RemoveJob(statisticalRule.Id.ToString());
            }
        }

        await _model.AsSugarClient().DeleteNav(modelList)
            .Include(w => w.ThingAlarm)
            .Include(w => w.ThingAttributes)
            .ExecuteCommandAsync();
    }

    /// <summary>
    ///     物模型-发布
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/model/publish")]
    [UnitOfWork]
    public async Task ModelPublish(EnableInput<long> input)
    {
        Model? model = await _model.AsQueryable().Where(w => w.Id == input.Id).Includes(w => w.ThingAttributes).FirstAsync();
        if (model == null)
        {
            throw Oops.Oh(ErrorCode.D1002);
        }

        model.Enable = input.Enable;

        try
        {
            // 使用CheckTableExists检查超级表是否存在
            bool tableExists = CheckTableExists(model.Uuid);
            if (tableExists)
            {
                // 对于发布操作，始终删除旧表并重新创建
                DeleteSTable(model.Uuid);
            }
            // 创建超级表
            CreateSTable(model);
            Log.Information($"【物模型-发布】: 创建超级表 {model.Uuid}");
        }
        catch (Exception ex)
        {
            // 发生异常时，尝试使用创建方式
            try
            {
                DeleteSTable(model.Uuid); // 先尝试删除，以防表存在但有问题
                CreateSTable(model);
                Log.Error($"【物模型-发布异常】: {ex.Message}, 已使用创建方式处理");
            }
            catch (Exception createEx)
            {
                Log.Error($"【物模型-发布严重异常】: 创建失败 - {createEx.Message}");
                throw Oops.Oh($"物模型发布失败: {createEx.Message}");
            }
        }

        // 修改模型
        await _model.AsUpdateable(model).UpdateColumns(w => new { w.Enable, w.UpdatedTime, w.UpdatedUserId, w.UpdatedUserName }).ExecuteCommandAsync();
        // 标记已经全部发布
        await _model.AsSugarClient().Updateable(model.ThingAttributes).UpdateColumns(w => new { w.Release }).ExecuteCommandAsync();
    }

    /// <summary>
    ///     物模型-重新发布
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/model/rePublish")]
    [UnitOfWork]
    public async Task ModelRePublish(EnableInput<long> input)
    {
        Model? model = await _model.AsQueryable().Where(w => w.Id == input.Id).Includes(w => w.ThingAttributes)
            .FirstAsync();
        if (model == null)
        {
            throw Oops.Oh(ErrorCode.D1002);
        }

        model.Enable = input.Enable;

        try
        {
            // 使用CheckTableExists检查超级表是否存在
            bool tableExists = CheckTableExists(model.Uuid);
            if (!tableExists)
            {
                // 超级表不存在，直接创建
                CreateSTable(model);
                Log.Information($"【物模型-重新发布】: 创建超级表 {model.Uuid}");
            }
            else
            {
                // 先获取当前表结构
                Dictionary<string, string> existingColumns = GetTableColumns(model.Uuid);

                // 如果未能获取到列信息，直接删除重建
                if (existingColumns.Count == 0)
                {
                    DeleteSTable(model.Uuid);
                    CreateSTable(model);
                    Log.Information($"【物模型-重新发布】: 未能获取表结构，使用删除重建方式");
                    return;
                }

                // 找出需要删除的列(已不再物模型中的列)
                List<string> columnsToRemove = GetColumnsToRemove(model, existingColumns);

                // 记录需要删除的列
                Log.Information($"【物模型-重新发布】: 需要删除的列数量: {columnsToRemove.Count}");

                // 如果有需要删除的列数量较多（超过50%的属性），则使用删除重建的方式
                if (columnsToRemove.Count > model.ThingAttributes.Count * 0.5)
                {
                    // 删除模型重新发布
                    DeleteSTable(model.Uuid);
                    // 生成Td超级表
                    CreateSTable(model);
                    Log.Information($"【物模型-重新发布】: 列变更较大，使用删除重建方式更新超级表 {model.Uuid}");
                }
                else
                {
                    // 更新超级表
                    bool updateSuccess = UpdateSTable(model);

                    // 如果更新失败，回退到删除重建方式
                    if (!updateSuccess)
                    {
                        DeleteSTable(model.Uuid);
                        CreateSTable(model);
                        Log.Warning($"【物模型-重新发布】: 增量更新失败，回退到删除重建方式");
                    }
                }
            }
        }
        catch (Exception ex)
        {
            // 发生异常时，回退到原始的删除重建方式
            try
            {
                DeleteSTable(model.Uuid);
                CreateSTable(model);
                Log.Error($"【物模型-重新发布异常】: {ex.Message}, 已回退到删除重建方式");
            }
            catch (Exception deleteEx)
            {
                Log.Error($"【物模型-重新发布严重异常】: 删除重建失败 - {deleteEx.Message}");
                throw Oops.Oh($"物模型重新发布失败: {deleteEx.Message}");
            }
        }

        await _model.AsUpdateable(model).UpdateColumns(w => new { w.Enable, w.UpdatedTime, w.UpdatedUserId, w.UpdatedUserName }).ExecuteCommandAsync();
        
        // 设置所有属性为已发布状态
        foreach (var attribute in model.ThingAttributes)
        {
            attribute.Release = true;
        }
        
        // 标记已经全部发布
        await _model.AsSugarClient().Updateable(model.ThingAttributes).UpdateColumns(w => new { w.Release }).ExecuteCommandAsync();
    }

    /// <summary>
    ///     检查超级表是否存在
    /// </summary>
    /// <param name="stableName">超级表名称</param>
    /// <returns>超级表是否存在</returns>
    [NonAction]
    private bool CheckTableExists(string stableName)
    {
        try
        {
            // 首先尝试使用SHOW TABLE TAGS命令检查表是否存在
            string checkSql = $"SHOW TABLE TAGS FROM cloudiot.{stableName};";
            _executeService.SelectNoLimit(checkSql);
            // 如果没有抛出异常，说明表存在
            return true;
        }
        catch (Exception)
        {
            try
            {
                // 如果上面的方法失败，尝试使用DESCRIBE命令
                string describeSql = $"DESCRIBE {stableName};";
                var result = _executeService.SelectNoLimit(describeSql);
                return result != null && result.Count > 0;
            }
            catch (Exception ex)
            {
                // 如果两种方法都失败，但是不是"表不存在"的错误，记录日志
                if (!ex.Message.Contains("not exist") && !ex.Message.Contains("does not exist"))
                {
                    Log.Error($"【检查超级表是否存在异常】: {ex.Message}");
                }
                return false;
            }
        }
    }

    /// <summary>
    ///     获取超级表的当前列信息
    /// </summary>
    /// <param name="stableName">超级表名称</param>
    /// <returns>列名和列类型的字典</returns>
    [NonAction]
    private Dictionary<string, string> GetTableColumns(string stableName)
    {
        Dictionary<string, string> columns = new Dictionary<string, string>();
        try
        {
            // 使用DESCRIBE命令获取表结构信息，这是最可靠的方式
            string describeSql = $"DESCRIBE {stableName};";
            var result = _executeService.SelectNoLimit(describeSql);
            if (result != null && result.Count > 0)
            {
                foreach (var column in result)
                {
                    // 根据DESCRIBE命令返回的结果格式获取字段名和类型
                    // 注意：不同版本的TDengine可能返回的列名不同
                    string columnName = null;
                    string columnType = null;

                    // 尝试不同的列名格式
                    if (column.ContainsKey("Field") && column.ContainsKey("Type"))
                    {
                        columnName = column["Field"]?.ToString();
                        columnType = column["Type"]?.ToString();
                    }
                    else if (column.ContainsKey("field") && column.ContainsKey("type"))
                    {
                        columnName = column["field"]?.ToString();
                        columnType = column["type"]?.ToString();
                    }
                    else if (column.ContainsKey("field_name") && column.ContainsKey("type"))
                    {
                        columnName = column["field_name"]?.ToString();
                        columnType = column["type"]?.ToString();
                    }

                    if (!string.IsNullOrEmpty(columnName) && !string.IsNullOrEmpty(columnType))
                    {
                        columns[columnName] = columnType;
                    }
                }
            }

            // 日志记录获取到的列信息
            Log.Information($"【获取超级表列信息】: 表'{stableName}'共有{columns.Count}列");
        }
        catch (Exception ex)
        {
            Log.Error($"【获取超级表列信息异常】: {ex.Message}");
        }

        return columns;
    }

    /// <summary>
    ///     获取需要删除的列
    /// </summary>
    /// <param name="model">物模型</param>
    /// <param name="existingColumns">现有列信息</param>
    /// <returns>需要删除的列列表</returns>
    [NonAction]
    private List<string> GetColumnsToRemove(Model model, Dictionary<string, string> existingColumns)
    {
        List<string> columnsToRemove = new List<string>();

        // 获取当前物模型中的所有列名（转小写用于比较）
        HashSet<string> modelColumnNames = new HashSet<string>(
            model.ThingAttributes.Select(a => a.Identification.ToLower()),
            StringComparer.OrdinalIgnoreCase
        );

        // 对每个现有列进行检查
        foreach (var columnEntry in existingColumns)
        {
            string columnName = columnEntry.Key;
            string columnLower = columnName.ToLower();

            // 检查是否是系统列
            if (SystemColumns.IsSystemColumn(columnName))
            {
                continue;
            }

            // 检查列是否存在于模型属性中
            if (!modelColumnNames.Contains(columnLower))
            {
                columnsToRemove.Add(columnName);
            }
        }

        // 最终安全检查：确保没有系统列被误添加到要删除的列表中
        columnsToRemove.RemoveAll(col => SystemColumns.IsSystemColumn(col));

        return columnsToRemove;
    }

    /// <summary>
    ///     增量更新超级表
    /// </summary>
    /// <param name="model">物模型</param>
    /// <returns>更新是否成功</returns>
    [NonAction]
    private bool UpdateSTable(Model model)
    {
        if (!model.ThingAttributes.Any())
        {
            return false;
        }

        try
        {
            // 检查超级表是否存在
            if (!CheckTableExists(model.Uuid))
            {
                // 超级表不存在，直接创建
                CreateSTable(model);
                return true;
            }

            // 获取当前超级表列信息
            Dictionary<string, string> existingColumns = GetTableColumns(model.Uuid);

            // 如果未能获取到列信息，可能是查询失败，此时也直接创建
            if (existingColumns.Count == 0)
            {
                Log.Warning($"【增量更新超级表】: 未能获取到表'{model.Uuid}'的列信息，将删除重建");
                DeleteSTable(model.Uuid);
                CreateSTable(model);
                return true;
            }

            // 创建列名小写到原始大小写的映射，便于后续查找
            Dictionary<string, string> lowerToOriginalColumnMapping = new Dictionary<string, string>();
            foreach (var key in existingColumns.Keys)
            {
                lowerToOriginalColumnMapping[key.ToLower()] = key;
            }

            // 找出需要删除的列
            List<string> columnsToRemove = GetColumnsToRemove(model, existingColumns);

            // 找出需要添加或修改的列（未发布或需要类型变更的列）
            List<ModelAttributes> columnsToHandle = new List<ModelAttributes>();

            foreach (ModelAttributes attribute in model.ThingAttributes)
            {
                string attributeIdentLower = attribute.Identification.ToLower();

                // 只处理未发布的属性
                if (!attribute.Release)
                {
                    columnsToHandle.Add(attribute);
                }
                // 或者已发布但类型发生变更的属性
                else if (lowerToOriginalColumnMapping.ContainsKey(attributeIdentLower))
                {
                    string originalColumnName = lowerToOriginalColumnMapping[attributeIdentLower];
                    string requiredType = GetColumnType(attribute);
                    string existingType = existingColumns[originalColumnName].ToLower();

                    // 检查类型是否变更
                    bool typeChanged = false;

                    // 特殊处理nchar类型，比较其长度
                    if (existingType.StartsWith("nchar") && requiredType.StartsWith("nchar"))
                    {
                        int existingLength = ExtractNcharLength(existingType);
                        int requiredLength = ExtractNcharLength(requiredType);

                        // 如果长度不同，视为类型变更
                        if (existingLength != requiredLength)
                        {
                            typeChanged = true;
                        }
                    }
                    // 其他情况，直接比较类型字符串
                    else if (existingType != requiredType.ToLower())
                    {
                        typeChanged = true;
                    }

                    if (typeChanged)
                    {
                        columnsToHandle.Add(attribute);
                    }
                }
                // 属性已发布但不在现有表中，需要添加
                else if (attribute.Release && !lowerToOriginalColumnMapping.ContainsKey(attributeIdentLower))
                {
                    columnsToHandle.Add(attribute);
                }
            }

            bool hasChanges = false;

            // 处理需要添加或修改的列
            foreach (ModelAttributes attribute in columnsToHandle)
            {
                string columnType = GetColumnType(attribute);
                string attributeIdentLower = attribute.Identification.ToLower();

                // 如果列已存在且类型需要变更，先删除后添加
                if (lowerToOriginalColumnMapping.ContainsKey(attributeIdentLower))
                {
                    // 使用原始大小写的列名进行删除操作
                    string originalColumnName = lowerToOriginalColumnMapping[attributeIdentLower];

                    // 检查是否是系统列，系统列不应通过这种方式修改
                    if (SystemColumns.IsSystemColumn(originalColumnName))
                    {
                        continue;
                    }
                    // 删除列
                    DeleteTableColumn(model.Uuid, originalColumnName);
               
                }

                // 检查是否是系统列，系统列不应通过这种方式添加
                if (SystemColumns.IsSystemColumn(attribute.Identification))
                {
                    continue;
                }

                // 添加列
                bool addResult = AddTableColumn(model.Uuid, attribute.Identification, columnType);
                if (addResult)
                {
                    // 标记为已发布
                    attribute.Release = true;
                    hasChanges = true;
                }
            }

            // 删除不再需要的列
            foreach (string columnName in columnsToRemove)
            {
                // 最终安全检查：确保不删除系统列
                if (SystemColumns.IsSystemColumn(columnName))
                {
                    continue;
                }

                // 执行删除操作
                bool deleteResult = DeleteTableColumn(model.Uuid, columnName);
                if (deleteResult)
                {
                    hasChanges = true;
                }
            }

            // 如果没有变更，记录日志
            if (!hasChanges)
            {
                Log.Information($"【增量更新超级表】: 超级表 {model.Uuid} 没有变更");
            }

            return true;
        }
        catch (Exception ex)
        {
            Log.Error($"【增量更新超级表异常】: {ex.Message}");
            return false;
        }
    }

    /// <summary>
    ///     获取属性对应的列类型
    /// </summary>
    /// <param name="attribute">物模型属性</param>
    /// <returns>列类型</returns>
    [NonAction]
    private string GetColumnType(ModelAttributes attribute)
    {
        string columnType;
        switch (attribute.DataType)
        {
            case DataTypeEnum.Number:
                columnType = "float";
                break;
            case DataTypeEnum.Int:
                columnType = "BigInt";
                break;
            case DataTypeEnum.Bool:
                columnType = "BOOL";
                break;
            case DataTypeEnum.Binary:
            case DataTypeEnum.Json:
            case DataTypeEnum.Array:
            case DataTypeEnum.String:
            default:
                columnType = $"nchar({(attribute.Length == 0 ? 64 : attribute.Length)})";
                break;
        }

        return columnType;
    }

    /// <summary>
    ///     添加超级表列
    /// </summary>
    /// <param name="stableName">超级表名称</param>
    /// <param name="columnName">列名</param>
    /// <param name="columnType">列类型</param>
    /// <returns>操作是否成功</returns>
    [NonAction]
    private bool AddTableColumn(string stableName, string columnName, string columnType)
    {
        // 如果列名或列类型为空，直接返回失败
        if (string.IsNullOrEmpty(columnName) || string.IsNullOrEmpty(columnType))
        {
            return false;
        }

        try
        {
            // 执行SQL操作
            string addSql = $"ALTER STABLE {stableName} ADD COLUMN `{columnName}` {columnType};";

            bool success = _executeService.ExecuteCommandNoTry(addSql);
            return success;
        }
        catch (Exception)
        {
            return false;
        }
    }

    /// <summary>
    ///     删除超级表列
    /// </summary>
    /// <param name="stableName">超级表名称</param>
    /// <param name="columnName">列名</param>
    /// <returns>操作是否成功</returns>
    [NonAction]
    private bool DeleteTableColumn(string stableName, string columnName)
    {
        // 如果列名为空，直接返回失败
        if (string.IsNullOrEmpty(columnName))
        {
            Log.Error("【删除超级表列】: 列名不能为空");
            return false;
        }

        try
        {
            // 系统列保护：阻止删除系统列
            if (SystemColumns.IsSystemColumn(columnName))
            {
                Log.Warning($"【系统列保护-删除操作】: 阻止删除系统列 '{columnName}'");
                return false;
            }

            // 执行SQL操作
            string dropSql = $"ALTER STABLE {stableName} DROP COLUMN `{columnName}`;";
            Log.Information($"【删除超级表列】: SQL: {dropSql} (列名: '{columnName}')");

            bool success = _executeService.ExecuteCommandNoTry(dropSql);

            if (success)
            {
                Log.Information($"【删除超级表列成功】: 已删除列 '{columnName}'");
            }
            else
            {
                Log.Warning($"【删除超级表列失败】: 删除列 '{columnName}' 失败");
            }

            return success;
        }
        catch (Exception ex)
        {
            Log.Error($"【删除超级表列异常】: 删除列 '{columnName}' 时发生异常: {ex.Message}");
            return false;
        }
    }

    /// <summary>
    ///     从nchar类型字符串中提取长度
    /// </summary>
    /// <param name="ncharType">nchar类型字符串，如 "nchar(64)"</param>
    /// <returns>nchar长度，如64，默认为64</returns>
    [NonAction]
    private int ExtractNcharLength(string ncharType)
    {
        try
        {
            // 从nchar(X)中提取X
            if (ncharType.Contains("(") && ncharType.Contains(")"))
            {
                string lengthStr = ncharType.Substring(ncharType.IndexOf("(") + 1);
                lengthStr = lengthStr.Substring(0, lengthStr.IndexOf(")"));
                if (int.TryParse(lengthStr, out int length))
                {
                    return length;
                }
            }
        }
        catch
        {
            // 解析失败，返回默认值
        }

        return 64; // 默认长度
    }

    /// <summary>
    ///     物模型-修改模型名称
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/model/reName")]
    public async Task ModelReName(BaseReName input)
    {
        bool isExist = await _model.IsAnyAsync(u => u.Name == input.Name && u.Id != input.Id && u.IsTemplate == false);
        if (isExist)
        {
            throw Oops.Oh(ErrorCode.Com1004);
        }

        Model? model = await _model.AsQueryable().FirstAsync(f => f.Id == input.Id);
        if (model == null)
        {
            throw Oops.Oh(ErrorCode.D1002);
        }

        model.Name = input.Name;
        await _model.UpdateAsync(model);
    }

    /// <summary>
    ///     生成超级表
    /// </summary>
    /// <param name="model"></param>
    [NonAction]
    private void CreateSTable(Model model)
    {
        if (!model.ThingAttributes.Any())
        {
            return;
        }

        string sql = $"CREATE STABLE IF NOT EXISTS {model.Uuid} (ts timestamp,`cloudTime` timestamp";
        foreach (ModelAttributes thingAttribute in model.ThingAttributes)
        {
            sql += ", `" + thingAttribute.Identification + "` ";
            switch (thingAttribute.DataType)
            {
                case DataTypeEnum.Number:
                    sql += "float";
                    break;
                case DataTypeEnum.Int:
                    sql += "BigInt";
                    break;
                case DataTypeEnum.Bool:
                    sql += "BOOL";
                    break;
                case DataTypeEnum.Binary:
                case DataTypeEnum.Json:
                case DataTypeEnum.Array:
                case DataTypeEnum.String:
                default:
                    sql += $"nchar({(thingAttribute.Length == 0 ? 64 : thingAttribute.Length)})";
                    break;
            }

            thingAttribute.Release = true;
        }

        sql += ") TAGS(deviceName nchar(64));";

        Log.Information($"【生成超级表】:{sql}");
        _executeService.ExecuteCommandNoTry(sql);
    }

    /// <summary>
    ///     删除超级表
    /// </summary>
    /// <param name="uuid"></param>
    [NonAction]
    private void DeleteSTable(string uuid)
    {
        string sql = $"DROP STABLE IF EXISTS  {uuid};";
        _ = _executeService.ExecuteCommandNoTry(sql);
    }

    /// <summary>
    ///     导出物模型
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/model/exPort")]
    public async Task<IActionResult> ExPort(BaseId input)
    {
        Model? model = await _model.AsQueryable().Where(w => w.Id == input.Id)
            .Includes(w => w.ModelClassify)
            .Includes(w => w.ModelGroup)
            .Includes(w => w.ThingAttributes)
            .Includes(w => w.ThingLinkVariable)
            .Includes(w => w.ThingAlarm)
            .FirstAsync();
        if (model == null)
        {
            throw Oops.Oh(ErrorCode.D1002);
        }

        string? identification = "";
        string? key = "";

        switch (model.ModelType)
        {
            case ModelTypeEnum.Device:
                identification = model.Device?.Identification;
                key = model.Device?.Key;
                break;
            case ModelTypeEnum.Compound:
                break;
            case ModelTypeEnum.Gateway:
                identification = model.Gateway?.Identification;
                key = model.Gateway?.Key;
                break;
        }

        var value = new
        {
            thing = new[]
            {
                new
                {
                    model.Name, ThingClassify = model.ModelClassify?.Name ?? "", Identification = identification, Password = key,
                    ModelType = model.ModelType.GetDescription(),
                    Tag = JSON.Serialize(model.Tags), Desc = model.Remark, GroupName = model.ModelGroup?.Name
                }
            },
            attribute = model.ThingAttributes.Select(s => new
            {
                s.Name,
                s.Identification,
                DataType = s.DataType.GetDescription(),
                ReadType = s.ReadType.GetDescription(),
                AttributesValueSource = s.AttributesValueSource.GetDescription(),
                TriggerMethod = s.TriggerMethod != 0 ? s.TriggerMethod.GetDescription() : TriggerMethodEnum.All.GetDescription(),
                s.Period,
                s.Internal,
                HistoricalData = s.HistoricalData.GetDescription(),
                RetentionMethod = s.RetentionMethod.GetDescription(),
                Tag = JSON.Serialize(s.Tags),
                s.Desc,
                s.ThingAttributeFilter.Min,
                s.ThingAttributeFilter.Max,
                MinFilterType = s.ThingAttributeFilter.MinFilterType.GetDescription(),
                s.ThingAttributeFilter.SetMin,
                MaxFilterType = s.ThingAttributeFilter.MaxFilterType.GetDescription(),
                s.ThingAttributeFilter.Save,
                s.ThingAttributeFilter.SetMax,
                s.AttributesValueModel?.Magnification,
                s.AttributesValueModel?.Base,
                s.Custom,
                s.AttributesScriptModel?.TimeWindow,
                s.AttributesScriptModel?.Order,
                s.Unit,
                Value = s.AttributesValueSource == AttributesValueSourceEnum.Variable ? s.AttributesValueModel?.ThingLinkVariableName ?? "" :
                    s.AttributesValueSource == AttributesValueSourceEnum.Rules ? s.AttributesScriptModel?.Script ?? "" : s.AttributesWriteModel?.Value ?? ""
            }),
            thingAlarm = model.ThingAlarm.Select(s => new
            {
                s.Name,
                s.Identification,
                s.Desc,
                AlarmLevel = s.AlarmLevel.GetDescription(),
                AlarmRuleType = s.AlarmRuleType.GetDescription(),
                SimpleAlarmRulesName = s.SimpleAlarmRulesModel?.ThingAttributeName,
                SimpleAlarmRulesSymbol = (s.SimpleAlarmRulesModel?.Symbol).GetDescription(),
                SimpleAlarmRulesValueType = (s.SimpleAlarmRulesModel?.AlarmRulesValueType).GetDescription(),
                s.IntervalTime,
                s.DelayTime,
                s.ContinuousTimeUnit,
                AlarmType = s.AlarmType.GetDescription(),
                s.DisarmedDelayTime,
                s.AlarmHandDisarmed,
                s.ContinuousTime,
                ModelAttributesIdList = s.ModelAttributesId != null ? JSON.Serialize(s.ModelAttributesId) : "",
                s.AlarmReason,
                s.Solution,
                Tag = JSON.Serialize(s.Tags),
                s.CustomAlarmTypeId,
                AlarmDisarmed = s.AlarmDisarmed.GetDescription(),
                SimpleAlarmDisarmedName = s.SimpleAlarmDisarmedModel?.ThingAttributeName,
                SimpleAlarmDisarmedSymbol = s.SimpleAlarmDisarmedModel?.Symbol != null ? (s.SimpleAlarmDisarmedModel?.Symbol).GetDescription() : "",
                SimpleAlarmDisarmedType = s.SimpleAlarmDisarmedModel?.AlarmRulesValueType != null ? (s.SimpleAlarmDisarmedModel?.AlarmRulesValueType).GetDescription() : "",
                SimpleValue = s.SimpleAlarmDisarmedModel?.Value,
                s.Expression,
                s.DisarmedExpression
            }),
            link = model.ThingLinkVariable.Select(s => new
            {
                s.Name,
                s.Desc
            })
        };

        try
        {
            MemoryStream memoryStream = new();
            await memoryStream.SaveAsByTemplateAsync("Templates/物模型导入模板.xlsx", value);
            memoryStream.Seek(0, SeekOrigin.Begin);

            return new FileStreamResult(memoryStream, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
            {
                FileDownloadName = $"{DateTime.Now:yyyyMMddHHmmssfff}.xlsx"
            };
        }
        catch (Exception e)
        {
            throw Oops.Bah("导出失败！");
        }
    }

    /// <summary>
    ///     导出物模型-模板导出
    /// </summary>
    /// <returns></returns>
    [HttpPost("/model/exPortTemplate")]
    public async Task<IActionResult> ExPortTemplate()
    {
        var value = new
        {
            thing = new[]
            {
                new
                {
                    Name = "示例", ThingClassify = "机床", Identification = "", Password = "", ModelType = "设备",
                    Tag = "[\"机床设备\"]", Desc = "这是描述", GroupName = "未分组"
                }
            },
            attribute = new[]
            {
                new
                {
                    Name = "在线状态",
                    Identification = "_online_",
                    DataType = "Boolean",
                    ReadType = "只读",
                    AttributesValueSource = "规则指定",
                    TriggerMethod = "模型内有任一属性有工况值上报即触发",
                    Period = 1000,
                    Internal = false,
                    HistoricalData = "上报保存",
                    RetentionMethod = "保留2位(例如9.99)",
                    Tag = "[\"设备状态\"]",
                    Desc = "这是描述",
                    Min = 0,
                    Max = 0,
                    MinFilterType = "原值",
                    SetMin = 0,
                    MaxFilterType = "原值",
                    SetMax = 0,
                    Magnification = 0,
                    Base = 1,
                    Custom = "",
                    TimeWindow = false,
                    Order = 0,
                    Unit = "位",
                    Value = "TEST",
                    Save = true
                }
            },
            thingAlarm = new[]
            {
                new
                {
                    Name = "状态异常报警",
                    Identification = "statusError",
                    Desc = "状态错误报警",
                    AlarmLevel = "一般",
                    AlarmRuleType = "简单规则",
                    SimpleAlarmRulesName = "status",
                    SimpleAlarmRulesSymbol = "小于",
                    SimpleAlarmRulesValueType = "值",
                    IntervalTime = 1000,
                    DelayTime = 60,
                    ContinuousTimeUnit = "秒",
                    AlarmType = "仅报警一次",
                    DisarmedDelayTime = 1000,
                    AlarmHandDisarmed = true,
                    ContinuousTime = 1000,
                    ModelAttributesIdList = "",
                    AlarmReason = "出现问题了",
                    Solution = "问题修复",
                    Tag = "[\"状态错误\"]",
                    CustomAlarmTypeId = "",
                    AlarmDisarmed = "自动解除",
                    SimpleAlarmDisarmedName = "status",
                    SimpleAlarmDisarmedSymbol = "大于",
                    SimpleAlarmDisarmedType = "值",
                    Expression = "status<10",
                    DisarmedExpression = "status>10",
                    SimpleValue = "10"
                }
            },
            link = new[]
            {
                new
                {
                    Name = "online_",
                    Desc = ""
                }
            }
        };

        try
        {
            MemoryStream memoryStream = new();
            await memoryStream.SaveAsByTemplateAsync("Templates/物模型导入模板.xlsx", value);
            memoryStream.Seek(0, SeekOrigin.Begin);

            return new FileStreamResult(memoryStream, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
            {
                FileDownloadName = $"{DateTime.Now:yyyyMMddHHmmssfff}.xlsx"
            };
        }
        catch
        {
            throw Oops.Bah("导出失败！");
        }
    }

    /// <summary>
    ///     导入物模型
    /// </summary>
    /// <returns></returns>
    [HttpPost("/model/inPort")]
    [UnitOfWork]
    public async Task<ExportOutput> InPortTemplate([FromQuery] InPortTemplateInput input)
    {
        MemoryStream stream = new();
        await input.File.CopyToAsync(stream);
        //返回结果
        ExportOutput result = new() { ErrorColumn = new List<ErrorColumn>() };
        Model model = new();
        //当前所在行
        int line = 2;
        foreach (ModelInPort? thingInPort in await stream.QueryAsync<ModelInPort>("模型信息"))
        {
            try
            {
                bool success = CheckInPortDataTem(thingInPort, line, result);
                if (!success)
                {
                    return result;
                }

                model = thingInPort.Adapt<Model>();
                model.Uuid = RandomExtensions.GetRandomString(10, false);

                model.Id = YitIdHelper.NextId();
                model.Tags = JSON.Deserialize<List<string>>(thingInPort.Tag);

                switch (model.ModelType)
                {
                    case ModelTypeEnum.Device:
                        model.Device = new Device
                        {
                            Identification = thingInPort.Identification,
                            Key = thingInPort.Password
                        };
                        break;
                    case ModelTypeEnum.Gateway:
                        model.Gateway = new Gateway
                        {
                            Identification = thingInPort.Identification,
                            Key = thingInPort.Password
                        };
                        break;
                }

                model.Remark = thingInPort.Desc;
                ModelClassify? thingClassify = await _model.AsSugarClient().Queryable<ModelClassify>().Where(w => w.Name == thingInPort.ThingClassifyName).FirstAsync();
                if (thingClassify != null)
                {
                    model.ThingClassifyId = thingClassify.Id;
                }

                ModelGroup? thingGroup = await _model.AsSugarClient().Queryable<ModelGroup>().Where(w => w.Name == thingInPort.GroupName).FirstAsync();
                if (thingGroup != null)
                {
                    model.ThingGroupId = thingGroup.Id;
                }

                line++;
            }
            catch (Exception e)
            {
                throw Oops.Oh("【模型信息】 " + e.Message);
            }
        }

        model.ThingAttributes ??= new List<ModelAttributes>();
        model.ThingAlarm ??= new List<ModelAlarm>();
        model.ThingLinkVariable ??= new List<ModelLinkVariable>();
        // 默认未分组
        model.ThingGroupId = 390350585901254;

        line = 2;
        foreach (ThingAttributesInPort? thingAttributesInPort in await stream.QueryAsync<ThingAttributesInPort>("属性"))
        {
            try
            {
                ModelAttributes thingAttribute = thingAttributesInPort.Adapt<ModelAttributes>();
                thingAttribute.ModelId = model.Id;
                thingAttribute.Id = YitIdHelper.NextId();
                thingAttribute.Tags = thingAttributesInPort.Tag.IsNotEmptyOrNull() ? JSON.Deserialize<List<string>>(thingAttributesInPort.Tag) : new List<string>();
                switch (thingAttribute.AttributesValueSource)
                {
                    case AttributesValueSourceEnum.Variable:
                        thingAttribute.AttributesValueModel = new AttributesValueModel
                        {
                            ThingLinkVariableName = thingAttributesInPort.Value,
                            Base = thingAttributesInPort.Base,
                            Magnification = thingAttributesInPort.Magnification
                        };
                        break;
                    case AttributesValueSourceEnum.Rules:
                        thingAttribute.AttributesScriptModel = new AttributesScriptModel
                        {
                            TimeWindow = thingAttributesInPort.TimeWindow,
                            Script = thingAttributesInPort.Value,
                            Order = thingAttributesInPort.Order
                        };
                        break;
                    case AttributesValueSourceEnum.Hand:
                        thingAttribute.AttributesWriteModel = new AttributesWriteModel
                        {
                            Value = thingAttributesInPort.Value
                        };
                        break;
                }

                model.ThingAttributes.Add(thingAttribute);
                line++;
            }
            catch (Exception e)
            {
                throw Oops.Oh("【属性】 " + e.Message);
            }
        }

        line = 2;
        foreach (ThingAlarmInPort? thingAlarmInPort in await stream.QueryAsync<ThingAlarmInPort>("报警"))
        {
            try
            {
                ModelAlarm thingAlarm = thingAlarmInPort.Adapt<ModelAlarm>();
                thingAlarm.Id = YitIdHelper.NextId();
                thingAlarm.ModelId = model.Id;
                thingAlarm.Tags = thingAlarmInPort.Tag.IsNotEmptyOrNull() ? JSON.Deserialize<List<string>>(thingAlarmInPort.Tag) : new List<string>();
                thingAlarm.ModelAttributesId = thingAlarmInPort.ModelAttributesIdList.IsNotEmptyOrNull() ? JSON.Deserialize<List<string>>(thingAlarmInPort.ModelAttributesIdList) : new List<string>();
                thingAlarm.CustomAlarmTypeIdBool = thingAlarmInPort.CustomAlarmTypeId.IsNotEmptyOrNull();
                switch (thingAlarm.AlarmRuleType)
                {
                    case AlarmRulesTypeEnum.Simple:
                        thingAlarm.Config = JSON.Serialize(new AlarmRulesModel
                        {
                            ThingAttributeName = thingAlarmInPort.SimpleAlarmRulesName,
                            Symbol = thingAlarmInPort.SimpleAlarmRulesSymbol,
                            AlarmRulesValueType = thingAlarmInPort.SimpleAlarmRulesValueType,
                            Value = thingAlarmInPort.SimpleValue
                        });
                        break;
                    case AlarmRulesTypeEnum.AlarmRules:
                        break;
                    case AlarmRulesTypeEnum.Custom:
                        break;
                }

                thingAlarm.DisarmedDelayTimeBool = thingAlarmInPort.DisarmedDelayTime > 0;


                switch (thingAlarm.AlarmDisarmed)
                {
                    case AlarmDisarmedEnum.Simple:
                        thingAlarm.AlarmDisarmedConfig = JSON.Serialize(new AlarmRulesModel
                        {
                            ThingAttributeName = thingAlarmInPort.SimpleAlarmRulesName,
                            Symbol = thingAlarmInPort.SimpleAlarmRulesSymbol,
                            AlarmRulesValueType = thingAlarmInPort.SimpleAlarmRulesValueType,
                            Value = thingAlarmInPort.SimpleValue
                        });
                        break;
                    case AlarmDisarmedEnum.AlarmRules:
                        break;
                    case AlarmDisarmedEnum.Custom:
                        break;
                }

                model.ThingAlarm.Add(thingAlarm);
                line++;
            }
            catch (Exception e)
            {
                throw Oops.Oh("【报警】 " + e.Message);
            }
        }

        line = 2;
        foreach (ThingLinkVariableInPort? thingInPort in await stream.QueryAsync<ThingLinkVariableInPort>("连接变量"))
        {
            try
            {
                ModelLinkVariable thingLinkVariable = thingInPort.Adapt<ModelLinkVariable>();
                thingLinkVariable.Id = YitIdHelper.NextId();
                thingLinkVariable.ModelId = model.Id;
                model.ThingLinkVariable.Add(thingLinkVariable);
                line++;
            }
            catch (Exception e)
            {
                throw Oops.Oh("【连接变量】 " + e.Message);
            }
        }

        await _model.AsSugarClient().InsertNav(model)
            .Include(w => w.ThingAttributes)
            .Include(w => w.ThingAlarm)
            .Include(w => w.ThingLinkVariable)
            .ExecuteCommandAsync();

        return result;
    }

    /// <summary>
    ///     检查物模型导入必填项
    /// </summary>
    private bool CheckInPortDataTem(ModelInPort deviceVariableExPort, int line, ExportOutput result)
    {
        if (deviceVariableExPort.Name.IsNullOrEmpty())
        {
            result.ErrorCount += 1;
            result.ErrorColumn.Add(new ErrorColumn { Error = $"【模型信息】 第【{line}】行 【Name】 不能是空！ ", Line = line });
            return false;
        }

        return true;
    }
}