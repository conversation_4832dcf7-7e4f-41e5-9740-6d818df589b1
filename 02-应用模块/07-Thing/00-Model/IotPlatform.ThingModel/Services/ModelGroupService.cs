namespace IotPlatform.ThingModel.Services;

/// <summary>
///     物模型-分组
///     版 本:V5.0.0
///     版 权:杭州峰回科技有限公司
///     作 者:蒋状
///     日 期:2023-07-07
/// </summary>
[ApiDescriptionSettings("物模型")]
public class ModelGroupService : IDynamicApiController, ITransient
{
    private readonly ISqlSugarRepository<ModelGroup> _thingGroup;

    /// <summary>
    /// </summary>
    /// <param name="thingGroup"></param>
    public ModelGroupService(ISqlSugarRepository<ModelGroup> thingGroup)
    {
        _thingGroup = thingGroup;
    }

    /// <summary>
    ///     物模型-物模型分组-树
    /// </summary>
    /// <returns></returns>
    [HttpGet("/modelGroup/tree")]
    public async Task<List<ModelGroup>> ThingGroupTree([FromQuery] ThingGroupTreeInput input)
    {
        List<ModelGroup>? list = await _thingGroup.AsQueryable()
            .Where(w => w.ThingGroupType == input.ThingGroupType)
            .Includes(x => x.Parent)
            .ToTreeAsync(x => x.Children, x => x.ParentId, 0);
        return list;
    }

    /// <summary>
    ///     物模型-分组--新增
    /// </summary>
    /// <returns></returns>
    [HttpPost("/modelGroup/add")]
    public async Task ThingGroupAdd(ThingGroupInput input)
    {
        bool isExist = await _thingGroup.IsAnyAsync(u => u.Name == input.Name && u.ThingGroupType == input.ThingGroupType);
        if (isExist)
        {
            throw Oops.Oh(ErrorCode.Com1004);
        }

        ModelGroup thingGroup = input.Adapt<ModelGroup>();
        await _thingGroup.InsertAsync(thingGroup);
    }

    /// <summary>
    ///     物模型-分组--修改
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/modelGroup/update")]
    public async Task ThingGroupUpdate(ThingGroupUpdateInput input)
    {
        bool isExist = await _thingGroup.IsAnyAsync(u => u.Name == input.Name && u.Id != input.Id && u.ThingGroupType == input.ThingGroupType);
        if (isExist)
        {
            throw Oops.Oh(ErrorCode.Com1004);
        }

        ModelGroup? thingGroup = await _thingGroup.GetFirstAsync(f => f.Id == input.Id);
        if (thingGroup == null)
        {
            throw Oops.Oh(ErrorCode.D1002);
        }

        thingGroup = input.Adapt<ModelGroup>();
        await _thingGroup.UpdateAsync(thingGroup);
    }

    /// <summary>
    ///     物模型-分组--删除
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/modelGroup/delete")]
    public async Task ThingGroupDelete(BaseId input)
    {
        ModelGroup? thingGroup = await _thingGroup.AsQueryable().FirstAsync(f => f.Id == input.Id);
        if (thingGroup == null)
        {
            throw Oops.Oh(ErrorCode.D1002);
        }

        if (await _thingGroup.AsSugarClient().Queryable<Thing.Entity.Model>().AnyAsync(a => a.ThingGroupId == input.Id))
        {
            throw Oops.Oh(ErrorCode.D1007);
        }

        if (await _thingGroup.AsSugarClient().Queryable<ModelGroup>().AnyAsync(a => a.ParentId == input.Id))
        {
            throw Oops.Oh(ErrorCode.D1007);
        }

        await _thingGroup.DeleteAsync(thingGroup);
    }
}