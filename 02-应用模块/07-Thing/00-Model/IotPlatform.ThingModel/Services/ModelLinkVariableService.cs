namespace IotPlatform.ThingModel.Services;

/// <summary>
///     物模型-属性-连接变量
///     版 本:V5.0.0
///     版 权:杭州峰回科技有限公司
///     作 者:蒋状
///     日 期:2023-07-07
/// </summary>
[ApiDescriptionSettings("物模型")]
public class ModelLinkVariableService : IDynamicApiController, ITransient
{
    private readonly ISqlSugarRepository<ModelLinkVariable> _thingLinkVariable;

    /// <summary>
    /// </summary>
    /// <param name="thingLinkVariable"></param>
    public ModelLinkVariableService(ISqlSugarRepository<ModelLinkVariable> thingLinkVariable)
    {
        _thingLinkVariable = thingLinkVariable;
    }

    /// <summary>
    ///     物模型-属性-连接变量-列表
    /// </summary>
    /// <returns></returns>
    [HttpGet("/modelLinkVariable/page")]
    public async Task<SqlSugarPagedList<ModelLinkVariable>> ThingLinkVariablePage([FromQuery] ThingLinkVariablePageInput input)
    {
        SqlSugarPagedList<ModelLinkVariable> thingLinkVariablePage = await _thingLinkVariable.AsQueryable()
            .Where(w => w.ModelId == input.ModelId)
            .WhereIF(input.SearchValue.IsNotEmptyOrNull(), u => u.Name.Contains(input.SearchValue) || u.Id.ToString().Contains(input.SearchValue))
            .Includes(w => w.ModelAttributes)
            .OrderByIF(!string.IsNullOrEmpty(input.Field), input.Field + " " + input.Order)
            .ToPagedListAsync(input.PageNo, input.PageSize);
        return thingLinkVariablePage;
    }

    /// <summary>
    ///     物模型-属性-连接变量-下拉
    /// </summary>
    /// <returns></returns>
    [HttpGet("/modelLinkVariable/select")]
    public async Task<List<ThingLinkVariableOutput>> ThingLinkVariableSelect([FromQuery] ThingLinkVariableSelectInput input)
    {
        List<ThingLinkVariableOutput>? thingLinkVariablePage = await _thingLinkVariable.AsQueryable()
            .Where(w => w.ModelId == input.ModelId)
            .Where(w => w.ModelAttributesId == 0)
            .Select<ThingLinkVariableOutput>()
            .ToListAsync();
        return thingLinkVariablePage;
    }

    /// <summary>
    ///     物模型-属性-连接变量--新增
    /// </summary>
    /// <returns></returns>
    [HttpPost("/modelLinkVariable/add")]
    public async Task ThingLinkVariableAdd(ThingLinkVariableInput input)
    {
        bool isExist = await _thingLinkVariable.IsAnyAsync(u => u.Name == input.Name && u.ModelId == input.ModelId);
        if (isExist)
        {
            throw Oops.Oh(ErrorCode.Com1004);
        }

        ModelLinkVariable thingLinkVariable = input.Adapt<ModelLinkVariable>();
        await _thingLinkVariable.InsertAsync(thingLinkVariable);
    }

    /// <summary>
    ///     物模型-属性-连接变量--修改
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/modelLinkVariable/update")]
    public async Task ThingLinkVariableUpdate(ThingLinkVariableUpdateInput input)
    {
        bool isExist = await _thingLinkVariable.IsAnyAsync(u => u.Name == input.Name && u.Id != input.Id);
        if (isExist)
        {
            throw Oops.Oh(ErrorCode.Com1004);
        }

        ModelLinkVariable? thingLinkVariable = await _thingLinkVariable.GetFirstAsync(f => f.Id == input.Id);
        if (thingLinkVariable == null)
        {
            throw Oops.Oh(ErrorCode.D1002);
        }

        thingLinkVariable = input.Adapt<ModelLinkVariable>();
        await _thingLinkVariable.UpdateAsync(thingLinkVariable);
    }

    /// <summary>
    ///     物模型-属性-连接变量--删除
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/modelLinkVariable/delete")]
    public async Task ThingLinkVariableDelete(BaseId<List<long>> input)
    {
        await _thingLinkVariable.DeleteAsync(w => input.Id.Contains(w.Id));
    }
}