namespace IotPlatform.Thing.Entity.SeedData;

/// <summary>
///     物模型-属性种子数据
/// </summary>
public class ModelAttributesSeedData : ISqlSugarEntitySeedData<ModelAttributes>
{
    public IEnumerable<ModelAttributes> HasData()
    {
        Func<PropertyInfo, object, object> valueProvider = (property, oldValue) =>
        {
            return property.Name switch
            {
                "AttributesValueModel" when oldValue != null => JsonSerializer.Deserialize<AttributesValueModel>(oldValue.ToString()),
                "AttributesScriptModel" when oldValue != null => JsonSerializer.Deserialize<AttributesScriptModel>(oldValue.ToString()),
                "AttributesWriteModel" when oldValue != null => JsonSerializer.Deserialize<AttributesWriteModel>(oldValue.ToString()),
                "ThingAttributeFilter" when oldValue != null => JsonSerializer.Deserialize<ThingAttributeFilter>(oldValue.ToString()),
                _ => oldValue
            };
        };

        IEnumerable<ModelAttributes>? model = SeedDataUtil.GetSeedData<ModelAttributes>("business_modelattributes.json", valueProvider);
        return model;
    }
}