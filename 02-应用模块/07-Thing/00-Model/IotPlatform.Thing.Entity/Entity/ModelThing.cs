using DateTime = System.DateTime;

namespace IotPlatform.Thing.Entity;

/// <summary>
///     物实例
/// </summary>
[SugarTable("business_thing", "物实例")]
public class ModelThing : EntityTenant
{
    /// <summary>
    ///     物模型Id
    /// </summary>
    [SugarColumn(ColumnDescription = "物模型Id")]
    public long ModelId { get; set; }

    /// <summary>
    ///     物模型类型：1;设备；2：复合物；3：网关
    /// </summary>
    [SugarColumn(ColumnDescription = "物模型类型：1;设备；2：复合物；3：网关")]
    public ModelTypeEnum ModelType { get; set; }

    /// <summary>
    ///     实例名称
    /// </summary>
    [SugarColumn(ColumnDescription = "实例名称", Length = 128)]
    public string Name { get; set; }

    /// <summary>
    ///     物标识
    /// </summary>
    [SugarColumn(ColumnDescription = "物标识", Length = 128)]
    public string Identification { get; set; }

    /// <summary>
    ///     设备类型配置
    /// </summary>
    [SugarColumn(IsJson = true, IsNullable = true, ColumnDataType = StaticConfig.CodeFirst_BigString)]
    public DeviceExampleModel? DeviceExampleModel { get; set; }

    /// <summary>
    ///     网关类型配置
    /// </summary>
    [SugarColumn(IsJson = true, IsNullable = true, ColumnDataType = StaticConfig.CodeFirst_BigString)]
    public GatewayExampleModel? GatewayExampleModel { get; set; }

    /// <summary>
    ///     标签
    /// </summary>
    [SugarColumn(ColumnDescription = "标签", IsJson = true, ColumnDataType = StaticConfig.CodeFirst_BigString)]
    public List<string> Tags { get; set; }

    /// <summary>
    ///     报警抑制
    /// </summary>
    [SugarColumn(ColumnDescription = "报警抑制")]
    public bool Restrain { get; set; }

    /// <summary>
    ///     物实例分组Id
    /// </summary>
    [SugarColumn(ColumnDescription = "物模型分组Id")]
    public long ThingGroupId { get; set; }

    /// <summary>
    ///     是否启用
    /// </summary>
    [SugarColumn(ColumnDescription = "是否启用")]
    public bool Enable { get; set; } = true;

    /// <summary>
    ///     实例状态:1:在线；2：离线
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public ExampleStatusEnum ExampleStatus { get; set; }

    /// <summary>
    ///     定位信息
    /// </summary>
    [SugarColumn(ColumnDescription = "定位信息", IsJson = true, IsNullable = true, ColumnDataType = StaticConfig.CodeFirst_BigString)]
    public LocationModel? Location { get; set; }

    /// <summary>
    ///     模型基本信息
    /// </summary>
    [SugarColumn(ColumnDescription = "模型基本信息", IsNullable = true, IsJson = true, ColumnDataType = StaticConfig.CodeFirst_BigString)]
    public Dictionary<string, string>? ThingInfo { get; set; }

    /// <summary>
    ///     模型基本扩展信息
    /// </summary>
    [SugarColumn(ColumnDescription = "模型基本扩展信息", IsNullable = true, IsJson = true, ColumnDataType = StaticConfig.CodeFirst_BigString)]
    public List<ThingInfoExtension>? ThingInfoExtensions { get; set; }

    /// <summary>
    ///     描述
    /// </summary>
    [SugarColumn(ColumnDescription = "描述", ColumnDataType = StaticConfig.CodeFirst_BigString, IsNullable = true)]
    public string? Remark { get; set; }

    /// <summary>
    ///     最后收到数据时间
    /// </summary>
    [SugarColumn(ColumnDescription = "最后收到数据时间", IsNullable = true)]
    public DateTime LastTime { get; set; }

    /// <summary>
    /// 能耗树Id
    /// </summary>
    [SugarColumn(ColumnDescription = "能耗树Id", IsNullable = true)]
    public long? EnergyOnlineTreeId { get; set; }

    #region 关联表

    /// <summary>
    ///     物模型
    /// </summary>
    [Navigate(NavigateType.OneToOne, nameof(ModelId))]
    public Model Model { get; set; }

    /// <summary>
    ///     物实例分组
    /// </summary>
    [Navigate(NavigateType.OneToOne, nameof(ThingGroupId))]
    public ModelGroup ModelGroup { get; set; }

    #endregion
}

/// <summary>
///     定位信息
/// </summary>
public class LocationModel
{
    /// <summary>
    ///     国家
    /// </summary>
    public string Country { get; set; }

    /// <summary>
    ///     省份
    /// </summary>
    public string State { get; set; }

    /// <summary>
    ///     城市
    /// </summary>
    public string City { get; set; }

    /// <summary>
    ///     区域
    /// </summary>
    public string District { get; set; }

    /// <summary>
    ///     纬度
    /// </summary>
    public double Latitude { get; set; }

    /// <summary>
    ///     经度
    /// </summary>
    public double Longitude { get; set; }

    /// <summary>
    ///     手动定位
    /// </summary>
    public bool ManualLocate { get; set; }

    /// <summary>
    ///     手动定位平台
    /// </summary>
    public string ManualLocationPlatform { get; set; }
}

/// <summary>
///     物实例-网关类型配置
/// </summary>
public class GatewayExampleModel
{
    /// <summary>
    ///     支持自动组网
    /// </summary>
    public bool NetWork { get; set; }

    /// <summary>
    ///     认证标识
    /// </summary>
    public string Identification { get; set; }

    /// <summary>
    ///     认证密钥
    /// </summary>
    public string Key { get; set; }

    /// <summary>
    ///     SN码
    /// </summary>
    public string Sn { get; set; }
}

/// <summary>
///     实例状态:1:在线；2：离线
/// </summary>
public enum ExampleStatusEnum
{
    /// <summary>
    ///     在线
    /// </summary>
    [Description("在线")] OnLine = 1,

    /// <summary>
    ///     离线
    /// </summary>
    [Description("离线")] OffLine = 2
}

/// <summary>
///     物实例-设备类型配置
/// </summary>
public class DeviceExampleModel
{
    /// <summary>
    ///     连网方式:1:直接连接：2；通过网关连接
    /// </summary>
    public ExampleConnectTypeEnum ExampleConnectType { get; set; }

    /// <summary>
    ///     直接连接配置
    /// </summary>
    public DeviceExampleConnectModel? DeviceExampleConnectModel { get; set; }

    /// <summary>
    ///     通过网关连接配置
    /// </summary>
    public DeviceExampleGatewayModel? DeviceExampleGatewayModel { get; set; }
}

/// <summary>
///     物实例-设备类型配置-直接连接配置
/// </summary>
public class DeviceExampleConnectModel
{
    /// <summary>
    ///     认证标识
    /// </summary>
    public string Identification { get; set; }

    /// <summary>
    ///     认证密钥
    /// </summary>
    public string Key { get; set; }
}

/// <summary>
///     物实例-设备类型配置-通过网关连接配置
/// </summary>
public class DeviceExampleGatewayModel
{
    /// <summary>
    ///     网关连接类型：1：关联网关；2：动态组网
    /// </summary>
    public DeviceExampleGatewayTypeEnum DeviceExampleGatewayType { get; set; }

    /// <summary>
    ///     网关Id
    /// </summary>
    public long ThingExampleId { get; set; }

    /// <summary>
    ///     网关名称
    /// </summary>
    public string ThingExampleName { get; set; }

    /// <summary>
    ///     网关标识
    /// </summary>
    public string ThingExampleIdentification { get; set; }

    /// <summary>
    ///     通讯标识
    /// </summary>
    public string Identification { get; set; }
}

/// <summary>
///     网关连接类型：1：关联网关；2：动态组网
/// </summary>
public enum DeviceExampleGatewayTypeEnum
{
    /// <summary>
    ///     关联网关
    /// </summary>
    [Description("关联网关")] Gateway = 1,

    /// <summary>
    ///     动态组网
    /// </summary>
    [Description("动态组网")] NetWork = 2
}

/// <summary>
///     连网方式:1:直接连接：2；通过网关连接
/// </summary>
public enum ExampleConnectTypeEnum
{
    /// <summary>
    ///     直接连接
    /// </summary>
    [Description("直接连接")] Connect = 1,

    /// <summary>
    ///     通过网关连接
    /// </summary>
    [Description("通过网关连接")] Gateway = 2
}