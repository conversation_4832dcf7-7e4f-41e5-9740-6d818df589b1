namespace IotPlatform.Thing.Entity;

/// <summary>
/// </summary>
[SugarTable("business_modelGroup", "物模型-分组")]
public class ModelGroup : EntityTenant
{
    /// <summary>
    /// </summary>
    [SugarColumn(ColumnDescription = "编码", Length = 50)]
    public string Name { get; set; }

    /// <summary>
    ///     父级
    /// </summary>
    [SugarColumn(ColumnDescription = "父级", Length = 50)]
    public long ParentId { get; set; }

    /// <summary>
    /// </summary>
    [Navigate(NavigateType.OneToOne, nameof(ParentId))] //设置导航 一对一
    [SugarColumn(IsIgnore = true)]
    public ModelGroup Parent { get; set; }

    /// <summary>
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public List<ModelGroup> Children { get; set; }

    /// <summary>
    ///     1.物模型；2物实例
    /// </summary>
    [SugarColumn(ColumnDescription = "类型1.物模型；2物实例")]
    public ThingGroupTypeEnum ThingGroupType { get; set; }
}

/// <summary>
///     1.物模型；2物实例
/// </summary>
public enum ThingGroupTypeEnum
{
    /// <summary>
    ///     物模型
    /// </summary>
    [Description("物模型")] ThingModel = 1,

    /// <summary>
    ///     物实例
    /// </summary>
    [Description("物实例")] ThingExample = 2
}