namespace IotPlatform.Thing.Entity;

/// <summary>
///     物模型-分类表
/// </summary>
[SugarTable("business_modelClassify", "物模型-分类表")]
public class ModelClassify : EntityTenant
{
    /// <summary>
    ///     父Id
    /// </summary>
    [SugarColumn(ColumnDescription = "父Id")]
    public long Pid { get; set; }

    /// <summary>
    ///     名称
    /// </summary>
    [SugarColumn(ColumnDescription = "名称", Length = 64)]
    [Required]
    [MaxLength(64)]
    public string Name { get; set; }

    /// <summary>
    ///     子项
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public List<ModelClassify> Children { get; set; }

    /// <summary>
    ///     模板类型：1：公共模板;2:自定义模板
    /// </summary>
    public OriginTypeEnum OriginType { get; set; } = OriginTypeEnum.Builtin;
}

/// <summary>
///     模板类型：1：公共模板;2:自定义模板
/// </summary>
public enum OriginTypeEnum
{
    /// <summary>
    ///     公共模板
    /// </summary>
    [Description("公共模板")] Builtin = 1,

    /// <summary>
    ///     自定义模板
    /// </summary>
    [Description("自定义模板")] Custom = 2
}