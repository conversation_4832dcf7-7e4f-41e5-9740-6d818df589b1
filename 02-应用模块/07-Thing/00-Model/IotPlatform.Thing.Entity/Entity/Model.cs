namespace IotPlatform.Thing.Entity;

/// <summary>
///     物模型表
/// </summary>
[SugarTable("business_model", "物模型表")]
public class Model : EntityTenant
{
    /// <summary>
    ///     模型名称
    /// </summary>
    [SugarColumn(ColumnDescription = "模型名称", Length = 128)]
    public string Name { get; set; }

    /// <summary>
    ///     uuid
    /// </summary>
    [SugarColumn(ColumnDescription = "模型Id", Length = 128)]
    public string Uuid { get; set; }

    /// <summary>
    ///     物模型分类Id表
    /// </summary>
    [SugarColumn(ColumnDescription = "物模型分类Id表")]
    public long ThingClassifyId { get; set; }

    /// <summary>
    ///     物模型类型：1;设备；2：复合物；3：网关
    /// </summary>
    [SugarColumn(ColumnDescription = "物模型类型：1;设备；2：复合物；3：网关")]
    public ModelTypeEnum ModelType { get; set; }

    /// <summary>
    ///     未发布；已发布；
    /// </summary>
    [SugarColumn(ColumnDescription = "未发布；已发布；")]
    public bool Enable { get; set; }

    /// <summary>
    ///     标签
    /// </summary>
    [SugarColumn(ColumnDescription = "标签", IsJson = true)]
    public List<string>? Tags { get; set; }

    /// <summary>
    ///     设备配置实体
    /// </summary>
    [SugarColumn(IsJson = true, IsNullable = true, ColumnDataType = "longtext,text,clob")]
    public Device? Device { get; set; }

    /// <summary>
    ///     复合物配置实体
    /// </summary>
    [SugarColumn(IsJson = true, IsNullable = true, ColumnDataType = "longtext,text,clob")]
    public Compound? Compound { get; set; }

    /// <summary>
    ///     网关配置实体
    /// </summary>
    [SugarColumn(IsJson = true, IsNullable = true, ColumnDataType = "longtext,text,clob")]
    public Gateway? Gateway { get; set; }

    /// <summary>
    ///     是否是模板
    /// </summary>
    public bool IsTemplate { get; set; }

    /// <summary>
    ///     描述
    /// </summary>
    [SugarColumn(ColumnDescription = "描述", ColumnDataType = "longtext,text,clob", IsNullable = true)]
    public string? Remark { get; set; }

    /// <summary>
    ///     模型基本信息
    /// </summary>
    [SugarColumn(ColumnDescription = "模型基本信息", IsNullable = true, IsJson = true, ColumnDataType = "longtext,text,clob")]
    public Dictionary<string, string>? ThingInfo { get; set; }

    /// <summary>
    ///     模型基本扩展信息
    /// </summary>
    [SugarColumn(ColumnDescription = "模型基本扩展信息", IsNullable = true, IsJson = true, ColumnDataType = "longtext,text,clob")]
    public List<ThingInfoExtension>? ThingInfoExtensions { get; set; }

    /// <summary>
    ///     物模型分组Id
    /// </summary>
    [SugarColumn(ColumnDescription = "物模型分组Id")]
    public long ThingGroupId { get; set; }

    #region 忽略字段

    /// <summary>
    /// 是否全部发布
    /// </summary>
    [SugarColumn(IsIgnore = true)] 
    public bool ReleaseAll { get; set; }

    /// <summary>
    ///     物实例使用总数
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public int ThingExampleCount => ThingExample?.Count ?? 0;

    #endregion

    #region 关联表

    /// <summary>
    ///     物模型分类表
    /// </summary>
    [Navigate(NavigateType.OneToOne, nameof(ThingClassifyId))]
    [SugarColumn(IsIgnore = true)]
    public ModelClassify ModelClassify { get; set; }

    /// <summary>
    ///     物模型分组
    /// </summary>
    [Navigate(NavigateType.OneToOne, nameof(ThingGroupId))]
    [SugarColumn(IsIgnore = true)]
    public ModelGroup ModelGroup { get; set; }

    /// <summary>
    ///     物模型-属性表
    /// </summary>
    [Navigate(NavigateType.OneToMany, nameof(ModelAttributes.ModelId))]
    [SugarColumn(IsIgnore = true)]
    public List<ModelAttributes> ThingAttributes { get; set; }

    /// <summary>
    ///     物模型-报警表
    /// </summary>
    [Navigate(NavigateType.OneToMany, nameof(ModelAlarm.ModelId))]
    [SugarColumn(IsIgnore = true)]
    public List<ModelAlarm> ThingAlarm { get; set; }

    /// <summary>
    ///     物模型-属性-连接变量
    /// </summary>
    [Navigate(NavigateType.OneToMany, nameof(ModelLinkVariable.ModelId))]
    [SugarColumn(IsIgnore = true)]
    public List<ModelLinkVariable> ThingLinkVariable { get; set; }

    /// <summary>
    ///     物实例
    /// </summary>
    [Navigate(NavigateType.OneToMany, nameof(ModelThing.ModelId))]
    [SugarColumn(IsIgnore = true)]
    public List<ModelThing> ThingExample { get; set; }

    #endregion
}

/// <summary>
///     模型基本信息扩展
/// </summary>
public class ThingInfoExtension
{
    /// <summary>
    ///     名称
    /// </summary>
    public string Name { get; set; }

    /// <summary>
    ///     名称
    /// </summary>
    public string DisplayName { get; set; }

    /// <summary>
    ///     数据类型
    /// </summary>
    public string Type { get; set; }

    /// <summary>
    ///     值
    /// </summary>
    public string Value { get; set; }
}

/// <summary>
///     物模型-网关类型配置
/// </summary>
public class Gateway
{
    /// <summary>
    ///     支持自动组网
    /// </summary>
    public bool NetWork { get; set; }

    /// <summary>
    ///     支持自动注册物实例
    /// </summary>
    public bool Register { get; set; }

    /// <summary>
    ///     认证标识
    /// </summary>
    public string Identification { get; set; }

    /// <summary>
    ///     认证密钥
    /// </summary>
    public string Key { get; set; }
}

/// <summary>
///     物模型-复合物配置
/// </summary>
public class Compound
{
    /// <summary>
    ///     方案
    /// </summary>
    public Dictionary<string, short> Plan { get; set; }

    /// <summary>
    ///     自动清空窗口
    /// </summary>
    public bool Clear { get; set; }
}

/// <summary>
///     物模型-设备类型配置
/// </summary>
public class Device
{
    /// <summary>
    ///     支持自动组网
    /// </summary>
    public bool NetWork { get; set; }

    /// <summary>
    ///     支持自动注册物实例
    /// </summary>
    public bool Register { get; set; }

    /// <summary>
    ///     认证标识
    /// </summary>
    public string Identification { get; set; }

    /// <summary>
    ///     认证密钥
    /// </summary>
    public string Key { get; set; }
}

/// <summary>
///     物模型类型：1;设备；2：复合物；3：网关
/// </summary>
public enum ModelTypeEnum
{
    /// <summary>
    ///     设备
    /// </summary>
    [Description("设备")] Device = 1,

    /// <summary>
    ///     复合物
    /// </summary>
    [Description("复合物")] Compound = 2,

    /// <summary>
    ///     网关
    /// </summary>
    [Description("网关")] Gateway = 3
}