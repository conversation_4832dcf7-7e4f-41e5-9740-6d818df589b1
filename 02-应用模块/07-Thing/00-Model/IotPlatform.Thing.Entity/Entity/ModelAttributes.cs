namespace IotPlatform.Thing.Entity;

/// <summary>
///     物模型-属性表
/// </summary>
[SugarTable("business_modelAttributes", "物模型-属性表")]
public class ModelAttributes : EntityTenant
{
    /// <summary>
    ///     物模型Id
    /// </summary>
    [SugarColumn(ColumnDescription = "物模型Id")]
    public long ModelId { get; set; }

    /// <summary>
    ///     名称
    /// </summary>
    [SugarColumn(ColumnDescription = "名称", Length = 128)]
    public string Name { get; set; }

    /// <summary>
    ///     属性ID
    /// </summary>
    [SugarColumn(ColumnDescription = "属性ID", Length = 128)]
    public string Identification { get; set; }

    /// <summary>
    ///     数据类型：1：String;2:Number;3:Bool;4:Int;5:Binary;6:Json;7:Array
    /// </summary>
    public DataTypeEnum DataType { get; set; } = DataTypeEnum.String;

    /// <summary>
    ///     读写操作设置：1：读写；2：只读；3：只写
    /// </summary>
    [SugarColumn(ColumnDescription = "读写操作设置：1：读写；2：只读；3：只写")]
    public ReadTypeEnum ReadType { get; set; } = ReadTypeEnum.Read;

    /// <summary>
    ///     属性值来源:1:连接变量；2：规则指定；3：手动写值
    /// </summary>
    [SugarColumn(ColumnDescription = "属性值来源:1:连接变量；2：规则指定；3：手动写值")]
    public AttributesValueSourceEnum AttributesValueSource { get; set; } = AttributesValueSourceEnum.Variable;

    /// <summary>
    ///     连接变量-属性值配置
    /// </summary>
    [SugarColumn(IsJson = true)]
    public AttributesValueModel? AttributesValueModel { get; set; }

    /// <summary>
    ///     规则指定-属性值配置
    /// </summary>
    [SugarColumn(IsJson = true, IsNullable = true)]
    public AttributesScriptModel? AttributesScriptModel { get; set; }

    /// <summary>
    ///     手动写值-属性值配置
    /// </summary>
    [SugarColumn(IsJson = true, IsNullable = true)]
    public AttributesWriteModel? AttributesWriteModel { get; set; }

    /// <summary>
    ///     触发方式：1：模型内有任一属性有工况值上报即触发；2：参与规则的任一属性有工况值报即触发：3：参与规则的所有属性同时有工况上报即触发
    /// </summary>
    [SugarColumn(ColumnDescription = "触发方式：1：模型内有任一属性有工况值上报即触发；2：参与规则的任一属性有工况值报即触发：3：参与规则的所有属性同时有工况上报即触发")]
    public TriggerMethodEnum TriggerMethod { get; set; } = TriggerMethodEnum.All;

    /// <summary>
    ///     设置为内部属性
    /// </summary>
    [SugarColumn(ColumnDescription = "设置为内部属性")]
    public bool Internal { get; set; }

    /// <summary>
    ///     属性值小数点位数保留方式:1:保留一位(例如9.9);2:保留2位(例如9.99);3:保留3位(例如9.999);4:保留4位;5:保留5位;6:保留6位
    /// </summary>
    [SugarColumn(ColumnDescription = "属性值小数点位数保留方式:1:保留一位(例如9.9);2:保留2位(例如9.99);3:保留3位(例如9.999);4:保留4位;5:保留5位;6:保留6位")]
    public RetentionMethodEnum RetentionMethod { get; set; } = RetentionMethodEnum.Two;

    /// <summary>
    ///     历史数据保存方式:1:上报保存；2：周期保存；3：变化保存；4：全部保存；5：不保存
    /// </summary>
    [SugarColumn(ColumnDescription = "历史数据保存方式:1:上报保存；2：周期保存；3：变化保存；4：全部保存；5：不保存")]
    public HistoricalDataEnum HistoricalData { get; set; } = HistoricalDataEnum.Save;

    /// <summary>
    ///     周期保存时间（秒）
    /// </summary>
    [SugarColumn(ColumnDescription = "周期保存时间（秒）", IsNullable = true)]
    public int Period { get; set; }

    /// <summary>
    ///     字符串长度
    /// </summary>
    [SugarColumn(ColumnDescription = "字符串长度", IsNullable = true)]
    public int Length { get; set; }

    /// <summary>
    ///     单位
    /// </summary>
    [SugarColumn(ColumnDescription = "单位", Length = 32, IsNullable = true)]
    public string? Unit { get; set; }

    /// <summary>
    ///     属性标签
    /// </summary>
    [SugarColumn(ColumnDescription = "属性标签", IsJson = true, IsNullable = true)]
    public List<string> Tags { get; set; } = new();

    /// <summary>
    ///     取值范围配置
    /// </summary>
    [SugarColumn(ColumnDescription = "取值范围配置", IsJson = true)]
    public ThingAttributeFilter ThingAttributeFilter { get; set; } = new();

    /// <summary>
    ///     为属性值添加自定义
    /// </summary>
    [SugarColumn(ColumnDescription = "描述", Length = 256)]
    public string? Custom { get; set; }

    /// <summary>
    ///     描述
    /// </summary>
    [SugarColumn(ColumnDescription = "描述", ColumnDataType = "longtext,text,clob")]
    public string? Desc { get; set; }

    /// <summary>
    ///     是否发布
    /// </summary>
    [SugarColumn(ColumnDescription = "是否发布")]
    public bool Release { get; set; } = false;
    
    #region 忽略字段

    /// <summary>
    ///     数据类型
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public string DataTypeName => DataType.GetDescription();

    #endregion

    #region 关联表

    /// <summary>
    ///     物模型
    /// </summary>
    [Navigate(NavigateType.OneToOne, nameof(ModelId))]
    [SugarColumn(IsIgnore = true)]
    public Model Model { get; set; }

    #endregion
}

/// <summary>
///     取值范围过滤
/// </summary>
public class ThingAttributeFilter
{
    /// <summary>
    ///     最小值
    /// </summary>
    public decimal Min { get; set; }

    /// <summary>
    ///     最大值
    /// </summary>
    public decimal Max { get; set; }

    /// <summary>
    ///     设备属性取值范围最大最小值类型枚举：1:原值；2：最小值；3:最大值；4：指定值；5：上一有效值 6：丢弃
    /// </summary>
    public DeviceVariableFilterTypeEnum MinFilterType { get; set; } = DeviceVariableFilterTypeEnum.This;

    /// <summary>
    ///     最小取值范围指定值
    /// </summary>
    public decimal SetMin { get; set; }

    /// <summary>
    ///     最大取值范围指定值
    /// </summary>
    public decimal SetMax { get; set; }

    /// <summary>
    ///     设备属性取值范围最大最小值类型枚举：1:原值；2：最小值；3:最大值；4：指定值；5：上一有效值 6：丢弃
    /// </summary>
    public DeviceVariableFilterTypeEnum MaxFilterType { get; set; } = DeviceVariableFilterTypeEnum.This;

    /// <summary>
    ///     是否保存
    /// </summary>
    public bool Save { get; set; }
}

/// <summary>
///     物模型-属性表-连接变量-属性值配置
/// </summary>
public class AttributesValueModel
{
    /// <summary>
    ///     物模型属性连接变量名称
    /// </summary>
    public string ThingLinkVariableName { get; set; }

    /// <summary>
    ///     倍率
    /// </summary>
    public decimal Magnification { get; set; } = 1;

    /// <summary>
    ///     基数
    /// </summary>
    public decimal Base { get; set; }
}

/// <summary>
///     物模型-属性表-规则指定-属性值配置
/// </summary>
public class AttributesScriptModel
{
    /// <summary>
    ///     允许使用添加时间窗口的属性
    /// </summary>
    public bool TimeWindow { get; set; }

    /// <summary>
    ///     脚本内容
    /// </summary>
    public string Script { get; set; }

    /// <summary>
    ///     优先级
    /// </summary>
    public short Order { get; set; }
}

/// <summary>
///     物模型-属性表-手动写值-属性值配置
/// </summary>
public class AttributesWriteModel
{
    /// <summary>
    /// </summary>
    public object Value { get; set; }
}

/// <summary>
///     触发方式：1：模型内有任一属性有工况值上报即触发；2：参与规则的任一属性有工况值报即触发：3：参与规则的所有属性同时有工况上报即触发
/// </summary>
public enum TriggerMethodEnum
{
    /// <summary>
    ///     模型内有任一属性有工况值上报即触发
    /// </summary>
    [Description("模型内有任一属性有工况值上报即触发")] Pub = 1,

    /// <summary>
    ///     参与规则的任一属性有工况值报即触发
    /// </summary>
    [Description("参与规则的任一属性有工况值报即触发")] Rules = 2,

    /// <summary>
    ///     参与规则的所有属性同时有工况上报即触发
    /// </summary>
    [Description("参与规则的所有属性同时有工况上报即触发")] All = 3
}

/// <summary>
///     设备属性取值范围最大最小值类型枚举：1:原值；2：最小值；3:最大值；4：指定值；5：上一有效值 6：丢弃
/// </summary>
public enum DeviceVariableFilterTypeEnum
{
    /// <summary>
    ///     原值
    /// </summary>
    [Description("原值")] This = 1,

    /// <summary>
    ///     最小值
    /// </summary>
    [Description("最小值")] Min = 2,

    /// <summary>
    ///     最大值
    /// </summary>
    [Description("最大值")] Max = 3,

    /// <summary>
    ///     指定值
    /// </summary>
    [Description("指定值")] Set = 4,

    /// <summary>
    ///     上一有效值
    /// </summary>
    [Description("上一有效值")] Cookie = 5,

    /// <summary>
    ///     丢弃
    /// </summary>
    [Description("丢弃")] Clear = 6
}

/// <summary>
///     读写操作设置：1：读写；2：只读；3：只写
/// </summary>
public enum ReadTypeEnum
{
    /// <summary>
    ///     读写
    /// </summary>
    [Description("读写")] ReadAndWrite = 1,

    /// <summary>
    ///     只读
    /// </summary>
    [Description("只读")] Read = 2,

    /// <summary>
    ///     只写
    /// </summary>
    [Description("只写")] Write = 3
}

/// <summary>
///     历史数据保存方式:1:上报保存；2：周期保存；3：变化保存；4：全部保存；5：不保存
/// </summary>
public enum HistoricalDataEnum
{
    /// <summary>
    ///     上报保存
    /// </summary>
    [Description("上报保存")] Save = 1,

    /// <summary>
    ///     周期保存
    /// </summary>
    [Description("周期保存")] Period = 2,

    /// <summary>
    ///     变化保存
    /// </summary>
    [Description("变化保存")] Change = 3,

    /// <summary>
    ///     全部保存
    /// </summary>
    [Description("全部保存")] All = 4,

    /// <summary>
    ///     不保存
    /// </summary>
    [Description("不保存")] No = 5
}

/// <summary>
///     属性值小数点位数保留方式:1:保留一位(例如9.9);2:保留2位(例如9.99);3:保留3位(例如9.999);4:保留4位;5:保留5位;6:保留6位
/// </summary>
public enum RetentionMethodEnum
{
    /// <summary>
    ///     保留一位(例如9.9)
    /// </summary>
    [Description("保留一位(例如9.9)")] One = 1,

    /// <summary>
    ///     保留2位(例如9.99)
    /// </summary>
    [Description("保留2位(例如9.99)")] Two = 2,

    /// <summary>
    ///     保留3位(例如9.999)
    /// </summary>
    [Description("保留3位(例如9.999)")] Three = 3,

    /// <summary>
    ///     保留4位
    /// </summary>
    [Description("保留4位")] Four = 4,

    /// <summary>
    ///     保留5位
    /// </summary>
    [Description("保留5位")] Five = 5,

    /// <summary>
    ///     保留6位
    /// </summary>
    [Description("保留6位")] Six = 6
}

/// <summary>
///     属性值来源:1:连接变量；2：规则指定；3：手动写值
/// </summary>
public enum AttributesValueSourceEnum
{
    /// <summary>
    ///     连接变量
    /// </summary>
    [Description("连接变量")] Variable = 1,

    /// <summary>
    ///     规则指定
    /// </summary>
    [Description("规则指定")] Rules = 2,

    /// <summary>
    ///     手动写值
    /// </summary>
    [Description("手动写值")] Hand = 3
}

/// <summary>
///     数据类型：1：String;2:Number;3:Bool;4:Int;5:Binary;6:Json;7:Array
/// </summary>
public enum DataTypeEnum
{
    /// <summary>
    ///     String
    /// </summary>
    [Description("String")] String = 1,

    /// <summary>
    ///     Number
    /// </summary>
    [Description("Number")] Number = 2,

    /// <summary>
    ///     Boolean
    /// </summary>
    [Description("Boolean")] Bool = 3,

    /// <summary>
    ///     Integer
    /// </summary>
    [Description("Integer")] Int = 4,

    /// <summary>
    ///     Binary
    /// </summary>
    [Description("Binary")] Binary = 5,

    /// <summary>
    ///     Json
    /// </summary>
    [Description("Json")] Json = 6,

    /// <summary>
    ///     Array
    /// </summary>
    [Description("Array")] Array = 7
}