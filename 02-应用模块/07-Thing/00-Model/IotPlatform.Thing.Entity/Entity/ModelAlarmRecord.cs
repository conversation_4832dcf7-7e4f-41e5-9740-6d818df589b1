using DateTime = System.DateTime;

namespace IotPlatform.Thing.Entity;

/// <summary>
///     物模型-报警记录表
/// </summary>
[SplitTable(SplitType.Day)]
[SugarTable("business_modelAlarmRecord_{year}{month}{day}", "物模型-报警记录表")]
public class ModelAlarmRecord : EntityTenantId
{
    /// <summary>
    /// </summary>
    [SugarColumn(ColumnDescription = "JobId")]
    public string JobId { get; set; }

    /// <summary>
    ///     物实例Id
    /// </summary>
    [SugarColumn(ColumnDescription = "物实例Id")]
    public long ThingId { get; set; }
    
    /// <summary>
    ///     物实例uuid
    /// </summary>
    [SugarColumn(ColumnDescription = "物实例uuid")]
    public string ThingIdent { get; set; }
    
    /// <summary>
    ///     物模型Id
    /// </summary>
    [SugarColumn(ColumnDescription = "物模型Id")]
    public long ModelId { get; set; }

    /// <summary>
    /// 物模型UUID
    /// </summary>
    [SugarColumn(ColumnDescription = "物模型UUID")]
    public string ModelUuId { get; set; }
    
    /// <summary>
    ///     物模型报警Id（主键Id）
    /// </summary>
    [SugarColumn(ColumnDescription = "（主键Id）")]
    public long ModelAlarmId { get; set; }
    
    /// <summary>
    ///     物模型报警Id
    /// </summary>
    [SugarColumn(ColumnDescription = "物模型报警Id")]
    public string ModelAlarmIdent { get; set; }
    
    /// <summary>
    ///     物模型报警名称
    /// </summary>
    [SugarColumn(ColumnDescription = "物模型报警名称")]
    public string AlarmName { get; set; }
    
    /// <summary>
    ///     触发时间
    /// </summary>
    [SugarColumn(ColumnDescription = "触发时间")]
    public DateTime TriggerTime { get; set; }
    
    /// <summary>
    ///     触发表达式
    /// </summary>
    [SugarColumn(ColumnDescription = "触发表达式")]
    public string TriggerScript { get; set; }

    /// <summary>
    ///     解除时间
    /// </summary>
    [SugarColumn(ColumnDescription = "解除时间", IsNullable = true)]
    public DateTime? CloseTime { get; set; }
    
    /// <summary>
    ///     解除表达式
    /// </summary>
    [SugarColumn(ColumnDescription = "解除表达式")]
    public string? CloseScript { get; set; }

    /// <summary>
    ///     同时上报的属性
    /// </summary>
    [SugarColumn(ColumnDescription = "同时上报的属性")]
    public string ThingAttributes { get; set; }

    /// <summary>
    ///     首次触发时间
    /// </summary>
    [SugarColumn(ColumnDescription = "首次触发时间")]
    [SplitField]
    public DateTime FirstTriggerTime { get; set; }

    /// <summary>
    ///     确认时间
    /// </summary>
    [SugarColumn(ColumnDescription = "确认时间", IsNullable = true)]
    public DateTime? ConfirmTime { get; set; }

    /// <summary>
    ///     状态：1持续中；2自动解除；3手动解除
    /// </summary>
    [SugarColumn(ColumnDescription = "状态：1持续中；2自动解除；3手动解除")]
    public ThingAlarmRecordStatusEnum Status { get; set; } = ThingAlarmRecordStatusEnum.Alarm;

    #region 忽略字段

    /// <summary>
    ///     状态：1持续中；2自动解除；3手动解除
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public string StatusName => Status.GetDescription();

    #endregion
    
    #region 关联表

    /// <summary>
    ///     物模型
    /// </summary>
    [Navigate(NavigateType.OneToOne, nameof(ModelId))]
    public Model Model { get; set; }
    
    /// <summary>
    ///     物实例
    /// </summary>
    [Navigate(NavigateType.OneToOne, nameof(ThingId))]
    public ModelThing Thing { get; set; }
    
    /// <summary>
    ///     物模型报警
    /// </summary>
    [Navigate(NavigateType.OneToOne, nameof(ModelAlarmId))]
    public ModelAlarm ModelAlarm { get; set; }

    #endregion
}