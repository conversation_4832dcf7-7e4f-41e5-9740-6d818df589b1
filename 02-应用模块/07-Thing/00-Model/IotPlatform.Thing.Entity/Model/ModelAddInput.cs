using DateTime = System.DateTime;

namespace IotPlatform.Thing.Entity;

/// <summary>
///     物模型-新增
/// </summary>
public class ModelAddInput
{
    /// <summary>
    ///     模型名称
    /// </summary>
    [Required]
    public string Name { get; set; }

    /// <summary>
    ///     物模型分类Id表
    /// </summary>
    public long ThingClassifyId { get; set; }

    /// <summary>
    ///     物模型类型：1;设备；2：复合物；3：网关
    /// </summary>
    [Required]
    public ModelTypeEnum ModelType { get; set; }

    /// <summary>
    ///     标签
    /// </summary>
    public List<string> Tags { get; set; }

    /// <summary>
    ///     设备配置实体
    /// </summary>
    public Device? Device { get; set; }

    /// <summary>
    ///     复合物配置实体
    /// </summary>
    public Compound? Compound { get; set; }

    /// <summary>
    ///     设备配置实体
    /// </summary>
    public Gateway? Gateway { get; set; }
}

/// <summary>
///     物模型-模板新增
/// </summary>
public class ModelTemplateAddInput
{
    /// <summary>
    ///     模型名称
    /// </summary>
    [Required]
    public string Name { get; set; }

    /// <summary>
    ///     物模型分类Id表
    /// </summary>
    [Required]
    public long ThingClassifyId { get; set; }

    /// <summary>
    ///     模板Id
    /// </summary>
    [Required]
    public long Id { get; set; }
}

/// <summary>
///     物模型-集合请求参数
/// </summary>
public class ModelPageInput : BasePageInput
{
    /// <summary>
    ///     物模型类型：1;设备；2：复合物；3：网关
    /// </summary>
    [Required]
    public ModelTypeEnum ModelType { get; set; }

    /// <summary>
    ///     未发布；已发布；
    /// </summary>
    public bool Enable { get; set; }

    /// <summary>
    ///     精确搜索
    /// </summary>
    public bool PreciseSearch { get; set; }

    /// <summary>
    ///     物模型分组Id
    /// </summary>
    public long ThingGroupId { get; set; }
}

/// <summary>
///     模型分类
/// </summary>
public class ModelSelectInput
{
    /// <summary>
    ///     物模型类型：1;设备；2：复合物；3：网关
    /// </summary>
    [Required]
    public int ModelType { get; set; }
}

/// <summary>
///     查询物模型请求
/// </summary>
public class OpenApiThingClassesInput
{
    /// <summary>
    ///     物模型类型：1;设备；2：复合物；3：网关
    /// </summary>
    [Required]
    [Description("物模型类型：1;设备；2：复合物；3：网关")]
    public ModelTypeEnum ModelType { get; set; }

    /// <summary>
    ///     物模型分类
    /// </summary>
    [Description("物模型分类")]
    public string Category { get; set; }

    /// <summary>
    ///     模型Id
    /// </summary>
    [Description("模型Id")]
    public string ModelId { get; set; }

    /// <summary>
    ///     模型名称
    /// </summary>
    [Description("模型名称")]
    public string Name { get; set; }

    /// <summary>
    ///     模型标签
    /// </summary>
    [Description("模型标签")]
    public string Tag { get; set; }
}

/// <summary>
///     查询物模型返回
/// </summary>
public class QueryModelOutput
{
    /// <summary>
    ///     Id
    /// </summary>
    [Description("Id")]
    public long Id { get; set; }

    /// <summary>
    ///     模型名称
    /// </summary>
    [Description("模型名称")]
    public string Name { get; set; }

    /// <summary>
    ///     uuid
    /// </summary>
    [Description("模型Id")]
    public string Uuid { get; set; }

    /// <summary>
    ///     物模型分类Id表
    /// </summary>
    [Description("物模型分类Id表")]
    public long ThingClassifyId { get; set; }

    /// <summary>
    ///     物模型配置
    /// </summary>
    [Description("物模型配置")]
    public string Config { get; set; }

    /// <summary>
    ///     物模型类型：1;设备；2：复合物；3：网关
    /// </summary>
    [Description("物模型类型：1;设备；2：复合物；3：网关")]
    public ModelTypeEnum ModelType { get; set; }


    /// <summary>
    ///     标签
    /// </summary>
    [Description("标签")]
    [SugarColumn(IsJson = true)]
    public List<string> Tags { get; set; }

    // /// <summary>
    // ///     设备配置实体
    // /// </summary>
    // [SugarColumn(IsIgnore = true)]
    // public Device? Device => ModelType == ModelTypeEnum.Device ? JSON.Deserialize<Device>(Config) : null;
    //
    // /// <summary>
    // ///     复合物配置实体
    // /// </summary>
    // [SugarColumn(IsIgnore = true)]
    // public Compound? Compound => ModelType == ModelTypeEnum.Compound ? JSON.Deserialize<Compound>(Config) : null;
    //
    // /// <summary>
    // ///     网关配置实体
    // /// </summary>
    // [SugarColumn(IsIgnore = true)]
    // public Gateway? Gateway => ModelType == ModelTypeEnum.Gateway ? JSON.Deserialize<Gateway>(Config) : null;
    /// <summary>
    ///     描述
    /// </summary>
    [Description("描述")]
    public string Remark { get; set; }

    /// <summary>
    ///     物模型分组Id
    /// </summary>
    [Description("物模型分组Id")]
    public long ThingGroupId { get; set; }
}

/// <summary>
///     开放Api --查询物实例
/// </summary>
public class OpenApiThingInstancesInput
{
    /// <summary>
    ///     物实例分组名称
    /// </summary>
    [Description("物实例分组名称")]
    public string ModelGroupName { get; set; }

    /// <summary>
    ///     模型Id
    /// </summary>
    [Description("模型Id")]
    public string ModelId { get; set; }

    /// <summary>
    ///     物模型类型：1;设备；2：复合物；3：网关
    /// </summary>
    [Required]
    [Description("物模型类型：1;设备；2：复合物；3：网关")]
    public ModelTypeEnum ModelType { get; set; }

    /// <summary>
    ///     物实例Id
    /// </summary>
    [Description("物实例Id")]
    public string ThingId { get; set; }

    /// <summary>
    ///     实例名称
    /// </summary>
    [Description("实例名称")]
    public string Name { get; set; }

    /// <summary>
    ///     实例标签
    /// </summary>
    [Description("实例标签")]
    public string Tag { get; set; }
}

/// <summary>
///     查询物实例 返回
/// </summary>
public class QueryInstanceOutput
{
    /// <summary>
    ///     物模型Id
    /// </summary>
    [Description("物模型Id")]
    public string ModelId { get; set; }

    /// <summary>
    ///     物模型类型：1;设备；2：复合物；3：网关
    /// </summary>
    [Description("物模型类型：1;设备；2：复合物；3：网关")]
    public ModelTypeEnum ModelType { get; set; }

    /// <summary>
    ///     实例名称
    /// </summary>
    [Description("实例名称")]
    public string Name { get; set; }

    /// <summary>
    ///     物标识
    /// </summary>
    [Description("物标识")]
    public string Identification { get; set; }

    /// <summary>
    ///     物模型类型配置
    /// </summary>
    [Description("物模型类型配置")]
    public string Config { get; set; }

    // /// <summary>
    // ///     设备类型配置
    // /// </summary>
    // [SugarColumn(IsIgnore = true)]
    // public DeviceExampleModel? DeviceExampleModel => ModelType == ModelTypeEnum.Device ? JSON.Deserialize<DeviceExampleModel>(Config) : null;
    //
    // /// <summary>
    // ///     网关类型配置
    // /// </summary>
    // [SugarColumn(IsIgnore = true)]
    // public GatewayExampleModel? GatewayExampleModel => ModelType == ModelTypeEnum.Gateway ? JSON.Deserialize<GatewayExampleModel>(Config) : null;

    /// <summary>
    ///     实例状态:1:在线；2：离线
    /// </summary>
    [Description("实例状态:1:在线；2：离线")]
    public ExampleStatusEnum ExampleStatus { get; set; }

    /// <summary>
    ///     标签
    /// </summary>
    [Description("标签")]
    [SugarColumn(IsJson = true)]
    public List<string> Tags { get; set; }

    /// <summary>
    ///     物实例分组Id
    /// </summary>
    [Description("物模型分组Id")]
    public long ThingGroupId { get; set; }
    
    /// <summary>
    ///     物实例分组名称
    /// </summary>
    [Description("物模型分组名称")]
    public string ThingGroupName { get; set; }
}

/// <summary>
///     当前报警集合查询
/// </summary>
public class QueryCurrentAlarmListInput
{
    /// <summary>
    ///     报警ID集合
    /// </summary>
    [Description("报警ID集合")]
    public List<string> AlarmIdens { get; set; } = new();

    /// <summary>
    ///     报警ID集合（主键Id）
    /// </summary>
    [Description("报警ID集合（主键Id）")]
    public List<long> AlarmIds { get; set; } = new();
    
    /// <summary>
    ///     报警名称
    /// </summary>
    [Description("报警名称")]
    public string Subject { get; set; }

    /// <summary>
    ///     模型Id集合
    /// </summary>
    [Description("模型Id集合")]
    public List<long> ModelIds { get; set; } = new();

    /// <summary>
    ///     物实例Id集合
    /// </summary>
    [Description("物实例Id集合")]
    public List<long> ThingIds { get; set; } = new();
    
    /// <summary>
    ///     物模型UUID集合
    /// </summary>
    [Description("物模型UUID集合")]
    public List<string> ModelIdens { get; set; } = new();

    /// <summary>
    ///     物实例标识集合
    /// </summary>
    [Description("物实例标识集合")]
    public List<string> ThingIdens { get; set; } = new();

    /// <summary>
    ///     报警类别标签
    /// </summary>
    [Description("报警类别标签")]
    public string Tag { get; set; }

    /// <summary>
    ///     报警级别集合：1：紧急；2：重要；3：警告；4：一般；5：不确定
    /// </summary>
    [Description("报警级别集合：1：紧急；2：重要；3：警告；4：一般；5：不确定")]
    public List<int> SeverityLevels { get; set; } = new();

    /// <summary>
    ///     查询返回的数据最大条数
    /// </summary>
    [Description("查询返回的数据最大条数")]
    public int Limit { get; set; } = 100;

    /// <summary>
    ///     类型；1：待处理报警；2报警历史
    /// </summary>
    [Description("类型；1：待处理报警；2报警历史")]
    public int Type { get; set; } = 1;
}

/// <summary>
///     当前报警集合查询返回
/// </summary>
public class QueryCurrentAlarmListOutput
{
    /// <summary>
    /// </summary>
    [Description("Id")]
    public long Id { get; set; }

    /// <summary>
    ///     物实例名称
    /// </summary>
    [Description("物实例名称")]
    public string ThingExampleName { get; set; }

    /// <summary>
    ///     物模型
    /// </summary>
    [Description("物模型")]
    public QueryModelOutput ThingModel { get; set; }

    /// <summary>
    ///     物模型报警
    /// </summary>
    public ThingAlarmOutput ThingAlarm { get; set; }

    /// <summary>
    ///     触发时间
    /// </summary>
    [Description("触发时间")]
    public DateTime TriggerTime { get; set; }

    /// <summary>
    ///     解除时间
    /// </summary>
    [Description("解除时间")]
    public DateTime? CloseTime { get; set; }

    /// <summary>
    ///     同时上报的属性
    /// </summary>
    [Description("同时上报的属性")]
    public string ThingAttributes { get; set; }

    /// <summary>
    ///     首次触发时间
    /// </summary>
    [Description("首次触发时间")]
    public DateTime FirstTriggerTime { get; set; }

    /// <summary>
    ///     确认时间
    /// </summary>
    [Description("确认时间")]
    public DateTime? ConfirmTime { get; set; }

    /// <summary>
    ///     状态：1持续中；2自动解除；3手动解除
    /// </summary>
    [Description("状态：1持续中；2自动解除；3手动解除")]
    public ThingAlarmRecordStatusEnum Status { get; set; }
}

/// <summary>
/// </summary>
public class ThingAlarmOutput
{
    /// <summary>
    ///     名称
    /// </summary>
    [Description("名称")]
    public string Name { get; set; }

    /// <summary>
    ///     报警ID
    /// </summary>
    [Description("报警ID")]
    public string Identification { get; set; }

    /// <summary>
    ///     描述
    /// </summary>
    [Description("描述")]
    public string Desc { get; set; }

    /// <summary>
    ///     报警级别：1：紧急；2：重要；3：警告；4：一般；5：不确定
    /// </summary>
    [Description("报警级别：1：紧急；2：重要；3：警告；4：一般；5：不确定")]
    public AlarmLevelEnum AlarmLevel { get; set; }


    /// <summary>
    ///     报警原因
    /// </summary>
    [Description("报警原因")]
    public string AlarmReason { get; set; }


    /// <summary>
    ///     报警标签
    /// </summary>
    [SugarColumn(IsJson = true)]
    [Description("报警标签")]
    public List<string> Tags { get; set; }
}

/// <summary>
///     开放api - 运行脚本
/// </summary>
public class RunScriptInput : BaseOpenApiInput
{
    /// <summary>
    ///     脚本Id
    /// </summary>
    [Required]
    [Description("脚本Id")]
    public long Id { get; set; }

    /// <summary>
    ///     参数
    /// </summary>
    [Description("参数(kv键值对)")]
    public Dictionary<string, object> Values { get; set; }
}

/// <summary>
///     开放api公共参数
/// </summary>
public class BaseOpenApiInput
{
    /// <summary>
    ///     应用Id
    /// </summary>
    [Required]
    [Description("应用Id")]
    public long AppId { get; set; }
}

/// <summary>
///     物模型分组
/// </summary>
public class ModelUpdateGroupInput
{
    /// <summary>
    ///     物模型分组Id
    /// </summary>
    [Required]
    public long ThingGroupId { get; set; }

    /// <summary>
    ///     物模型Id
    /// </summary>
    public List<long> ModelId { get; set; }
}