namespace IotPlatform.Thing.Entity;

/// <summary>
///     设备属性列表请求
/// </summary>
public class ThingAttributesInput : BasePageInput
{
    /// <summary>
    ///     物模型Id
    /// </summary>
    [Required]
    public long ModelId { get; set; }

    /// <summary>
    ///     根据属性过滤
    /// </summary>
    public List<string> ThingAttributeName { get; set; } = new();

    /// <summary>
    ///     属性标签
    /// </summary>
    [SugarColumn(IsJson = true)]
    public List<string> Tags { get; set; } = new();

    /// <summary>
    ///     精确搜索
    /// </summary>
    public bool PreciseSearch { get; set; }

    /// <summary>
    ///     数据类型
    /// </summary>
    public List<int> DataType { get; set; } = new();

    /// <summary>
    ///     数据来源
    /// </summary>
    public List<int> AttributesValueSource { get; set; } = new();
}

/// <summary>
///     物模型属性新增
/// </summary>
public class ThingAttributesAddInput
{
    /// <summary>
    ///     物模型Id
    /// </summary>
    [Required]
    public long ModelId { get; set; }

    /// <summary>
    ///     属性值来源:1:连接变量；2：规则指定；3：手动写值
    /// </summary>
    public AttributesValueSourceEnum AttributesValueSource { get; set; }

    /// <summary>
    ///     名称
    /// </summary>
    [Required]
    public string Name { get; set; }

    /// <summary>
    ///     属性ID
    /// </summary>
    [Required]
    public string Identification { get; set; }

    /// <summary>
    ///     数据类型：1：String;2:Number;3:Bool;4:Int;5:Binary;6:Json;7:Array
    /// </summary>
    public DataTypeEnum DataType { get; set; }

    /// <summary>
    ///     读写操作设置：1：读写；2：只读；3：只写
    /// </summary>
    public ReadTypeEnum ReadType { get; set; }

    /// <summary>
    ///     为属性值添加自定义
    /// </summary>
    public string Custom { get; set; }

    /// <summary>
    ///     连接变量-属性值配置
    /// </summary>
    public AttributesValueModel? AttributesValueModel { get; set; }

    /// <summary>
    ///     规则指定-属性值配置
    /// </summary>
    public AttributesScriptModel? AttributesScriptModel { get; set; }

    /// <summary>
    ///     手动写值-属性值配置
    /// </summary>
    public AttributesWriteModel? AttributesWriteModel { get; set; }

    /// <summary>
    ///     触发方式：1：模型内有任一属性有工况值上报即触发；2：参与规则的任一属性有工况值报即触发：3：参与规则的所有属性同时有工况上报即触发
    /// </summary>
    public TriggerMethodEnum TriggerMethod { get; set; }

    /// <summary>
    ///     设置为内部属性
    /// </summary>
    public bool Internal { get; set; }

    /// <summary>
    ///     字符串长度，字符串是必填
    /// </summary>
    public int Length { get; set; }

    /// <summary>
    ///     属性值小数点位数保留方式:1:保留一位(例如9.9);2:保留2位(例如9.99);3:保留3位(例如9.999);4:保留4位;5:保留5位;6:保留6位
    /// </summary>
    public RetentionMethodEnum RetentionMethod { get; set; }

    /// <summary>
    ///     历史数据保存方式:1:上报保存；2：周期保存；3：变化保存；4：全部保存；5：不保存
    /// </summary>
    public HistoricalDataEnum HistoricalData { get; set; }

    /// <summary>
    ///     单位
    /// </summary>
    public string Unit { get; set; }

    /// <summary>
    ///     属性标签
    /// </summary>
    public List<string> Tags { get; set; }

    /// <summary>
    ///     取值范围配置
    /// </summary>
    public ThingAttributeFilter ThingAttributeFilter { get; set; } = new();

    /// <summary>
    ///     描述
    /// </summary>
    public string Desc { get; set; }
}

/// <summary>
///     物模型-属性下拉请求参数
/// </summary>
public class ThingAttributesSelectInput
{
    /// <summary>
    ///     Id
    /// </summary>
    [Required]
    public long ModelId { get; set; }

    /// <summary>
    ///     标签
    /// </summary>
    public List<string> Tags { get; set; } = new();
}

/// <summary>
///     物模型-属性修改请求参数
/// </summary>
public class ThingAttributesUpdateInput : ThingAttributesAddInput
{
    /// <summary>
    ///     Id
    /// </summary>
    [Required]
    public long Id { get; set; }
}

/// <summary>
///     物模型-属性导入-来源网关
/// </summary>
public class EdgeInPortInput
{
    /// <summary>
    ///     上传文件
    /// </summary>
    [Required]
    public IFormFile File { get; set; }

    /// <summary>
    ///     1:覆盖原数据,2:忽略
    /// </summary>
    [Required]
    public DeviceVariableInPortTypeEnum InPortType { get; set; }

    /// <summary>
    ///     模型Id
    /// </summary>
    [Required]
    public long ModelId { get; set; }
}

/// <summary>
///     设备属性导入
/// </summary>
public class DeviceVariableInPortDto
{
    /// <summary>
    ///     标识符
    /// </summary>
    [ExcelColumnName("标识符")]
    public string Identifier { get; set; }

    /// <summary>
    ///     变量名
    /// </summary>
    [ExcelColumnName("名称")]
    public string Name { get; set; }

    /// <summary>
    ///     转换类型
    /// </summary>
    [ExcelColumnName("转换类型")]
    public string TransitionType { get; set; }

    /// <summary>
    ///     取值方式
    /// </summary>
    [ExcelColumnName("取值方式")]
    public string ValueSource { get; set; }

    /// <summary>
    ///     上报方式
    /// </summary>
    [ExcelColumnName("上报方式")]
    public string SendType { get; set; }

    /// <summary>
    ///     取值范围 选填
    /// </summary>
    [ExcelColumnName("取值范围 选填")]
    public string DeviceVariableFilterExtend { get; set; }

    /// <summary>
    ///     驱动配置拓展 选填
    /// </summary>
    [ExcelColumnName("驱动配置拓展 选填")]
    public string VariableExtend { get; set; }

    /// <summary>
    ///     默认值 选填
    /// </summary>
    [ExcelColumnName("默认值 选填")]
    public string DefaultValue { get; set; }

    /// <summary>
    ///     保留长度 选填
    /// </summary>
    [ExcelColumnName("保留长度 选填")]
    public string LengthEx { get; set; }

    /// <summary>
    ///     单位 选填
    /// </summary>
    [ExcelColumnName("单位 选填")]
    public string Unit { get; set; }

    /// <summary>
    ///     表达式 选填
    /// </summary>
    [ExcelColumnName("表达式 选填")]
    public string Expressions { get; set; }

    /// <summary>
    ///     计算赋值(脚本) 选填
    /// </summary>
    [ExcelColumnName("计算赋值(脚本) 选填")]
    public string Script { get; set; }

    /// <summary>
    ///     为属性添加自定义
    /// </summary>
    [ExcelColumnName("属性自定义映射 选填")]
    public string Custom { get; set; }

    /// <summary>
    ///     属性标签 选填
    /// </summary>
    [ExcelColumnName("属性标签 选填")]
    public string Tag { get; set; }

    /// <summary>
    ///     采集周期
    /// </summary>
    [ExcelColumnName("采集周期 选填")]
    public string PeriodEx { get; set; }

    /// <summary>
    ///     强制归档时间
    /// </summary>
    [ExcelColumnName("强制归档时间 选填")]
    public string ArchiveTimeEx { get; set; }

    /// <summary>
    ///     持久化存储(脚本) 选填
    /// </summary>
    [ExcelColumnName("持久化存储(脚本) 选填")]
    public bool Persistence { get; set; }

    /// <summary>
    /// </summary>
    [ExcelColumnName("是否启用")]
    public bool Enable { get; set; }

    /// <summary>
    ///     描述
    /// </summary>
    [ExcelColumnName("描述 选填")]
    public string Description { get; set; }
}

/// <summary>
/// </summary>
public enum DeviceVariableInPortTypeEnum
{
    [Description("覆盖")] [Display(Name = "覆盖")]
    Coverage = 1,

    [Description("忽略")] [Display(Name = "忽略")]
    OverLook = 2
}