namespace IotPlatform.Thing.Entity;

/// <summary>
///     物模型-模板列表返回参数
/// </summary>
public class ThingTemplateListOutput
{
    /// <summary>
    ///     模型名称
    /// </summary>
    public string Name { get; set; }

    /// <summary>
    ///     uuid
    /// </summary>
    public string Uuid { get; set; }

    /// <summary>
    ///     物模型分类Id表
    /// </summary>
    public long ThingClassifyId { get; set; }

    /// <summary>
    ///     物模型分类表
    /// </summary>
    [JsonIgnore]
    public ModelClassify ModelClassify { get; set; }

    /// <summary>
    ///     物模型分类Id表集合
    /// </summary>
    public string ThingClassifyIdList => ModelClassify != null && ModelClassify.Pid != 0 ? ModelClassify.Pid + "," + ThingClassifyId : ThingClassifyId.ToString();

    /// <summary>
    ///     Id
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    ///     描述
    /// </summary>
    public string Remark { get; set; }
}