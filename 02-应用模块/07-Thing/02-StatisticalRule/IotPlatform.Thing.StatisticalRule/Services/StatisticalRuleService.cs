namespace IotPlatform.Thing.StatisticalRule.Services;

/// <summary>
///     物模型-统计规则
///     版 本:V5.0.0
///     版 权:杭州峰回科技有限公司
///     作 者:蒋状
///     日 期:2023-08-16
/// </summary>
[ApiDescriptionSettings("物模型")]
public class StatisticalRuleService : IDynamicApiController, ITransient
{
    private readonly ExecuteService _executeService;

    /// <summary>
    ///     定时任务
    /// </summary>
    private readonly ISchedulerFactory _schedulerFactory;

    private readonly ISqlSugarRepository<StatisticalRuleConf> _statisticalRule;
    private readonly IUserManager _userManager;

    public StatisticalRuleService(ISqlSugarRepository<StatisticalRuleConf> statisticalRule, ISchedulerFactory schedulerFactory, ExecuteService executeService, IUserManager userManager)
    {
        _statisticalRule = statisticalRule;
        _schedulerFactory = schedulerFactory;
        _executeService = executeService;
        _userManager = userManager;
    }

    /// <summary>
    ///     物模型-统计规则-列表
    /// </summary>
    /// <returns></returns>
    [HttpGet("/statisticalRule/page")]
    public async Task<SqlSugarPagedList<StatisticalRuleConf>> StatisticalRulePage([FromQuery] StatisticalRulePageInput input)
    {
        SqlSugarPagedList<StatisticalRuleConf> statisticalRulePage = await _statisticalRule.AsQueryable()
            .Where(w => w.ModelId == input.ModelId)
            .WhereIF(input.SearchValue.IsNotEmptyOrNull(), u => u.RuleCode.Contains(input.SearchValue) || u.Remark.ToString().Contains(input.SearchValue))
            .ToPagedListAsync(input.PageNo, input.PageSize);

        return statisticalRulePage;
    }

    /// <summary>
    ///     物模型-统计规则-详情
    /// </summary>
    /// <returns></returns>
    [HttpGet("/statisticalRule/detail")]
    public async Task<StatisticalRuleConf> StatisticalRuleDetail([FromQuery] BaseId input)
    {
        StatisticalRuleConf? statisticalRule = await _statisticalRule.AsQueryable().FirstAsync(f => f.Id == input.Id);
        return statisticalRule;
    }

    /// <summary>
    ///     物模型-统计规则-新增
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/statisticalRule/add")]
    [UnitOfWork]
    public async Task StatisticalRuleAdd(StatisticalRuleAddInput input)
    {
        Model? model = await _statisticalRule.AsSugarClient().Queryable<Model>().FirstAsync(f => f.Id == input.ModelId);
        if (model == null)
        {
            throw Oops.Oh("物模型已经被删除！");
        }

        StatisticalRuleConf statisticalRule = input.Adapt<StatisticalRuleConf>();
        if (!string.IsNullOrEmpty(statisticalRule.RuleCode))
        {
            if (await _statisticalRule.IsAnyAsync(u => u.RuleCode == statisticalRule.RuleCode))
            {
                throw Oops.Oh("规则编码已存在！");
            }
        }
        else
        {
            statisticalRule.RuleCode = RandomExtensions.GetRandomString(10, false);
        }

        statisticalRule.Id = YitIdHelper.NextId();
        statisticalRule.Model = model;
        statisticalRule.TenantId = _userManager.TenantId;
        foreach (StatisticalParameter parameter in statisticalRule.Parameter.Where(parameter => string.IsNullOrEmpty(parameter.Identification)))
        {
            parameter.Identification = parameter.Name.ConvertPinYin();
        }

        // 保证Code唯一性
        while (await _statisticalRule.IsAnyAsync(u => u.RuleCode == statisticalRule.RuleCode))
        {
            statisticalRule.RuleCode = RandomExtensions.GetRandomString(10, false);
        }

        // 创建定时任务
        CreateStatisticalRuleJob(statisticalRule);
        await _statisticalRule.InsertAsync(statisticalRule);
    }

    /// <summary>
    ///     物模型-统计规则-修改
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/statisticalRule/update")]
    [UnitOfWork]
    public async Task StatisticalRuleUpdate(StatisticalRuleUpdateInput input)
    {
        Model? model = await _statisticalRule.AsSugarClient().Queryable<Model>().FirstAsync(f => f.Id == input.ModelId);
        if (model == null)
        {
            throw Oops.Oh("物模型已经被删除！");
        }

        // 统计规则
        StatisticalRuleConf? statisticalRule = await _statisticalRule.AsQueryable().Where(f => f.Id == input.Id).Includes(w => w.Model).FirstAsync();
        if (statisticalRule == null)
        {
            throw Oops.Oh(ErrorCode.D1002);
        }

        statisticalRule = input.Adapt<StatisticalRuleConf>();
        statisticalRule.Model = model;
        statisticalRule.TenantId = _userManager.TenantId;
        foreach (StatisticalParameter parameter in statisticalRule.Parameter.Where(parameter => string.IsNullOrEmpty(parameter.Identification)))
        {
            parameter.Identification = parameter.Name.ConvertPinYin();
        }

        // 停止Job
        _schedulerFactory.RemoveJob(statisticalRule.Id.ToString());
        // 创建定时任务
        CreateStatisticalRuleJob(statisticalRule);
        await _statisticalRule.AsSugarClient().Updateable(statisticalRule).IgnoreColumns(w => w.RuleCode).ExecuteCommandAsync();
    }

    /// <summary>
    ///     物模型-统计规则-删除
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/statisticalRule/delete")]
    [UnitOfWork]
    public async Task StatisticalRuleDelete(BaseId input)
    {
        StatisticalRuleConf? statisticalRule = await _statisticalRule.AsQueryable().Where(f => f.Id == input.Id).Includes(w => w.Model).FirstAsync();
        if (statisticalRule == null)
        {
            return;
        }

        // 停止Job
        _schedulerFactory.RemoveJob(statisticalRule.Id.ToString());
        await _statisticalRule.DeleteAsync(statisticalRule);
    }

    /// <summary>
    ///     创建统计规则定时任务
    /// </summary>
    /// <param name="statisticalRule"></param>
    private void CreateStatisticalRuleJob(StatisticalRuleConf statisticalRule)
    {
        JobBuilder? jobBuilder = JobBuilder.Create<StatisticalRuleJob>()
            .SetJobId(statisticalRule.Id.ToString()) // 作业 Id
            .SetGroupName("物模型-统计规则") // 作业组名称
            .SetJobType("IotPlatform.Thing.StatisticalRule", "IotPlatform.Thing.StatisticalRule.Job.StatisticalRuleJob") // 作业类型，支持多个重载
            .SetJobType<StatisticalRuleJob>() // 作业类型，支持多个重载
            .SetJobType(typeof(StatisticalRuleJob)) // 作业类型，支持多个重载
            .SetDescription(!string.IsNullOrEmpty(statisticalRule.Remark) ? statisticalRule.RuleCode + "_" + statisticalRule.Remark : statisticalRule.RuleCode) // 作业描述
            .SetConcurrent(true) // 并行还是串行方式，false 为 串行
            .SetIncludeAnnotations(true) // 是否扫描 IJob 类型的触发器特性，true 为 扫描
            .SetProperties("{}") // 作业额外数据 Dictionary<string, object> 类型序列化，支持多个重载
            .SetProperties(new Dictionary<string, object> {{"StatisticalRule", statisticalRule.ToJson()}});

        // 按分钟 or 小时创建定时任务
        string cron = statisticalRule.DateTimeUnit == DateTimeUnitEnum.Minute ? $"00 0/{statisticalRule.DateTimeNumber} * ? * *" : $"00 00 0/{statisticalRule.DateTimeNumber} ? * *";
        _schedulerFactory.TryAddJob(jobBuilder, new[]
        {
            Triggers.Cron(cron, CronStringFormat.WithSeconds).SetTriggerId(statisticalRule.Id.ToString())
        }, out IScheduler? scheduler);
        scheduler.UpdateDetail(jobBuilder =>
        {
            jobBuilder.SetDescription(!string.IsNullOrEmpty(statisticalRule.Remark) ? statisticalRule.RuleCode + "_" + statisticalRule.Remark : statisticalRule.RuleCode);
            jobBuilder.SetGroupName("物模型-统计规则");
        });
        scheduler.Persist();

        _statisticalRule.AsSugarClient().Updateable<SysJobDetail>().SetColumns(u => new SysJobDetail {CreateType = JobCreateTypeEnum.Script})
            .Where(u => u.JobId.Equals(statisticalRule.Id.ToString())).ExecuteCommand();
    }

    /// <summary>
    ///     创建规则数据库超级表
    /// </summary>
    /// <param name="statisticalRule"></param>
    /// <exception cref="ArgumentOutOfRangeException"></exception>
    private void CreateStatisticalRuleTable(StatisticalRuleConf statisticalRule)
    {
        string sql = $"CREATE STABLE IF NOT EXISTS {statisticalRule.RuleCode} (ts timestamp,startTime timestamp, endTime timestamp";
        switch (statisticalRule.StatisticalRuleType)
        {
            case StatisticalRuleTypeEnum.Total:
            {
                sql += "," + "_first float";
                sql += "," + "_last float";
                sql += "," + "_sum float";

                break;
            }

            case StatisticalRuleTypeEnum.MaxOrMinOrAvg:
            {
                sql += "," + "_max float";
                sql += "," + "_min float";
                sql += "," + "_avg float";

                break;
            }
            case StatisticalRuleTypeEnum.OpenOrClose:
            {
                sql += "," + "_open Int";
                sql += "," + "_close Int";
                sql += "," + "_openTime BigInt";

                break;
            }
            case StatisticalRuleTypeEnum.If:
            {
                foreach (StatisticalParameter parameter in statisticalRule.Parameter)
                {
                    sql += "," + parameter.Identification + "_count BigInt";
                    sql += "," + parameter.Identification + "_time BigInt";
                }

                break;
            }
            default:
                throw new ArgumentOutOfRangeException();
        }

        sql += ") TAGS(deviceName nchar(128));";
        _ = _executeService.ExecuteCommand(sql);
    }
}