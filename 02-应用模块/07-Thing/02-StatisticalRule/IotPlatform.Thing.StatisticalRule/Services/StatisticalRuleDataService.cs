using DateTime = System.DateTime;

namespace IotPlatform.Thing.StatisticalRule.Services;

/// <summary>
///     数据统计-数据查看
/// </summary>
[ApiDescriptionSettings("数据统计")]
public class StatisticalRuleDataService : ID<PERSON><PERSON><PERSON><PERSON>ontroller, ITransient
{
    private readonly ISqlSugarRepository<StatisticalRuleConf> _statisticalRule;
    private readonly IUserManager _userManager; // 用户管理

    public StatisticalRuleDataService(ISqlSugarRepository<StatisticalRuleConf> statisticalRule, IUserManager userManager)
    {
        _statisticalRule = statisticalRule;
        _userManager = userManager;
    }

    /// <summary>
    ///     数据统计-树
    /// </summary>
    /// <returns></returns>
    [HttpGet("/statisticalRuleData/tree")]
    public async Task<List<StatisticalRuleDataTreeOutput>> StatisticalRuleDataTree()
    {
        // 规则统计
        List<StatisticalRuleConf>? statisticalRuleList = await _statisticalRule.AsQueryable()
            .Includes(w => w.Model, w => w.ThingExample)
            .ToListAsync();
        // 按类型分组
        List<IGrouping<StatisticalRuleTypeEnum, StatisticalRuleConf>> statisticalRuleGroupList = statisticalRuleList.GroupBy(w => w.StatisticalRuleType).ToList();
        //  返回参数
        List<StatisticalRuleDataTreeOutput> output = new();
        foreach (IGrouping<StatisticalRuleTypeEnum, StatisticalRuleConf> statisticalRuleGroup in statisticalRuleGroupList)
        {
            // 返回参数
            StatisticalRuleDataTreeOutput ruleDataTreeOutput = new() {RuleType = statisticalRuleGroup.Key, Model = new List<StatisticalRuleDataTreeModel>()};
            foreach (StatisticalRuleConf? statisticalRule in statisticalRuleGroup)
            {
                if (statisticalRule.Model == null)
                {
                    continue;
                }

                // 模型配置
                StatisticalRuleDataTreeModel ruleDataTreeModel = new()
                {
                    Propertys = statisticalRule.Propertys,
                    Id = statisticalRule.ModelId,
                    RuleCode = statisticalRule.RuleCode,
                    // Name = statisticalRule.Dto.Name + "（" + statisticalRule.DateTimeNumber + statisticalRule.DateTimeUnitName + "）",
                    Name = statisticalRule.Remark + "（" + statisticalRule.DateTimeNumber + statisticalRule.DateTimeUnitName + "）",
                    Uuid = statisticalRule.Model.Uuid
                };

                ruleDataTreeOutput.Model.Add(ruleDataTreeModel);
            }

            output.Add(ruleDataTreeOutput);
        }

        return output;
    }

    /// <summary>
    ///     数据统计-列表
    /// </summary>
    /// <returns></returns>
    [HttpGet("/statisticalRuleData/page")]
    public async Task<dynamic> StatisticalRuleDataPage([FromQuery] StatisticalRuleDataPageInput input)
    {
        // 统计规则
        StatisticalRuleConf? statisticalRule = await _statisticalRule.AsQueryable()
            .Where(w => w.ModelId == input.ModelId && w.RuleCode == input.RuleCode)
            .Includes(w => w.Model)
            .FirstAsync();
        if (statisticalRule == null)
        {
            throw Oops.Oh("统计规则已经被删除,请刷新后重试!");
        }

        DateTime startTime = input.TimeName.ParseToDateTime();
        string timeType = input.TimeUnit switch
        {
            "日" => "day",
            "月" => "day",
            "年" => "month",
            "历年" => "year"
        };
        DateTime endTime = input.TimeUnit == "日" ? startTime.AddDays(+1)
            : input.TimeUnit == "月" ? startTime.AddDays(+DateTime.Now.Day)
            : startTime.AddMonths(+DateTime.Now.Month);
        // 分表名称
        string tableName = input.TimeUnit == "日"
            ? $"report_{statisticalRule.RuleCode}_{statisticalRule.Model?.Uuid}_{startTime:yyyy-MM}"
            : $"report_{statisticalRule.RuleCode}_{statisticalRule.Model?.Uuid}_carryover";

        switch (statisticalRule.StatisticalRuleType)
        {
            case StatisticalRuleTypeEnum.Total:
            {
                switch (input.TimeUnit)
                {
                    case "日":
                    {
                        List<ReportTotal>? output = await _statisticalRule.AsSugarClient().Queryable<ReportTotal>()
                            .SplitTable(tabs => tabs.InTableNames(tableName))
                            .Where(w => w.DeviceName == input.ThingIdentification && w.TagName == input.Identification)
                            .Where(w => w.StartTime >= startTime && w.StartTime < endTime)
                            .ToListAsync();
                        return output;
                    }
                    case "月":
                    case "年":
                    case "历年":
                    {
                        return await _statisticalRule.AsSugarClient().Queryable<ReportTotalCarryover>()
                            .SplitTable(tabs => tabs.InTableNames(tableName))
                            .Where(w => w.Type == timeType)
                            .Where(w => w.DeviceName == input.ThingIdentification && w.TagName == input.Identification)
                            .Where(w => w.StartTime >= startTime && w.StartTime < endTime)
                            .ToListAsync();
                    }
                    default:
                        throw Oops.Oh("暂不支持时间单位");
                }
            }
            case StatisticalRuleTypeEnum.MaxOrMinOrAvg:
            {
                switch (input.TimeUnit)
                {
                    case "日":
                    {
                        return await _statisticalRule.AsSugarClient().Queryable<ReportMax>()
                            .SplitTable(tabs => tabs.InTableNames(tableName))
                            .Where(w => w.DeviceName == input.ThingIdentification && w.TagName == input.Identification)
                            .Where(w => w.StartTime >= startTime && w.StartTime < endTime)
                            .ToListAsync();
                    }
                    case "月":
                    case "年":
                    case "历年":
                    {
                        return await _statisticalRule.AsSugarClient().Queryable<ReportMaxCarryover>()
                            .SplitTable(tabs => tabs.InTableNames(tableName))
                            .Where(w => w.Type == timeType)
                            .Where(w => w.DeviceName == input.ThingIdentification && w.TagName == input.Identification)
                            .Where(w => w.StartTime >= startTime && w.StartTime < endTime)
                            .ToListAsync();
                    }
                    default:
                        throw Oops.Oh("暂不支持时间单位");
                }
            }
            case StatisticalRuleTypeEnum.OpenOrClose:
            {
                switch (input.TimeUnit)
                {
                    case "日":
                    {
                        return await _statisticalRule.AsSugarClient().Queryable<ReportOpen>()
                            .SplitTable(tabs => tabs.InTableNames(tableName))
                            .Where(w => w.DeviceName == input.ThingIdentification && w.TagName == input.Identification)
                            .Where(w => w.StartTime >= startTime && w.StartTime < endTime)
                            .ToListAsync();
                    }
                    case "月":
                    case "年":
                    case "历年":
                    {
                        return await _statisticalRule.AsSugarClient().Queryable<ReportOpenCarryover>()
                            .SplitTable(tabs => tabs.InTableNames(tableName))
                            .Where(w => w.Type == timeType)
                            .Where(w => w.DeviceName == input.ThingIdentification && w.TagName == input.Identification)
                            .Where(w => w.StartTime >= startTime && w.StartTime < endTime)
                            .ToListAsync();
                    }
                    default:
                        throw Oops.Oh("暂不支持时间单位");
                }
            }
            case StatisticalRuleTypeEnum.If:
            {
                switch (input.TimeUnit)
                {
                    case "日":
                    {
                        return await _statisticalRule.AsSugarClient().Queryable<ReportIf>()
                            .SplitTable(tabs => tabs.InTableNames(tableName))
                            .Where(w => w.DeviceName == input.ThingIdentification && w.TagName == input.Identification)
                            .Where(w => w.StartTime >= startTime && w.StartTime < endTime)
                            .ToListAsync();
                    }
                    case "月":
                    case "年":
                    case "历年":
                    {
                        return await _statisticalRule.AsSugarClient().Queryable<ReportIfCarryover>()
                            .SplitTable(tabs => tabs.InTableNames(tableName))
                            .Where(w => w.Type == timeType)
                            .Where(w => w.DeviceName == input.ThingIdentification && w.TagName == input.Identification)
                            .Where(w => w.StartTime >= startTime && w.StartTime < endTime)
                            .ToListAsync();
                    }
                    default:
                        throw Oops.Oh("暂不支持时间单位");
                }
            }
            default:
                throw Oops.Oh("暂不支持统计！");
        }
    }

    /// <summary>
    ///     数据统计-手动修正
    /// </summary>
    /// <returns></returns>
    [HttpPost("/statisticalRuleData/setReport")]
    public async Task SetReport(SetReportInput input)
    {
        // 统计规则
        StatisticalRuleConf? statisticalRule = await _statisticalRule.AsQueryable()
            .Where(w => w.ModelId == input.ModelId && w.RuleCode == input.RuleCode)
            .Includes(w => w.Model)
            .FirstAsync();
        if (statisticalRule == null)
        {
            throw Oops.Oh("统计规则已经被删除,请刷新后重试!");
        }

        if (statisticalRule.StatisticalRuleType != StatisticalRuleTypeEnum.Total)
        {
            throw Oops.Oh("暂不支持修正！");
        }

        DateTime startTime = Core.Extension.DateTime.ToTime(input.TimeName);
        // 分表名称
        string tableName = input.TimeUnit == "日"
            ? $"report_{statisticalRule.RuleCode}_{statisticalRule.Model?.Uuid}_{startTime:yyyy-MM}"
            : $"report_{statisticalRule.RuleCode}_{statisticalRule.Model?.Uuid}_carryover";

        switch (input.TimeUnit)
        {
            case "日":
            {
                ReportTotal? reportTotal = await _statisticalRule.AsSugarClient().Queryable<ReportTotal>()
                    .SplitTable(tabs => tabs.InTableNames(tableName))
                    .Where(w => w.Id == input.Id)
                    .FirstAsync();
                if (reportTotal == null)
                {
                    throw Oops.Oh("记录未找到！");
                }

                reportTotal.InputRemarks = input.InputRemarks;
                reportTotal.InputVal = input.InputVal;
                reportTotal.InputUserId = _userManager.RealName;
                reportTotal.InputTime = DateTime.Now;
                await _statisticalRule.AsSugarClient().Updateable(reportTotal)
                    .SplitTable(tabs => tabs.InTableNames(tableName)).ExecuteCommandAsync();
                break;
            }
            case "月":
            case "年":
            {
                ReportTotalCarryover? reportTotalCarryover = await _statisticalRule.AsSugarClient().Queryable<ReportTotalCarryover>()
                    .SplitTable(tabs => tabs.InTableNames(tableName))
                    .Where(w => w.Id == input.Id)
                    .FirstAsync();
                reportTotalCarryover.InputRemarks = input.InputRemarks;
                reportTotalCarryover.InputVal = input.InputVal;
                reportTotalCarryover.InputUserId = _userManager.RealName;
                reportTotalCarryover.InputTime = DateTime.Now;
                await _statisticalRule.AsSugarClient().Updateable(reportTotalCarryover)
                    .SplitTable(tabs => tabs.InTableNames(tableName)).ExecuteCommandAsync();
                break;
            }
            default:
                throw Oops.Oh("暂不支持时间单位");
        }
    }
}