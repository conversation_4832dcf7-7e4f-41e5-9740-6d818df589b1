using IotPlatform.Core.Extension;
using IotPlatform.Thing.Entity;

namespace IotPlatform.Thing.StatisticalRule.Entity;

/// <summary>
///     物模型-统计规则
/// </summary>
[SugarTable("business_statisticalRule", "物模型-统计规则")]
public class StatisticalRuleConf : EntityTenantId
{
    /// <summary>
    ///     物模型Id
    /// </summary>
    [SugarColumn(ColumnDescription = "物模型Id")]
    public long ModelId { get; set; }

    /// <summary>
    ///     规则编码
    /// </summary>
    [SugarColumn(ColumnDescription = "规则编码")]
    public string RuleCode { get; set; }

    /// <summary>
    ///     描述
    /// </summary>
    [SugarColumn(ColumnDescription = "描述", Length = 1024)]
    public string Remark { get; set; }

    /// <summary>
    ///     时间类型:1:分钟；2：小时
    /// </summary>
    [SugarColumn(ColumnDescription = "时间类型:1:分钟；2：小时")]
    public DateTimeUnitEnum DateTimeUnit { get; set; } = DateTimeUnitEnum.Minute;

    /// <summary>
    ///     间隔时长
    /// </summary>
    [SugarColumn(ColumnDescription = "间隔时长")]
    public int DateTimeNumber { get; set; } = 5;

    /// <summary>
    ///     统计类型：1：累计统计；2：MAX/MIN/AVG ；3:开关统计；4：条件统计
    /// </summary>
    [SugarColumn(ColumnDescription = "时间类型:1:分钟；2：小时")]
    public StatisticalRuleTypeEnum StatisticalRuleType { get; set; } = StatisticalRuleTypeEnum.Total;

    /// <summary>
    ///     统计属性
    /// </summary>
    [SugarColumn(ColumnDescription = "统计属性", IsJson = true, IsNullable = true)]
    public List<StatisticalProperty>? Propertys { get; set; }

    /// <summary>
    ///     统计参数
    /// </summary>
    [SugarColumn(ColumnDescription = "统计参数", IsJson = true, IsNullable = true)]
    public List<StatisticalParameter>? Parameter { get; set; }

    #region 忽略字段

    /// <summary>
    ///     统计类型：1：累计统计；2：MAX/MIN/AVG ；3:开关统计；4：条件统计
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public string StatisticalRuleTypeName => StatisticalRuleType.GetDescription();
    
    /// <summary>
    ///     时间类型:1:分钟；2：小时
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public string DateTimeUnitName => DateTimeUnit.GetDescription();

    #endregion
    
    #region 关联表

    /// <summary>
    ///     物模型
    /// </summary>
    [Navigate(NavigateType.OneToOne, nameof(ModelId))]
    public Model? Model { get; set; }

    #endregion
}

/// <summary>
///     条件统计-统计参数
/// </summary>
public class StatisticalParameter
{
    /// <summary>
    ///     唯一标识符
    /// </summary>
    public string Identification { get; set; }

    /// <summary>
    ///     显示名称
    /// </summary>
    public string Name { get; set; }

    /// <summary>
    ///     表达式
    /// </summary>
    public string Expression { get; set; }
}

/// <summary>
///     统计属性
/// </summary>
public class StatisticalProperty
{
    /// <summary>
    ///     物模型-属性Id
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    ///     物模型-属性标识符
    /// </summary>
    public string Identification { get; set; }

    /// <summary>
    ///     物模型-属性名称
    /// </summary>
    public string Name { get; set; }
}

/// <summary>
///     时间类型:1:分钟；2：小时
/// </summary>
public enum DateTimeUnitEnum
{
    /// <summary>
    ///     分钟
    /// </summary>
    [Description("分钟")] Minute = 1,

    /// <summary>
    ///     小时
    /// </summary>
    [Description("小时")] Hour = 2
}

/// <summary>
///     统计类型：1：累计统计；2：MAX/MIN/AVG ；3:开关统计；4：条件统计
/// </summary>
public enum StatisticalRuleTypeEnum
{
    /// <summary>
    ///     累计统计
    /// </summary>
    [Description("累计统计")] Total = 1,

    /// <summary>
    ///     MAX/MIN/AVG
    /// </summary>
    [Description("MAX/MIN/AVG")] MaxOrMinOrAvg = 2,

    /// <summary>
    ///     开关统计
    /// </summary>
    [Description("开关统计")] OpenOrClose = 3,

    /// <summary>
    ///     条件统计
    /// </summary>
    [Description("条件统计")] If = 4
}