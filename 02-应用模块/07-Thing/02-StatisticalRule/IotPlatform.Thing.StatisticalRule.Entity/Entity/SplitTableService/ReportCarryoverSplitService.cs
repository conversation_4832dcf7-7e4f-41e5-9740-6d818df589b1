namespace IotPlatform.Thing.StatisticalRule.Entity.SplitTableService;

/// <summary>
///     统计分表规则
/// </summary>
public class ReportCarryoverSplitService : ISplitTableService
{
    /// <summary>
    ///     返回数据库中所有分表
    /// </summary>
    /// <param name="db"></param>
    /// <param name="entityInfo"></param>
    /// <param name="tableInfos"></param>
    /// <returns></returns>
    public List<SplitTableInfo> GetAllTables(ISqlSugarClient db, EntityInfo entityInfo, List<DbTableInfo> tableInfos)
    {
        List<SplitTableInfo> result = new();
        foreach (DbTableInfo item in tableInfos)
        {
            if (item.Name.Contains("_carryover")) //区分标识如果不用正则符复杂一些，防止找错表
            {
                SplitTableInfo data = new()
                {
                    TableName = item.Name //要用item.name不要写错了
                };
                result.Add(data);
            }
        }

        return result.OrderBy(it => it.TableName).ToList(); //打断点看一下有没有查出所有分表
    }

    /// <summary>
    ///     获取分表字段的值
    /// </summary>
    /// <param name="db"></param>
    /// <param name="entityInfo"></param>
    /// <param name="splitType"></param>
    /// <param name="entityValue"></param>
    /// <returns></returns>
    public object GetFieldValue(ISqlSugarClient db, EntityInfo entityInfo, SplitType splitType, object entityValue)
    {
        EntityColumnInfo? splitColumn = entityInfo.Columns.FirstOrDefault(it => it.PropertyInfo.GetCustomAttribute<SplitFieldAttribute>() != null);
        object? value = splitColumn.PropertyInfo.GetValue(entityValue, null);
        return value;
    }

    /// <summary>
    /// </summary>
    /// <param name="db"></param>
    /// <param name="entityInfo"></param>
    /// <returns></returns>
    public string GetTableName(ISqlSugarClient db, EntityInfo entityInfo)
    {
        return entityInfo.DbTableName;
    }

    public string GetTableName(ISqlSugarClient db, EntityInfo entityInfo, SplitType type)
    {
        return entityInfo.DbTableName;
    }

    /// <summary>
    ///     确定生成数据库表的时候，表的名称
    /// </summary>
    /// <param name="db"></param>
    /// <param name="entityInfo"></param>
    /// <param name="splitType"></param>
    /// <param name="fieldValue"></param>
    /// <returns></returns>
    public string GetTableName(ISqlSugarClient db, EntityInfo entityInfo, SplitType splitType, object fieldValue)
    {
        return entityInfo.DbTableName + "_" + fieldValue; //根据值按首字母
    }
}