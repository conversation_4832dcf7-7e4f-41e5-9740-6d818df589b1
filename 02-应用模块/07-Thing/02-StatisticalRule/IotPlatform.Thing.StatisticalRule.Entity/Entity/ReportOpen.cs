using System;

namespace IotPlatform.Thing.StatisticalRule.Entity;

[SplitTable(SplitType._Custom02, typeof(ReportSplitService))]
[SugarTable("report", "开关统计")]
public class ReportOpen : EntityTenantId
{
    /// <summary>
    ///     存储的开始时间
    /// </summary>
    [SugarColumn(ColumnDescription = "存储的开始时间")]
    public DateTime StartTime { get; set; }

    /// <summary>
    ///     存储的结束时间
    /// </summary>
    [SugarColumn(ColumnDescription = "存储的结束时间")]
    public DateTime EndTime { get; set; }

    /// <summary>
    ///     设备名称
    /// </summary>
    [SugarColumn(ColumnDescription = "设备名称")]
    public string DeviceName { get; set; }

    /// <summary>
    ///     属性标识
    /// </summary>
    [SugarColumn(ColumnDescription = "属性标识")]
    public string TagName { get; set; }

    /// <summary>
    ///     开次数
    /// </summary>
    [SugarColumn(ColumnDescription = "开次数", IsNullable = true)]
    public double OpenNum { set; get; }

    /// <summary>
    ///     关次数
    /// </summary>
    [SugarColumn(ColumnDescription = "关次数", IsNullable = true)]
    public double ShutNum { get; set; }

    /// <summary>
    ///     开机时长
    /// </summary>
    [SugarColumn(ColumnDescription = "开机时长", IsNullable = true)]
    public double OpenTimeLong { get; set; }

    /// <summary>
    ///     分表标签
    /// </summary>
    [SplitField]
    [SugarColumn(IsIgnore = true)]
    [JsonIgnore]
    public string Key { get; set; }

    /// <summary>
    ///     时间
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public DateTime FTime => StartTime;
}