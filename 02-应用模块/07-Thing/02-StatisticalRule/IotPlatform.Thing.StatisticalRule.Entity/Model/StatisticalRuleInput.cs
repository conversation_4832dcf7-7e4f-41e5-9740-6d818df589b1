using IotPlatform.Thing.Entity;

namespace IotPlatform.Thing.StatisticalRule.Entity;

/// <summary>
///     物模型-统计规则-列表请求参数
/// </summary>
public class StatisticalRulePageInput : BasePageInput
{
    /// <summary>
    ///     物模型Id
    /// </summary>
    [Required]
    public long ModelId { get; set; }
}

/// <summary>
///     物模型-统计规则-新增请求参数
/// </summary>
public class StatisticalRuleAddInput
{
    /// <summary>
    ///     物模型Id
    /// </summary>
    [Required]
    public long ModelId { get; set; }

    /// <summary>
    ///     规则编码
    /// </summary>
    public string RuleCode { get; set; }

    /// <summary>
    ///     规则描述
    /// </summary>
    public string Remark { get; set; }

    /// <summary>
    ///     时间类型:1:分钟；2：小时
    /// </summary>
    public DateTimeUnitEnum DateTimeUnit { get; set; } = DateTimeUnitEnum.Minute;

    /// <summary>
    ///     间隔时长
    /// </summary>
    [Required]
    public short DateTimeNumber { get; set; } = 5;

    /// <summary>
    ///     统计类型：1：累计统计；2：MAX/MIN/AVG ；3:开关统计；4：条件统计
    /// </summary>
    public StatisticalRuleTypeEnum StatisticalRuleType { get; set; } = StatisticalRuleTypeEnum.Total;

    /// <summary>
    ///     统计属性
    /// </summary>
    [SugarColumn(ColumnDescription = "统计属性", IsJson = true, IsNullable = true)]
    [Required]
    public List<StatisticalProperty> Propertys { get; set; }

    /// <summary>
    ///     统计参数
    /// </summary>
    [SugarColumn(ColumnDescription = "统计参数", IsJson = true, IsNullable = true)]
    public List<StatisticalParameter> Parameter { get; set; } = new();
}

/// <summary>
///     物模型-统计规则-修改请求参数
/// </summary>
public class StatisticalRuleUpdateInput : StatisticalRuleAddInput
{
    /// <summary>
    ///     Id
    /// </summary>
    [Required]
    public long Id { get; set; }

    /// <summary>
    ///     规则编码
    /// </summary>
    [Required]
    public string RuleCode { get; set; }
}

/// <summary>
///     数据统计-列表 请求参数
/// </summary>
public class StatisticalRuleDataPageInput
{
    /// <summary>
    ///     规则编码
    /// </summary>
    [Required]
    public string RuleCode { get; set; }

    /// <summary>
    ///     物实例-标识
    /// </summary>
    [Required]
    public string ThingIdentification { get; set; }

    /// <summary>
    ///     物实例名称
    /// </summary>
    [Required]
    public string DeviceName { get; set; }

    /// <summary>
    ///     物模型Id
    /// </summary>
    [Required]
    public long ModelId { get; set; }

    /// <summary>
    ///     物模型-属性标识符
    /// </summary>
    [Required]
    public string Identification { get; set; }

    /// <summary>
    ///     时间单位:年；月；日
    /// </summary>
    [Required]
    public string TimeUnit { get; set; }

    /// <summary>
    ///     时间
    /// </summary>
    [Required]
    public string TimeName { get; set; }
}

/// <summary>
///     数据统计-手动修正
/// </summary>
public class SetReportInput
{
    /// <summary>
    ///     规则编码
    /// </summary>
    [Required]
    public string RuleCode { get; set; }

    /// <summary>
    ///     物模型Id
    /// </summary>
    [Required]
    public long ModelId { get; set; }

    /// <summary>
    ///     时间单位:年；月；日
    /// </summary>
    [Required]
    public string TimeUnit { get; set; }

    /// <summary>
    ///     时间
    /// </summary>
    [Required]
    public string TimeName { get; set; }

    /// <summary>
    ///     记录Id
    /// </summary>
    [Required]
    public long Id { get; set; }

    /// <summary>
    ///     修正值
    /// </summary>
    public double InputVal { get; set; }

    /// <summary>
    ///     修正备注
    /// </summary>
    public string InputRemarks { get; set; }
}