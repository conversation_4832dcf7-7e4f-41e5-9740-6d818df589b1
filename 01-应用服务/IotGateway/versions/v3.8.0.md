# Feng-Edge 更新日志

## V3.8.0

- 🌈 重构 `重构插件模块`
- 🌈 重构 `重构Dnc模块`
-

## V3.8.1

- 🌟 新增 `新增了更多的dnc相关http接口`
-
- 🌟 修改 `rk3506的串口信息调整`

## V3.8.1.1

- 🌟 新增 `Flink支持了端口的配置`
-
- 🌟 修改 `rk3506的命名改成FengEdge150`

## V3.8.1.2

- 🌟 新增 `增加蓝卓的新版本主题适配`
-
- 🌟 修改 `modbus串口改回原来下拉的选择方式`

## V3.8.1.3

- 🌟 修改 `moubusServer整体结构和功能调整优化`
-
- 🐞 修复 `modbusRtu无法正常连接的问题`

## V3.8.1.4

- 🌟 修改 `按照最新的底层fanuc协议同步`
-
- 🐞 修复 `modbus服务配置相关代码被回滚导致配置和订阅不生效问题`

## V3.8.1.6

- 🌟 修改 `fanuc协议和fanuc双通道协议增加了报警的语言问题`
- 🌟 修改 `cnc生成的默认字符串长度改为50`

## V3.8.1.7

- 🌟 新增 `兼容dnc的西门子插件，将依赖包System.IO.Ports升级到7.0版本`
- 🌟 新增 `mqtt增加了一个定时订阅mqtt主题的功能`
-
- 🌟 修改 `dnc相关的插件都已经根据现场使用进行适配完成`

## V3.8.1.9

- 🌟 优化 `自定义MQTT设置转发时，未配置脚本也允许发送内容，默认使用原样发送`
-
- 🌟 新增 `增加dnc插件nfs相关功能`
- 🌟 新增 `增加黑/白名单管理功能`
- 🌟 新增 `opcua服务端的插件服务，注：当前版本配置完需要重启采集程序服务`

## V3.8.2.3

- 🌟 优化 `opcua增加缓存机制，支持重启构建服务`
- 🌟 优化 `西门子协议批量读取优化`
- 🌟 优化 `dnc插件nfs测试调整`

## V3.8.2.4

- 🐞 修复 `opcua转发标识不唯一问题`

## v3.8.2.5

- 🌟 优化 `modbus协议重写了批量算法`
- 🌟 优化 `下行报文增加批量报文的耗时和读取起始地址和长度`
- 🌟 优化 `优化了采集间隔的算法，休眠时间 - 采集间隔耗时 = 实际休眠时间`
- 🌟 优化 `fanuc/fanuc18i协议适配最新底层文件，增加新的采集点，增加轴数配置`
- 🌟 优化 `西门子cnc协议适配最新底层文件，增加新的采集点`

## v3.8.2.6

- 🐞 修复 `Bool类型转发false问题修复`
- 
- 🌟 新增 `福斯特需求新增，针对opcua 服务一键创建 按照设备分组`



## v3.8.3.0

- 🌈 重构 `欧姆龙批量读取算法重写,采用最底层读取字节方式解析，性能最强`
- 🌈 重构 `hsl已经升级到v12.0+版本，协议全部适配最新版本`
-
- 🎯 优化 `截止该版本，modbus，西门子，欧姆龙批量读取都已经重写完成`