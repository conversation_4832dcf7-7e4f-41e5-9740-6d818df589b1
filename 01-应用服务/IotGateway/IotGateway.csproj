<Project Sdk="Microsoft.NET.Sdk.Web">

    <PropertyGroup>
        <TargetFrameworks>net6.0;net8.0</TargetFrameworks>
        <ImplicitUsings>enable</ImplicitUsings>
        <SatelliteResourceLanguages>en-US</SatelliteResourceLanguages>
        <PublishReadyToRunComposite>true</PublishReadyToRunComposite>
        <RootNamespace>IotGateway</RootNamespace>
        <UserSecretsId>55cf6b75-4642-44fc-92ae-90dd4747d7e2</UserSecretsId>
        <Version>3.8.3.0</Version>
        <!-- 使用opcda打包 使用不依赖环境方式 -->
         <!-- <Platforms>AnyCPU;x86</Platforms> -->
    </PropertyGroup>

    <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
        <DebugType>portable</DebugType>
        <Optimize>False</Optimize>
    </PropertyGroup>

    <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x86'">
        <DebugType>portable</DebugType>
        <Optimize>False</Optimize>
    </PropertyGroup>

    <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
        <DebugType>none</DebugType>
        <Optimize>True</Optimize>
        <PlatformTarget>AnyCPU</PlatformTarget>
    </PropertyGroup>

    <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x86'">
        <DebugType>none</DebugType>
        <Optimize>True</Optimize>
        <PlatformTarget>AnyCPU</PlatformTarget>
    </PropertyGroup>

    <ItemGroup>
        <InternalsVisibleTo Include="TestProject"/>
        <Compile Remove="Controllers\**"/>
        <Content Remove="Controllers\**"/>
        <EmbeddedResource Remove="Controllers\**"/>
        <None Remove="Controllers\**"/>
        <PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.21.0"/>
        <None Update="Quickstarts.ReferenceServer.Config.xml">
            <CopyToOutputDirectory>Always</CopyToOutputDirectory>
        </None>
        <None Update="Shell\apn_rg200u.sh">

            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="Shell\apn_m5700.sh">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="Shell\autoapn.sh">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="version3.0.x.md">
            <CopyToOutputDirectory>Never</CopyToOutputDirectory>
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="Shell\apn_m5700.sh">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="Shell\autoapn.sh">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="版本升级解决方案.md">
            <CopyToOutputDirectory>Never</CopyToOutputDirectory>
        </None>
        <None Update="TemplateComplex.xlsx">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="version3.0.x.md">
            <CopyToOutputDirectory>Never</CopyToOutputDirectory>

        </None>
        <None Update="Shell\dns_config.sh">
            <CopyToOutputDirectory>Always</CopyToOutputDirectory>
        </None>
        <None Update="Templates\EdgeComputingScriptImport.xlsx">
            <CopyToOutputDirectory>Always</CopyToOutputDirectory>
        </None>
        <None Update="Shell\openvpn_setup.sh">
            <CopyToOutputDirectory>Always</CopyToOutputDirectory>
        </None>
        <Content Update="sysappsettings.json">
            <CopyToOutputDirectory>Always</CopyToOutputDirectory>
        </Content>
        <None Update="upload.md">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="versions\v3.4.0.md">
            <CopyToOutputDirectory>Never</CopyToOutputDirectory>
        </None>
        <None Update="versions\v3.3.0.md">
            <CopyToOutputDirectory>Never</CopyToOutputDirectory>
        </None>
        <None Remove="IotGateway.csproj.DotSettings"/>
        <None Update="versions\v3.5.0.md">
            <CopyToOutputDirectory>Never</CopyToOutputDirectory>
        </None>
        <None Update="Templates\DeviceVariableImport.xlsx">
            <CopyToOutputDirectory>Always</CopyToOutputDirectory>
        </None>
        <None Update="Templates\DeviceVariableSimpleImport.xlsx">
            <CopyToOutputDirectory>Always</CopyToOutputDirectory>
        </None>
        <None Update="Templates\EdgeComputingPolicyImport.xlsx">
            <CopyToOutputDirectory>Always</CopyToOutputDirectory>
        </None>
        <None Update="versions\v3.6.0.md">
            <CopyToOutputDirectory>Never</CopyToOutputDirectory>
        </None>
        <None Update="versions\v3.7.0.md">
            <CopyToOutputDirectory>Never</CopyToOutputDirectory>
        </None>
        <None Update="versions\v3.8.0.md">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
    </ItemGroup>
    <ItemGroup>
        <ProjectReference Include="..\..\04-架构核心\IotGateway.Web.Core\IotGateway.Web.Core.csproj"/>
    </ItemGroup>
    <ItemGroup>
        <_ContentIncludedByDefault Remove="plugins\net6.0\IotGateway.Equipment.deps.json"/>
        <_ContentIncludedByDefault Remove="plugins\net6.0\IotGateway.Px30.deps.json"/>
        <_ContentIncludedByDefault Remove="plugins\net6.0\IotGateway.Windows.deps.json"/>
        <_ContentIncludedByDefault Remove="plugins\net6.0\IotGateway.Wr610.deps.json"/>
    </ItemGroup>
    <ItemGroup>
        <Folder Include="plugins\"/>
    </ItemGroup>
    <ProjectExtensions>
        <VisualStudio>
            <UserProperties properties_4launchsettings_1json__JsonSchema=""/>
        </VisualStudio>

        <Version>*******</Version>

    </ProjectExtensions>
</Project>
