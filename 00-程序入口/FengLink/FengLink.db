SQLite format 3   @     +   B           
                                                 + .f�
  �
�	GM��vaD?�k"      �n
11�tableEdgeGateway_dg_tmpEdgeGateway_dg_tmpCREATE TABLE EdgeGateway_dg_tmp
(
    Id        bigint      not null
�:--�'tableGatewayTransPondGatewayTransPondCREATE TABLE "GatewayTransPond"(
"Id" bigint NOT NULL PRIMARY KEY ,
"EdgeGatewayId" bigint NOT NULL  ,
"Identifier" varchar(255) NOT NULL  ,
"TransPondType" integer NOT NULL  ,
"Config" varchar(255) NOT NULL  ,
"Enable" bit NOT NULL  ,
"Master" bit NOT NULL    )�x##�7tablesystem_usersystem_userCREATE TABLE "system_user"(
"Id" bigint NOT NULL PRIMARY KEY ,
"Account" varchar(50) NOT NULL  ,
"Password" varchar(50) NOT NULL  ,
"Name" varchar(20) NOT NULL  ,
"LastLoginTime" datetime NULL  ,
"AdminType" integer NOT NULL  ,
"Status" bit NOT NULL  ,
"CreatedTime" datetime NULL  ,
"UpdatedTime" datetime NULL  ,
"CreatedUserId" bigint NULL  ,
"UpdatedUserId" bigint NULL  ,
"CreatedUserName" varchar(255) NULL  ,
"UpdatedUserName" varchar(255) NULL    )5I# indexsqlite_autoindex_system_user_1system_user
�h	''�tableGatewayDriverGatewayDriver
CREATE TABLE "GatewayDriver"(
"Id" bigint NOT NULL PRIMARY KEY ,
"EdgeGatewayId" bigint NOT NULL  ,
"DriverType" integer NOT NULL  ,
"Name" varchar(64) NOT NULL  ,
"Methods" text NULL    )9
M' indexsqlite_autoindex_GatewayDriver_1GatewayDriver�]77�YtableGatewayDeviceVariableGatewayDeviceVariableCREATE TABLE "GatewayDeviceVariable"(
"Id" bigint NOT NULL PRIMARY KEY ,
"Identifier" varchar(128) NOT NULL  ,
"Name" varchar(128) NOT NULL  ,
"DefaultValue" varchar(128) NULL  ,
"Length" smallint(12) NOT NULL  ,
"TransitionType" integer(6) NOT NULL  ,
"ValueSource" integer(6) NOT NULL  ,
"Unit" varchar(32) NULL  ,
"Order" smallint(6) NOT NULL  ,
"Custom" text NULL  ,
"Expressions" text NULL  ,
"Script" text NULL  ,
"DeviceId" bigint(32) NOT NULL  ,
"SendType" integer(6) NOT NULL  ,
"Enable" bit NOT NULL  ,
"Tags" text NOT NULL  ,
"Period" integer(32) NOT NULL  ,
"ArchiveTime" integer(32) NOT NULL  ,
"Description" text NULL  ,
"DeviceVariableFilter" varchar(4000) NULL  ,
"DeviceVariableEx" varchar(4000) NOT NULL  ,
"IsSystem" bit NOT NULL  ,
"Persistence" bit NOT NULL    )I]7 indexsqlite_autoindex_GatewayDeviceVariable_1GatewayDeviceVariable	�33�-tableGatewayDeviceConfigGatewayDeviceConfigCREATE TABLE "GatewayDeviceConfig"(
"Id" bigint NOT NULL PRIMARY KEY ,
"DeviceConfigName" varchar(255) NOT NULL  ,
"Description" varchar(255) NULL  ,
"Value" varchar(255) NOT NULL  ,
"EnumInfo" varchar(255) NULL  ,
"DeviceId" bigint NOT NULL  ,
"GroupName" varchar(255) NULL  ,
"Remark" varchar(255) NULL  ,
"IsRequired" bit NULL  ,
"Display" bit NULL  ,
"DisplayExpress" varchar(255) NULL  ,
"Order" smallint NULL  ,
"Type" varchar(255) NOT NULL    )EY3 indexsqlite_autoindex_GatewayDeviceConfig_1GatewayDeviceConfig�L''�WtableGatewayDeviceGatewayDeviceCREATE TABLE "GatewayDevice"(
"Id" bigint NOT NULL PRIMARY KEY ,
"EdgeGatewayId" bigint NOT NULL  ,
"DeviceName" varchar(128) NOT NULL  ,
"OtherName" varchar(128) NULL  ,
"DataCollectionReportingRule" integer NOT NULL  ,
"Index" integer NOT NULL  ,
"Description" text NULL  ,
"DriverId" bigint NOT NULL  ,
"Enable" bit NOT NULL  ,
"CreatedTime" datetime NULL  ,
"UpdatedTime" datetime NULL  ,
"CreatedUserId" bigint NULL  ,
"UpdatedUserId" bigint NULL  ,
"CreatedUserName" varchar(255) NULL  ,
"UpdatedUserName" varchar(255) NULL    )9M' indexsqlite_autoindex_GatewayDevice_1GatewayDevicec ?S- indexsqlite_autoindex_GatewayTransPond_1GatewayTransPond       5I# indexsqlite_autoindex_EdgeGateway_1EdgeGateway�[
##�}tableEdgeGatewayEdgeGatewayCREATE TABLE "EdgeGateway"
(
    Id        bigint      not null
        primary key,
    Name      varchar(64) not null,
    Sn        varchar(64) not null,
    Vsersion  varchar(16),
    FirstTime datetime    not null,
    LastTime  datetime    not null,
    RunTime   datetime    not null,
    Config    text
)
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     ��Q�E	 ��&�E
   � �"�T
�
�
:�w�                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             Z 	
	;  +  �/�� ��Q�Etttc Fx�82024-07-16 20:00:35.391  �mri W超级管理员^
 	
;  +  ���� ��Q�EZZ_3_4_c i9��m2024-07-16 20:00:35.391  �mri W超级管理员_	 	
;  +  ���0� ��Q�EFANUC_01c Fx�92024-07-16 20:00:35.391  �mri W超级管理员` 	
;  +  ��sa@� ��Q�EErpResultc i9��i2024-07-16 20:00:35.391  �mri W超级管理员Z 	
;  +  ��2��� ��Q�Ettyc i9��s2024-07-16 20:00:35.390  �mri W超级管理员Y 	
;    ��p4�� ��Q�Ehumiturec Fx�82024-07-16 20:00:35.390  �mri X管理员a	
;    �n�� � ��Q�EDVP_PLC台达PLCc i9��m2024-07-16 20:00:35.390  �mri X管理员b+	
;    �l�F`� ��Q�ECT电流互感器c i9��82024-07-16 20:00:35.390  �mri X管理员h)	
;    �jn � ��Q�Eelectric_meter电能表c i9��82024-07-16 20:00:35.390  �mri X管理员l+	
	;  +  �o0� ��Q�EAX_256碳氢回收机c Fx�82024-07-16 20:00:35.390  �mri W超级管理员n+	
;  +  ��o`� ��Q�EAX_TQHSQ碳氢回收机c Fx�82024-07-16 20:00:35.390  �mri W超级管理员
   r ���������r                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     �/�� ����
 ���0�	 ��sa@� ��2��� ��p4�� �n�� � �l�F`� �jn � �o0�	 ��o`�   �    &������8
�
�
*	����q� .��7 � � X                      O+- %
		
 �o0�MessageInterval报文间隔(ms)0 �o0�高级配置ctext? %
		
 �o0�Port端口号502 �o0�连接配置ctextK# %
		
 �o0�IpAddressIP地址*********** �o0�连接配置ctext�6#9 %�'	3 �o0�WaitTimeout监听超时时间(ms)30000 �o0�高级配置等待的超时时间，如果超时时间为-1的话，则是无期限等待{"ReadType": ["2"]}text�%9 %m	3 �o0�ReadInterval刷新数据频率(ms)100 �o0�高级配置监听属性地址的读取频率,单位(毫秒){"ReadType": ["2"]}text�'%
 %{	3 �o0�VariableValue指定的值 �o0�高级配置设置指定的值,满足后会触发读取设备地址{"ReadType": ["2"]}text^- %
	3	 �o0�MinPeriod轮询周期(ms)1000 �o0�高级配置{"ReadType": ["1"]}text�,1%
 %�	3	 �o0�VariableIdentifier监听属性 �o0�高级配置等待指定地址的值为设定指定的值,再触发读取设备地址{"ReadType": ["2"]}combine�o%_%�		
 �o0�ReadType读取模式1{"轮询模式":1,"订阅触发模式":2} �o0�高级配置轮询模式：主动遍历轮询读取配置采集点
订阅触发模式：订阅某个地址，达到设置值后在进行读取text�''9 %�/		
 ��R �WriteInterval写入间隔周期(ms)120 ��o`�高级配置每个报文的间隔时间，避免数据写入不成功（针对低速设备）ctext�$%5%�#		
 ��R �BulkRead批量读取1{"true":1,"false":2} ��o`�高级配置不连续报文组成合并包，减少查询包次数，缩短轮询周期ctext�
+ %�		
 ��R�WaitTime等待时间(s)0 ��o`�高级配置设备每次进行连接前等待的时间，通常针对慢设备ctext� - %y		
 ��R�Timeout超时时间(ms)3000 ��o`�高级配置发报文收到响应报文的时间，超出算超时ctext� !+ %y		
 ��R�ReConnTime重连周期(s)60 ��o`�高级配置当设备关机后下一轮进行重新连接的时间ctext�\#+ %�-		
 ��R �BackoffTime退避时间(m)10 ��o`�高级配置当设备连续超时达到设置超时次数时，进入退避逻辑开始计时，达到退避设定时间，再将该设备加入到轮询里ctext�b%% %�?		
 ��R �TimeOutCount超时次数0 ��o`�高级配置设备所有属性均超时计为一次设备超时，连续超时次数达到设定次数，进去退避逻辑，再下一次的轮询里摘除该设备ctext`
'+5%
		
 ��Q��StringReverse字符串颠倒2{"true":1,"false":2} ��o`�连接配置ctext`'+5%
		
 ��Q��ByteTransform大小端转换2{"true":1,"false":2} ��o`�连接配置ctextk!%W%
		
 ��Q��DataFormat解析类型0{"ABCD":0,"BADC":1,"CDAB":2,"DCBA":3} ��o`�连接配置ctext=
 %
		
 ��Q��Station站号1 ��o`�连接配置ctextO	+- %
		
 ��Qp�MessageInterval报文间隔(ms)0 ��o`�高级配置ctext? %
		
 ��Q`�Port端口号502 ��o`�连接配置ctextL% %
		
 ��Q`�IpAddressIP地址************ ��o`�连接配置ctext�6#9 %�'	3 ��Q��WaitTimeout监听超时时间(ms)30000 ��o`�高级配置等待的超时时间，如果超时时间为-1的话，则是无期限等待{"ReadType": ["2"]}text�%9 %m	3 ��Q��ReadInterval刷新数据频率(ms)100 ��o`�高级配置监听属性地址的读取频率,单位(毫秒){"ReadType": ["2"]}text�'%
 %{	3 ��Q��VariableValue指定的值 ��o`�高级配置设置指定的值,满足后会触发读取设备地址{"ReadType": ["2"]}text]- %
	3	 ��R0�MinPeriod轮询周期(ms)200 ��o`�高级配置{"ReadType": ["1"]}text�,1%
 %�	3	 ��Q��VariableIdentifier监听属性 ��o`�高级配置等待指定地址的值为设定指定的值,再触发读取设备地址{"ReadType": ["2"]}combine�o%_%�		
 ��Q��ReadType读取模式1{"轮询模式":1,"订阅触发模式":2} ��o`�高级配置轮询模式：主动遍历轮询读取配置采集点
订阅触发模式：订阅某个地址，达�   %�'   $�   #o   "T   !9    
   � ���reX�����K>1$
�����������{naTG:- 
�
�
�
�
w
j
]
P
C
6
)

�
�
�
�
�

�����
�fYL?2%������s���������.!
�oUH;|
�
�
�
�
�
�b
�
7
*


	�	�
x
^
Q
D
�	�	�	�	�	�	�	�
k	d	V	H	:	,		�		���	r����v�hZL>0�
�"���������zl^PB4&
���������~pbTF8*                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        
 �/
�� �
 �/
p� �
 �/
p� �
 �/
`� �
 �/
`� �
 �/
`� �
 �/
P� �
 �/
P� �
 �/
@� �
 �/
@� �
 �/
0� �
 �/
0� �
 �/
� �
 �/
� �
 �/
 � �
 �/	�� �
 �/	�� �
 �/	�� �
 �/	�� �
 �/	�� �
 ��P� �
 ��@� �
 ��0� �
 �� � �
 �� � �
 ��� �
 �� � �
 ���� �
 ���� �
 ���� �
 ��� �
 �� � �
 ���� �
 �� � �
 ���� �
 ���� �
 ��`� �
 ��P� �
 �� � �
 ���w � �
 ���w � �
 ���w � �
 ���w� �
 ���w� �
 ���v�� �
 ���v�� �
 ���v�� �
 ���w � �
 ���w� �
 ��sS0� �
 ��sR�� �
 ��2��� �
 ��2��� �
 ��2��� �
 ��2�p� �
 ��2�p� �
 ��2�P� �
 ��2�@� �
 ��2�0� �
 ��2� � �
 ��2�� �
 ��2��� �
 ��2��� �
 ��2�`� � ��p(�� ��p(��~ ��p(��} ��p(��| ��p(��{ ��p(��z ��p(��y ��p(@�x ��p(0�w ��p(�v ��p( �u ��p( �t ��p( �s ��p( �r ��p(p�q ��p(`�p ��p(P�o ��p(��n ��p(@�m ��p(p�l  ����k ��YE �j �n����i �n����h �n����g �n����f �n����e �n����d �n�Ҡ�c �n�Ґ�b �n�Ҁ�a �n��p�` �n��`�_ �n�Ұ�^ �n�Ұ�] �n�Ұ�\ �n����[ �n�Ұ�Z �n����Y �l�F �X �l�F �W �l�F �V �l�F �U �l�F �T �l�F �S �l�E��R �l�E��Q �l�E��P �l�E��O �l�E��N �l�E��M �l�E��L �l�E��K �l�E��J �l�E��I �l�E��H �l�E��G �l�E��F �l�E��E �l�E��D �l�F �C �l�E��B �l�E��A �jm`�@ �jm`�? �jm`�> �jm`�= �jmP�< �jmP�; �jmP�: �jm@�9 �jm@�8 �jm@�7 �jm@�6 �jm@�5 �jm0�4 �jm �3 �jm�2 �jm�1 �jl��0 �jl��/ �jmP�. �jmP�- �jmP�, �jm`�+ �jmP�* �jmP�) �o0�( �o0�' �o0�& �o0�% �o0�$ �o0�# �o0�" �o0�! �o0�  �o0� �o0� �o0� �o0� �o0� �o0� �o0� �o0� �o0� �o0� �o0� ��R � ��R � ��R� ��R� ��R� ��R � ��R � ��Q��
 ��Q�� ��Q�� ��Q��
 ��Qp�	 ��Q`� ��Q`� ��Q�� ��Q�� ��Q�� ��R0� ��Q��	 ��Q��   �    ���������������                                                                                                                                �a!7
	
 

		
�M�w �k[@�htfcurtemp导热油当前温度 ��o`�[]{"Min":0.0,"Max":0.0,"MinFilterType":1,"SetMin":0.0,"SetMax":0.0,"MaxFilterType":1,"Save":false}{"ProtectType":1,"ProtectTypeName":"只读","Method":"ReadHoldingRegisters","RegisterAddress":"59","DataType":4,"DataTypeName":"int16","Encoding":1,"EncodingName":"utf8","Length":0}�\
''
	
 

		
�M�w �k[0�recoverycount下写参数1 ��o`�[]{"Min":0.0,"Max":0.0,"MinFilterType":1,"SetMin":0.0,"SetMax":0.0,"MaxFilterType":1,"Save":false}{"ProtectType":2,"ProtectTypeName":"读写","Method":"ReadHoldingRegisters","RegisterAddress":"57","DataType":4,"DataTypeName":"int16","Encoding":1,"EncodingName":"utf8","Length":0}�V	%
	
 

		
�M�u �k[0�steamtemp蒸汽温度 ��o`�[]{"Min":0.0,"Max":0.0,"MinFilterType":1,"SetMin":0.0,"SetMax":0.0,"MaxFilterType":1,"Save":false}{"ProtectType":1,"ProtectTypeName":"只读","Method":"ReadHoldingRegisters","RegisterAddress":"1","DataType":4,"DataTypeName":"int16","Encoding":1,"EncodingName":"utf8","Length":0}�dA
	
 

		
�M�w �k[ �autohour自动运行时间(小时) ��o`�[]{"Min":0.0,"Max":0.0,"MinFilterType":1,"SetMin":0.0,"SetMax":0.0,"MaxFilterType":1,"Save":false}{"ProtectType":1,"ProtectTypeName":"只读","Method":"ReadHoldingRegisters","RegisterAddress":"39","DataType":4,"DataTypeName":"int16","Encoding":1,"EncodingName":"utf8","Length":0}�L
	
 

		
�M�w �k[�stop停止 ��o`�[]{"Min":0.0,"Max":0.0,"MinFilterType":1,"SetMin":0.0,"SetMax":0.0,"MaxFilterType":1,"Save":false}{"ProtectType":1,"ProtectTypeName":"只读","Method":"ReadHoldingRegisters","RegisterAddress":"80","DataType":4,"DataTypeName":"int16","Encoding":1,"EncodingName":"utf8","Length":0}�K
	
 

		
�M�w �k[ �run启动 ��o`�[]{"Min":0.0,"Max":0.0,"MinFilterType":1,"SetMin":0.0,"SetMax":0.0,"MaxFilterType":1,"Save":false}{"ProtectType":1,"ProtectTypeName":"只读","Method":"ReadHoldingRegisters","RegisterAddress":"79","DataType":4,"DataTypeName":"int16","Encoding":1,"EncodingName":"utf8","Length":0}�Y%%
	
 

		
�M�u �kZ��devicestatus运行状态 ��o`�[]{"Min":0.0,"Max":0.0,"MinFilterType":1,"SetMin":0.0,"SetMax":0.0,"MaxFilterType":1,"Save":false}{"ProtectType":2,"ProtectTypeName":"读写","Method":"ReadHoldingRegisters","RegisterAddress":"4","DataType":4,"DataTypeName":"int16","Encoding":1,"EncodingName":"utf8","Length":0}�c!;
	
 

		
�M�w �kZ��autominute自动运行时间(分) ��o`�[]{"Min":0.0,"Max":0.0,"MinFilterType":1,"SetMin":0.0,"SetMax":0.0,"MaxFilterType":1,"Save":false}{"ProtectType":1,"ProtectTypeName":"只读","Method":"ReadHoldingRegisters","RegisterAddress":"37","DataType":4,"DataTypeName":"int16","Encoding":1,"EncodingName":"utf8","Length":0}�c!;
	
 

		
�M�w �kZ��autosecond自动运行时间(秒) ��o`�[]{"Min":0.0,"Max":0.0,"MinFilterType":1,"SetMin":0.0,"SetMax":0.0,"MaxFilterType":1,"Save":false}{"ProtectType":1,"ProtectTypeName":"只读","Method":"ReadHoldingRegisters","RegisterAddress":"35","DataType":4,"DataTypeName":"int16","Encoding":1,"EncodingName":"utf8","Length":0}�S
	
 

		
�M�s �kZ��totalflow总流量 ��o`�[]{"Min":0.0,"Max":0.0,"MinFilterType":1,"SetMin":0.0,"SetMax":0.0,"MaxFilterType":1,"Save":false}{"ProtectType":1,"ProtectTypeName":"只读","Method":"ReadInputRegisters","RegisterAddress":"20","DataType":4,"DataTypeName":"int16","Encoding":1,"EncodingName":"utf8","Length":0}�5% 	 	Q c		-=�M�S	 �kZ��online在线状态{"True":"在线","False":"离线"}return device.Status('${this.DeviceName}'); ��o`�["系统属性"]true:在线;false:关机{"Min":0.0,"Max":0.0,"MinFilterType":1,"SetMin":0.0,"SetMax":0.0,"MaxFilterType":1,"Save":false}{"ProtectType":1,"ProtectTypeName":"只读","Method":null,"RegisterAddress":null,"DataT   �   }   q   e   b   X   N   I   G   ?   5   +          
   �	: ���������reXK>1$
��������{naTG:- 
�
�
�
�
�
�
�
�
�
�
w
j
]
P
C
6
)


��������sfYL?2%�������������|obUH;.!
��
�
�
�
�
�
�
�
�
�
x
k
^
Q
D
7
*


	�	�	�	�	�	�	�	�	�	�	r	d	V	:	H                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        
 �/
�� �
 
Ϲg� �
 f[�p� �
 1�� �
 ���� �
 ��`� � ���y`� ���yP�~ ���yP�} ���yP�| ���yP�{ ���yP�z ���yP�y ���y@�x ���y@�w ���y@�v ���y@�u ���y@�t ���y@�s ���y@�r ���y0�q ���y0�p ���y0�o ���y0�n ���y0�m ���y0�l ���y �k ���y �j ���y �i ���y �h ���y �g ���y �f ���y�e ���x��d ��+p�c ��+`�b ��+P�a ��+@�` ��+@�_ ��+0�^ ��+ �] ��+�\ ��+ �[ ��*��Z ��*��Y ��*��X ��*��W ��*��V ��(��U ��8�p�T ��2���S ��9� �R ��O@�Q ��p(��P �1��`�O ���@�N �r�2 �M �r�0�L �r�A��K �rn۠�J �r`
@�I �q�`��H �q�V0�G �p@�@�F �p:|��E �oհ�D �n����C �m�@�B �m~p�A �l�w@�@ �l�F �? ��M�0�> ��8* �= �ju���< �jj� �; �j_ �: �j0�0�9 �jm`�8 �o0�7 �o0�6 �o0�5 �o0�4 �o0�3 �o0�2 �o0�1 �o0�0 �o0�/ �o0�. �o0�- �o0�, �o0�+ �o0�* �o0�) �o0�( �o0�' �o0�& �o0�% �o0�$ �o0�# �o0�" �o0�! �o0�  �o0� �o0� �o0� ��O�� ���ʠ� �س�0� �k[�� �k[�� �k[�� �k[�� �k[�� �k[�� �k[�� �k[�� �k[�� �k[�� �k[p� �k[`� �k[P�
 �k[P� �k[@� �k[0�
 �k[0�	 �k[ � �k[� �k[ � �kZ�� �kZ�� �kZ�� �kZ��	 �kZ��   �    B����������������������                                                                                                                                                                                                                                                                                                                                                                                                                                                    �H	�_ Fx�: ��Q�EFanuc18i[{"Text":"模式","Value":"ReadWorkMode","Desc":"0:手动输入；1:自动循环；3:程序编辑；4:×100;5:连续进给；6:TeachInJog；7:TeachInHandle；8:InCfeed；9:ReFerence；10:ReMoTe"},{"Text":"状态","Value":"ReadRunStatus","Desc":"0:Reset；1:Stop；2:Hold；3:Start；4:Mstr；"},{"Text":"报警状态","Value":"ReadAlarmStatus","Desc":""},{"Text":"绝对坐标","Value":"ReadAbsPos","Desc":""},{"Text":"机械坐标","Value":"ReadMachPos","Desc":""},{"Text":"相对坐标","Value":"ReadRelPos","Desc":""},{"Text":"报警信息","Value":"ReadAlarm","Desc":""},{"Text":"报警Id","Value":"ReadAlarmId","Desc":""},{"Text":"报警类型Id","Value":"ReadAlarmType","Desc":""},{"Text":"程序名","Value":"ReadMainName","Desc":""},{"Text":"程序号","Value":"ReadCurPgm","Desc":""},{"Text":"产量","Value":"ReadCurrentNum","Desc":""},{"Text":"主轴倍率","Value":"ReadOvSpin","Desc":""},{"Text":"进给速度","Value":"ReadActFeed","Desc":""},{"Text":"主轴温度","Value":"ReadSpinTemp","Desc":""},{"Text":"伺服温度","Value":"ReadSvTemp","Desc":""},{"Text":"刀具号","Value":"ReadToolNum","Desc":""},{"Text":"设定进给速度","Value":"ReadSetFSpeed","Desc":""},{"Text":"设定主轴速度","Value":"ReadSetsSpeed","Desc":""},{"Text":"进给倍率","Value":"ReadOvFeed","Desc":""},{"Text":"伺服负载","Value":"ReadAxisLoad","Desc":""},{"Text":"主轴转速","Value":"ReadsSpeed","Desc":""},{"Text":"上电时间","Value":"ReadAliveTime","Desc":""},{"Text":"循环时间","Value":"ReadCycleTime","Desc":""},{"Text":"切削时间","Value":"ReadCutTime","Desc":""},{"Text":"运行时间","Value":"ReadRunTime","Desc":""},{"Text":"主轴负载","Value":"ReadSpindleLoad","Desc":""}]�F	�a Fx�9 ��Q�EFanuc[{"Text":"模式","Value":"ReadWorkMode","Desc":"0:手动输入；1:自动循环；3:程序编辑；4:×100;5:连续进给；6:TeachInJog；7:TeachInHandle；8:InCfeed；9:ReFerence；10:ReMoTe"},{"Text":"状态","Value":"ReadRunStatus","Desc":"0:Reset；1:Stop；2:Hold；3:Start；4:Mstr；"},{"Text":"报警状态","Value":"ReadAlarmStatus","Desc":""},{"Text":"程序名","Value":"ReadMainName","Desc":""},{"Text":"程序号","Value":"ReadCurPgm","Desc":""},{"Text":"绝对坐标","Value":"ReadAbsPos","Desc":""},{"Text":"机械坐标","Value":"ReadMachPos","Desc":""},{"Text":"相对坐标","Value":"ReadRelPos","Desc":""},{"Text":"报警信息","Value":"ReadAlarm","Desc":""},{"Text":"报警Id","Value":"ReadAlarmId","Desc":""},{"Text":"报警类型Id","Value":"ReadAlarmType","Desc":""},{"Text":"产量","Value":"ReadCurrentNum","Desc":""},{"Text":"主轴温度","Value":"ReadSpinTemp","Desc":""},{"Text":"伺服温度","Value":"ReadSvTemp","Desc":""},{"Text":"刀具号","Value":"ReadToolNum","Desc":""},{"Text":"主轴倍率","Value":"ReadOvSpin","Desc":""},{"Text":"进给速度","Value":"ReadActFeed","Desc":""},{"Text":"进给设定速度","Value":"ReadSetFSpeed","Desc":""},{"Text":"主轴速度","Value":"ReadsSpeed","Desc":""},{"Text":"主轴设定速度","Value":"ReadSetsSpeed","Desc":""},{"Text":"进给倍率","Value":"ReadOvFeed","Desc":""},{"Text":"伺服负载","Value":"ReadAxisLoad","Desc":""},{"Text":"上电时间","Value":"ReadAliveTime","Desc":""},{"Text":"循环时间","Value":"ReadCycleTime","Desc":""},{"Text":"切削时间","Value":"ReadCutTime","Desc":""},{"Text":"运行时间","Value":"ReadCycSec","Desc":""},{"Text":"主轴负载","Value":"ReadSpindleLoad","Desc":""},{"Text":"读PMC变�   AE   @B   >@   :>   8=   77   63   5,   4+   3*   2)   1(   0"   /   .   -   ,   +   *
   )   (   '
   Fs K���:����reX>��1������{nG- �$

�
�
�
�
�
�
�
�
�
�
w
jaT
]
P
6
)�


C�
����������s�                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                i9��rF i9��qE i9��sD i9��pC i9��oB i9��nA i9��m@ i9��l? i9��k> i9��i= i9��g< i9��f; i9��c: i9��b9 i9��_8 i9��^7 i9��d6 i9��]5 i9��\4 i9��Y3 i9��X2 i9��W1 i9��V0 i9��U/ i9��T. i9��S- i9��Q, i9��P+ i9��O* i9��N) i9��R( i9��H' i9��G& i9��F% i9��E$ Fx�<# i9��D" i9��[! i9��Z  i9��C i9��B i9��A i9��j i9��@ i9��? i9��> i9��= i9��< Fx�@ i9��: i9��9 i9��M i9��L i9��K i9��; i9��8 Fx�8 Fx�D
 Fx�C Fx�B Fx�A
 i9��e	 i9��J Fx�? Fx�> Fx�= i9��` Fx�; Fx�:	 Fx�9
: � ��                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               }!M+ 		;;      �mri WsuperAdminc246266016e1e235aa62adf75ea29e18超级管理员2024-07-16 18:02:03.0782024-07-17 09:29:10.034   QM 	;       �mri Xadmine10adc3949ba59abbe56e057f20f883e管理员2024-07-16sM 	;;      �mri Xadmine10adc3949ba59abbe56e057f20f883e管理员2024-07-16 18:02:03.0832024-07-17 09:29:10.034
   � ��                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             �mri X	  �mri W
 � ��                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            y	--3C3 ��&�EfengEdge-windowsae4410e23950cbfa1.1.01900-01-01 00:00:002024-07-16 15:50:35.51283552024-07-16 07:19:18{}   s!+3C3 ��Q�EhogoBox6102412140001000123.5.6.01900-01-01 00:00:002024-07-16 19:44:48.31685832024-07-16 11:40:1x	!+;;; ��Q�EhogoBox6102412140001000123.5.6.01900-01-01 00:00:00.0002024-07-16 20:00:35.2952024-07-16 11:40:13.000{}
   � ��                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            ��Q�E	 ��&�E
    � H��
&�|-�m �                                                                                                                                            �a!7
	
 

		
�M�w �k[@�htfcurtemp导热油当前温度 ��o`�[]{"Min":0.0,"Max":0.0,"MinFilterType":1,"SetMin":0.0,"SetMax":0.0,"MaxFilterType":1,"Save":false}{"ProtectType":1,"ProtectTypeName":"只读","Method":"ReadHoldingRegisters","RegisterAddress":"59","DataType":4,"DataTypeName":"int16","Encoding":1,"EncodingName":"utf8","Length":0}�\
''
	
 

		
�M�w �k[0�recoverycount下写参数1 ��o`�[]{"Min":0.0,"Max":0.0,"MinFilterType":1,"SetMin":0.0,"SetMax":0.0,"MaxFilterType":1,"Save":false}{"ProtectType":2,"ProtectTypeName":"读写","Method":"ReadHoldingRegisters","RegisterAddress":"57","DataType":4,"DataTypeName":"int16","Encoding":1,"EncodingName":"utf8","Length":0}�V	%
	
 

		
�M�u �k[0�steamtemp蒸汽温度 ��o`�[]{"Min":0.0,"Max":0.0,"MinFilterType":1,"SetMin":0.0,"SetMax":0.0,"MaxFilterType":1,"Save":false}{"ProtectType":1,"ProtectTypeName":"只读","Method":"ReadHoldingRegisters","RegisterAddress":"1","DataType":4,"DataTypeName":"int16","Encoding":1,"EncodingName":"utf8","Length":0}�dA
	
 

		
�M�w �k[ �autohour自动运行时间(小时) ��o`�[]{"Min":0.0,"Max":0.0,"MinFilterType":1,"SetMin":0.0,"SetMax":0.0,"MaxFilterType":1,"Save":false}{"ProtectType":1,"ProtectTypeName":"只读","Method":"ReadHoldingRegisters","RegisterAddress":"39","DataType":4,"DataTypeName":"int16","Encoding":1,"EncodingName":"utf8","Length":0}�L
	
 

		
�M�w �k[�stop停止 ��o`�[]{"Min":0.0,"Max":0.0,"MinFilterType":1,"SetMin":0.0,"SetMax":0.0,"MaxFilterType":1,"Save":false}{"ProtectType":1,"ProtectTypeName":"只读","Method":"ReadHoldingRegisters","RegisterAddress":"80","DataType":4,"DataTypeName":"int16","Encoding":1,"EncodingName":"utf8","Length":0}�K
	
 

		
�M�w �k[ �run启动 ��o`�[]{"Min":0.0,"Max":0.0,"MinFilterType":1,"SetMin":0.0,"SetMax":0.0,"MaxFilterType":1,"Save":false}{"ProtectType":1,"ProtectTypeName":"只读","Method":"ReadHoldingRegisters","RegisterAddress":"79","DataType":4,"DataTypeName":"int16","Encoding":1,"EncodingName":"utf8","Length":0}�Y%%
	
 

		
�M�u �kZ��devicestatus运行状态 ��o`�[]{"Min":0.0,"Max":0.0,"MinFilterType":1,"SetMin":0.0,"SetMax":0.0,"MaxFilterType":1,"Save":false}{"ProtectType":2,"ProtectTypeName":"读写","Method":"ReadHoldingRegisters","RegisterAddress":"4","DataType":4,"DataTypeName":"int16","Encoding":1,"EncodingName":"utf8","Length":0}�c!;
	
 

		
�M�w �kZ��autominute自动运行时间(分) ��o`�[]{"Min":0.0,"Max":0.0,"MinFilterType":1,"SetMin":0.0,"SetMax":0.0,"MaxFilterType":1,"Save":false}{"ProtectType":1,"ProtectTypeName":"只读","Method":"ReadHoldingRegisters","RegisterAddress":"37","DataType":4,"DataTypeName":"int16","Encoding":1,"EncodingName":"utf8","Length":0}�c!;
	
 

		
�M�w �kZ��autosecond自动运行时间(秒) ��o`�[]{"Min":0.0,"Max":0.0,"MinFilterType":1,"SetMin":0.0,"SetMax":0.0,"MaxFilterType":1,"Save":false}{"ProtectType":1,"ProtectTypeName":"只读","Method":"ReadHoldingRegisters","RegisterAddress":"35","DataType":4,"DataTypeName":"int16","Encoding":1,"EncodingName":"utf8","Length":0}�S
	
 

		
�M�s �kZ��totalflow总流量 ��o`�[]{"Min":0.0,"Max":0.0,"MinFilterType":1,"SetMin":0.0,"SetMax":0.0,"MaxFilterType":1,"Save":false}{"ProtectType":1,"ProtectTypeName":"只读","Method":"ReadInputRegisters","RegisterAddress":"20","DataType":4,"DataTypeName":"int16","Encoding":1,"EncodingName":"utf8","Length":0}�5% 	 	Q c		-=�M�S	 �kZ��online在线状态{"True":"在线","False":"离线"}return device.Status('${this.DeviceName}'); ��o`�["系统属性"]true:在线;false:关机{"Min":0.0,"Max":0.0,"MinFilterType":1,"SetMin":0.0,"SetMax":0.0,"MaxFilterType":1,"Save":false}{"ProtectType":1,"ProtectTypeName":"只读","Method":null,"RegisterAddress":null,"DataType":6,"DataTypeName":"int32","Encoding":1,"EncodingName":"utf8","Length":0}
    � �
5�
m	�B�z �                                                                                                                                                   �c%7
	
 

		
�M�w �k[��htflimittemp导热油极限温度 ��o`�[]{"Min":0.0,"Max":0.0,"MinFilterType":1,"SetMin":0.0,"SetMax":0.0,"MaxFilterType":1,"Save":false}{"ProtectType":1,"ProtectTypeName":"只读","Method":"ReadHoldingRegisters","RegisterAddress":"91","DataType":4,"DataTypeName":"int16","Encoding":1,"EncodingName":"utf8","Length":0}�`/%
	
 

		
�M�w �k[��Instantaneousflow瞬时流量 ��o`�[]{"Min":0.0,"Max":0.0,"MinFilterType":1,"SetMin":0.0,"SetMax":0.0,"MaxFilterType":1,"Save":false}{"ProtectType":1,"ProtectTypeName":"只读","Method":"ReadHoldingRegisters","RegisterAddress":"50","DataType":4,"DataTypeName":"int16","Encoding":1,"EncodingName":"utf8","Length":0}�d-1
	
 

		
�M�w �k[��slagsetcountdown排渣时间设置 ��o`�[]{"Min":0.0,"Max":0.0,"MinFilterType":1,"SetMin":0.0,"SetMax":0.0,"MaxFilterType":1,"Save":false}{"ProtectType":1,"ProtectTypeName":"只读","Method":"ReadHoldingRegisters","RegisterAddress":"77","DataType":4,"DataTypeName":"int16","Encoding":1,"EncodingName":"utf8","Length":0}�^'+
	
 

		
�M�w �k[��slagcountdown排渣倒计时 ��o`�[]{"Min":0.0,"Max":0.0,"MinFilterType":1,"SetMin":0.0,"SetMax":0.0,"MaxFilterType":1,"Save":false}{"ProtectType":1,"ProtectTypeName":"只读","Method":"ReadHoldingRegisters","RegisterAddress":"75","DataType":4,"DataTypeName":"int16","Encoding":1,"EncodingName":"utf8","Length":0}�e/1
	
 

		
�M�w �k[��steamsetcountdown蒸干计时设置 ��o`�[]{"Min":0.0,"Max":0.0,"MinFilterType":1,"SetMin":0.0,"SetMax":0.0,"MaxFilterType":1,"Save":false}{"ProtectType":1,"ProtectTypeName":"只读","Method":"ReadHoldingRegisters","RegisterAddress":"73","DataType":4,"DataTypeName":"int16","Encoding":1,"EncodingName":"utf8","Length":0}�_)+
	
 

		
�M�w �k[��steamcountdown蒸干倒计时 ��o`�[]{"Min":0.0,"Max":0.0,"MinFilterType":1,"SetMin":0.0,"SetMax":0.0,"MaxFilterType":1,"Save":false}{"ProtectType":1,"ProtectTypeName":"只读","Method":"ReadHoldingRegisters","RegisterAddress":"71","DataType":4,"DataTypeName":"int16","Encoding":1,"EncodingName":"utf8","Length":0}�^+'
	
 

		
�M�w �k[��recoverysettime下写参数3 ��o`�[]{"Min":0.0,"Max":0.0,"MinFilterType":1,"SetMin":0.0,"SetMax":0.0,"MaxFilterType":1,"Save":false}{"ProtectType":2,"ProtectTypeName":"读写","Method":"ReadHoldingRegisters","RegisterAddress":"69","DataType":4,"DataTypeName":"int16","Encoding":1,"EncodingName":"utf8","Length":0}�^+'
	
 

		
�M�w �k[p�recoverycurtime下写参数2 ��o`�[]{"Min":0.0,"Max":0.0,"MinFilterType":1,"SetMin":0.0,"SetMax":0.0,"MaxFilterType":1,"Save":false}{"ProtectType":2,"ProtectTypeName":"读写","Method":"ReadHoldingRegisters","RegisterAddress":"67","DataType":4,"DataTypeName":"int16","Encoding":1,"EncodingName":"utf8","Length":0}�d-1
	
 

		
�M�w �k[`�coldwatersettemp冷水温度设置 ��o`�[]{"Min":0.0,"Max":0.0,"MinFilterType":1,"SetMin":0.0,"SetMax":0.0,"MaxFilterType":1,"Save":false}{"ProtectType":1,"ProtectTypeName":"只读","Method":"ReadHoldingRegisters","RegisterAddress":"63","DataType":4,"DataTypeName":"int16","Encoding":1,"EncodingName":"utf8","Length":0}�d
-1
	
 

		
�M�w �k[P�coldwatercurtemp冷水当前温度 ��o`�[]{"Min":0.0,"Max":0.0,"MinFilterType":1,"SetMin":0.0,"SetMax":0.0,"MaxFilterType":1,"Save":false}{"ProtectType":1,"ProtectTypeName":"只读","Method":"ReadHoldingRegisters","RegisterAddress":"63","DataType":4,"DataTypeName":"int16","Encoding":1,"EncodingName":"utf8","Length":0}�a!7
	
 

		
�M�w �k[P�htfsettemp导热油温度设置 ��o`�[]{"Min":0.0,"Max":0.0,"MinFilterType":1,"SetMin":0.0,"SetMax":0.0,"MaxFilterType":1,"Save":false}{"ProtectType":1,"ProtectTypeName":"只读","Method":"ReadHoldingRegisters","RegisterAddress":"61","DataType":4,"DataTypeName":"int16","Encoding":1,"EncodingName":"utf8","Length":0}
   
 � ��
�	7���y �                                                                                                                                                 �c !;
	
 

		
�M�w �o0�autominute自动运行时间(分) �o0�[]{"Min":0.0,"Max":0.0,"MinFilterType":1,"SetMin":0.0,"SetMax":0.0,"MaxFilterType":1,"Save":false}{"ProtectType":1,"ProtectTypeName":"只读","Method":"ReadHoldingRegisters","RegisterAddress":"37","DataType":4,"DataTypeName":"int16","Encoding":1,"EncodingName":"utf8","Length":0}�c!;
	
 

		
�M�w �o0�autosecond自动运行时间(秒) �o0�[]{"Min":0.0,"Max":0.0,"MinFilterType":1,"SetMin":0.0,"SetMax":0.0,"MaxFilterType":1,"Save":false}{"ProtectType":1,"ProtectTypeName":"只读","Method":"ReadHoldingRegisters","RegisterAddress":"35","DataType":4,"DataTypeName":"int16","Encoding":1,"EncodingName":"utf8","Length":0}�S
	
 

		
�M�s �o0�totalflow总流量 �o0�[]{"Min":0.0,"Max":0.0,"MinFilterType":1,"SetMin":0.0,"SetMax":0.0,"MaxFilterType":1,"Save":false}{"ProtectType":1,"ProtectTypeName":"只读","Method":"ReadInputRegisters","RegisterAddress":"20","DataType":4,"DataTypeName":"int16","Encoding":1,"EncodingName":"utf8","Length":0}�5% 	 	Q c		-=�M�S	 �o0�online在线状态{"True":"在线","False":"离线"}return device.Status('${this.DeviceName}'); �o0�["系统属性"]true:在线;false:关机{"Min":0.0,"Max":0.0,"MinFilterType":1,"SetMin":0.0,"SetMax":0.0,"MaxFilterType":1,"Save":false}{"ProtectType":1,"ProtectTypeName":"只读","Method":null,"RegisterAddress":null,"DataType":6,"DataTypeName":"int32","Encoding":1,"EncodingName":"utf8","Length":0}�V!!
	
 

		
�M�w ��O��steamtemp1steamtemp1 ��o`�[]{"Min":0.0,"Max":0.0,"MinFilterType":1,"SetMin":0.0,"SetMax":0.0,"MaxFilterType":1,"Save":false}{"ProtectType":1,"ProtectTypeName":"只读","Method":"ReadHoldingRegisters","RegisterAddress":"0","DataType":4,"DataTypeName":"int16","Encoding":1,"EncodingName":"utf8","Length":10}�T)#
 

		
�M�S ���ʠ�steamlimittemp条码号20241105001 ��o`�[]{"Min":0.0,"Max":0.0,"MinFilterType":1,"SetMin":0.0,"SetMax":0.0,"MaxFilterType":1,"Save":false}{"ProtectType":0,"ProtectTypeName":"只读","Method":null,"RegisterAddress":null,"DataType":6,"DataTypeName":"int32","Encoding":1,"EncodingName":"utf8","Length":0}�21
	
 
�a		
�M�S �س�0�isRun是否正在运行var stop = device.Get('${this.DeviceName}.stop');
if (stop == 0)
{
    return true;
}
return false;
 ��o`�[]{"Min":0.0,"Max":0.0,"MinFilterType":1,"SetMin":0.0,"SetMax":0.0,"MaxFilterType":1,"Save":false}{"ProtectType":0,"ProtectTypeName":"只读","Method":null,"RegisterAddress":null,"DataType":6,"DataTypeName":"int32","Encoding":1,"EncodingName":"utf8","Length":0}�
#

 
�		
�M�S �k[��consumption耗电量var Status =  device.Status('${this.DeviceName}');
if(Status){
  var consumption = device.Get('${this.DeviceName}.consumption');
  consumption = parseInt(consumption) + 1;
  return consumption;
} ��o`�[]{"Min":0.0,"Max":0.0,"MinFilterType":1,"SetMin":0.0,"SetMax":0.0,"MaxFilterType":1,"Save":false}{"ProtectType":0,"ProtectTypeName":"只读","Method":null,"RegisterAddress":null,"DataType":6,"DataTypeName":"int32","Encoding":1,"EncodingName":"utf8","Length":0}�

 
�G		
�M�S �k[��Status状态var  Status = device.Status('${this.DeviceName}');
if(Status){
  return 2
}
else 
  return -1 ��o`�[]{"Min":0.0,"Max":0.0,"MinFilterType":1,"SetMin":0.0,"SetMax":0.0,"MaxFilterType":1,"Save":false}{"ProtectType":0,"ProtectTypeName":"只读","Method":null,"RegisterAddress":null,"DataType":6,"DataTypeName":"int32","Encoding":1,"EncodingName":"utf8","Length":0}�f11
	
 

		
�M�w �k[��coldwaterlimittemp冷水极限温度 ��o`�[]{"Min":0.0,"Max":0.0,"MinFilterType":1,"SetMin":0.0,"SetMax":0.0,"MaxFilterType":1,"Save":false}{"ProtectType":1,"ProtectTypeName":"只读","Method":"ReadHoldingRegisters","RegisterAddress":"95","DataType":4,"DataTypeName":"int16","Encoding":1,"EncodingName":"utf8","Length":0}
    � �
V
�	G��!�S �                                                                                                                                                                                                               �c++1
	
 

		
�M�w �o0�recoverycurtime回收当前时间 �o0�[]{"Min":0.0,"Max":0.0,"MinFilterType":1,"SetMin":0.0,"SetMax":0.0,"MaxFilterType":1,"Save":false}{"ProtectType":1,"ProtectTypeName":"只读","Method":"ReadHoldingRegisters","RegisterAddress":"67","DataType":4,"DataTypeName":"int16","Encoding":1,"EncodingName":"utf8","Length":0}�d*-1
	
 

		
�M�w �o0�coldwatersettemp冷水温度设置 �o0�[]{"Min":0.0,"Max":0.0,"MinFilterType":1,"SetMin":0.0,"SetMax":0.0,"MaxFilterType":1,"Save":false}{"ProtectType":1,"ProtectTypeName":"只读","Method":"ReadHoldingRegisters","RegisterAddress":"63","DataType":4,"DataTypeName":"int16","Encoding":1,"EncodingName":"utf8","Length":0}�d)-1
	
 

		
�M�w �o0�coldwatercurtemp冷水当前温度 �o0�[]{"Min":0.0,"Max":0.0,"MinFilterType":1,"SetMin":0.0,"SetMax":0.0,"MaxFilterType":1,"Save":false}{"ProtectType":1,"ProtectTypeName":"只读","Method":"ReadHoldingRegisters","RegisterAddress":"63","DataType":4,"DataTypeName":"int16","Encoding":1,"EncodingName":"utf8","Length":0}�a(!7
	
 

		
�M�w �o0�htfsettemp导热油温度设置 �o0�[]{"Min":0.0,"Max":0.0,"MinFilterType":1,"SetMin":0.0,"SetMax":0.0,"MaxFilterType":1,"Save":false}{"ProtectType":1,"ProtectTypeName":"只读","Method":"ReadHoldingRegisters","RegisterAddress":"61","DataType":4,"DataTypeName":"int16","Encoding":1,"EncodingName":"utf8","Length":0}�a'!7
	
 

		
�M�w �o0�htfcurtemp导热油当前温度 �o0�[]{"Min":0.0,"Max":0.0,"MinFilterType":1,"SetMin":0.0,"SetMax":0.0,"MaxFilterType":1,"Save":false}{"ProtectType":1,"ProtectTypeName":"只读","Method":"ReadHoldingRegisters","RegisterAddress":"59","DataType":4,"DataTypeName":"int16","Encoding":1,"EncodingName":"utf8","Length":0}�[&'%
	
 

		
�M�w �o0�recoverycount回收次数 �o0�[]{"Min":0.0,"Max":0.0,"MinFilterType":1,"SetMin":0.0,"SetMax":0.0,"MaxFilterType":1,"Save":false}{"ProtectType":1,"ProtectTypeName":"只读","Method":"ReadHoldingRegisters","RegisterAddress":"57","DataType":4,"DataTypeName":"int16","Encoding":1,"EncodingName":"utf8","Length":0}�V%%
	
 

		
�M�u �o0�steamtemp蒸汽温度 �o0�[]{"Min":0.0,"Max":0.0,"MinFilterType":1,"SetMin":0.0,"SetMax":0.0,"MaxFilterType":1,"Save":false}{"ProtectType":1,"ProtectTypeName":"只读","Method":"ReadHoldingRegisters","RegisterAddress":"1","DataType":4,"DataTypeName":"int16","Encoding":1,"EncodingName":"utf8","Length":0}�d$A
	
 

		
�M�w �o0�autohour自动运行时间(小时) �o0�[]{"Min":0.0,"Max":0.0,"MinFilterType":1,"SetMin":0.0,"SetMax":0.0,"MaxFilterType":1,"Save":false}{"ProtectType":1,"ProtectTypeName":"只读","Method":"ReadHoldingRegisters","RegisterAddress":"39","DataType":4,"DataTypeName":"int16","Encoding":1,"EncodingName":"utf8","Length":0}�L#
	
 

		
�M�w �o0�stop停止 �o0�[]{"Min":0.0,"Max":0.0,"MinFilterType":1,"SetMin":0.0,"SetMax":0.0,"MaxFilterType":1,"Save":false}{"ProtectType":1,"ProtectTypeName":"只读","Method":"ReadHoldingRegisters","RegisterAddress":"80","DataType":4,"DataTypeName":"int16","Encoding":1,"EncodingName":"utf8","Length":0}�K"
	
 

		
�M�w �o0�run启动 �o0�[]{"Min":0.0,"Max":0.0,"MinFilterType":1,"SetMin":0.0,"SetMax":0.0,"MaxFilterType":1,"Save":false}{"ProtectType":1,"ProtectTypeName":"只读","Method":"ReadHoldingRegisters","RegisterAddress":"79","DataType":4,"DataTypeName":"int16","Encoding":1,"EncodingName":"utf8","Length":0}�Y!%%
	
 

		
�M�u �o0�devicestatus运行状态 �o0�[]{"Min":0.0,"Max":0.0,"MinFilterType":1,"SetMin":0.0,"SetMax":0.0,"MaxFilterType":1,"Save":false}{"ProtectType":1,"ProtectTypeName":"只读","Method":"ReadHoldingRegisters","RegisterAddress":"4","DataType":4,"DataTypeName":"int16","Encoding":1,"EncodingName":"utf8","Length":0}
   
� �
8�
o	�?�q�                                                                                                                                                                                                                                                                                                                                                                                                                                                       �5

 
�G		
�M�S �o0�Status状态var  Status = device.Status('${this.DeviceName}');
if(Status){
  return 1
}
else 
  return -1 �o0�[]{"Min":0.0,"Max":0.0,"MinFilterType":1,"SetMin":0.0,"SetMax":0.0,"MaxFilterType":1,"Save":false}{"ProtectType":1,"ProtectTypeName":"只读","Method":null,"RegisterAddress":null,"DataType":6,"DataTypeName":"int32","Encoding":1,"EncodingName":"utf8","Length":0}�f411
	
 

		
�M�w �o0�coldwaterlimittemp冷水极限温度 �o0�[]{"Min":0.0,"Max":0.0,"MinFilterType":1,"SetMin":0.0,"SetMax":0.0,"MaxFilterType":1,"Save":false}{"ProtectType":1,"ProtectTypeName":"只读","Method":"ReadHoldingRegisters","RegisterAddress":"95","DataType":4,"DataTypeName":"int16","Encoding":1,"EncodingName":"utf8","Length":0}�b3)1
	
 

		
�M�w �o0�steamlimittemp蒸汽极限温度 �o0�[]{"Min":0.0,"Max":0.0,"MinFilterType":1,"SetMin":0.0,"SetMax":0.0,"MaxFilterType":1,"Save":false}{"ProtectType":1,"ProtectTypeName":"只读","Method":"ReadHoldingRegisters","RegisterAddress":"93","DataType":4,"DataTypeName":"int16","Encoding":1,"EncodingName":"utf8","Length":0}�c2%7
	
 

		
�M�w �o0�htflimittemp导热油极限温度 �o0�[]{"Min":0.0,"Max":0.0,"MinFilterType":1,"SetMin":0.0,"SetMax":0.0,"MaxFilterType":1,"Save":false}{"ProtectType":1,"ProtectTypeName":"只读","Method":"ReadHoldingRegisters","RegisterAddress":"91","DataType":4,"DataTypeName":"int16","Encoding":1,"EncodingName":"utf8","Length":0}�`1/%
	
 

		
�M�w �o0�Instantaneousflow瞬时流量 �o0�[]{"Min":0.0,"Max":0.0,"MinFilterType":1,"SetMin":0.0,"SetMax":0.0,"MaxFilterType":1,"Save":false}{"ProtectType":1,"ProtectTypeName":"只读","Method":"ReadHoldingRegisters","RegisterAddress":"50","DataType":4,"DataTypeName":"int16","Encoding":1,"EncodingName":"utf8","Length":0}�d0-1
	
 

		
�M�w �o0�slagsetcountdown排渣时间设置 �o0�[]{"Min":0.0,"Max":0.0,"MinFilterType":1,"SetMin":0.0,"SetMax":0.0,"MaxFilterType":1,"Save":false}{"ProtectType":1,"ProtectTypeName":"只读","Method":"ReadHoldingRegisters","RegisterAddress":"77","DataType":4,"DataTypeName":"int16","Encoding":1,"EncodingName":"utf8","Length":0}�^/'+
	
 

		
�M�w �o0�slagcountdown排渣倒计时 �o0�[]{"Min":0.0,"Max":0.0,"MinFilterType":1,"SetMin":0.0,"SetMax":0.0,"MaxFilterType":1,"Save":false}{"ProtectType":1,"ProtectTypeName":"只读","Method":"ReadHoldingRegisters","RegisterAddress":"75","DataType":4,"DataTypeName":"int16","Encoding":1,"EncodingName":"utf8","Length":0}�e./1
	
 

		
�M�w �o0�steamsetcountdown蒸干计时设置 �o0�[]{"Min":0.0,"Max":0.0,"MinFilterType":1,"SetMin":0.0,"SetMax":0.0,"MaxFilterType":1,"Save":false}{"ProtectType":1,"ProtectTypeName":"只读","Method":"ReadHoldingRegisters","RegisterAddress":"73","DataType":4,"DataTypeName":"int16","Encoding":1,"EncodingName":"utf8","Length":0}�_-)+
	
 

		
�M�w �o0�steamcountdown蒸干倒计时 �o0�[]{"Min":0.0,"Max":0.0,"MinFilterType":1,"SetMin":0.0,"SetMax":0.0,"MaxFilterType":1,"Save":false}{"ProtectType":1,"ProtectTypeName":"只读","Method":"ReadHoldingRegisters","RegisterAddress":"71","DataType":4,"DataTypeName":"int16","Encoding":1,"EncodingName":"utf8","Length":0}�c,+1
	
 

		
�M�w �o0�recoverysettime回收时间设置 �o0�[]{"Min":0.0,"Max":0.0,"MinFilterType":1,"SetMin":0.0,"SetMax":0.0,"MaxFilterType":1,"Save":false}{"ProtectType":1,"ProtectTypeName":"只读","Method":"ReadHoldingRegisters","RegisterAddress":"69","DataType":4,"DataTypeName":"int16","Encoding":1,"EncodingName":"utf8","Length":0}
   
 � 
�>
�	$�y$�b �                                                                                                                                              �5?% 	 	Q c		-=�M�S	 �l�F �online在线状态{"True":"在线","False":"离线"}return device.Status('${this.DeviceName}'); �l�F`�["系统属性"]true:在线;false:关机{"Min":0.0,"Max":0.0,"MinFilterType":1,"SetMin":0.0,"SetMax":0.0,"MaxFilterType":1,"Save":false}{"ProtectType":0,"ProtectTypeName":"只读","Method":null,"RegisterAddress":null,"DataType":6,"DataTypeName":"int32","Encoding":1,"EncodingName":"utf8","Length":0}�_>#
	
 
		
�M� ��M�0�temperature温度raw*0.1 �jn �[]{"Min":0.0,"Max":0.0,"MinFilterType":1,"SetMin":0.0,"SetMax":0.0,"MaxFilterType":1,"Save":false}{"ProtectType":1,"ProtectTypeName":"只读","Method":"ReadHoldingRegisters","RegisterAddress":"s=1;1","DataType":4,"DataTypeName":"int16","Encoding":1,"EncodingName":"utf8","Length":10}�]=
	
 
	
�M� ��8* �humidity湿度raw*0.1 �jn �[]{"Min":0.0,"Max":0.0,"MinFilterType":1,"SetMin":0.0,"SetMax":0.0,"MaxFilterType":1,"Save":false}{"ProtectType":1,"ProtectTypeName":"只读","Method":"ReadHoldingRegisters","RegisterAddress":"s=1;0","DataType":4,"DataTypeName":"int16","Encoding":1,"EncodingName":"utf8","Length":10}�R<
	
 

		
�M� �ju���IA电流 �jn �[]{"Min":0.0,"Max":0.0,"MinFilterType":1,"SetMin":0.0,"SetMax":0.0,"MaxFilterType":1,"Save":false}{"ProtectType":1,"ProtectTypeName":"只读","Method":"ReadHoldingRegisters","RegisterAddress":"s=6;8195","DataType":7,"DataTypeName":"float","Encoding":1,"EncodingName":"utf8","Length":10}�R;
	
 

		
�M� �jj� �UA电压 �jn �[]{"Min":0.0,"Max":0.0,"MinFilterType":1,"SetMin":0.0,"SetMax":0.0,"MaxFilterType":1,"Save":false}{"ProtectType":1,"ProtectTypeName":"只读","Method":"ReadHoldingRegisters","RegisterAddress":"s=6;8193","DataType":4,"DataTypeName":"int16","Encoding":1,"EncodingName":"utf8","Length":10}�S:
	
 

		
�M� �j_ �EP电能 �jn �[]{"Min":0.0,"Max":0.0,"MinFilterType":1,"SetMin":0.0,"SetMax":0.0,"MaxFilterType":1,"Save":false}{"ProtectType":1,"ProtectTypeName":"只读","Method":"ReadHoldingRegisters","RegisterAddress":"s=6;16385","DataType":7,"DataTypeName":"float","Encoding":1,"EncodingName":"utf8","Length":10}�_9%
	
 
		
�M� �j0�0�P有功功率raw/10.0 �jn �[]{"Min":0.0,"Max":0.0,"MinFilterType":1,"SetMin":0.0,"SetMax":0.0,"MaxFilterType":1,"Save":false}{"ProtectType":1,"ProtectTypeName":"只读","Method":"ReadHoldingRegisters","RegisterAddress":"s=6;8197","DataType":7,"DataTypeName":"float","Encoding":1,"EncodingName":"utf8","Length":10}�58% 	 	Q c		-=�M�S	 �jm`�online在线状态{"True":"在线","False":"离线"}return device.Status('${this.DeviceName}'); �jn �["系统属性"]true:在线;false:关机{"Min":0.0,"Max":0.0,"MinFilterType":1,"SetMin":0.0,"SetMax":0.0,"MaxFilterType":1,"Save":false}{"ProtectType":0,"ProtectTypeName":"只读","Method":null,"RegisterAddress":null,"DataType":6,"DataTypeName":"int32","Encoding":1,"EncodingName":"utf8","Length":0}�271
	
 
�a		
�M�S �o0�isRun是否正在运行var stop = device.Get('${this.DeviceName}.stop');
if (stop == 0)
{
    return true;
}
return false;
 �o0�[]{"Min":0.0,"Max":0.0,"MinFilterType":1,"SetMin":0.0,"SetMax":0.0,"MaxFilterType":1,"Save":false}{"ProtectType":0,"ProtectTypeName":"只读","Method":null,"RegisterAddress":null,"DataType":6,"DataTypeName":"int32","Encoding":1,"EncodingName":"utf8","Length":0}�
6#

 
�		
�M�S �o0�consumption耗电量var Status =  device.Status('${this.DeviceName}');
if(Status){
  var consumption = device.Get('${this.DeviceName}.consumption');
  consumption = parseInt(consumption) + 1;
  return consumption;
} �o0�[]{"Min":0.0,"Max":0.0,"MinFilterType":1,"SetMin":0.0,"SetMax":0.0,"MaxFilterType":1,"Save":false}{"ProtectType":0,"ProtectTypeName":"只读","Method":null,"RegisterAddress":null,"DataType":6,"DataTypeName":"int32","Encoding":1,"EncodingName":"utf8","Length":0}
   � �
B�
+��-�                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              �G%

 
�-		
�M�S �q�V0�status工作状态// 获取当前设备的连接状态,返回Bool值
var status = device.Status('${this.DeviceName}');
if (status) {
  // 如果status == true 代表在线,则返回2(根据实际情况调整)
  var DeviceStatus = device.Get('${this.DeviceName}.launch');
  var Alarms = device.Get('${this.DeviceName}.Alarms');
  if (DeviceStatus && Alarms) {
    status = 3; //报警
  } else if (DeviceStatus && !Alarms) {
    status = 2;
  } else if (!DeviceStatus && Alarms) {
    status = 3;
  } else{
    status = 1;
  }
} else {
  // 假设1代表关机
  status =  -1;
}
return status �n�� �[]{"Min":0.0,"Max":0.0,"MinFilterType":1,"SetMin":0.0,"SetMax":0.0,"MaxFilterType":1,"Save":false}{"ProtectType":0,"ProtectTypeName":"只读","Method":null,"RegisterAddress":null,"DataType":6,"DataTypeName":"int32","Encoding":1,"EncodingName":"utf8","Length":0}�VF%)
	
 

		
�M�k �p@�@�Production_22#设备产量 �n�� �[]{"Min":0.0,"Max":0.0,"MinFilterType":1,"SetMin":0.0,"SetMax":0.0,"MaxFilterType":1,"Save":false}{"ProtectType":1,"ProtectTypeName":"只读","Method":"ReadD0D1311","RegisterAddress":"D104","DataType":6,"DataTypeName":"int32","Encoding":1,"EncodingName":"utf8","Length":10}�IE%
		
 

		
�M�c �p:|��launch启动按钮 �n�� �[]{"Min":0.0,"Max":0.0,"MinFilterType":1,"SetMin":0.0,"SetMax":0.0,"MaxFilterType":1,"Save":false}{"ProtectType":1,"ProtectTypeName":"只读","Method":"ReadX0X177","RegisterAddress":"X0","DataType":2,"DataTypeName":"bool","Encoding":1,"EncodingName":"utf8","Length":10}�VD%)
	
 

		
�M�k �oհ�Production_11#设备产量 �n�� �[]{"Min":0.0,"Max":0.0,"MinFilterType":1,"SetMin":0.0,"SetMax":0.0,"MaxFilterType":1,"Save":false}{"ProtectType":1,"ProtectTypeName":"只读","Method":"ReadD0D1311","RegisterAddress":"D102","DataType":6,"DataTypeName":"int32","Encoding":1,"EncodingName":"utf8","Length":10}�5C% 	 	Q c		-=�M�S	 �n����online在线状态{"True":"在线","False":"离线"}return device.Status('${this.DeviceName}'); �n�� �["系统属性"]true:在线;false:关机{"Min":0.0,"Max":0.0,"MinFilterType":1,"SetMin":0.0,"SetMax":0.0,"MaxFilterType":1,"Save":false}{"ProtectType":0,"ProtectTypeName":"只读","Method":null,"RegisterAddress":null,"DataType":6,"DataTypeName":"int32","Encoding":1,"EncodingName":"utf8","Length":0}�\B!
	
 !
	
�M�} �m�@�ICC相电流raw/1000.0 �l�F`�[]{"Min":0.0,"Max":0.0,"MinFilterType":1,"SetMin":0.0,"SetMax":0.0,"MaxFilterType":1,"Save":false}{"ProtectType":1,"ProtectTypeName":"只读","Method":"ReadHoldingRegisters","RegisterAddress":"8226","DataType":6,"DataTypeName":"int32","Encoding":1,"EncodingName":"utf8","Length":10}�\A!
	
 !
	
�M�} �m~p�IBB相电流raw/1000.0 �l�F`�[]{"Min":0.0,"Max":0.0,"MinFilterType":1,"SetMin":0.0,"SetMax":0.0,"MaxFilterType":1,"Save":false}{"ProtectType":1,"ProtectTypeName":"只读","Method":"ReadHoldingRegisters","RegisterAddress":"8213","DataType":6,"DataTypeName":"int32","Encoding":1,"EncodingName":"utf8","Length":10}�\@!
	
 !
		
�M�} �l�w@�IAA相电流raw/1000.0 �l�F`�[]{"Min":0.0,"Max":0.0,"MinFilterType":1,"SetMin":0.0,"SetMax":0.0,"MaxFilterType":1,"Save":false}{"ProtectType":1,"ProtectTypeName":"只读","Method":"ReadHoldingRegisters","RegisterAddress":"8200","DataType":6,"DataTypeName":"int32","Encoding":1,"EncodingName":"utf8","Length":10}
   ( 
z(                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            �OI-1

 
�		
�M�S �r`
@�TotalIdleTimeRun设备运行时长//该脚本用于统计当日设备运行时间累计
//持久化的累计空闲时间
var totalTime = share.Get('${this.DeviceName}_TotalIdleTimeRun') || 0;
//当前设备连接状态
var runStatus = device.Status('${this.DeviceName}');
//当前设备标识运行状态的属性
var status = device.GetData('${this.DeviceName}.status');
if (status == null) {
  return totalTime;
}

//设备在线 && 设备当前状态和上次状态一致才累计时间 && 设备状态是空闲（此处空闲状态表示1,请根据实际情况调整）
if (runStatus && status.Value == status.CookieValue && status.Value == 2) {

  //累计时间= 上一次累计时间+(本次运行状态的读取时间-上一次状态的读取时间)
  totalTime = parseInt(totalTime) + parseInt(status.ReadTime - status.CookieTime);
  //持久化保存，并且设置过期时间
  share.Save('${this.DeviceName}_TotalIdleTimeRun', totalTime, '00:00:00');
}
//返回统计结果
return Math.round(totalTime/1000/60*100)/100; �n�� �[]{"Min":0.0,"Max":0.0,"MinFilterType":1,"SetMin":0.0,"SetMax":0.0,"MaxFilterType":1,"Save":false}{"ProtectType":0,"ProtectTypeName":"只读","Method":null,"RegisterAddress":null,"DataType":6,"DataTypeName":"int32","Encoding":1,"EncodingName":"utf8","Length":0}�H'1

 
�o		
�M�S �q�`��TotalIdleTime设备空闲时长//该脚本用于统计当日设备空闲时间累计
//持久化的累计空闲时间
var totalTime = share.Get('${this.DeviceName}_TotalIdleTime') || 0;
//当前设备连接状态
var runStatus = device.Status('${this.DeviceName}');
//当前设备标识运行状态的属性
var status = device.GetData('${this.DeviceName}.status');
if (status == null) {
  return totalTime;
}
var AlarmStr = device.GetData('${this.DeviceName}.AlarmStr');
//设备在线 && 设备当前状态和上次状态一致才累计时间 && 设备状态是空闲（此处空闲状态表示1,请根据实际情况调整）
if (runStatus && status.Value == status.CookieValue && status.Value == 1) {

  //累计时间= 上一次累计时间+(本次运行状态的读取时间-上一次状态的读取时间)
  totalTime = parseInt(totalTime) + parseInt(status.ReadTime - status.CookieTime);
  //持久化保存，并且设置过期时间
  share.Save('${this.DeviceName}_TotalIdleTime', totalTime, '00:00:00');
}
//返回统计结果
return Math.round(totalTime/1000/60*100)/100; �n�� �[]{"Min":0.0,"Max":0.0,"MinFilterType":1,"SetMin":0.0,"SetMax":0.0,"MaxFilterType":1,"Save":false}{"ProtectType":0,"ProtectTypeName":"只读","Method":null,"RegisterAddress":null,"DataType":6,"DataTypeName":"int32","Encoding":1,"EncodingName":"utf8","Length":0}
   W 
�G�W                                                                                                                                                                                                                                                                                                                                     �HN
	
 

		
�M�o ���@�tmh条码
 �n�� �[]{"Min":0.0,"Max":0.0,"MinFilterType":1,"SetMin":0.0,"SetMax":0.0,"MaxFilterType":1,"Save":false}{"ProtectType":2,"ProtectTypeName":"读写","Method":"ReadD0D1311","RegisterAddress":"D110","DataType":11,"DataTypeName":"string","Encoding":1,"EncodingName":"utf8","Length":10}�]M;+
		
 

		
�M�c �r�2 �running_indicator_light运行指示灯 �n�� �[]{"Min":0.0,"Max":0.0,"MinFilterType":1,"SetMin":0.0,"SetMax":0.0,"MaxFilterType":1,"Save":false}{"ProtectType":1,"ProtectTypeName":"只读","Method":"ReadX0X177","RegisterAddress":"X0","DataType":2,"DataTypeName":"bool","Encoding":1,"EncodingName":"utf8","Length":10}�BL
		
 

		
�M�c �r�0�Alarm报警 �n�� �[]{"Min":0.0,"Max":0.0,"MinFilterType":1,"SetMin":0.0,"SetMax":0.0,"MaxFilterType":1,"Save":false}{"ProtectType":1,"ProtectTypeName":"只读","Method":"ReadX0X177","RegisterAddress":"X1","DataType":2,"DataTypeName":"bool","Encoding":1,"EncodingName":"utf8","Length":10}�iK11

 
�1		
�M�S �r�A��TotalIdleTimeAlarm设备报警时长//该脚本用于统计当日设备报警时间累计
//持久化的累计报警时间
var totalTime = share.Get('${this.DeviceName}_TotalIdleTimeAlarm') || 0;
//当前设备连接状态
var runAlarmStr = device.status('${this.DeviceName}');
//当前设备标识运行状态的属性
var AlarmStr = device.GetData('${this.DeviceName}.AlarmStr');
if (AlarmStr == null) {
  return totalTime;
}

//设备在线 && 设备当前状态和上次状态一致才累计时间 && 设备状态是空闲（此处空闲状态表示1,请根据实际情况调整）
if (runAlarmStr && AlarmStr.Value == AlarmStr.CookieValue && AlarmStr.Value != 3) {

  //累计时间= 上一次累计时间+(本次运行状态的读取时间-上一次状态的读取时间)
  totalTime = parseInt(totalTime) + parseInt(AlarmStr.ReadTime - AlarmStr.CookieTime);
  //持久化保存，并且设置过期时间
  share.Save('${this.DeviceName}_TotalIdleTimeAlarm', totalTime, '00:00:00');
}
//返回统计结果
return Math.round(totalTime/1000/60*100)/100; �n�� �[]{"Min":0.0,"Max":0.0,"MinFilterType":1,"SetMin":0.0,"SetMax":0.0,"MaxFilterType":1,"Save":false}{"ProtectType":0,"ProtectTypeName":"只读","Method":null,"RegisterAddress":null,"DataType":6,"DataTypeName":"int32","Encoding":1,"EncodingName":"utf8","Length":0}�JJ11

 
�q	
�M�S �rn۠�TotalIdleTimePower设备关机时长//该脚本用于统计当日设备关机时间累计
//持久化的累计空闲时间
var totalTime = share.Get('${this.DeviceName}_TotalIdleTimePower') || 0;
//当前设备连接状态
var runStatus = device.Status('${this.DeviceName}');
//当前设备标识运行状态的属性
var status = device.GetData('${this.DeviceName}.status');
if (status == null) {
  return totalTime;
}

//设备在线 && 设备当前状态和上次状态一致才累计时间 && 设备状态是空闲（此处空闲状态表示1,请根据实际情况调整）
if (status.Value == status.CookieValue && status.Value == -1) {

  //累计时间= 上一次累计时间+(本次运行状态的读取时间-上一次状态的读取时间)
  totalTime = parseInt(totalTime) + parseInt(status.ReadTime - status.CookieTime);
  //持久化保存，并且设置过期时间
  share.Save('${this.DeviceName}_TotalIdleTimePower', totalTime, '00:00:00');
}
//返回统计结果
return Math.round(totalTime/1000/60*100)/100; �n�� �[]{"Min":0.0,"Max":0.0,"MinFilterType":1,"SetMin":0.0,"SetMax":0.0,"MaxFilterType":1,"Save":false}{"ProtectType":0,"ProtectTypeName":"只读","Method":null,"RegisterAddress":null,"DataType":6,"DataTypeName":"int32","Encoding":1,"EncodingName":"utf8","Length":0}
   

 ���
!i$\�[
                                                                                                                                                                                                                                              �NX%    		 �M�S ��*��batchNo任务单号停机中 ��sa@�[]{"Min":0.0,"Max":0.0,"MinFilterType":1,"SetMin":0.0,"SetMax":0.0,"MaxFilterType":1,"Save":false}{"ProtectType":1,"ProtectTypeName":"只读","Method":null,"RegisterAddress":null,"DataType":6,"DataTypeName":"int32","Encoding":1,"EncodingName":"utf8","Length":0}�FW    	 �M�S ��*��Parameter参数AE33
 ��sa@�[]{"Min":0.0,"Max":0.0,"MinFilterType":1,"SetMin":0.0,"SetMax":0.0,"MaxFilterType":1,"Save":false}{"ProtectType":1,"ProtectTypeName":"只读","Method":null,"RegisterAddress":null,"DataType":6,"DataTypeName":"int32","Encoding":1,"EncodingName":"utf8","Length":0}�5V% 	 	Q c		-=�M�S	 ��*��online在线状态{"True":"在线","False":"离线"}return device.Status('${this.DeviceName}'); ��sa@�["系统属性"]true:在线;false:关机{"Min":0.0,"Max":0.0,"MinFilterType":1,"SetMin":0.0,"SetMax":0.0,"MaxFilterType":1,"Save":false}{"ProtectType":1,"ProtectTypeName":"只读","Method":null,"RegisterAddress":null,"DataType":6,"DataTypeName":"int32","Encoding":1,"EncodingName":"utf8","Length":0}�EU+

 
�		
�M�S ��(��replace处理数据源var getData = device.Get('${this.DeviceName}.data');
if (getData != null) {
    return getData.replace('\\0D\\0A', '');
} ��2���[]{"Min":0.0,"Max":0.0,"MinFilterType":1,"SetMin":0.0,"SetMax":0.0,"MaxFilterType":1,"Save":false}{"ProtectType":0,"ProtectTypeName":"只读","Method":null,"RegisterAddress":null,"DataType":6,"DataTypeName":"int32","Encoding":1,"EncodingName":"utf8","Length":0}�BT
	
 

	
�M�Y ��8�p�data数据源
 ��2���[]{"Min":0.0,"Max":0.0,"MinFilterType":1,"SetMin":0.0,"SetMax":0.0,"MaxFilterType":1,"Save":false}{"ProtectType":0,"ProtectTypeName":"只读","Method":"ReadY","RegisterAddress":null,"DataType":6,"DataTypeName":"int32","Encoding":1,"EncodingName":"utf8","Length":0}�5S% 	 	Q c		-=�M�S	 ��2���online在线状态{"True":"在线","False":"离线"}return device.Status('${this.DeviceName}'); ��2���["系统属性"]true:在线;false:关机{"Min":0.0,"Max":0.0,"MinFilterType":1,"SetMin":0.0,"SetMax":0.0,"MaxFilterType":1,"Save":false}{"ProtectType":0,"ProtectTypeName":"只读","Method":null,"RegisterAddress":null,"DataType":6,"DataTypeName":"int32","Encoding":1,"EncodingName":"utf8","Length":0}�lR#

 
_		
�M�S ��9� �temperature温度device.Get('electric_meter.temperature'); ��p4��[]{"Min":0.0,"Max":0.0,"MinFilterType":1,"SetMin":0.0,"SetMax":0.0,"MaxFilterType":1,"Save":false}{"ProtectType":0,"ProtectTypeName":"只读","Method":null,"RegisterAddress":null,"DataType":6,"DataTypeName":"int32","Encoding":1,"EncodingName":"utf8","Length":0}�fQ

 
Y		
�M�S ��O@�humidity湿度device.Get('electric_meter.humidity'); ��p4��[]{"Min":0.0,"Max":0.0,"MinFilterType":1,"SetMin":0.0,"SetMax":0.0,"MaxFilterType":1,"Save":false}{"ProtectType":0,"ProtectTypeName":"只读","Method":null,"RegisterAddress":null,"DataType":6,"DataTypeName":"int32","Encoding":1,"EncodingName":"utf8","Length":0}�5P% 	 	Q c		-=�M�S	 ��p(��online在线状态{"True":"在线","False":"离线"}return device.Status('${this.DeviceName}'); ��p4��["系统属性"]true:在线;false:关机{"Min":0.0,"Max":0.0,"MinFilterType":1,"SetMin":0.0,"SetMax":0.0,"MaxFilterType":1,"Save":false}{"ProtectType":0,"ProtectTypeName":"只读","Method":null,"RegisterAddress":null,"DataType":6,"DataTypeName":"int32","Encoding":1,"EncodingName":"utf8","Length":0}�LO
	
 
		
�M�k �1��`�testtestraw*0.4 �n�� �[]d{"Min":0.0,"Max":0.0,"MinFilterType":1,"SetMin":0.0,"SetMax":0.0,"MaxFilterType":1,"Save":false}{"ProtectType":1,"ProtectTypeName":"只读","Method":"ReadD0D1311","RegisterAddress":"D100","DataType":4,"DataTypeName":"int16","Encoding":1,"EncodingName":"utf8","Length":10}
   
� �
W
�	|)��?�                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  �Nb%    		 �M�S ��+`�devCode设备代码停机中
 ��sa@�[]{"Min":0.0,"Max":0.0,"MinFilterType":1,"SetMin":0.0,"SetMax":0.0,"MaxFilterType":1,"Save":false}{"ProtectType":1,"ProtectTypeName":"只读","Method":null,"RegisterAddress":null,"DataType":6,"DataTypeName":"int32","Encoding":1,"EncodingName":"utf8","Length":0}�Sa%%    		 �M�S ��+P�slittingDate出卷时间停机中 ��sa@�[]{"Min":0.0,"Max":0.0,"MinFilterType":1,"SetMin":0.0,"SetMax":0.0,"MaxFilterType":1,"Save":false}{"ProtectType":1,"ProtectTypeName":"只读","Method":null,"RegisterAddress":null,"DataType":6,"DataTypeName":"int32","Encoding":1,"EncodingName":"utf8","Length":0}�D`%    		 �M�S ��+@�reiNum剩余卷数0 ��sa@�[]{"Min":0.0,"Max":0.0,"MinFilterType":1,"SetMin":0.0,"SetMax":0.0,"MaxFilterType":1,"Save":false}{"ProtectType":1,"ProtectTypeName":"只读","Method":null,"RegisterAddress":null,"DataType":6,"DataTypeName":"int32","Encoding":1,"EncodingName":"utf8","Length":0}�J_    		 �M�S ��+@�psCode订单号停机中 ��sa@�[]{"Min":0.0,"Max":0.0,"MinFilterType":1,"SetMin":0.0,"SetMax":0.0,"MaxFilterType":1,"Save":false}{"ProtectType":1,"ProtectTypeName":"只读","Method":null,"RegisterAddress":null,"DataType":6,"DataTypeName":"int32","Encoding":1,"EncodingName":"utf8","Length":0}�P^+    		 �M�S ��+0�mtcode产品类型码停机中 ��sa@�[]{"Min":0.0,"Max":0.0,"MinFilterType":1,"SetMin":0.0,"SetMax":0.0,"MaxFilterType":1,"Save":false}{"ProtectType":1,"ProtectTypeName":"只读","Method":null,"RegisterAddress":null,"DataType":6,"DataTypeName":"int32","Encoding":1,"EncodingName":"utf8","Length":0}�Q]!%    		 �M�S ��+ �invSpecies产品规格停机中 ��sa@�[]{"Min":0.0,"Max":0.0,"MinFilterType":1,"SetMin":0.0,"SetMax":0.0,"MaxFilterType":1,"Save":false}{"ProtectType":1,"ProtectTypeName":"只读","Method":null,"RegisterAddress":null,"DataType":6,"DataTypeName":"int32","Encoding":1,"EncodingName":"utf8","Length":0}�?\    		 �M�S ��+�iNum总卷数0 ��sa@�[]{"Min":0.0,"Max":0.0,"MinFilterType":1,"SetMin":0.0,"SetMax":0.0,"MaxFilterType":1,"Save":false}{"ProtectType":1,"ProtectTypeName":"只读","Method":null,"RegisterAddress":null,"DataType":6,"DataTypeName":"int32","Encoding":1,"EncodingName":"utf8","Length":0}�B[!    		 �M�S ��+ �gramWeight克重0 ��sa@�[]{"Min":0.0,"Max":0.0,"MinFilterType":1,"SetMin":0.0,"SetMax":0.0,"MaxFilterType":1,"Save":false}{"ProtectType":1,"ProtectTypeName":"只读","Method":null,"RegisterAddress":null,"DataType":6,"DataTypeName":"int32","Encoding":1,"EncodingName":"utf8","Length":0}�TZ-1    		 �M�S ��*��dictNameColorNum名称颜色代码0 ��sa@�[]{"Min":0.0,"Max":0.0,"MinFilterType":1,"SetMin":0.0,"SetMax":0.0,"MaxFilterType":1,"Save":false}{"ProtectType":1,"ProtectTypeName":"只读","Method":null,"RegisterAddress":null,"DataType":6,"DataTypeName":"int32","Encoding":1,"EncodingName":"utf8","Length":0}�OY%    		 �M�S ��*��custCode客户代码停机中 ��sa@�[]{"Min":0.0,"Max":0.0,"MinFilterType":1,"SetMin":0.0,"SetMax":0.0,"MaxFilterType":1,"Save":false}{"ProtectType":1,"ProtectTypeName":"只读","Method":null,"RegisterAddress":null,"DataType":6,"DataTypeName":"int32","Encoding":1,"EncodingName":"utf8","Length":0}
    c�                                                                                                                                                                                                                                                             �ye
	



		k�M�i ���y�RunStatus状态 ���0�[]0:Reset；1:Stop；2:Hold；3:Start；4:Mstr；{"Min":0.0,"Max":0.0,"MinFilterType":1,"SetMin":0.0,"SetMax":0.0,"MaxFilterType":1,"Save":false}{"ProtectType":1,"ProtectTypeName":"只读","Method":"ReadRunStatus","RegisterAddress":null,"DataType":6,"DataTypeName":"int32","Encoding":1,"EncodingName":"utf8","Length":0}�Yd
	



		�-�M�g ���x��WorkMode模式 ���0�[]0:手动输入；1:自动循环；3:程序编辑；4:×100;5:连续进给；6:TeachInJog；7:TeachInHandle；8:InCfeed；9:ReFerence；10:ReMoTe{"Min":0.0,"Max":0.0,"MinFilterType":1,"SetMin":0.0,"SetMax":0.0,"MaxFilterType":1,"Save":false}{"ProtectType":1,"ProtectTypeName":"只读","Method":"ReadWorkMode","RegisterAddress":null,"DataType":6,"DataTypeName":"int32","Encoding":1,"EncodingName":"utf8","Length":0}�c!C 
 
�		
�M�S ��+p�get2000API获取大网关产品信息//开机时才获取接口数据
if (device.Get('OmronPLCNX102.ProductState')) {
  var url = 'http://**************:9011/api/openapi/deviceVariable/realTime';
  //示例 请求头
  var headers = {
    'Accept': 'application/json'
  };
  //示例 请求参数
  var query = {
    'AppId': '474584515924165',
    'DeviceName': 'GetERP',
    'Identifier':'AllValue'
  };
  // 调用api获取返回数据
  var getDevInfoResult = http.Get(url, headers, query);

  if (getDevInfoResult.success != true) {
    return "获取失败";
  }

  var objectData = JSON.parse(getDevInfoResult.data);
  var objectDataSecond = objectData.Data[0].Value;
  var result = JSON.parse(objectDataSecond);
  for (var iteam in result) {
    if (result[iteam].devCode == device.Get('${this.DeviceName}.Parameter')) { //需要更改此处的条件
      var typeName = result[iteam].dictName;
      var num = 0;
      if (typeName == "透明EVA") {
        num = 1;
      } else if (typeName == "交联型POE") {
        num = 2;
      } else if (typeName == "白膜") {
        num = 3;
      } else if (typeName == "共挤胶膜") {
        num = 4;
      } else if (typeName == "连接膜") {
        num = 5;
      } else {
        num = 6;
      }
      device.SetVariable('${this.DeviceName}', {
        'batchNo': result[iteam].batchNo,
        'custCode': result[iteam].custCode,
        'devCode': result[iteam].devCode,
        'gramWeight': result[iteam].gramWeight,
        'iNum': result[iteam].iNum,
        'invSpecies': result[iteam].invSpecies,
        'mtcode': result[iteam].mtcode,
        'reiNum': result[iteam].reiNum,
        'slittingDate': result[iteam].slittingDate,
        'psCode':result[iteam].psCode,
        'dictNameColorNum': num,
      });
      // return dateTime.AddDate(dateTime.Now(), 'hour', 8);
      return result[iteam].invSpecies
    } else {
      device.SetVariable('${this.DeviceName}', {
        'batchNo': "无数据",
        'custCode': "无数据",
        'devCode': "无数据",
        'gramWeight': 0,
        'iNum': 0,
        'invSpecies': "无数据",
        'mtcode': "无数据",
        'reiNum': 0,
        'slittingDate': "无数据",
        'dictNameColorNum': 0,
        'psCode':"无数据",
      });
    }
  }
} else {
  device.SetVariable('${this.DeviceName}', {
    'batchNo': "停机中",
    'custCode': "停机中",
    'devCode': "停机中",
    'gramWeight': 0,
    'iNum': 0,
    'invSpecies': "停机中",
    'mtcode': "停机中",
    'reiNum': 0,
    'slittingDate': "停机中",
    'dictNameColorNum': 0,
    'psCode':"停机中",
  });
  return '停机中';
} ��sa@�[] �`{"Min":0.0,"Max":0.0,"MinFilterType":1,"SetMin":0.0,"SetMax":0.0,"MaxFilterType":1,"Save":false}{"ProtectType":1,"ProtectTypeName":"只读","Method":null,"RegisterAddress":null,"DataType":6,"DataTypeName":"int32","Encoding":1,"EncodingName":"utf8","Length":0}
    H �
Z
�	r$��5�� H                                        �Jq%
	



		
�M�c ���y0�SvTemp伺服温度 ���0�[]{"Min":0.0,"Max":0.0,"MinFilterType":1,"SetMin":0.0,"SetMax":0.0,"MaxFilterType":1,"Save":false}{"ProtectType":1,"ProtectTypeName":"只读","Method":"ReadSvTemp","RegisterAddress":null,"DataType":6,"DataTypeName":"int32","Encoding":1,"EncodingName":"utf8","Length":0}�Np%
	



		
�M�g ���y0�SpinTemp主轴温度 ���0�[]{"Min":0.0,"Max":0.0,"MinFilterType":1,"SetMin":0.0,"SetMax":0.0,"MaxFilterType":1,"Save":false}{"ProtectType":1,"ProtectTypeName":"只读","Method":"ReadSpinTemp","RegisterAddress":null,"DataType":6,"DataTypeName":"int32","Encoding":1,"EncodingName":"utf8","Length":0}�Lo!
	



		
�M�k ���y0�CurrentNum产量 ���0�[]{"Min":0.0,"Max":0.0,"MinFilterType":1,"SetMin":0.0,"SetMax":0.0,"MaxFilterType":1,"Save":false}{"ProtectType":1,"ProtectTypeName":"只读","Method":"ReadCurrentNum","RegisterAddress":null,"DataType":6,"DataTypeName":"int32","Encoding":1,"EncodingName":"utf8","Length":0}�Sn)
	



		
�M�i ���y0�AlarmType报警类型Id ���0�[]{"Min":0.0,"Max":0.0,"MinFilterType":1,"SetMin":0.0,"SetMax":0.0,"MaxFilterType":1,"Save":false}{"ProtectType":1,"ProtectTypeName":"只读","Method":"ReadAlarmType","RegisterAddress":null,"DataType":6,"DataTypeName":"int32","Encoding":1,"EncodingName":"utf8","Length":0}�Im
	



		
�M�e ���y0�AlarmId报警Id ���0�[]{"Min":0.0,"Max":0.0,"MinFilterType":1,"SetMin":0.0,"SetMax":0.0,"MaxFilterType":1,"Save":false}{"ProtectType":1,"ProtectTypeName":"只读","Method":"ReadAlarmId","RegisterAddress":null,"DataType":6,"DataTypeName":"int32","Encoding":1,"EncodingName":"utf8","Length":0}�Jl%
	
 

		
�M�a ���y0�Alarm报警信息 � ���0�[]{"Min":0.0,"Max":0.0,"MinFilterType":1,"SetMin":0.0,"SetMax":0.0,"MaxFilterType":1,"Save":false}{"ProtectType":1,"ProtectTypeName":"只读","Method":"ReadAlarm","RegisterAddress":null,"DataType":6,"DataTypeName":"int32","Encoding":1,"EncodingName":"utf8","Length":0}�Kk%
	



		
�M�c ���y �RelPos相对坐标 ���0�[]{"Min":0.0,"Max":0.0,"MinFilterType":1,"SetMin":0.0,"SetMax":0.0,"MaxFilterType":1,"Save":false}{"ProtectType":1,"ProtectTypeName":"只读","Method":"ReadRelPos","RegisterAddress":null,"DataType":6,"DataTypeName":"int32","Encoding":1,"EncodingName":"utf8","Length":0}�Mj%
	



		
�M�e ���y �MachPos机械坐标 ���0�[]{"Min":0.0,"Max":0.0,"MinFilterType":1,"SetMin":0.0,"SetMax":0.0,"MaxFilterType":1,"Save":false}{"ProtectType":1,"ProtectTypeName":"只读","Method":"ReadMachPos","RegisterAddress":null,"DataType":6,"DataTypeName":"int32","Encoding":1,"EncodingName":"utf8","Length":0}�Ki%
	



		
�M�c ���y �AbsPos绝对坐标 ���0�[]{"Min":0.0,"Max":0.0,"MinFilterType":1,"SetMin":0.0,"SetMax":0.0,"MaxFilterType":1,"Save":false}{"ProtectType":1,"ProtectTypeName":"只读","Method":"ReadAbsPos","RegisterAddress":null,"DataType":6,"DataTypeName":"int32","Encoding":1,"EncodingName":"utf8","Length":0}�Gh
	



		
�M�c ���y �CurPgm程序号 ���0�[]{"Min":0.0,"Max":0.0,"MinFilterType":1,"SetMin":0.0,"SetMax":0.0,"MaxFilterType":1,"Save":false}{"ProtectType":1,"ProtectTypeName":"只读","Method":"ReadCurPgm","RegisterAddress":null,"DataType":6,"DataTypeName":"int32","Encoding":1,"EncodingName":"utf8","Length":0}�Lg
	



		
�M�g ���y �MainName程序名 ���0�[]{"Min":0.0,"Max":0.0,"MinFilterType":1,"SetMin":0.0,"SetMax":0.0,"MaxFilterType":1,"Save":false}{"ProtectType":1,"ProtectTypeName":"只读","Method":"ReadMainName","RegisterAddress":null,"DataType":6,"DataTypeName":"int32","Encoding":1,"EncodingName":"utf8","Length":0}�Tf#%
	



		
�M�m ���y �AlarmStatus报警状态 ���0�[]{"Min":0.0,"Max":0.0,"MinFilterType":1,"SetMin":0.0,"SetMax":0.0,"MaxFilterType":1,"Save":false}{"ProtectType":1,"ProtectTypeName":"只读","Method":"ReadAlarmStatus","RegisterAddress":null,"DataType":6,"DataTypeName":"int32","Encoding":1,"EncodingName":"utf8","Length":0}
    6 �
g
�	q�y&�� 6                      �K}%
	



		
�M�c ���yP�RunTime运行时间 ���0�[]{"Min":0.0,"Max":0.0,"MinFilterType":1,"SetMin":0.0,"SetMax":0.0,"MaxFilterType":1,"Save":false}{"ProtectType":1,"ProtectTypeName":"只读","Method":"ReadCycSec","RegisterAddress":null,"DataType":6,"DataTypeName":"int32","Encoding":1,"EncodingName":"utf8","Length":0}�L|%
	



		
�M�e ���yP�CutTime切削时间 ���0�[]{"Min":0.0,"Max":0.0,"MinFilterType":1,"SetMin":0.0,"SetMax":0.0,"MaxFilterType":1,"Save":false}{"ProtectType":1,"ProtectTypeName":"只读","Method":"ReadCutTime","RegisterAddress":null,"DataType":6,"DataTypeName":"int32","Encoding":1,"EncodingName":"utf8","Length":0}�P{%
	



		
�M�i ���yP�CycleTime循环时间 ���0�[]{"Min":0.0,"Max":0.0,"MinFilterType":1,"SetMin":0.0,"SetMax":0.0,"MaxFilterType":1,"Save":false}{"ProtectType":1,"ProtectTypeName":"只读","Method":"ReadCycleTime","RegisterAddress":null,"DataType":6,"DataTypeName":"int32","Encoding":1,"EncodingName":"utf8","Length":0}�Pz%
	



		
�M�i ���yP�AliveTime上电时间 ���0�[]{"Min":0.0,"Max":0.0,"MinFilterType":1,"SetMin":0.0,"SetMax":0.0,"MaxFilterType":1,"Save":false}{"ProtectType":1,"ProtectTypeName":"只读","Method":"ReadAliveTime","RegisterAddress":null,"DataType":6,"DataTypeName":"int32","Encoding":1,"EncodingName":"utf8","Length":0}�Oy%
	



		
�M�g ���yP�AxisLoad伺服负载 ���0�[]{"Min":0.0,"Max":0.0,"MinFilterType":1,"SetMin":0.0,"SetMax":0.0,"MaxFilterType":1,"Save":false}{"ProtectType":1,"ProtectTypeName":"只读","Method":"ReadAxisLoad","RegisterAddress":null,"DataType":6,"DataTypeName":"int32","Encoding":1,"EncodingName":"utf8","Length":0}�Jx%
	



		
�M�c ���y@�OvFeed进给倍率 ���0�[]{"Min":0.0,"Max":0.0,"MinFilterType":1,"SetMin":0.0,"SetMax":0.0,"MaxFilterType":1,"Save":false}{"ProtectType":1,"ProtectTypeName":"只读","Method":"ReadOvFeed","RegisterAddress":null,"DataType":6,"DataTypeName":"int32","Encoding":1,"EncodingName":"utf8","Length":0}�Vw1
	



		
�M�i ���y@�SetsSpeed主轴设定速度 ���0�[]{"Min":0.0,"Max":0.0,"MinFilterType":1,"SetMin":0.0,"SetMax":0.0,"MaxFilterType":1,"Save":false}{"ProtectType":1,"ProtectTypeName":"只读","Method":"ReadSetsSpeed","RegisterAddress":null,"DataType":6,"DataTypeName":"int32","Encoding":1,"EncodingName":"utf8","Length":0}�Kv%
	



		
�M�c ���y@�sSpeed主轴速度 ���0�[]{"Min":0.0,"Max":0.0,"MinFilterType":1,"SetMin":0.0,"SetMax":0.0,"MaxFilterType":1,"Save":false}{"ProtectType":1,"ProtectTypeName":"只读","Method":"ReadsSpeed","RegisterAddress":null,"DataType":6,"DataTypeName":"int32","Encoding":1,"EncodingName":"utf8","Length":0}�Vu1
	



		
�M�i ���y@�SetFSpeed进给设定速度 ���0�[]{"Min":0.0,"Max":0.0,"MinFilterType":1,"SetMin":0.0,"SetMax":0.0,"MaxFilterType":1,"Save":false}{"ProtectType":1,"ProtectTypeName":"只读","Method":"ReadSetFSpeed","RegisterAddress":null,"DataType":6,"DataTypeName":"int32","Encoding":1,"EncodingName":"utf8","Length":0}�Lt%
	



		
�M�e ���y@�ActFeed进给速度 ���0�[]{"Min":0.0,"Max":0.0,"MinFilterType":1,"SetMin":0.0,"SetMax":0.0,"MaxFilterType":1,"Save":false}{"ProtectType":1,"ProtectTypeName":"只读","Method":"ReadActFeed","RegisterAddress":null,"DataType":6,"DataTypeName":"int32","Encoding":1,"EncodingName":"utf8","Length":0}�Js%
	



		
�M�c ���y@�OvSpin主轴倍率 ���0�[]{"Min":0.0,"Max":0.0,"MinFilterType":1,"SetMin":0.0,"SetMax":0.0,"MaxFilterType":1,"Save":false}{"ProtectType":1,"ProtectTypeName":"只读","Method":"ReadOvSpin","RegisterAddress":null,"DataType":6,"DataTypeName":"int32","Encoding":1,"EncodingName":"utf8","Length":0}�Ir
	



		
�M�e ���y@�ToolNum刀具号 ���0�[]{"Min":0.0,"Max":0.0,"MinFilterType":1,"SetMin":0.0,"SetMax":0.0,"MaxFilterType":1,"Save":false}{"ProtectType":1,"ProtectTypeName":"只读","Method":"ReadToolNum","RegisterAddress":null,"DataType":6,"DataTypeName":"int32","Encoding":1,"EncodingName":"utf8","Length":0}
    ��
?                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           � �+

 
�?		
�M�S ����NYD当年用电量// 定义保存变量名称
var dl_TotalConstName = '${this.DeviceName}_dl_Total';
// 获取当前设备保存的电量相关信息
var dl_Total = share.Get(dl_TotalConstName);
// 空
if (dl_Total != null) { 
   dl_Total = JSON.parse(dl_Total);
}
// 月合计
var monthSum = dl_Total.months.reduce((total, current) => total + current, 0);
// 日合计
var daySum = dl_Total.days.reduce((total, current) => total + current, 0);
return  monthSum + daySum + dl_Total.lastTotal ����[]�{"Min":0.0,"Max":0.0,"MinFilterType":1,"SetMin":0.0,"SetMax":0.0,"MaxFilterType":1,"Save":false}{"ProtectType":0,"ProtectTypeName":"只读","Method":null,"RegisterAddress":null,"DataType":6,"DataTypeName":"int32","Encoding":1,"EncodingName":"utf8","Length":0}�.� +

 
�[		
�M�S ��`�YYD当月用电量// 定义保存变量名称
var dl_TotalConstName = '${this.DeviceName}_dl_Total';
// 获取当前设备保存的电量相关信息
var dl_Total = share.Get(dl_TotalConstName);
// 空
if (dl_Total != null) { 
   dl_Total = JSON.parse(dl_Total);
}
 var daySum = dl_Total.days.reduce((total, current) => total + current, 0);
return  daySum + dl_Total.lastTotal
 ����[]�{"Min":0.0,"Max":0.0,"MinFilterType":1,"SetMin":0.0,"SetMax":0.0,"MaxFilterType":1,"Save":false}{"ProtectType":0,"ProtectTypeName":"只读","Method":null,"RegisterAddress":null,"DataType":6,"DataTypeName":"int32","Encoding":1,"EncodingName":"utf8","Length":0}�5% 	 	Q c		-=�M�S	 ���y`�online在线状态{"True":"在线","False":"离线"}return device.Status('${this.DeviceName}'); ���0�["系统属性"]true:在线;false:关机{"Min":0.0,"Max":0.0,"MinFilterType":1,"SetMin":0.0,"SetMax":0.0,"MaxFilterType":1,"Save":false}{"ProtectType":0,"ProtectTypeName":"只读","Method":null,"RegisterAddress":null,"DataType":6,"DataTypeName":"int32","Encoding":1,"EncodingName":"utf8","Length":0}�T~#%
	



		
�M�m ���yP�SpindleLoad主轴负载 ���0�[]{"Min":0.0,"Max":0.0,"MinFilterType":1,"SetMin":0.0,"SetMax":0.0,"MaxFilterType":1,"Save":false}{"ProtectType":1,"ProtectTypeName":"只读","Method":"ReadSpindleLoad","RegisterAddress":null,"DataType":6,"DataTypeName":"int32","Encoding":1,"EncodingName":"utf8","Length":0}
    � �R �                                                                                                                                         �5�% 	 	Q c		-=�M�S	 �/
��online在线状态{"True":"在线","False":"离线"}return device.Status('${this.DeviceName}'); �/��["系统属性"]true:在线;false:关机{"Min":0.0,"Max":0.0,"MinFilterType":1,"SetMin":0.0,"SetMax":0.0,"MaxFilterType":1,"Save":false}{"ProtectType":0,"ProtectTypeName":"只读","Method":null,"RegisterAddress":null,"DataType":6,"DataTypeName":"int32","Encoding":1,"EncodingName":"utf8","Length":0}�5�% 	 	Q c		-=�M�S	 
Ϲg�online在线状态{"True":"在线","False":"离线"}return device.Status('${this.DeviceName}'); ����["系统属性"]true:在线;false:关机{"Min":0.0,"Max":0.0,"MinFilterType":1,"SetMin":0.0,"SetMax":0.0,"MaxFilterType":1,"Save":false}{"ProtectType":0,"ProtectTypeName":"只读","Method":null,"RegisterAddress":null,"DataType":6,"DataTypeName":"int32","Encoding":1,"EncodingName":"utf8","Length":0}�"�7

 
�=		
�M�S f[�p�DN1正向有功总电能var dn1 = device.Get('${this.DeviceName}.DN1');
var dl = parseInt(dn1) + 1;
return dl; ����[]{"Min":0.0,"Max":0.0,"MinFilterType":1,"SetMin":0.0,"SetMax":0.0,"MaxFilterType":1,"Save":false}{"ProtectType":0,"ProtectTypeName":"只读","Method":null,"RegisterAddress":null,"DataType":6,"DataTypeName":"int32","Encoding":1,"EncodingName":"utf8","Length":0}�K�/%

 
�		
�M�S 1��Electricity_Today今日用电
// 采集当前用电量
var currentDL = device.Get("${this.DeviceName}.DN1") || 0; // 电量

// 定义保存变量名称
var dl_TotalConstName = '${this.DeviceName}_dl_Total';
// 获取当前设备保存的电量相关信息
var dl_Total = share.Get(dl_TotalConstName);
// 空
if (dl_Total == null) {
    // 初始化
    dl_Total = {
        lastTime: dateTime.AddDate(new Date(), 'hour', 8),
        firstTotal: currentDL,
        lastTotal: 0,
        days: [],
        months: [],
        years: []
    }
} else {
    // 转换成对象
    dl_Total = JSON.parse(dl_Total);
}

// 当前日期
var nowTime = dateTime.Format(dateTime.AddDate(new Date(), 'hour', 8), "yyyy-MM-dd");
// 最后记录日期
var lastTime = dl_Total.lastTime;
// 创建 Date 对象并提取年、月
var date1 = new Date(nowTime);
var year1 = date1.getFullYear();
var month1 = date1.getMonth();

var date2 = new Date(lastTime);
var year2 = date2.getFullYear();
var month2 = date2.getMonth();

// 是同一天数据
if (nowTime == lastTime) {
    // 检查电量是否增加
    if (currentDL > dl_Total.firstTotal) {
        dl_Total.lastTotal = decrease(currentDL, dl_Total.firstTotal);
    }
} else {
    // 昨日总耗电量
    var yesterdayTotal = dl_Total.lastTotal;
    // 每日耗电量
    dl_Total.days.push(yesterdayTotal);
    // 更新当日时间
    dl_Total.lastTime = nowTime;
    // 更新首次产量
    dl_Total.firstTotal = currentDL;
    // 实时产量归0
    dl_Total.lastTotal = 0;
    // 判断是不是当月
    if (month1 !== month2) {
        var daySum = dl_Total.days.reduce((total, current) => total + current, 0);
        dl_Total.months.push(daySum)
        dl_Total.days = [];
    }

    // 判断是不是当年
    if (year1 !== year2) {
        var monthSum = dl_Total.months.reduce((total, current) => total + current, 0);
        dl_Total.years.push(monthSum)
        dl_Total.days = [];
        dl_Total.months = [];
    }
}

// 保存
var save = share.Save(dl_TotalConstName, dl_Total);
return dl_Total.lastTotal;


// 相加
function increase(a, b) {
    return (a * 100000 + b * 100000) / 100000;
};

// 相减
function decrease(a, b) {
    return (a * 100000 - b * 100000) / 100000;
}; ����[]{"Min":0.0,"Max":0.0,"MinFilterType":1,"SetMin":0.0,"SetMax":0.0,"MaxFilterType":1,"Save":false}{"ProtectType":0,"ProtectTypeName":"只读","Method":null,"RegisterAddress":null,"DataType":6,"DataTypeName":"int32","Encoding":1,"EncodingName":"utf8","Length":0}
    X _ 
k���8
�
�
*	����q� .��7 � � X                      O+- %
		
 �o0�MessageInterval报文间隔(ms)0 �o0�高级配置ctext? %
		
 �o0�Port端口号502 �o0�连接配置ctextK# %
		
 �o0�IpAddressIP地址*********** �o0�连接配置ctext�6#9 %�'	3 �o0�WaitTimeout监听超时时间(ms)30000 �o0�高级配置等待的超时时间，如果超时时间为-1的话，则是无期限等待{"ReadType": ["2"]}text�%9 %m	3 �o0�ReadInterval刷新数据频率(ms)100 �o0�高级配置监听属性地址的读取频率,单位(毫秒){"ReadType": ["2"]}text�'%
 %{	3 �o0�VariableValue指定的值 �o0�高级配置设置指定的值,满足后会触发读取设备地址{"ReadType": ["2"]}text^- %
	3	 �o0�MinPeriod轮询周期(ms)1000 �o0�高级配置{"ReadType": ["1"]}text�,1%
 %�	3	 �o0�VariableIdentifier监听属性 �o0�高级配置等待指定地址的值为设定指定的值,再触发读取设备地址{"ReadType": ["2"]}combine�o%_%�		
 �o0�ReadType读取模式1{"轮询模式":1,"订阅触发模式":2} �o0�高级配置轮询模式：主动遍历轮询读取配置采集点
订阅触发模式：订阅某个地址，达到设置值后在进行读取text�''9 %�/		
 ��R �WriteInterval写入间隔周期(ms)120 ��o`�高级配置每个报文的间隔时间，避免数据写入不成功（针对低速设备）ctext�$%5%�#		
 ��R �BulkRead批量读取1{"true":1,"false":2} ��o`�高级配置不连续报文组成合并包，减少查询包次数，缩短轮询周期ctext�
+ %�		
 ��R�WaitTime等待时间(s)0 ��o`�高级配置设备每次进行连接前等待的时间，通常针对慢设备ctext� - %y		
 ��R�Timeout超时时间(ms)3000 ��o`�高级配置发报文收到响应报文的时间，超出算超时ctext� !+ %y		
 ��R�ReConnTime重连周期(s)60 ��o`�高级配置当设备关机后下一轮进行重新连接的时间ctext�\#+ %�-		
 ��R �BackoffTime退避时间(m)10 ��o`�高级配置当设备连续超时达到设置超时次数时，进入退避逻辑开始计时，达到退避设定时间，再将该设备加入到轮询里ctext�b%% %�?		
 ��R �TimeOutCount超时次数0 ��o`�高级配置设备所有属性均超时计为一次设备超时，连续超时次数达到设定次数，进去退避逻辑，再下一次的轮询里摘除该设备ctext`
'+5%
		
 ��Q��StringReverse字符串颠倒2{"true":1,"false":2} ��o`�连接配置ctext`'+5%
		
 ��Q��ByteTransform大小端转换2{"true":1,"false":2} ��o`�连接配置ctextk!%W%
		
 ��Q��DataFormat解析类型0{"ABCD":0,"BADC":1,"CDAB":2,"DCBA":3} ��o`�连接配置ctext=
 %
		
 ��Q��Station站号1 ��o`�连接配置ctextO	+- %
		
 ��Qp�MessageInterval报文间隔(ms)0 ��o`�高级配置ctext? %
		
 ��Q`�Port端口号502 ��o`�连接配置ctextL% %
		
 ��Q`�IpAddressIP地址************ ��o`�连接配置ctext�6#9 %�'	3 ��Q��WaitTimeout监听超时时间(ms)30000 ��o`�高级配置等待的超时时间，如果超时时间为-1的话，则是无期限等待{"ReadType": ["2"]}text�%9 %m	3 ��Q��ReadInterval刷新数据频率(ms)100 ��o`�高级配置监听属性地址的读取频率,单位(毫秒){"ReadType": ["2"]}text�'%
 %{	3 ��Q��VariableValue指定的值 ��o`�高级配置设置指定的值,满足后会触发读取设备地址{"ReadType": ["2"]}text]- %
	3	 ��R0�MinPeriod轮询周期(ms)200 ��o`�高级配置{"ReadType": ["1"]}text�,1%
 %�	3	 ��Q��VariableIdentifier监听属性 ��o`�高级配置等待指定地址的值为设定指定的值,再触发读取设备地址{"ReadType": ["2"]}combine�o%_%�		
 ��Q��ReadType读取模式1{"轮询模式":1,"订阅触发模式":2} ��o`�高级配置轮询模式：主动遍历轮询读取配置采集点
订阅触发模式：订阅某个地址，达到设置值后在进行读取text
    �T��
��I�9
�	��G�R��&�E�j��J�{                                                                                                                                                                                                                         `9'+5%
		
 �jm@�StringReverse字符串颠倒2{"true":1,"false":2} �jn �连接配置ctext`8'+5%
		
 �jm@�ByteTransform大小端转换2{"true":1,"false":2} �jn �连接配置ctextk7!%W%
		
 �jm@�DataFormat解析类型0{"ABCD":0,"BADC":1,"CDAB":2,"DCBA":3} �jn �连接配置ctext=6 %
		
 �jm@�Station站号1 �jn �连接配置ctextO5+- %
		
 �jm@�MessageInterval报文间隔(ms)0 �jn �高级配置ctext�
4%5%y		
 �jm0�IsAsync异步读取2{"true":1,"false":2} �jn �高级配置同串口接入多个设备，建议关闭异步读取ctexto3i%
		
 �jm �Checkout校验位0{"None":0,"Odd":1,"Even":2,"Mark":3,"Space":4} �jn �连接配置ctexth2c%
		
 �jm�Stop停止位1{"None":0,"One":1,"Two":2,"OnePointFive":3} �jn �连接配置ctextA1 %
		
 �jm�DataBits数据位8 �jn �连接配置ctext�0�?%
		
 �jl��BaudRate波特率3{"9600":3,"1200":0,"2400":1,"4800":2,"19200":4,"38400":6,"57600":7,"115200":8,"187500":5} �jn �连接配置ctext�V/%�%�1		
 �jl��SerialNumber串口号6{"/dev/ttyS4":6,"/dev/ttyS3":2,"/dev/ttyS0":4,"/dev/ttyS9":7} �jn �连接配置ttyS3：A1/B1(485) 
ttyS4：A2/B2(485) 
ttyS0：RX1/TX1(232) 
ttyS9：RX2/TX2(232)ctext�6.#9 %�'	3 �jmP�WaitTimeout监听超时时间(ms)30000 �jn �高级配置等待的超时时间，如果超时时间为-1的话，则是无期限等待{"ReadType": ["2"]}text�-%9 %m	3 �jmP�ReadInterval刷新数据频率(ms)100 �jn �高级配置监听属性地址的读取频率,单位(毫秒){"ReadType": ["2"]}text�,'%
 %{	3 �jmP�VariableValue指定的值 �jn �高级配置设置指定的值,满足后会触发读取设备地址{"ReadType": ["2"]}text^+- %
	3	 �jm`�MinPeriod轮询周期(ms)1000 �jn �高级配置{"ReadType": ["1"]}text�,*1%
 %�	3	 �jmP�VariableIdentifier监听属性 �jn �高级配置等待指定地址的值为设定指定的值,再触发读取设备地址{"ReadType": ["2"]}combine�o)%_%�		
 �jmP�ReadType读取模式1{"轮询模式":1,"订阅触发模式":2} �jn �高级配置轮询模式：主动遍历轮询读取配置采集点
订阅触发模式：订阅某个地址，达到设置值后在进行读取text�'('9 %�/		
 �o0�WriteInterval写入间隔周期(ms)120 �o0�高级配置每个报文的间隔时间，避免数据写入不成功（针对低速设备）ctext�$'%5%�#		
 �o0�BulkRead批量读取1{"true":1,"false":2} �o0�高级配置不连续报文组成合并包，减少查询包次数，缩短轮询周期ctext�
&+ %�		
 �o0�WaitTime等待时间(s)0 �o0�高级配置设备每次进行连接前等待的时间，通常针对慢设备ctext� %- %y		
 �o0�Timeout超时时间(ms)3000 �o0�高级配置发报文收到响应报文的时间，超出算超时ctext� $!+ %y		
 �o0�ReConnTime重连周期(s)60 �o0�高级配置当设备关机后下一轮进行重新连接的时间ctext�\##+ %�-		
 �o0�BackoffTime退避时间(m)10 �o0�高级配置当设备连续超时达到设置超时次数时，进入退避逻辑开始计时，达到退避设定时间，再将该设备加入到轮询里ctext�b"%% %�?		
 �o0�TimeOutCount超时次数0 �o0�高级配置设备所有属性均超时计为一次设备超时，连续超时次数达到设定次数，进去退避逻辑，再下一次的轮询里摘除该设备ctext`!'+5%
		
 �o0�StringReverse字符串颠倒2{"true":1,"false":2} �o0�连接配置ctext` '+5%
		
 �o0�ByteTransform大小端转换2{"true":1,"false":2} �o0�连接配置ctextk!%W%
		
 �o0�DataFormat解析类型0{"ABCD":0,"BADC":1,"CDAB":2,"DCBA":3} �o0�连接配置ctext= %
		
 �o0�Station站号1 �o0�连接配置ctext
    B <
�
6�X
f	�	W�(o���K�J��M��� � B    � T!+ %y		
 �l�F �ReConnTime重连周期(s)60 �l�F`�高级配置当设备关机后下一轮进行重新连接的时间ctext�\S#+ %�-		
 �l�F �BackoffTime退避时间(m)10 �l�F`�高级配置当设备连续超时达到设置超时次数时，进入退避逻辑开始计时，达到退避设定时间，再将该设备加入到轮询里ctext�bR%% %�?		
 �l�E��TimeOutCount超时次数0 �l�F`�高级配置设备所有属性均超时计为一次设备超时，连续超时次数达到设定次数，进去退避逻辑，再下一次的轮询里摘除该设备ctext`Q'+5%
		
 �l�E��StringReverse字符串颠倒2{"true":1,"false":2} �l�F`�连接配置ctext`P'+5%
		
 �l�E��ByteTransform大小端转换2{"true":1,"false":2} �l�F`�连接配置ctextkO!%W%
		
 �l�E��DataFormat解析类型0{"ABCD":0,"BADC":1,"CDAB":2,"DCBA":3} �l�F`�连接配置ctext=N %
		
 �l�E��Station站号1 �l�F`�连接配置ctextOM+- %
		
 �l�E��MessageInterval报文间隔(ms)0 �l�F`�高级配置ctext�
L%5%y		
 �l�E��IsAsync异步读取2{"true":1,"false":2} �l�F`�高级配置同串口接入多个设备，建议关闭异步读取ctextoKi%
		
 �l�E��Checkout校验位0{"None":0,"Odd":1,"Even":2,"Mark":3,"Space":4} �l�F`�连接配置ctexthJc%
		
 �l�E��Stop停止位1{"None":0,"One":1,"Two":2,"OnePointFive":3} �l�F`�连接配置ctextAI %
		
 �l�E��DataBits数据位8 �l�F`�连接配置ctext�H�?%
		
 �l�E��BaudRate波特率3{"9600":3,"1200":0,"2400":1,"4800":2,"19200":4,"38400":6,"57600":7,"115200":8,"187500":5} �l�F`�连接配置ctext�VG%�%�1		
 �l�E��SerialNumber串口号2{"/dev/ttyS4":6,"/dev/ttyS3":2,"/dev/ttyS0":4,"/dev/ttyS9":7} �l�F`�连接配置ttyS3：A1/B1(485) 
ttyS4：A2/B2(485) 
ttyS0：RX1/TX1(232) 
ttyS9：RX2/TX2(232)ctext�6F#9 %�'	3 �l�E��WaitTimeout监听超时时间(ms)30000 �l�F`�高级配置等待的超时时间，如果超时时间为-1的话，则是无期限等待{"ReadType": ["2"]}text�E%9 %m	3 �l�E��ReadInterval刷新数据频率(ms)100 �l�F`�高级配置监听属性地址的读取频率,单位(毫秒){"ReadType": ["2"]}text�D'%
 %{	3 �l�E��VariableValue指定的值 �l�F`�高级配置设置指定的值,满足后会触发读取设备地址{"ReadType": ["2"]}text^C- %
	3	 �l�F �MinPeriod轮询周期(ms)1000 �l�F`�高级配置{"ReadType": ["1"]}text�,B1%
 %�	3	 �l�E��VariableIdentifier监听属性 �l�F`�高级配置等待指定地址的值为设定指定的值,再触发读取设备地址{"ReadType": ["2"]}combine�oA%_%�		
 �l�E��ReadType读取模式1{"轮询模式":1,"订阅触发模式":2} �l�F`�高级配置轮询模式：主动遍历轮询读取配置采集点
订阅触发模式：订阅某个地址，达到设置值后在进行读取text�'@'9 %�/		
 �jm`�WriteInterval写入间隔周期(ms)120 �jn �高级配置每个报文的间隔时间，避免数据写入不成功（针对低速设备）ctext�$?%5%�#		
 �jm`�BulkRead批量读取2{"true":1,"false":2} �jn �高级配置不连续报文组成合并包，减少查询包次数，缩短轮询周期ctext�
>+ %�		
 �jm`�WaitTime等待时间(s)0 �jn �高级配置设备每次进行连接前等待的时间，通常针对慢设备ctext� =- %y		
 �jm`�Timeout超时时间(ms)3000 �jn �高级配置发报文收到响应报文的时间，超出算超时ctext� <!+ %y		
 �jmP�ReConnTime重连周期(s)60 �jn �高级配置当设备关机后下一轮进行重新连接的时间ctext�\;#+ %�-		
 �jmP�BackoffTime退避时间(m)10 �jn �高级配置当设备连续超时达到设置超时次数时，进入退避逻辑开始计时，达到退避设定时间，再将该设备加入到轮询里ctext�b:%% %�?		
 �jmP�TimeOutCount超时次数0 �jn �高级配置设备所有属性均超时计为一次设备超时，连续超时次数达到设定次数，进去退避逻辑，再下一次的轮询里摘除该设备ctext
    � }�I
����	
o	�	g	%��>Yz�t�=�R`�Q �                                                                                                                              �o'%
 %{	3 ��p(P�VariableValue指定的值 ��p4��高级配置设置指定的值,满足后会触发读取设备地址{"ReadType": ["2"]}text^n- %
	3	 ��p(��MinPeriod轮询周期(ms)1000 ��p4��高级配置{"ReadType": ["1"]}text�,m1%
 %�	3	 ��p(@�VariableIdentifier监听属性 ��p4��高级配置等待指定地址的值为设定指定的值,再触发读取设备地址{"ReadType": ["2"]}combine�ol%_%�		
 ��p(p�ReadType读取模式1{"轮询模式":1,"订阅触发模式":2} ��p4��高级配置轮询模式：主动遍历轮询读取配置采集点
订阅触发模式：订阅某个地址，达到设置值后在进行读取textYk%5%
		
  ����ReadAsync异步读取1{"true":1,"false":2} �n�� �连接配置ctext�
j%5%y		
 ��YE �IsAsync异步读取1{"true":1,"false":2} �n�� �高级配置同串口接入多个设备，建议关闭异步读取ctext�'i'9 %�/		
 �n����WriteInterval写入间隔周期(ms)120 �n�� �高级配置每个报文的间隔时间，避免数据写入不成功（针对低速设备）ctext�
h+ %�		
 �n����WaitTime等待时间(s)0 �n�� �高级配置设备每次进行连接前等待的时间，通常针对慢设备ctext� g- %y		
 �n����Timeout超时时间(ms)3000 �n�� �高级配置发报文收到响应报文的时间，超出算超时ctext� f!+ %y		
 �n����ReConnTime重连周期(s)60 �n�� �高级配置当设备关机后下一轮进行重新连接的时间ctext�\e#+ %�-		
 �n����BackoffTime退避时间(m)10 �n�� �高级配置当设备连续超时达到设置超时次数时，进入退避逻辑开始计时，达到退避设定时间，再将该设备加入到轮询里ctext�bd%% %�?		
 �n����TimeOutCount超时次数0 �n�� �高级配置设备所有属性均超时计为一次设备超时，连续超时次数达到设定次数，进去退避逻辑，再下一次的轮询里摘除该设备ctextLc-%
		
 �n�Ҡ�Series系列0{"Dvp":0,"AS":1} �n�� �连接配置ctext=b %
		
 �n�Ґ�Station站号1 �n�� �连接配置ctextXa%5%
		
 �n�Ҁ�BulkRead批量读取2{"true":1,"false":2} �n�� �高级配置ctext@` %
		
 �n��p�Port端口号1102 �n�� �连接配置ctextM_' %
		
 �n��`�IpAddressIP地址************* �n�� �连接配置ctext�6^#9 %�'	3 �n�Ұ�WaitTimeout监听超时时间(ms)30000 �n�� �高级配置等待的超时时间，如果超时时间为-1的话，则是无期限等待{"ReadType": ["2"]}text�]%9 %m	3 �n�Ұ�ReadInterval刷新数据频率(ms)100 �n�� �高级配置监听属性地址的读取频率,单位(毫秒){"ReadType": ["2"]}text�\'%
 %{	3 �n�Ұ�VariableValue指定的值 �n�� �高级配置设置指定的值,满足后会触发读取设备地址{"ReadType": ["2"]}text^[- %
	3	 �n����MinPeriod轮询周期(ms)1000 �n�� �高级配置{"ReadType": ["1"]}text�,Z1%
 %�	3	 �n�Ұ�VariableIdentifier监听属性 �n�� �高级配置等待指定地址的值为设定指定的值,再触发读取设备地址{"ReadType": ["2"]}combine�oY%_%�		
 �n����ReadType读取模式1{"轮询模式":1,"订阅触发模式":2} �n�� �高级配置轮询模式：主动遍历轮询读取配置采集点
订阅触发模式：订阅某个地址，达到设置值后在进行读取text�'X'9 %�/		
 �l�F �WriteInterval写入间隔周期(ms)120 �l�F`�高级配置每个报文的间隔时间，避免数据写入不成功（针对低速设备）ctext�$W%5%�#		
 �l�F �BulkRead批量读取2{"true":1,"false":2} �l�F`�高级配置不连续报文组成合并包，减少查询包次数，缩短轮询周期ctext�
V+ %�		
 �l�F �WaitTime等待时间(s)0 �l�F`�高级配置设备每次进行连接前等待的时间，通常针对慢设备ctext� U- %y		
 �l�F �Timeout超时时间(ms)3000 �l�F`�高级配置发报文收到响应报文的时间，超出算超时ctext
    � f�b!
�
�
$�`{
�
	�		b��d���<�u��+ �                                                                                                       � �- %y		
 ��2���Timeout超时时间(ms)3000 ��2���高级配置发报文收到响应报文的时间，超出算超时ctext� �
!+ %y		
 ��2���ReConnTime重连周期(s)60 ��2���高级配置当设备关机后下一轮进行重新连接的时间ctext�\�	#+ %�-		
 ��2�p�BackoffTime退避时间(m)10 ��2���高级配置当设备连续超时达到设置超时次数时，进入退避逻辑开始计时，达到退避设定时间，再将该设备加入到轮询里ctext�b�%% %�?		
 ��2�p�TimeOutCount超时次数0 ��2���高级配置设备所有属性均超时计为一次设备超时，连续超时次数达到设定次数，进去退避逻辑，再下一次的轮询里摘除该设备ctextR�5%
		
 ��2�P�IsBinaryBinary2{"true":1,"false":2} ��2���连接配置ctexto�i%
		
 ��2�@�Checkout校验位0{"None":0,"Odd":1,"Even":2,"Mark":3,"Space":4} ��2���连接配置ctexth�c%
		
 ��2�0�Stop停止位1{"None":0,"One":1,"Two":2,"OnePointFive":3} ��2���连接配置ctextA� %
		
 ��2� �DataBits数据位8 ��2���连接配置ctext���?%
		
 ��2��BaudRate波特率3{"1200":0,"2400":1,"4800":2,"9600":3,"19200":4,"38400":6,"57600":7,"115200":8,"187500":5} ��2���连接配置ctext�V�%�%�1		
 ��2���SerialNumber串口号4{"/dev/ttyS3":2,"/dev/ttyS0":4,"/dev/ttyS4":6,"/dev/ttyS9":7} ��2���连接配置ttyS3：A1/B1(485) 
ttyS4：A2/B2(485) 
ttyS0：RX1/TX1(232) 
ttyS9：RX2/TX2(232)ctext^�- %
	3	 ��2���MinPeriod轮询周期(ms)1000 ��2���高级配置{"ReadType": ["1"]}text�o� %_%�		
 ��2�`�ReadType读取模式1{"轮询模式":1,"订阅触发模式":2} ��2���高级配置轮询模式：主动遍历轮询读取配置采集点
订阅触发模式：订阅某个地址，达到设置值后在进行读取text�''9 %�/		
 ��p(��WriteInterval写入间隔周期(ms)120 ��p4��高级配置每个报文的间隔时间，避免数据写入不成功（针对低速设备）ctext�$~%5%�#		
 ��p(��BulkRead批量读取1{"true":1,"false":2} ��p4��高级配置不连续报文组成合并包，减少查询包次数，缩短轮询周期ctext�
}+ %�		
 ��p(��WaitTime等待时间(s)0 ��p4��高级配置设备每次进行连接前等待的时间，通常针对慢设备ctext� |- %y		
 ��p(��Timeout超时时间(ms)3000 ��p4��高级配置发报文收到响应报文的时间，超出算超时ctext� {!+ %y		
 ��p(��ReConnTime重连周期(s)60 ��p4��高级配置当设备关机后下一轮进行重新连接的时间ctext�\z#+ %�-		
 ��p(��BackoffTime退避时间(m)10 ��p4��高级配置当设备连续超时达到设置超时次数时，进入退避逻辑开始计时，达到退避设定时间，再将该设备加入到轮询里ctext�by%% %�?		
 ��p(��TimeOutCount超时次数0 ��p4��高级配置设备所有属性均超时计为一次设备超时，连续超时次数达到设定次数，进去退避逻辑，再下一次的轮询里摘除该设备ctext`x'+5%
		
 ��p(@�StringReverse字符串颠倒2{"true":1,"false":2} ��p4��连接配置ctext`w'+5%
		
 ��p(0�ByteTransform大小端转换2{"true":1,"false":2} ��p4��连接配置ctextkv!%W%
		
 ��p(�DataFormat解析类型0{"ABCD":0,"BADC":1,"CDAB":2,"DCBA":3} ��p4��连接配置ctext=u %
		
 ��p( �Station站号1 ��p4��连接配置ctextOt+- %
		
 ��p( �MessageInterval报文间隔(ms)0 ��p4��高级配置ctext?s %
		
 ��p( �Port端口号503 ��p4��连接配置ctextIr %
		
 ��p( �IpAddressIP地址127.0.0.1 ��p4��连接配置ctext�6q#9 %�'	3 ��p(p�WaitTimeout监听超时时间(ms)30000 ��p4��高级配置等待的超时时间，如果超时时间为-1的话，则是无期限等待{"ReadType": ["2"]}text�p%9 %m	3 ��p(`�ReadInterval刷新数据频率(ms)100 ��p4��高级配置监听属性地址的读取频率,单位(毫秒){"ReadType": ["2"]}text
    H r
+�z7�
�
	�	�8�d	�zn�=��� � H        �'!+ %y		
 �� �ReConnTime重连周期(s)5 ����高级配置当设备关机后下一轮进行重新连接的时间ctext�\�&#+ %�-		
 ���BackoffTime退避时间(m)10 ����高级配置当设备连续超时达到设置超时次数时，进入退避逻辑开始计时，达到退避设定时间，再将该设备加入到轮询里ctext�b�%%% %�?		
 �� �TimeOutCount超时次数0 ����高级配置设备所有属性均超时计为一次设备超时，连续超时次数达到设定次数，进去退避逻辑，再下一次的轮询里摘除该设备ctext�o�$%_%�		
 ����ReadType读取模式1{"轮询模式":1,"订阅触发模式":2} ����高级配置轮询模式：主动遍历轮询读取配置采集点
订阅触发模式：订阅某个地址，达到设置值后在进行读取text�6�##9 %�'	3 ����WaitTimeout监听超时时间(ms)30000 ����高级配置等待的超时时间，如果超时时间为-1的话，则是无期限等待{"ReadType": ["2"]}text��"%9 %m	3 ����ReadInterval刷新数据频率(ms)100 ����高级配置监听属性地址的读取频率,单位(毫秒){"ReadType": ["2"]}text��!'%
 %{	3 ���VariableValue指定的值 ����高级配置设置指定的值,满足后会触发读取设备地址{"ReadType": ["2"]}text�,� 1%
 %�	3	 �� �VariableIdentifier监听属性 ����高级配置等待指定地址的值为设定指定的值,再触发读取设备地址{"ReadType": ["2"]}combineY�%5%
		
 ����ReadAsync异步读取1{"true":1,"false":2} ����连接配置ctextL�-%
		
 �� �Series系列0{"Dvp":0,"AS":1} ����连接配置ctext=� %
		
 ����Station站号2 ����连接配置ctextX�%5%
		
 ����BulkRead批量读取2{"true":1,"false":2} ����高级配置ctext�
�%5%y		
 ��`�IsAsync异步读取1{"true":1,"false":2} ����高级配置同串口接入多个设备，建议关闭异步读取ctext@� %
		
 ��P�Port端口号8102 ����连接配置ctextM�' %
		
 �� �IpAddressIP地址************* ����连接配置ctext�
�+ %�		
 ���w �WaitTime等待时间(s)0 ���0�高级配置设备每次进行连接前等待的时间，通常针对慢设备ctext� �- %y		
 ���w �Timeout超时时间(ms)3000 ���0�高级配置发报文收到响应报文的时间，超出算超时ctext� �!+ %y		
 ���w �ReConnTime重连周期(s)60 ���0�高级配置当设备关机后下一轮进行重新连接的时间ctext�\�#+ %�-		
 ���w�BackoffTime退避时间(m)10 ���0�高级配置当设备连续超时达到设置超时次数时，进入退避逻辑开始计时，达到退避设定时间，再将该设备加入到轮询里ctext�b�%% %�?		
 ���w�TimeOutCount超时次数0 ���0�高级配置设备所有属性均超时计为一次设备超时，连续超时次数达到设定次数，进去退避逻辑，再下一次的轮询里摘除该设备ctextP�5%
		
 ���v��DncDNC管理1{"true":1,"false":2} ���0�高级配置ctext@� %
		
 ���v��Port端口号8193 ���0�连接配置ctextM�' %
		
 ���v��IpAddressIP地址************* ���0�连接配置ctext^�- %
	3	 ���w �MinPeriod轮询周期(ms)1000 ���0�高级配置{"ReadType": ["1"]}text�o�%_%�		
 ���w�ReadType读取模式1{"轮询模式":1,"订阅触发模式":2} ���0�高级配置轮询模式：主动遍历轮询读取配置采集点
订阅触发模式：订阅某个地址，达到设置值后在进行读取text^�- %
	3	 ��sS0�MinPeriod轮询周期(ms)1000 ��sa@�高级配置{"ReadType": ["1"]}text�o�
%_%�		
 ��sR��ReadType读取模式1{"轮询模式":1,"订阅触发模式":2} ��sa@�高级配置轮询模式：主动遍历轮询读取配置采集点
订阅触发模式：订阅某个地址，达到设置值后在进行读取text�
�+ %�		
 ��2���WaitTime等待时间(s)0 ��2���高级配置设备每次进行连接前等待的时间，通常针对慢设备ctext
   � |�C
�
�
T
�T��
�
H	�� :�2��Q�                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        ^�?- %
	3	 �/
��MinPeriod轮询周期(ms)1000 �/��高级配置{"ReadType": ["1"]}text�'�>'9 %�/		
 �/
p�WriteInterval写入间隔周期(ms)120 �/��高级配置每个报文的间隔时间，避免数据写入不成功（针对低速设备）ctext�$�=%5%�#		
 �/
p�BulkRead批量读取1{"true":1,"false":2} �/��高级配置不连续报文组成合并包，减少查询包次数，缩短轮询周期ctext�
�<+ %�		
 �/
`�WaitTime等待时间(s)0 �/��高级配置设备每次进行连接前等待的时间，通常针对慢设备ctext� �;- %y		
 �/
`�Timeout超时时间(ms)3000 �/��高级配置发报文收到响应报文的时间，超出算超时ctext� �:!+ %y		
 �/
`�ReConnTime重连周期(s)60 �/��高级配置当设备关机后下一轮进行重新连接的时间ctext�\�9#+ %�-		
 �/
P�BackoffTime退避时间(m)10 �/��高级配置当设备连续超时达到设置超时次数时，进入退避逻辑开始计时，达到退避设定时间，再将该设备加入到轮询里ctext�b�8%% %�?		
 �/
P�TimeOutCount超时次数0 �/��高级配置设备所有属性均超时计为一次设备超时，连续超时次数达到设定次数，进去退避逻辑，再下一次的轮询里摘除该设备ctext�o�7%_%�		
 �/
@�ReadType读取模式1{"轮询模式":1,"订阅触发模式":2} �/��高级配置轮询模式：主动遍历轮询读取配置采集点
订阅触发模式：订阅某个地址，达到设置值后在进行读取text�6�6#9 %�'	3 �/
@�WaitTimeout监听超时时间(ms)30000 �/��高级配置等待的超时时间，如果超时时间为-1的话，则是无期限等待{"ReadType": ["2"]}text��5%9 %m	3 �/
0�ReadInterval刷新数据频率(ms)100 �/��高级配置监听属性地址的读取频率,单位(毫秒){"ReadType": ["2"]}text��4'%
 %{	3 �/
0�VariableValue指定的值 �/��高级配置设置指定的值,满足后会触发读取设备地址{"ReadType": ["2"]}text�,�31%
 %�	3	 �/
�VariableIdentifier监听属性 �/��高级配置等待指定地址的值为设定指定的值,再触发读取设备地址{"ReadType": ["2"]}combine`�2'+5%
		
 �/
�StringReverse字符串颠倒2{"true":1,"false":2} �/��连接配置ctext`�1'+5%
		
 �/
 �ByteTransform大小端转换2{"true":1,"false":2} �/��连接配置ctextk�0!%W%
		
 �/	��DataFormat解析类型0{"ABCD":0,"BADC":1,"CDAB":2,"DCBA":3} �/��连接配置ctext=�/ %
		
 �/	��Station站号1 �/��连接配置ctextO�.+- %
		
 �/	��MessageInterval报文间隔(ms)0 �/��高级配置ctext?�- %
		
 �/	��Port端口号502 �/��连接配置ctextI�, %
		
 �/	��IpAddressIP地址127.0.0.1 �/��连接配置ctext^�+- %
	3	 ��P�MinPeriod轮询周期(ms)5000 ����高级配置{"ReadType": ["1"]}text�'�*'9 %�/		
 ��@�WriteInterval写入间隔周期(ms)120 ����高级配置每个报文的间隔时间，避免数据写入不成功（针对低速设备）ctext�
�)+ %�		
 ��0�WaitTime等待时间(s)0 ����高级配置设备每次进行连接前等待的时间，通常针对慢设备ctext� �(- %y		
 �� �Timeout超时时间(ms)3000 ����高级配置发报文收到响应报文的时间，超出算超时ctext
   � ��                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                �H	�_ Fx�: ��Q�EFanuc18i[{"Text":"模式","Value":"ReadWorkMode","Desc":"0:手动输入；1:自动循环；3:程序编辑；4:×100;5:连续进给；6:TeachInJog；7:TeachInHandle；8:InCfeed；9:ReFerence；10:ReMoTe"},{"Text":"状态","Value":"ReadRunStatus","Desc":"0:Reset；1:Stop；2:Hold；3:Start；4:Mstr；"},{"Text":"报警状态","Value":"ReadAlarmStatus","Desc":""},{"Text":"绝对坐标","Value":"ReadAbsPos","Desc":""},{"Text":"机械坐标","Value":"ReadMachPos","Desc":""},{"Text":"相对坐标","Value":"ReadRelPos","Desc":""},{"Text":"报警信息","Value":"ReadAlarm","Desc":""},{"Text":"报警Id","Value":"ReadAlarmId","Desc":""},{"Text":"报警类型Id","Value":"ReadAlarmType","Desc":""},{"Text":"程序名","Value":"ReadMainName","Desc":""},{"Text":"程序号","Value":"ReadCurPgm","Desc":""},{"Text":"产量","Value":"ReadCurrentNum","Desc":""},{"Text":"主轴倍率","Value":"ReadOvSpin","Desc":""},{"Text":"进给速度","Value":"ReadActFeed","Desc":""},{"Text":"主轴温度","Value":"ReadSpinTemp","Desc":""},{"Text":"伺服温度","Value":"ReadSvTemp","Desc":""},{"Text":"刀具号","Value":"ReadToolNum","Desc":""},{"Text":"设定进给速度","Value":"ReadSetFSpeed","Desc":""},{"Text":"设定主轴速度","Value":"ReadSetsSpeed","Desc":""},{"Text":"进给倍率","Value":"ReadOvFeed","Desc":""},{"Text":"伺服负载","Value":"ReadAxisLoad","Desc":""},{"Text":"主轴转速","Value":"ReadsSpeed","Desc":""},{"Text":"上电时间","Value":"ReadAliveTime","Desc":""},{"Text":"循环时间","Value":"ReadCycleTime","Desc":""},{"Text":"切削时间","Value":"ReadCutTime","Desc":""},{"Text":"运行时间","Value":"ReadRunTime","Desc":""},{"Text":"主轴负载","Value":"ReadSpindleLoad","Desc":""}]�F	�a Fx�9 ��Q�EFanuc[{"Text":"模式","Value":"ReadWorkMode","Desc":"0:手动输入；1:自动循环；3:程序编辑；4:×100;5:连续进给；6:TeachInJog；7:TeachInHandle；8:InCfeed；9:ReFerence；10:ReMoTe"},{"Text":"状态","Value":"ReadRunStatus","Desc":"0:Reset；1:Stop；2:Hold；3:Start；4:Mstr；"},{"Text":"报警状态","Value":"ReadAlarmStatus","Desc":""},{"Text":"程序名","Value":"ReadMainName","Desc":""},{"Text":"程序号","Value":"ReadCurPgm","Desc":""},{"Text":"绝对坐标","Value":"ReadAbsPos","Desc":""},{"Text":"机械坐标","Value":"ReadMachPos","Desc":""},{"Text":"相对坐标","Value":"ReadRelPos","Desc":""},{"Text":"报警信息","Value":"ReadAlarm","Desc":""},{"Text":"报警Id","Value":"ReadAlarmId","Desc":""},{"Text":"报警类型Id","Value":"ReadAlarmType","Desc":""},{"Text":"产量","Value":"ReadCurrentNum","Desc":""},{"Text":"主轴温度","Value":"ReadSpinTemp","Desc":""},{"Text":"伺服温度","Value":"ReadSvTemp","Desc":""},{"Text":"刀具号","Value":"ReadToolNum","Desc":""},{"Text":"主轴倍率","Value":"ReadOvSpin","Desc":""},{"Text":"进给速度","Value":"ReadActFeed","Desc":""},{"Text":"进给设定速度","Value":"ReadSetFSpeed","Desc":""},{"Text":"主轴速度","Value":"ReadsSpeed","Desc":""},{"Text":"主轴设定速度","Value":"ReadSetsSpeed","Desc":""},{"Text":"进给倍率","Value":"ReadOvFeed","Desc":""},{"Text":"伺服负载","Value":"ReadAxisLoad","Desc":""},{"Text":"上电时间","Value":"ReadAliveTime","Desc":""},{"Text":"循环时间","Value":"ReadCycleTime","Desc":""},{"Text":"切削时间","Value":"ReadCutTime","Desc":""},{"Text":"运行时间","Value":"ReadCycSec","Desc":""},{"Text":"主轴负载","Value":"ReadSpindleLoad","Desc":""},{"Text":"读PMC变量","Value":"R","Desc":"地址支持:G,F,Y,X,A,R,T,K,C,D,E"},{"Text":"读宏变量","Value":"Macro","Desc":""}]
   ' '                                                                                                                                                                                                                                                                                             �V	/�i Fx�; ��Q�EFanucMultiStation[{"Text":"模式","Value":"ReadWorkMode","Desc":"0:手动输入；1:自动循环；3:程序编辑；4:×100;5:连续进给；6:TeachInJog；7:TeachInHandle；8:InCfeed；9:ReFerence；10:ReMoTe"},{"Text":"报警类型Id","Value":"ReadAlarmType","Desc":""},{"Text":"状态","Value":"ReadRunStatus","Desc":"0:Reset；1:Stop；2:Hold；3:Start；4:Mstr；"},{"Text":"程序名","Value":"ReadMainName","Desc":""},{"Text":"程序号","Value":"ReadCurPgm","Desc":""},{"Text":"报警状态","Value":"ReadAlarmStatus","Desc":""},{"Text":"产量","Value":"ReadCurrentNum","Desc":""},{"Text":"主轴倍率","Value":"ReadOvSpin","Desc":""},{"Text":"进给速度","Value":"ReadActFeed","Desc":""},{"Text":"主轴温度","Value":"ReadSpinTemp","Desc":""},{"Text":"伺服温度","Value":"ReadSvTemp","Desc":""},{"Text":"刀具号","Value":"ReadToolNum","Desc":""},{"Text":"设定进给速度","Value":"ReadSetFSpeed","Desc":""},{"Text":"设定主轴速度","Value":"ReadSetsSpeed","Desc":""},{"Text":"进给倍率","Value":"ReadOvFeed","Desc":""},{"Text":"伺服负载","Value":"ReadAxisLoad","Desc":""},{"Text":"主轴转速","Value":"ReadsSpeed","Desc":""},{"Text":"绝对坐标","Value":"ReadAbsPos","Desc":""},{"Text":"机械坐标","Value":"ReadMachPos","Desc":""},{"Text":"相对坐标","Value":"ReadRelPos","Desc":""},{"Text":"报警信息","Value":"ReadAlarm","Desc":""},{"Text":"报警Id","Value":"ReadAlarmId","Desc":""},{"Text":"上电时间","Value":"ReadAliveTime","Desc":""},{"Text":"循环时间","Value":"ReadCycleTime","Desc":""},{"Text":"切削时间","Value":"ReadCutTime","Desc":""},{"Text":"运行时间","Value":"ReadCycSec","Desc":""},{"Text":"主轴负载","Value":"ReadSpindleLoad","Desc":""},{"Text":"通道二:模式","Value":"ReadWorkModeTwo","Desc":"0:手动输入；1:自动循环；3:程序编辑；4:×100;5:连续进给；6:TeachInJog；7:TeachInHandle；8:InCfeed；9:ReFerence；10:ReMoTe"},{"Text":"通道二:状态","Value":"ReadRunStatusTwo","Desc":"0:Reset；1:Stop；2:Hold；3:Start；4:Mstr；"},{"Text":"通道二:程序名","Value":"ReadMainNameTwo","Desc":""},{"Text":"通道二:程序号","Value":"ReadCurPgmTwo","Desc":""},{"Text":"通道二:报警状态","Value":"ReadAlarmStatusTwo","Desc":""},{"Text":"通道二:产量","Value":"ReadCurrentNumTwo","Desc":""},{"Text":"通道二:报警类型Id","Value":"ReadAlarmTypeTwo","Desc":""},{"Text":"通道二:主轴倍率","Value":"ReadOvSpinTwo","Desc":""},{"Text":"通道二:进给速度","Value":"ReadActFeedTwo","Desc":""},{"Text":"通道二:主轴温度","Value":"ReadSpinTempTwo","Desc":""},{"Text":"通道二:伺服温度","Value":"ReadSvTempTwo","Desc":""},{"Text":"通道二:刀具号","Value":"ReadToolNumTwo","Desc":""},{"Text":"通道二:设定进给速度","Value":"ReadSetFSpeedTwo","Desc":""},{"Text":"通道二:设定主轴速度","Value":"ReadSetsSpeedTwo","Desc":""},{"Text":"通道二:进给倍率","Value":"ReadOvFeedTwo","Desc":""},{"Text":"通道二:伺服负载","Value":"ReadAxisLoadTwo","Desc":""},{"Text":"通道二:主轴转速","Value":"ReadsSpeedTwo","Desc":""},{"Text":"通道二:绝对坐标","Value":"ReadAbsPosTwo","Desc":""},{"Text":"通道二:机械坐标","Value":"ReadMachPosTwo","Desc":""},{"Text":"通道二:相对坐标","Value":"ReadRelPosTwo","Desc":""},{"Text":"通道二:报警信息","Value":"ReadAlarmTwo","Desc":""},{"Text":"通道二:报警Id","Value":"ReadAlarmIdTwo","Desc":""},{"Text":"通道二:上电时间","Value":"ReadAliveTimeTwo","Desc":""},{"Text":"通道二:循环时间","Value":"ReadCycleTimeTwo","Desc":""},{"Text":"通道二:切削时间","Value":"ReadCutTimeTwo","Desc":""},{"Text":"通道二:运行时间","Value":"ReadCycSecTwo","Desc":""},{"Text":"通道二:主轴负载","Value":"ReadSpindleLoadTwo","Desc":""}]
   � 	
��                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       �m	�) Fx�> ��Q�ESyntecV2[{"Text":"模式","Value":"ReadWorkMode","Desc":""},{"Text":"状态","Value":"ReadRunStatus","Desc":""},{"Text":"产量","Value":"ReadCurrentNum","Desc":""},{"Text":"主轴转速","Value":"ReadsSpeed","Desc":""},{"Text":"上电时间","Value":"ReadAliveTime","Desc":""},{"Text":"循环时间","Value":"ReadCycleTime","Desc":""},{"Text":"切削时间","Value":"ReadCutTime","Desc":""},{"Text":"运行时间","Value":"ReadCycSec","Desc":""},{"Text":"报警状态","Value":"ReadAlarmStatus","Desc":""},{"Text":"程序名","Value":"ReadMainName","Desc":""},{"Text":"报警信息","Value":"ReadAlarm","Desc":""},{"Text":"版本信息","Value":"ReadVersion","Desc":""},{"Text":"进给率","Value":"ReadActFspeed","Desc":""}]�2	�3 Fx�= ��Q�ESyntecV4[{"Text":"模式","Value":"ReadWorkMode","Desc":""},{"Text":"状态","Value":"ReadRunStatus","Desc":""},{"Text":"产量","Value":"ReadCurrentNum","Desc":""},{"Text":"主轴转速","Value":"ReadsSpeed","Desc":""},{"Text":"上电时间","Value":"ReadAliveTime","Desc":""},{"Text":"循环时间","Value":"ReadCycleTime","Desc":""},{"Text":"切削时间","Value":"ReadCutTime","Desc":""},{"Text":"运行时间","Value":"ReadCycSec","Desc":""},{"Text":"报警状态","Value":"ReadAlarmStatus","Desc":""},{"Text":"程序名","Value":"ReadMainName","Desc":""},{"Text":"主轴设定速度","Value":"ReadSetSpeed","Desc":""},{"Text":"进给率设定值","Value":"ReadSetFspeed","Desc":""},{"Text":"主轴负载","Value":"ReadSpindleLoad","Desc":""},{"Text":"总工件数量","Value":"ReadTotalProduceCount","Desc":""},{"Text":"进给率","Value":"ReadActFspeed","Desc":""},{"Text":"机械坐标","Value":"ReadMachinePos","Desc":""},{"Text":"相对坐标","Value":"ReadRelativePos","Desc":""},{"Text":"绝对坐标","Value":"ReadAbsolutePos","Desc":""}]�s	�5 i9��` ��Q�EFanucTtc[{"Text":"主轴转速","Value":"ReadsSpeed","Desc":""},{"Text":"程序名","Value":"ReadMainName","Desc":""},{"Text":"程序号","Value":"ReadCurPgm","Desc":""},{"Text":"模式","Value":"ReadWorkMode","Desc":"0:手动输入；1:自动循环；3:程序编辑；4:×100;5:连续进给；6:TeachInJog；7:TeachInHandle；8:InCfeed；9:ReFerence；10:ReMoTe"},{"Text":"状态","Value":"ReadRunStatus","Desc":"0:Reset；1:Stop；2:Hold；3:Start；4:Mstr；"},{"Text":"报警状态","Value":"ReadAlarmStatus","Desc":""},{"Text":"绝对坐标","Value":"ReadAbsPos","Desc":""},{"Text":"机械坐标","Value":"ReadMachPos","Desc":""},{"Text":"相对坐标","Value":"ReadRelPos","Desc":""},{"Text":"报警信息","Value":"ReadAlarm","Desc":""},{"Text":"报警Id","Value":"ReadAlarmId","Desc":""},{"Text":"报警类型Id","Value":"ReadAlarmType","Desc":""},{"Text":"产量","Value":"ReadCurrentNum","Desc":""},{"Text":"主轴温度","Value":"ReadSpinTemp","Desc":""},{"Text":"伺服温度","Value":"ReadSvTemp","Desc":""},{"Text":"刀具号","Value":"ReadToolNum","Desc":""},{"Text":"主轴倍率","Value":"ReadOvSpin","Desc":""},{"Text":"进给速度","Value":"ReadActFeed","Desc":""},{"Text":"设定进给速度","Value":"ReadSetFSpeed","Desc":""},{"Text":"设定主轴速度","Value":"ReadSetsSpeed","Desc":""},{"Text":"进给倍率","Value":"ReadOvFeed","Desc":""},{"Text":"伺服负载","Value":"ReadAxisLoad","Desc":""},{"Text":"上电时间","Value":"ReadAliveTime","Desc":""},{"Text":"循环时间","Value":"ReadCycleTime","Desc":""},{"Text":"切削时间","Value":"ReadCutTime","Desc":""},{"Text":"运行时间","Value":"ReadCycSec","Desc":""},{"Text":"主轴负载","Value":"ReadSpindleLoad","Desc":""},{"Text":"读R变量","Value":"R","Desc":""}]
   � 
	F6�                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          �)
	�# Fx�A ��Q�EGsk25im[{"Text":"模式","Value":"ReadWorkMode","Desc":"运行模式  1 自动"},{"Text":"状态","Value":"ReadRunStatus","Desc":" 0:停止  3:运行 5复位"},{"Text":"产量","Value":"ReadCurrentNum","Desc":""},{"Text":"程序行号","Value":"ReadCurPgm","Desc":""},{"Text":"程序名","Value":"ReadMainName","Desc":""},{"Text":"循环时间","Value":"ReadCycleTime","Desc":""},{"Text":"运行时间","Value":"ReadCycSec","Desc":""},{"Text":"主轴倍率","Value":"ReadOvSpin","Desc":""},{"Text":"进给倍率","Value":"ReadOvFeed","Desc":""},{"Text":"主轴转速","Value":"ReadsSpeed","Desc":""},{"Text":"快速倍率","Value":"ReadRapidFeed","Desc":""}]�
		�g i9��e ��Q�ESyntec6MA[{"Text":"模式","Value":"ReadWorkMode","Desc":""},{"Text":"状态","Value":"ReadRunStatus","Desc":""},{"Text":"产量","Value":"ReadCurrentNum","Desc":""},{"Text":"主轴转速","Value":"ReadsSpeed","Desc":""},{"Text":"上电时间","Value":"ReadAliveTime","Desc":""},{"Text":"循环时间","Value":"ReadCycleTime","Desc":""},{"Text":"切削时间","Value":"ReadCutTime","Desc":""},{"Text":"报警状态","Value":"ReadAlarmStatus","Desc":""},{"Text":"程序名","Value":"ReadMainName","Desc":""},{"Text":"当前程序名","Value":"ReadCurrentProgram","Desc":""},{"Text":"主轴设定速度","Value":"ReadSetSpeed","Desc":""},{"Text":"进给设定速度","Value":"ReadSetFspeed","Desc":""},{"Text":"进给速度","Value":"ReadActFspeed","Desc":""}]�G	�[ i9��J ��Q�ESyntec6AE[{"Text":"模式","Value":"ReadWorkMode","Desc":""},{"Text":"状态","Value":"ReadRunStatus","Desc":""},{"Text":"产量","Value":"ReadCurrentNum","Desc":""},{"Text":"主轴转速","Value":"ReadsSpeed","Desc":""},{"Text":"上电时间","Value":"ReadAliveTime","Desc":""},{"Text":"循环时间","Value":"ReadCycleTime","Desc":""},{"Text":"切削时间","Value":"ReadCutTime","Desc":""},{"Text":"运行时间","Value":"ReadCycSec","Desc":""},{"Text":"报警状态","Value":"ReadAlarmStatus","Desc":""},{"Text":"程序名","Value":"ReadMainName","Desc":""},{"Text":"报警信息","Value":"ReadAlarm","Desc":""},{"Text":"总工件数量","Value":"ReadTotalProduceCount","Desc":""},{"Text":"设定工件产量","Value":"ReadRequireProduceCount","Desc":""},{"Text":"主轴设定速度","Value":"ReadSetSpeed","Desc":""},{"Text":"进给设定速度","Value":"ReadSetFspeed","Desc":""},{"Text":"进给速度","Value":"ReadActFspeed","Desc":""}]�m	�) Fx�? ��Q�ESyntecV3[{"Text":"模式","Value":"ReadWorkMode","Desc":""},{"Text":"状态","Value":"ReadRunStatus","Desc":""},{"Text":"产量","Value":"ReadCurrentNum","Desc":""},{"Text":"主轴转速","Value":"ReadsSpeed","Desc":""},{"Text":"上电时间","Value":"ReadAliveTime","Desc":""},{"Text":"循环时间","Value":"ReadCycleTime","Desc":""},{"Text":"切削时间","Value":"ReadCutTime","Desc":""},{"Text":"运行时间","Value":"ReadCycSec","Desc":""},{"Text":"报警状态","Value":"ReadAlarmStatus","Desc":""},{"Text":"程序名","Value":"ReadMainName","Desc":""},{"Text":"报警信息","Value":"ReadAlarm","Desc":""},{"Text":"版本信息","Value":"ReadVersion","Desc":""},{"Text":"进给率","Value":"ReadActFspeed","Desc":""}]
    x S�0� x                                                                                                      �Y�} i9��8 ��Q�EModbusRtu[{"Text":"01:ReadCoil","Value":"ReadCoil","Desc":"读取线圈"},{"Text":"02:ReadInput","Value":"ReadInput","Desc":"读输入"},{"Text":"03:ReadHoldingRegisters","Value":"ReadHoldingRegisters","Desc":"读保持寄存器"},{"Text":"04:ReadInputRegisters","Value":"ReadInputRegisters","Desc":"读输入寄存器"}]�Y�} Fx�8 ��Q�EModbusTcp[{"Text":"01:ReadCoil","Value":"ReadCoil","Desc":"读取线圈"},{"Text":"02:ReadInput","Value":"ReadInput","Desc":"读输入"},{"Text":"03:ReadHoldingRegisters","Value":"ReadHoldingRegisters","Desc":"读保持寄存器"},{"Text":"04:ReadInputRegisters","Value":"ReadInputRegisters","Desc":"读输入寄存器"}]�w
	�G Fx�D ��Q�EKnd[{"Text":"模式","Value":"ReadWorkMode","Desc":"0:录入方式；1:自动方式；3:编辑方式;4:单步方式； 5:手动方式； 6:手动编辑（示教）方式；7:手轮编辑（示教）方式; 8:手轮方式； 9:（机械）回零方式; 10:程序回零方式"},{"Text":"状态","Value":"ReadRunStatus","Desc":"0:CNC 处于停止状态;1:CNC 处于暂停（进给保持）状态;2:CNC 处于运行状态"},{"Text":"报警信息","Value":"ReadAlarm","Desc":""},{"Text":"报警状态","Value":"ReadAlarmStatus","Desc":"1:报警；0:未报警"},{"Text":"未就绪原因","Value":"ReadNoReadyReason","Desc":" 0x1:急停信号有效;0x2:伺服准备未绪;0x4:IO 准备未绪（远程 IO 设备等）"},{"Text":"系统软件版本号","Value":"ReadSoftVersion","Desc":""},{"Text":"FPGA版本号","Value":"ReadFpgaVersion","Desc":""},{"Text":"梯图版本号","Value":"ReadLadderVersion","Desc":""},{"Text":"循环时间","Value":"ReadCycleTime","Desc":""},{"Text":"加工时间","Value":"ReadCutTime","Desc":""},{"Text":"产量","Value":"ReadCurrentNum","Desc":""},{"Text":"主轴1倍率","Value":"ReadOverRidesSp1","Desc":""},{"Text":"手动倍率","Value":"ReadOverRidesJog","Desc":""},{"Text":"快速倍率","Value":"ReadOverRidesRapid","Desc":""},{"Text":"工件坐标系","Value":"ReadWorkCoorsCur","Desc":""},{"Text":"程序号","Value":"ReadCurPgm","Desc":""}]�&	� Fx�C ��Q�EGskUdp[{"Text":"程序名","Value":"ReadMainName","Desc":""},{"Text":"模式","Value":"ReadWorkMode","Desc":""},{"Text":"状态","Value":"ReadRunStatus","Desc":"0:RESET复位,1:STOP停止 2:RUN 3:HOLD 进给保持"},{"Text":"产量","Value":"ReadCurrentNum","Desc":""},{"Text":"切削时间","Value":"ReadCutTime","Desc":""},{"Text":"运行时间","Value":"ReadCycSec","Desc":""},{"Text":"报警状态","Value":"ReadAlarmStatus","Desc":""},{"Text":"主轴倍率","Value":"ReadOvSpin","Desc":""},{"Text":"进给速度","Value":"ReadActFeed","Desc":""},{"Text":"设定进给速度","Value":"ReadSetFSpeed","Desc":""},{"Text":"设定主轴速度","Value":"ReadSetsSpeed","Desc":""},{"Text":"进给倍率","Value":"ReadOvFeed","Desc":""},{"Text":"主轴转速","Value":"ReadsSpeed","Desc":""},{"Text":"上电时间","Value":"ReadAliveTime","Desc":""},{"Text":"报警信息","Value":"ReadSystemAlarmInfo","Desc":""}]�*	�' Fx�B ��Q�EGskTcp[{"Text":"报警信息","Value":"ReadAlarm","Desc":""},{"Text":"程序名","Value":"ReadMainName","Desc":""},{"Text":"模式","Value":"ReadWorkMode","Desc":"当前工作模式 模式 0:EDIT,1:MEM 2:MDI 3:DNC 4:JOG,5:HANDLE,6:REF"},{"Text":"状态","Value":"ReadRunStatus","Desc":"0:RESET复位,1:STOP停止 2:RUN 3:HOLD 进给保持"},{"Text":"产量","Value":"ReadCurrentNum","Desc":""},{"Text":"切削时间","Value":"ReadCutTime","Desc":""},{"Text":"运行时间","Value":"ReadRunTime","Desc":""},{"Text":"报警状态","Value":"ReadAlarmStatus","Desc":""},{"Text":"主轴倍率","Value":"ReadOvSpin","Desc":""},{"Text":"进给速度","Value":"ReadActFeed","Desc":""},{"Text":"设定进给速度","Value":"ReadSetFSpeed","Desc":""},{"Text":"设定主轴速度","Value":"ReadSetsSpeed","Desc":""},{"Text":"进给倍率","Value":"ReadOvFeed","Desc":""},{"Text":"主轴转速","Value":"ReadsSpeed","Desc":""}]
     �
?�
{$            �-�A i9��: ��Q�EDeltaSerialAscii[{"Text":"S0-S127","Value":"ReadS0S127","Desc":""},{"Text":"X0-X177:输入继电器","Value":"ReadX0X177","Desc":""},{"Text":"Y0-Y177:输出继电器","Value":"ReadY0Y177","Desc":"地址8进制"},{"Text":"T0-T127:定时器","Value":"ReadT0T127","Desc":"如果是读位,就是通断继电器,如果是读字,就是当前值"},{"Text":"C0-C127 C232-C255:计数器","Value":"ReadC0C127C232C255","Desc":"如果是读位,就是通断继电器,如果是读字,就是当前值"},{"Text":"M0-M1279:内部继电器","Value":"ReadM0M1279","Desc":""},{"Text":"D0-D1311:数据寄存器","Value":"ReadD0D1311","Desc":""},{"Text":"S0-S1023","Value":"ReadS0S1023","Desc":""},{"Text":"T0-T255:定时器","Value":"ReadT0T255","Desc":""},{"Text":"C0-C199 C200-C255:计数器","Value":"ReadC0C199C200C255","Desc":"如果是读位,就是通断继电器,如果是读字,就是当前值"},{"Text":"M0-M4095:内部继电器","Value":"ReadM0M4095","Desc":""},{"Text":"D0-D4999:数据寄存器","Value":"ReadD0D4999","Desc":""},{"Text":"X0-X377:输入继电器","Value":"ReadX0X377","Desc":"只读操作,地址8进制"},{"Text":"Y0-Y377:输出继电器","Value":"ReadY0Y377","Desc":"地址8进制"},{"Text":"D0-D9999:数据寄存器","Value":"ReadD0D9999","Desc":""}]�T�u i9��9 ��Q�EDeltaTcp[{"Text":"X0-X177:输入寄存器","Value":"ReadCoil","Desc":"只读操作,地址8进制"},{"Text":"S0-S127","Value":"ReadS0S127","Desc":""},{"Text":"X0-X177:输入继电器","Value":"ReadX0X177","Desc":""},{"Text":"Y0-Y177:输出继电器","Value":"ReadY0Y177","Desc":"地址8进制"},{"Text":"T0-T127:定时器","Value":"ReadT0T127","Desc":"如果是读位,就是通断继电器,如果是读字,就是当前值"},{"Text":"C0-C127 C232-C255:计数器","Value":"ReadC0C127C232C255","Desc":"如果是读位,就是通断继电器,如果是读字,就是当前值"},{"Text":"M0-M1279:内部继电器","Value":"ReadM0M1279","Desc":""},{"Text":"D0-D1311:数据寄存器","Value":"ReadD0D1311","Desc":""},{"Text":"S0-S1023","Value":"ReadS0S1023","Desc":""},{"Text":"T0-T255:定时器","Value":"ReadT0T255","Desc":""},{"Text":"C0-C199 C200-C255:计数器","Value":"ReadC0C199C200C255","Desc":"如果是读位,就是通断继电器,如果是读字,就是当前值"},{"Text":"M0-M4095:内部继电器","Value":"ReadM0M4095","Desc":""},{"Text":"D0-D4999:数据寄存器","Value":"ReadD0D4999","Desc":""},{"Text":"X0-X377:输入继电器","Value":"ReadX0X377","Desc":"只读操作,地址8进制"},{"Text":"Y0-Y377:输出继电器","Value":"ReadY0Y377","Desc":"地址8进制"},{"Text":"D0-D9999:数据寄存器","Value":"ReadD0D9999","Desc":""}]�\%�} i9��M ��Q�EModbusUdpNet[{"Text":"01:ReadCoil","Value":"ReadCoil","Desc":"读取线圈"},{"Text":"02:ReadInput","Value":"ReadInput","Desc":"读输入"},{"Text":"03:ReadHoldingRegisters","Value":"ReadHoldingRegisters","Desc":"读保持寄存器"},{"Text":"04:ReadInputRegisters","Value":"ReadInputRegisters","Desc":"读输入寄存器"}]�b1�} i9��L ��Q�EModbusAsciiOverTcp[{"Text":"01:ReadCoil","Value":"ReadCoil","Desc":"读取线圈"},{"Text":"02:ReadInput","Value":"ReadInput","Desc":"读输入"},{"Text":"03:ReadHoldingRegisters","Value":"ReadHoldingRegisters","Desc":"读保持寄存器"},{"Text":"04:ReadInputRegisters","Value":"ReadInputRegisters","Desc":"读输入寄存器"}]�`-�} i9��K ��Q�EModbusRtuOverTcp[{"Text":"01:ReadCoil","Value":"ReadCoil","Desc":"读取线圈"},{"Text":"02:ReadInput","Value":"ReadInput","Desc":"读输入"},{"Text":"03:ReadHoldingRegisters","Value":"ReadHoldingRegisters","Desc":"读保持寄存器"},{"Text":"04:ReadInputRegisters","Value":"ReadInputRegisters","Desc":"读输入寄存器"}]�[#�} i9��; ��Q�EModbusAscii[{"Text":"01:ReadCoil","Value":"ReadCoil","Desc":"读取线圈"},{"Text":"02:ReadInput","Value":"ReadInput","Desc":"读输入"},{"Text":"03:ReadHoldingRegisters","Value":"ReadHoldingRegisters","Desc":"读保持寄存器"},{"Text":"04:ReadInputRegisters","Value":"ReadInputRegisters","Desc":"读输入寄存器"}]
   \ �\                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                �I�e i9��< ��Q�EMcA1E[{"Text":"M:内部继电器","Value":"M","Desc":""},{"Text":"X:输入继电器","Value":"X","Desc":"默认16进制,8进制 X011"},{"Text":"Y:输出继电器","Value":"Y","Desc":"默认16进制,8进制 X011"},{"Text":"SM:SM特殊继电器","Value":"SM","Desc":""},{"Text":"S:步进继电器","Value":"S","Desc":""},{"Text":"L:锁存继电器","Value":"L","Desc":""},{"Text":"F:报警器","Value":"F","Desc":""},{"Text":"V:边沿继电器","Value":"V","Desc":""},{"Text":"B:链接继电器","Value":"B","Desc":"16进制地址"},{"Text":"SB:特殊链接继电器","Value":"SB","Desc":"16进制地址"},{"Text":"DX:直接输入","Value":"DX","Desc":""},{"Text":"DY:直接输出","Value":"DY","Desc":""},{"Text":"TS:定时器触点","Value":"TS","Desc":""},{"Text":"TC:定时器线圈","Value":"TC","Desc":""},{"Text":"SS:累计定时器触点","Value":"SS","Desc":""},{"Text":"SC:累计定时器线圈","Value":"SC","Desc":""},{"Text":"CS:计数器触点","Value":"CS","Desc":""},{"Text":"CC:计数器线圈","Value":"CC","Desc":""},{"Text":"D:数据寄存器","Value":"D","Desc":""},{"Text":"SD:特殊数据寄存器","Value":"SD","Desc":""},{"Text":"W:链接寄存器","Value":"W","Desc":""},{"Text":"SW:特殊链接寄存器","Value":"SW","Desc":""},{"Text":"R:文件寄存器","Value":"R","Desc":""},{"Text":"Z:变址寄存器","Value":"Z","Desc":""},{"Text":"ZR:ZR文件寄存器","Value":"ZR","Desc":""},{"Text":"TN:定时器当前值","Value":"TN","Desc":""},{"Text":"SN:累计定时器当前值","Value":"SN","Desc":""},{"Text":"CN:计数器当前值","Value":"CN","Desc":""}]�U	�} Fx�@ ��Q�EMitsub[{"Text":"模式","Value":"ReadWorkMode","Desc":"1:加工模式;2:自动模式; 3:MDI; 4:手动模式 ;5:寸动模式;6:手轮模式; 7:原点模式;"},{"Text":"状态","Value":"ReadRunStatus","Desc":"1:ready复位准备 2.auto 运行 3.hold 暂停"},{"Text":"工件数量","Value":"ReadCurrentNum","Desc":""},{"Text":"主轴转速","Value":"ReadActSpeed","Desc":""},{"Text":"程序名","Value":"ReadMainName","Desc":""},{"Text":"上电时间","Value":"ReadAliveTime","Desc":""},{"Text":"循环时间","Value":"ReadCycleTime","Desc":""},{"Text":"切削时间","Value":"ReadCutTime","Desc":""},{"Text":"运行时间","Value":"ReadCycSec","Desc":""},{"Text":"报警状态","Value":"ReadSysAlarm","Desc":""},{"Text":"进给率","Value":"ReadActFspeed","Desc":""},{"Text":"报警信息","Value":"ReadSystemAlarmInfo","Desc":""}]
   b 	�b                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      �K�e i9��> ��Q�EMcQna3E[{"Text":"M:内部继电器","Value":"M","Desc":""},{"Text":"X:输入继电器","Value":"X","Desc":"默认16进制,8进制 X011"},{"Text":"Y:输出继电器","Value":"Y","Desc":"默认16进制,8进制 X011"},{"Text":"SM:SM特殊继电器","Value":"SM","Desc":""},{"Text":"S:步进继电器","Value":"S","Desc":""},{"Text":"L:锁存继电器","Value":"L","Desc":""},{"Text":"F:报警器","Value":"F","Desc":""},{"Text":"V:边沿继电器","Value":"V","Desc":""},{"Text":"B:链接继电器","Value":"B","Desc":"16进制地址"},{"Text":"SB:特殊链接继电器","Value":"SB","Desc":"16进制地址"},{"Text":"DX:直接输入","Value":"DX","Desc":""},{"Text":"DY:直接输出","Value":"DY","Desc":""},{"Text":"TS:定时器触点","Value":"TS","Desc":""},{"Text":"TC:定时器线圈","Value":"TC","Desc":""},{"Text":"SS:累计定时器触点","Value":"SS","Desc":""},{"Text":"SC:累计定时器线圈","Value":"SC","Desc":""},{"Text":"CS:计数器触点","Value":"CS","Desc":""},{"Text":"CC:计数器线圈","Value":"CC","Desc":""},{"Text":"D:数据寄存器","Value":"D","Desc":""},{"Text":"SD:特殊数据寄存器","Value":"SD","Desc":""},{"Text":"W:链接寄存器","Value":"W","Desc":""},{"Text":"SW:特殊链接寄存器","Value":"SW","Desc":""},{"Text":"R:文件寄存器","Value":"R","Desc":""},{"Text":"Z:变址寄存器","Value":"Z","Desc":""},{"Text":"ZR:ZR文件寄存器","Value":"ZR","Desc":""},{"Text":"TN:定时器当前值","Value":"TN","Desc":""},{"Text":"SN:累计定时器当前值","Value":"SN","Desc":""},{"Text":"CN:计数器当前值","Value":"CN","Desc":""}]�M�e i9��= ��Q�EMcFxLinks[{"Text":"M:内部继电器","Value":"M","Desc":""},{"Text":"X:输入继电器","Value":"X","Desc":"默认16进制,8进制 X011"},{"Text":"Y:输出继电器","Value":"Y","Desc":"默认16进制,8进制 X011"},{"Text":"SM:SM特殊继电器","Value":"SM","Desc":""},{"Text":"S:步进继电器","Value":"S","Desc":""},{"Text":"L:锁存继电器","Value":"L","Desc":""},{"Text":"F:报警器","Value":"F","Desc":""},{"Text":"V:边沿继电器","Value":"V","Desc":""},{"Text":"B:链接继电器","Value":"B","Desc":"16进制地址"},{"Text":"SB:特殊链接继电器","Value":"SB","Desc":"16进制地址"},{"Text":"DX:直接输入","Value":"DX","Desc":""},{"Text":"DY:直接输出","Value":"DY","Desc":""},{"Text":"TS:定时器触点","Value":"TS","Desc":""},{"Text":"TC:定时器线圈","Value":"TC","Desc":""},{"Text":"SS:累计定时器触点","Value":"SS","Desc":""},{"Text":"SC:累计定时器线圈","Value":"SC","Desc":""},{"Text":"CS:计数器触点","Value":"CS","Desc":""},{"Text":"CC:计数器线圈","Value":"CC","Desc":""},{"Text":"D:数据寄存器","Value":"D","Desc":""},{"Text":"SD:特殊数据寄存器","Value":"SD","Desc":""},{"Text":"W:链接寄存器","Value":"W","Desc":""},{"Text":"SW:特殊链接寄存器","Value":"SW","Desc":""},{"Text":"R:文件寄存器","Value":"R","Desc":""},{"Text":"Z:变址寄存器","Value":"Z","Desc":""},{"Text":"ZR:ZR文件寄存器","Value":"ZR","Desc":""},{"Text":"TN:定时器当前值","Value":"TN","Desc":""},{"Text":"SN:累计定时器当前值","Value":"SN","Desc":""},{"Text":"CN:计数器当前值","Value":"CN","Desc":""}]
   � 	�,��                                                                                                                                                                                                                                                                                                                                                                                                                                           �e� i9��A ��Q�EFinsNet[{"Text":"D:DM Area","Value":"D","Desc":"读取位使用D10.11"},{"Text":"C:CI0 Area","Value":"C","Desc":"读取位使用C10.11"},{"Text":"W:Work Area","Value":"W","Desc":"读取位使用W10.11"},{"Text":"H:Holding Bit Area","Value":"H","Desc":"读取位使用H10.11"},{"Text":"A:Auxiliary Bit Area","Value":"A","Desc":"读取位使用A10.11"},{"Text":"E:EM Area","Value":"E","Desc":"E0.0-EF.0"},{"Text":"TIM:Timer Area","Value":"Tim","Desc":"TIM100,TIM200"},{"Text":"CNT:Counter Area","Value":"Cnt","Desc":"CNT100,CNT200"},{"Text":"IR:Index Register","Value":"Ir","Desc":"IR0,IR15"},{"Text":"DR:Data Register","Value":"Dr","Desc":"DR0,DR15"},{"Text":"CF:Condition Flags","Value":"Cf","Desc":"CF1.2,CF1001.0"}]�-�I i9��j ��Q�EAllenBradleyPccc[{"Text":"A","Value":"A","Desc":"A9:0/1 或 A9:0.1"},{"Text":"B","Value":"B","Desc":"B9:0/1 或 B9:0.1"},{"Text":"N","Value":"N","Desc":"N9:0/1 或 N9:0.1"},{"Text":"F","Value":"F","Desc":"N9:0/1 或 N9:0.1"},{"Text":"S","Value":"S","Desc":"S:0/1 或 S:0.1"},{"Text":"ST","Value":"St","Desc":""},{"Text":"C","Value":"C","Desc":"C9:0/1 或 C9:0.1"},{"Text":"I","Value":"I","Desc":"I:0/1 或 I9:0.1"},{"Text":"O","Value":"O","Desc":"O:0/1 或 O9:0.1"},{"Text":"R","Value":"R","Desc":"R9:0/1 或 R9:0.1"},{"Text":"T","Value":"T","Desc":"T9:0/1 或 T9:0.1"},{"Text":"L","Value":"L","Desc":"L9:0/1 或 L9:0.1"}]�+�I i9��@ ��Q�EAllenBradleyCip[{"Text":"A","Value":"A","Desc":"A9:0/1 或 A9:0.1"},{"Text":"B","Value":"B","Desc":"B9:0/1 或 B9:0.1"},{"Text":"N","Value":"N","Desc":"N9:0/1 或 N9:0.1"},{"Text":"F","Value":"F","Desc":"N9:0/1 或 N9:0.1"},{"Text":"S","Value":"S","Desc":"S:0/1 或 S:0.1"},{"Text":"ST","Value":"St","Desc":""},{"Text":"C","Value":"C","Desc":"C9:0/1 或 C9:0.1"},{"Text":"I","Value":"I","Desc":"I:0/1 或 I9:0.1"},{"Text":"O","Value":"O","Desc":"O:0/1 或 O9:0.1"},{"Text":"R","Value":"R","Desc":"R9:0/1 或 R9:0.1"},{"Text":"T","Value":"T","Desc":"T9:0/1 或 T9:0.1"},{"Text":"L","Value":"L","Desc":"L9:0/1 或 L9:0.1"}]�I�e i9��? ��Q�EMcUdp[{"Text":"M:内部继电器","Value":"M","Desc":""},{"Text":"X:输入继电器","Value":"X","Desc":"默认16进制,8进制 X011"},{"Text":"Y:输出继电器","Value":"Y","Desc":"默认16进制,8进制 X011"},{"Text":"SM:SM特殊继电器","Value":"SM","Desc":""},{"Text":"S:步进继电器","Value":"S","Desc":""},{"Text":"L:锁存继电器","Value":"L","Desc":""},{"Text":"F:报警器","Value":"F","Desc":""},{"Text":"V:边沿继电器","Value":"V","Desc":""},{"Text":"B:链接继电器","Value":"B","Desc":"16进制地址"},{"Text":"SB:特殊链接继电器","Value":"SB","Desc":"16进制地址"},{"Text":"DX:直接输入","Value":"DX","Desc":""},{"Text":"DY:直接输出","Value":"DY","Desc":""},{"Text":"TS:定时器触点","Value":"TS","Desc":""},{"Text":"TC:定时器线圈","Value":"TC","Desc":""},{"Text":"SS:累计定时器触点","Value":"SS","Desc":""},{"Text":"SC:累计定时器线圈","Value":"SC","Desc":""},{"Text":"CS:计数器触点","Value":"CS","Desc":""},{"Text":"CC:计数器线圈","Value":"CC","Desc":""},{"Text":"D:数据寄存器","Value":"D","Desc":""},{"Text":"SD:特殊数据寄存器","Value":"SD","Desc":""},{"Text":"W:链接寄存器","Value":"W","Desc":""},{"Text":"SW:特殊链接寄存器","Value":"SW","Desc":""},{"Text":"R:文件寄存器","Value":"R","Desc":""},{"Text":"Z:变址寄存器","Value":"Z","Desc":""},{"Text":"ZR:ZR文件寄存器","Value":"ZR","Desc":""},{"Text":"TN:定时器当前值","Value":"TN","Desc":""},{"Text":"SN:累计定时器当前值","Value":"SN","Desc":""},{"Text":"CN:计数器当前值","Value":"CN","Desc":""}]
   P 

1HXP                                                                                                                                                                                                                                                                                                                              �"+�I i9��D ��Q�EMewtocolOverTcp[{"Text":"X:外部输入继电器","Value":"ReadX0","Desc":"X33等同于X3.3"},{"Text":"Y:外部输出继电器","Value":"ReadY0","Desc":"Y33等同于Y3.3"},{"Text":"R2.1:内部继电器","Value":"ReadR21","Desc":"R21等同于R2.1"},{"Text":"T:定时器","Value":"ReadT0","Desc":""},{"Text":"L2.1:链接继电器","Value":"ReadL2","Desc":""},{"Text":"D:数据寄存器","Value":"ReadD0","Desc":""},{"Text":"LD:链接寄存器","Value":"ReadLd","Desc":""},{"Text":"F:文件寄存器","Value":"ReadF","Desc":""},{"Text":"S:目标值 SV","Value":"ReadS","Desc":""},{"Text":"K:经过值 EV","Value":"ReadK","Desc":""},{"Text":"IX:索引寄存器 IX","Value":"ReadIx","Desc":""},{"Text":"IY:索引寄存器 IY","Value":"ReadIy","Desc":""}]�m!+� i9��[ ��Q�EHostLinkOverTcp[{"Text":"D:DM Area","Value":"D","Desc":"读取位使用D10.11"},{"Text":"C:CI0 Area","Value":"C","Desc":"读取位使用C10.11"},{"Text":"W:Work Area","Value":"W","Desc":"读取位使用W10.11"},{"Text":"H:Holding Bit Area","Value":"H","Desc":"读取位使用H10.11"},{"Text":"A:Auxiliary Bit Area","Value":"A","Desc":"读取位使用A10.11"},{"Text":"E:EM Area","Value":"E","Desc":"E0.0-EF.0"},{"Text":"TIM:Timer Area","Value":"Tim","Desc":"TIM100,TIM200"},{"Text":"CNT:Counter Area","Value":"Cnt","Desc":"CNT100,CNT200"},{"Text":"IR:Index Register","Value":"Ir","Desc":"IR0,IR15"},{"Text":"DR:Data Register","Value":"Dr","Desc":"DR0,DR15"},{"Text":"CF:Condition Flags","Value":"Cf","Desc":"CF1.2,CF1001.0"}]�f � i9��Z ��Q�EHostLink[{"Text":"D:DM Area","Value":"D","Desc":"读取位使用D10.11"},{"Text":"C:CI0 Area","Value":"C","Desc":"读取位使用C10.11"},{"Text":"W:Work Area","Value":"W","Desc":"读取位使用W10.11"},{"Text":"H:Holding Bit Area","Value":"H","Desc":"读取位使用H10.11"},{"Text":"A:Auxiliary Bit Area","Value":"A","Desc":"读取位使用A10.11"},{"Text":"E:EM Area","Value":"E","Desc":"E0.0-EF.0"},{"Text":"TIM:Timer Area","Value":"Tim","Desc":"TIM100,TIM200"},{"Text":"CNT:Counter Area","Value":"Cnt","Desc":"CNT100,CNT200"},{"Text":"IR:Index Register","Value":"Ir","Desc":"IR0,IR15"},{"Text":"DR:Data Register","Value":"Dr","Desc":"DR0,DR15"},{"Text":"CF:Condition Flags","Value":"Cf","Desc":"CF1.2,CF1001.0"}]�d� i9��C ��Q�ECipNet[{"Text":"D:DM Area","Value":"D","Desc":"读取位使用D10.11"},{"Text":"C:CI0 Area","Value":"C","Desc":"读取位使用C10.11"},{"Text":"W:Work Area","Value":"W","Desc":"读取位使用W10.11"},{"Text":"H:Holding Bit Area","Value":"H","Desc":"读取位使用H10.11"},{"Text":"A:Auxiliary Bit Area","Value":"A","Desc":"读取位使用A10.11"},{"Text":"E:EM Area","Value":"E","Desc":"E0.0-EF.0"},{"Text":"TIM:Timer Area","Value":"Tim","Desc":"TIM100,TIM200"},{"Text":"CNT:Counter Area","Value":"Cnt","Desc":"CNT100,CNT200"},{"Text":"IR:Index Register","Value":"Ir","Desc":"IR0,IR15"},{"Text":"DR:Data Register","Value":"Dr","Desc":"DR0,DR15"},{"Text":"CF:Condition Flags","Value":"Cf","Desc":"CF1.2,CF1001.0"}]�e� i9��B ��Q�EFinsUdp[{"Text":"D:DM Area","Value":"D","Desc":"读取位使用D10.11"},{"Text":"C:CI0 Area","Value":"C","Desc":"读取位使用C10.11"},{"Text":"W:Work Area","Value":"W","Desc":"读取位使用W10.11"},{"Text":"H:Holding Bit Area","Value":"H","Desc":"读取位使用H10.11"},{"Text":"A:Auxiliary Bit Area","Value":"A","Desc":"读取位使用A10.11"},{"Text":"E:EM Area","Value":"E","Desc":"E0.0-EF.0"},{"Text":"TIM:Timer Area","Value":"Tim","Desc":"TIM100,TIM200"},{"Text":"CNT:Counter Area","Value":"Cnt","Desc":"CNT100,CNT200"},{"Text":"IR:Index Register","Value":"Ir","Desc":"IR0,IR15"},{"Text":"DR:Data Register","Value":"Dr","Desc":"DR0,DR15"},{"Text":"CF:Condition Flags","Value":"Cf","Desc":"CF1.2,CF1001.0"}]
    ��#�q                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          Q(o i9��R ��Q�ETcpClient[{"Text":"读取","Value":"ReadY","Desc":"Read"}]Z'+u i9��H ��Q�EMtConnectClient[{"Text":"读取","Value":"Reader","Desc":"Reader"}]T&#q i9��G ��Q�EOpcUaClient[{"Text":"读取节点","Value":"Read","Desc":""}]�3%!�/ i9��F ��Q�ESiemensPPI[{"Text":"I:输入寄存器","Value":"I","Desc":"位地址示例:I1.6"},{"Text":"Q:输出寄存器","Value":"Q","Desc":"位地址示例:Q1.6"},{"Text":"M:内部寄存器","Value":"M","Desc":"位地址示例:M1.6"},{"Text":"DB:数据寄存器","Value":"DB","Desc":"位地址示例:DB1.0.1"},{"Text":"V:数据寄存器","Value":"V","Desc":"等同于DB1.0"},{"Text":"T:定时器寄存器","Value":"T","Desc":"smart200测试通过"},{"Text":"C:计数器寄存器","Value":"C","Desc":"smart200测试通过"},{"Text":"AI:智能输入寄存器","Value":"AI","Desc":"仅支持字单位"},{"Text":"AQ:智能输出寄存器","Value":"AQ","Desc":"仅支持字单位"}]�2$�/ i9��E ��Q�ESiemensS7[{"Text":"I:输入寄存器","Value":"I","Desc":"位地址示例:I1.6"},{"Text":"Q:输出寄存器","Value":"Q","Desc":"位地址示例:Q1.6"},{"Text":"M:内部寄存器","Value":"M","Desc":"位地址示例:M1.6"},{"Text":"DB:数据寄存器","Value":"DB","Desc":"位地址示例:DB1.0.1"},{"Text":"V:数据寄存器","Value":"V","Desc":"等同于DB1.0"},{"Text":"T:定时器寄存器","Value":"T","Desc":"smart200测试通过"},{"Text":"C:计数器寄存器","Value":"C","Desc":"smart200测试通过"},{"Text":"AI:智能输入寄存器","Value":"AI","Desc":"仅支持字单位"},{"Text":"AQ:智能输出寄存器","Value":"AQ","Desc":"仅支持字单位"}]�o#	�/ Fx�< ��Q�ESiemens[{"Text":"读PLC地址","Value":"ReadPlc","Desc":""},{"Text":"模式","Value":"ReadWorkMode","Desc":"0:手动模式; 1:子运行模式示教 ;2:mdi 3:自动运行模式 ;4:再定位,重新逼近轮廓; 5:返回参考点 ;6:以可变增量运行; 7:增量进给 8:未知"},{"Text":"状态","Value":"ReadRunStatus","Desc":"RESET = 0:复位 STOP = 1:程序块结束 HOLD = 2:进给保持; START = 3:程序运行 SPENDLE_CW_CCW = 4:主轴正反转 OTHER = 5 一般是报警"},{"Text":"R变量","Value":"R","Desc":""},{"Text":"报警号","Value":"ReadAlarmInfo","Desc":""},{"Text":"循环时间","Value":"ReadCycleTime","Desc":""},{"Text":"工件数量","Value":"ReadCurrentNum","Desc":""},{"Text":"剩余加工时间","Value":"ReadRemainTime","Desc":""},{"Text":"设定主轴速度","Value":"ReadSetSpeed","Desc":""},{"Text":"主轴转速","Value":"ReadActSpeed","Desc":""},{"Text":"主轴倍率","Value":"ReadSpindleRate","Desc":""},{"Text":"设定进给速度","Value":"ReadSetFSpeed","Desc":""},{"Text":"进给速度","Value":"ReadActFSpeed","Desc":""},{"Text":"进给轴倍率","Value":"ReadFeedRate","Desc":""},{"Text":"程序名","Value":"ReadMainName","Desc":""},{"Text":"刀具号","Value":"ReadToolNum","Desc":""},{"Text":"报警个数","Value":"ReadAlarmNum","Desc":""},{"Text":"剩余坐标","Value":"ReadRemainPos","Desc":""},{"Text":"机械坐标","Value":"ReadMachPos","Desc":""},{"Text":"相对坐标","Value":"ReadRelPos","Desc":""},{"Text":"伺服轴负载X","Value":"ReadFeedLoadX","Desc":""},{"Text":"伺服轴负载Y","Value":"ReadFeedLoadY","Desc":""},{"Text":"伺服轴负载Z","Value":"ReadFeedLoadZ","Desc":""},{"Text":"伺服轴负载B","Value":"ReadFeedLoadB","Desc":""},{"Text":"伺服轴负载C","Value":"ReadFeedLoadC","Desc":""},{"Text":"报警状态","Value":"ReadAlarm","Desc":""},{"Text":"PLC报警信息","Value":"ReadAlarmMessage","Desc":""}]
   � �                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                �C)5�; i9��N ��Q�EXinJE TcpNet(ModBus)[{"Text":"M:内部继电器","Value":"ReadM","Desc":"\tM0-M7999，M8000-M8511"},{"Text":"S:流程继电器","Value":"ReadS","Desc":"S0-S1023"},{"Text":"T:定时器","Value":"ReadT","Desc":"T0-T618"},{"Text":"C:计数器","Value":"ReadC","Desc":"C0-C634"},{"Text":"X:输入","Value":"ReadX","Desc":"X0-X1037 或者X0.0-X103.7"},{"Text":"Y:输出","Value":"ReadY","Desc":"Y0-Y1037 或者Y0.0-Y103.7"},{"Text":"D:数据寄存器","Value":"ReadXcD","Desc":"\tD0-D8511"},{"Text":"F:Flash寄存器","Value":"ReadXcF","Desc":"F0-F5000;F8000-F8511"},{"Text":"E:扩展内部寄存器","Value":"ReadXcE","Desc":"E0-E36863"},{"Text":"T:定时器","Value":"ReadXcT","Desc":"\tT0-T618"},{"Text":"C:计数器","Value":"ReadXcC","Desc":"C0-C634"},{"Text":"M:中间寄电器","Value":"ReadXdM","Desc":"M0-M7999"},{"Text":"X:输入","Value":"ReadXdX","Desc":"X0-X77,X10000-X11177,X20000-X20177,X30000-X30077 或者X0.0-X37.7"},{"Text":"Y:输出","Value":"ReadXdY","Desc":"Y0-Y77,Y10000-Y11177,Y20000-Y20177,Y30000-Y30077 或者Y0.0-Y37.7"},{"Text":"S:输出","Value":"ReadXdS","Desc":"S0-S1023"},{"Text":"SM:输出","Value":"ReadXdSm","Desc":"SM0-SM2047"},{"Text":"T:定时器","Value":"ReadXdT","Desc":"T0-T575"},{"Text":"C:计数器","Value":"ReadXdC","Desc":"C0-C575"},{"Text":"ET:计数器","Value":"ReadXdEt","Desc":"ET0-ET31"},{"Text":"SEM:计数器","Value":"ReadXdSem","Desc":"SEM0-SEM31"},{"Text":"HM:计数器","Value":"ReadXdHm","Desc":"HM0-HM959"},{"Text":"HS:计数器","Value":"ReadXdHs","Desc":"HS0-HS127"},{"Text":"HT:计数器","Value":"ReadXdHt","Desc":"HT0-HT95"},{"Text":"Hc:计数器","Value":"ReadXdHc","Desc":"HT0-HC95"},{"Text":"HSC:计数器","Value":"ReadXdHsc","Desc":"HST0-HSC31"},{"Text":"D:数据寄存器","Value":"ReadXdD","Desc":"D0-D7999"},{"Text":"ID:数据寄存器","Value":"ReadXdId","Desc":"ID0-ID99，ID10000-ID10999，ID20000-ID20199,ID30000-ID30099"},{"Text":"QD:数据寄存器","Value":"ReadXdQd","Desc":"QD0-QD99，QD10000-QD10999，QD20000-QD20199,QD30000-QD30099"},{"Text":"SD:数据寄存器","Value":"ReadXdSd","Desc":"SD0-SD2047"},{"Text":"TD:数据寄存器","Value":"ReadXdTd","Desc":"TD0-TD575"},{"Text":"CD:数据寄存器","Value":"ReadXdCd","Desc":"CD0-CD575"},{"Text":"ETD:数据寄存器","Value":"ReadXdEtd","Desc":"ETD0-ETD31"},{"Text":"HD:数据寄存器","Value":"ReadXdHd","Desc":"HD0-HD999"},{"Text":"HSD:数据寄存器","Value":"ReadXdHsd","Desc":"HSD0-HSD499"},{"Text":"HTD:数据寄存器","Value":"ReadXdHtd","Desc":"HTD0-HTD95"},{"Text":"HCD:数据寄存器","Value":"ReadXdHcd","Desc":"HCD0-HCD95"},{"Text":"HSCD:数据寄存器","Value":"ReadXdHscd","Desc":"HSCD0-HSCD31"},{"Text":"FD:数据寄存器","Value":"ReadXdFd","Desc":"FD0-FD5119"},{"Text":"SFD:数据寄存器","Value":"ReadXdSfd","Desc":"SFD0-SFD1999"},{"Text":"FSD:数据寄存器","Value":"ReadXdFsd","Desc":"FS0-FS47"}]
   � �                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  �A*1�; i9��O ��Q�EXinJESerialOverTcp[{"Text":"M:内部继电器","Value":"ReadM","Desc":"\tM0-M7999，M8000-M8511"},{"Text":"S:流程继电器","Value":"ReadS","Desc":"S0-S1023"},{"Text":"T:定时器","Value":"ReadT","Desc":"T0-T618"},{"Text":"C:计数器","Value":"ReadC","Desc":"C0-C634"},{"Text":"X:输入","Value":"ReadX","Desc":"X0-X1037 或者X0.0-X103.7"},{"Text":"Y:输出","Value":"ReadY","Desc":"Y0-Y1037 或者Y0.0-Y103.7"},{"Text":"D:数据寄存器","Value":"ReadXcD","Desc":"\tD0-D8511"},{"Text":"F:Flash寄存器","Value":"ReadXcF","Desc":"F0-F5000;F8000-F8511"},{"Text":"E:扩展内部寄存器","Value":"ReadXcE","Desc":"E0-E36863"},{"Text":"T:定时器","Value":"ReadXcT","Desc":"\tT0-T618"},{"Text":"C:计数器","Value":"ReadXcC","Desc":"C0-C634"},{"Text":"M:中间寄电器","Value":"ReadXdM","Desc":"M0-M7999"},{"Text":"X:输入","Value":"ReadXdX","Desc":"X0-X77,X10000-X11177,X20000-X20177,X30000-X30077 或者X0.0-X37.7"},{"Text":"Y:输出","Value":"ReadXdY","Desc":"Y0-Y77,Y10000-Y11177,Y20000-Y20177,Y30000-Y30077 或者Y0.0-Y37.7"},{"Text":"S:输出","Value":"ReadXdS","Desc":"S0-S1023"},{"Text":"SM:输出","Value":"ReadXdSm","Desc":"SM0-SM2047"},{"Text":"T:定时器","Value":"ReadXdT","Desc":"T0-T575"},{"Text":"C:计数器","Value":"ReadXdC","Desc":"C0-C575"},{"Text":"ET:计数器","Value":"ReadXdEt","Desc":"ET0-ET31"},{"Text":"SEM:计数器","Value":"ReadXdSem","Desc":"SEM0-SEM31"},{"Text":"HM:计数器","Value":"ReadXdHm","Desc":"HM0-HM959"},{"Text":"HS:计数器","Value":"ReadXdHs","Desc":"HS0-HS127"},{"Text":"HT:计数器","Value":"ReadXdHt","Desc":"HT0-HT95"},{"Text":"Hc:计数器","Value":"ReadXdHc","Desc":"HT0-HC95"},{"Text":"HSC:计数器","Value":"ReadXdHsc","Desc":"HST0-HSC31"},{"Text":"D:数据寄存器","Value":"ReadXdD","Desc":"D0-D7999"},{"Text":"ID:数据寄存器","Value":"ReadXdId","Desc":"ID0-ID99，ID10000-ID10999，ID20000-ID20199,ID30000-ID30099"},{"Text":"QD:数据寄存器","Value":"ReadXdQd","Desc":"QD0-QD99，QD10000-QD10999，QD20000-QD20199,QD30000-QD30099"},{"Text":"SD:数据寄存器","Value":"ReadXdSd","Desc":"SD0-SD2047"},{"Text":"TD:数据寄存器","Value":"ReadXdTd","Desc":"TD0-TD575"},{"Text":"CD:数据寄存器","Value":"ReadXdCd","Desc":"CD0-CD575"},{"Text":"ETD:数据寄存器","Value":"ReadXdEtd","Desc":"ETD0-ETD31"},{"Text":"HD:数据寄存器","Value":"ReadXdHd","Desc":"HD0-HD999"},{"Text":"HSD:数据寄存器","Value":"ReadXdHsd","Desc":"HSD0-HSD499"},{"Text":"HTD:数据寄存器","Value":"ReadXdHtd","Desc":"HTD0-HTD95"},{"Text":"HCD:数据寄存器","Value":"ReadXdHcd","Desc":"HCD0-HCD95"},{"Text":"HSCD:数据寄存器","Value":"ReadXdHscd","Desc":"HSCD0-HSCD31"},{"Text":"FD:数据寄存器","Value":"ReadXdFd","Desc":"FD0-FD5119"},{"Text":"SFD:数据寄存器","Value":"ReadXdSfd","Desc":"SFD0-SFD1999"},{"Text":"FSD:数据寄存器","Value":"ReadXdFsd","Desc":"FS0-FS47"}]
   � �                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         �:+#�; i9��P ��Q�EXinJESerial[{"Text":"M:内部继电器","Value":"ReadM","Desc":"\tM0-M7999，M8000-M8511"},{"Text":"S:流程继电器","Value":"ReadS","Desc":"S0-S1023"},{"Text":"T:定时器","Value":"ReadT","Desc":"T0-T618"},{"Text":"C:计数器","Value":"ReadC","Desc":"C0-C634"},{"Text":"X:输入","Value":"ReadX","Desc":"X0-X1037 或者X0.0-X103.7"},{"Text":"Y:输出","Value":"ReadY","Desc":"Y0-Y1037 或者Y0.0-Y103.7"},{"Text":"D:数据寄存器","Value":"ReadXcD","Desc":"\tD0-D8511"},{"Text":"F:Flash寄存器","Value":"ReadXcF","Desc":"F0-F5000;F8000-F8511"},{"Text":"E:扩展内部寄存器","Value":"ReadXcE","Desc":"E0-E36863"},{"Text":"T:定时器","Value":"ReadXcT","Desc":"\tT0-T618"},{"Text":"C:计数器","Value":"ReadXcC","Desc":"C0-C634"},{"Text":"M:中间寄电器","Value":"ReadXdM","Desc":"M0-M7999"},{"Text":"X:输入","Value":"ReadXdX","Desc":"X0-X77,X10000-X11177,X20000-X20177,X30000-X30077 或者X0.0-X37.7"},{"Text":"Y:输出","Value":"ReadXdY","Desc":"Y0-Y77,Y10000-Y11177,Y20000-Y20177,Y30000-Y30077 或者Y0.0-Y37.7"},{"Text":"S:输出","Value":"ReadXdS","Desc":"S0-S1023"},{"Text":"SM:输出","Value":"ReadXdSm","Desc":"SM0-SM2047"},{"Text":"T:定时器","Value":"ReadXdT","Desc":"T0-T575"},{"Text":"C:计数器","Value":"ReadXdC","Desc":"C0-C575"},{"Text":"ET:计数器","Value":"ReadXdEt","Desc":"ET0-ET31"},{"Text":"SEM:计数器","Value":"ReadXdSem","Desc":"SEM0-SEM31"},{"Text":"HM:计数器","Value":"ReadXdHm","Desc":"HM0-HM959"},{"Text":"HS:计数器","Value":"ReadXdHs","Desc":"HS0-HS127"},{"Text":"HT:计数器","Value":"ReadXdHt","Desc":"HT0-HT95"},{"Text":"Hc:计数器","Value":"ReadXdHc","Desc":"HT0-HC95"},{"Text":"HSC:计数器","Value":"ReadXdHsc","Desc":"HST0-HSC31"},{"Text":"D:数据寄存器","Value":"ReadXdD","Desc":"D0-D7999"},{"Text":"ID:数据寄存器","Value":"ReadXdId","Desc":"ID0-ID99，ID10000-ID10999，ID20000-ID20199,ID30000-ID30099"},{"Text":"QD:数据寄存器","Value":"ReadXdQd","Desc":"QD0-QD99，QD10000-QD10999，QD20000-QD20199,QD30000-QD30099"},{"Text":"SD:数据寄存器","Value":"ReadXdSd","Desc":"SD0-SD2047"},{"Text":"TD:数据寄存器","Value":"ReadXdTd","Desc":"TD0-TD575"},{"Text":"CD:数据寄存器","Value":"ReadXdCd","Desc":"CD0-CD575"},{"Text":"ETD:数据寄存器","Value":"ReadXdEtd","Desc":"ETD0-ETD31"},{"Text":"HD:数据寄存器","Value":"ReadXdHd","Desc":"HD0-HD999"},{"Text":"HSD:数据寄存器","Value":"ReadXdHsd","Desc":"HSD0-HSD499"},{"Text":"HTD:数据寄存器","Value":"ReadXdHtd","Desc":"HTD0-HTD95"},{"Text":"HCD:数据寄存器","Value":"ReadXdHcd","Desc":"HCD0-HCD95"},{"Text":"HSCD:数据寄存器","Value":"ReadXdHscd","Desc":"HSCD0-HSCD31"},{"Text":"FD:数据寄存器","Value":"ReadXdFd","Desc":"FD0-FD5119"},{"Text":"SFD:数据寄存器","Value":"ReadXdSfd","Desc":"SFD0-SFD1999"},{"Text":"FSD:数据寄存器","Value":"ReadXdFsd","Desc":"FS0-FS47"}]
   � �                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         �z,5�) i9��Q ��Q�EXinJE TcpNet(专用)[{"Text":"M:内部继电器","Value":"M","Desc":"范围：M0-M69999"},{"Text":"X:输入线圈","Value":"X","Desc":"8进制地址，也可以带小数点表示"},{"Text":"Y:输出线圈","Value":"Y","Desc":"8进制地址，也可以带小数点表示"},{"Text":"S:流程继电器","Value":"S","Desc":"范围：S0-S7999"},{"Text":"HS:流程继电器","Value":"Hs","Desc":"范围：HS0-HS999"},{"Text":"SM:特殊继电器","Value":"Sm","Desc":"范围：SM0-SM4999"},{"Text":"T:定时器","Value":"T","Desc":"范围：T0-T4999"},{"Text":"C:计数器","Value":"C","Desc":"范围：C0-C4999"},{"Text":"ET:精确定时器","Value":"Et","Desc":"范围：ET0-ET39"},{"Text":"SEM:顺序功能块专用指令","Value":"Sem","Desc":"范围：SEM0-SEM31"},{"Text":"HM:内部继电器","Value":"Hm","Desc":"范围：HM0-HM11999"},{"Text":"HT:定时器","Value":"Ht","Desc":"范围：HT0-HT1999"},{"Text":"Hc:计数器","Value":"Hc","Desc":"范围：HT0-HC1999"},{"Text":"HSC:高速计数器","Value":"Hsc","Desc":"范围：HST0-HSC39"},{"Text":"D:数据寄存器","Value":"D","Desc":"一般范围：D0-D69999"},{"Text":"ID:本体/扩展模块/扩展BD/扩展ED","Value":"Id","Desc":"ID0-ID99（本体），ID10X00（X为0-9表示10个模块）"},{"Text":"QD:本体/扩展模块/扩展BD/扩展ED","Value":"Qd","Desc":"QD0-QD99（本体），QD10X00（X为0-9表示10个模块）"},{"Text":"SD:特殊寄存器","Value":"Sd","Desc":"一般范围：SD0-SD4999"},{"Text":"TD:定时器当前值","Value":"Td","Desc":"一般范围：TD0-TD4999"},{"Text":"CD:计数器当前值","Value":"Cd","Desc":"一般范围：CD0-CD4999"},{"Text":"ETD:精确定时器当前值","Value":"Etd","Desc":"一般范围：ETD0-ETD39"},{"Text":"HD:数据寄存器","Value":"Hd","Desc":"一般范围：HD0-HD24999"},{"Text":"HSD:特殊用寄存器","Value":"Hsd","Desc":"一般范围：HSD0-HSD1023"},{"Text":"HTD:定时器当前值","Value":"Htd","Desc":"一般范围：HTD0-HTD1999"},{"Text":"HCD:计数器当前值","Value":"Hcd","Desc":"一般范围：HCD0-HCD1999"},{"Text":"HSCD:高速计数器当前值","Value":"Hscd","Desc":"一般范围：HSCD0-HSCD39"},{"Text":"FD:FlashROM寄存器","Value":"Fd","Desc":"一般范围：FD0-FD8191"},{"Text":"SFD:特殊用FlashROM寄存器","Value":"Sfd","Desc":"一般范围：SFD0-SFD5999"},{"Text":"FSD:特殊保密寄存器","Value":"Fsd","Desc":"一般范围：FSD0-FSD47"}]
    : 	:���j :                                    �-3#�! i9��Y ��Q�EInovanceTcp[{"Text":"Q:输出","Value":"ReadAmQ","Desc":"Q0.0-Q8191.7 或是 Q0-Q65535"},{"Text":"I:输入","Value":"ReadAmI","Desc":"IX0.0-IX8191.7 或是 I0-I65535"},{"Text":"M:M寄存器(字访问)","Value":"ReadAmM","Desc":"MW0-MW65535"},{"Text":"M:M寄存器(位访问)","Value":"ReadAmMb","Desc":"MX0.0-MX1000.10"},{"Text":"SM","Value":"ReadAmSm","Desc":"SM0.0-SM8191.7 或是 SM0-SM65535"},{"Text":"SD","Value":"ReadAmSd","Desc":"SDW0-SDW65535"},{"Text":"M:中间寄电器","Value":"ReadH3Um","Desc":"M0-M7679，M8000-M8511"},{"Text":"SM","Value":"ReadH3USm","Desc":"SM0-SM1023"},{"Text":"S","Value":"ReadH3Us","Desc":"S0-S4095"},{"Text":"T:定时器","Value":"ReadH3Ut","Desc":"T0-T511"},{"Text":"C:计数器","Value":"ReadH3Uc","Desc":"C0-C199,C200-C255"},{"Text":"X:输入","Value":"ReadH3Ux","Desc":"X0-X377 或者X0.0-X37.7"},{"Text":"Y:输出","Value":"ReadH3Uy","Desc":"Y0-Y377 或者Y0.0-Y37.7"},{"Text":"D:数据寄存器","Value":"ReadH3Ud","Desc":"D0-D8511"},{"Text":"SD:数据寄存器","Value":"ReadH3USd","Desc":"SD0-SD1023"},{"Text":"R","Value":"ReadH3Ur","Desc":"R0-R32767"},{"Text":"M:中间寄电器","Value":"ReadH5Um","Desc":"M0-M3071，M8000-M8511"},{"Text":"S","Value":"ReadH5Us","Desc":"S0-S999"},{"Text":"T:定时器","Value":"ReadH5Ut","Desc":"T0-T255"},{"Text":"X:输入","Value":"ReadH5Ux","Desc":"X0-X377 或者X0.0-X37.7"},{"Text":"Y:输出","Value":"ReadH5Uy","Desc":"Y0-Y377 或者Y0.0-Y37.7"},{"Text":"C:计数器","Value":"ReadH5Uc","Desc":"C0-C255"},{"Text":"D:数据寄存器","Value":"ReadH5Ud","Desc":"D0-D8511"}]�2%�c i9��X ��Q�EDlt698TcpNet[{"Text":"ReadDouble","Value":"ReadDouble","Desc":""},{"Text":"ReadString","Value":"ReadString","Desc":""}]�1'�c i9��W ��Q�EDlt698OverTcp[{"Text":"ReadDouble","Value":"ReadDouble","Desc":""},{"Text":"ReadString","Value":"ReadString","Desc":""}]�	0�c i9��V ��Q�EDlt698[{"Text":"ReadDouble","Value":"ReadDouble","Desc":""},{"Text":"ReadString","Value":"ReadString","Desc":""}]�/'�c i9��U ��Q�EDlt645OverTcp[{"Text":"ReadDouble","Value":"ReadDouble","Desc":""},{"Text":"ReadString","Value":"ReadString","Desc":""}]�	.�c i9��T ��Q�EDlt645[{"Text":"ReadDouble","Value":"ReadDouble","Desc":""},{"Text":"ReadString","Value":"ReadString","Desc":""}]�C-+�E i9��S ��Q�EProgram OverTcp[{"Text":"M:内部继电器","Value":"ReadM","Desc":""},{"Text":"X:输入继电器","Value":"ReadX","Desc":"默认16进制,8进制 X011"},{"Text":"Y:输出继电器","Value":"ReadY","Desc":"默认16进制,8进制 X011"},{"Text":"SM:SM特殊继电器","Value":"ReadSm","Desc":""},{"Text":"S:步进继电器","Value":"ReadS","Desc":""},{"Text":"L:锁存继电器","Value":"ReadL","Desc":""},{"Text":"F:报警器","Value":"ReadF","Desc":""},{"Text":"V:边沿继电器","Value":"ReadV","Desc":""},{"Text":"B:链接继电器","Value":"ReadB","Desc":"16进制地址"},{"Text":"SB:特殊链接继电器","Value":"ReadSb","Desc":"16进制地址"},{"Text":"DX:直接输入","Value":"ReadDx","Desc":""},{"Text":"DY:直接输出","Value":"ReadDy","Desc":""},{"Text":"TS:定时器触点","Value":"ReadTs","Desc":""},{"Text":"TC:定时器线圈","Value":"ReadTc","Desc":""},{"Text":"SS:累计定时器触点","Value":"ReadSs","Desc":""},{"Text":"SC:累计定时器线圈","Value":"ReadSc","Desc":""},{"Text":"CS:计数器触点","Value":"ReadCs","Desc":""},{"Text":"CC:计数器线圈","Value":"ReadCc","Desc":""},{"Text":"D:数据寄存器","Value":"ReadD","Desc":""},{"Text":"SD:特殊数据寄存器","Value":"ReadSd","Desc":""},{"Text":"W:链接寄存器","Value":"ReadW","Desc":""},{"Text":"SW:特殊链接寄存器","Value":"ReadSw","Desc":""},{"Text":"R:文件寄存器","Value":"ReadR","Desc":""},{"Text":"Z:变址寄存器","Value":"ReadZ","Desc":""},{"Text":"ZR:ZR文件寄存器","Value":"ReadZr","Desc":""},{"Text":"TN:定时器当前值","Value":"ReadTn","Desc":""},{"Text":"SN:累计定时器当前值","Value":"ReadSn","Desc":""},{"Text":"CN:计数器当前值","Value":"ReadCn","Desc":""}]
   ! �o!                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 L7o i9��^ ��Q�ESnmp[{"Text":"读取","Value":"ReadY","Desc":"Read"}]�F6	!�W i9��d ��Q�EMazakSmart[{"Text":"工作模式","Value":"ReadWorkMode","Desc":""},{"Text":"系统状态","Value":"ReadRunStatus","Desc":""},{"Text":"刀具号","Value":"ReadToolNum","Desc":""},{"Text":"切削时间","Value":"ReadCutTime","Desc":""},{"Text":"目标工件","Value":"ReadtagProduce","Desc":""},{"Text":"工件计数","Value":"ReadCurProduce","Desc":""},{"Text":"快速倍率","Value":"ReadQuickRate","Desc":""},{"Text":"总时间","Value":"ReadTotalTime","Desc":""},{"Text":"自动运行时间","Value":"ReadRunTime","Desc":""},{"Text":"主轴倍率","Value":"ReadOvSpin","Desc":""},{"Text":"进给倍率","Value":"ReadOvFeed","Desc":""},{"Text":"主轴温度","Value":"ReadSpinTemp","Desc":""},{"Text":"主轴负载","Value":"ReadSLoad","Desc":""},{"Text":"主轴速度","Value":"ReadActSpeed","Desc":""},{"Text":"程序号","Value":"ReadProgramNo","Desc":""},{"Text":"块号","Value":"ReadBlockNo","Desc":""},{"Text":"顺序号","Value":"ReadSeqNo","Desc":""},{"Text":"程序名","Value":"ReadName","Desc":""},{"Text":"程序注释","Value":"ReadAnnotation","Desc":""}]�G5	#�W i9��] ��Q�EMazakSmooth[{"Text":"工作模式","Value":"ReadWorkMode","Desc":""},{"Text":"系统状态","Value":"ReadRunStatus","Desc":""},{"Text":"刀具号","Value":"ReadToolNum","Desc":""},{"Text":"切削时间","Value":"ReadCutTime","Desc":""},{"Text":"目标工件","Value":"ReadtagProduce","Desc":""},{"Text":"工件计数","Value":"ReadCurProduce","Desc":""},{"Text":"快速倍率","Value":"ReadQuickRate","Desc":""},{"Text":"总时间","Value":"ReadTotalTime","Desc":""},{"Text":"自动运行时间","Value":"ReadRunTime","Desc":""},{"Text":"主轴倍率","Value":"ReadOvSpin","Desc":""},{"Text":"进给倍率","Value":"ReadOvFeed","Desc":""},{"Text":"主轴温度","Value":"ReadSpinTemp","Desc":""},{"Text":"主轴负载","Value":"ReadSLoad","Desc":""},{"Text":"主轴速度","Value":"ReadActSpeed","Desc":""},{"Text":"程序号","Value":"ReadProgramNo","Desc":""},{"Text":"块号","Value":"ReadBlockNo","Desc":""},{"Text":"顺序号","Value":"ReadSeqNo","Desc":""},{"Text":"程序名","Value":"ReadName","Desc":""},{"Text":"程序注释","Value":"ReadAnnotation","Desc":""}]�{4	#�? i9��\ ��Q�EMazakMatrix[{"Text":"最后活动时间","Value":"ReadLastTime","Desc":""},{"Text":"产量","Value":"ReadParts","Desc":""},{"Text":"运行状态","Value":"ReadRunStatus","Desc":""},{"Text":"报警号","Value":"ReadAlarmNo","Desc":""},{"Text":"类型","Value":"ReadType","Desc":""},{"Text":"Ip","Value":"ReadIp","Desc":""},{"Text":"模式","Value":"ReadMode","Desc":""},{"Text":"程序名","Value":"ReadProName","Desc":""},{"Text":"程序内容","Value":"ReadProComment","Desc":""},{"Text":"刀具号","Value":"ReadToolNum","Desc":""},{"Text":"快速进给","Value":"ReadRapidFeed","Desc":""},{"Text":"主轴倍率","Value":"ReadSFeed","Desc":""},{"Text":"进给倍率","Value":"ReadFFeed","Desc":""},{"Text":"报警信息","Value":"ReadAlarm","Desc":""},{"Text":"铣削负载","Value":"ReadMillingLoad","Desc":""},{"Text":"车削负载","Value":"ReadTurningLoad","Desc":""},{"Text":"铣削倍率","Value":"ReadMillingSpeed","Desc":""},{"Text":"车削转速","Value":"ReadTurningSpeed","Desc":""}]
   � 
_�a�$�                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      (=+ i9��i ��Q�EVirtualProtocol[]�(<	�) i9��g ��Q�EHnc[{"Text":"主轴倍率","Value":"ReadOvSpin","Desc":""},{"Text":"进给倍率","Value":"ReadOvFeed","Desc":""},{"Text":"状态","Value":"ReadRunStatus","Desc":"0停止;1运行"},{"Text":"产量","Value":"ReadCurrentNum","Desc":""},{"Text":"总产量","Value":"ReadCurrentNumTotal","Desc":""},{"Text":"程序号","Value":"ReadCurPgm","Desc":""},{"Text":"程序名","Value":"ReadMainName","Desc":""}]�;	�m i9��f ��Q�EFagorNet[{"Text":"主轴速度","Value":"ReadsSpeed","Desc":""},{"Text":"进给速度","Value":"ReadActFeed","Desc":""}]T:!s i9��c ��Q�EMqttServer[{"Text":"读取","Value":"GetData","Desc":"Read"}]�%9	� i9��b ��Q�ELncTcp[{"Text":"进给实际速度","Value":"ReadActFSpeed","Desc":""},{"Text":"主轴实际速度","Value":"ReadActsPeed","Desc":""},{"Text":"主轴设定速度","Value":"ReadSetsSpeed","Desc":""},{"Text":"进给设定速度","Value":"ReadSetFSpeed","Desc":""},{"Text":"主程序名","Value":"ReadMainName","Desc":""},{"Text":"当前程序名","Value":"ReadCurrentName","Desc":""},{"Text":"工件数量","Value":"ReadCurrentNum","Desc":""},{"Text":"最大数量","Value":"ReadMaxCount","Desc":""},{"Text":"系统状态","Value":"ReadRunStatus","Desc":"状态:1:准备就绪;2:自动运行;4:连续寸动"},{"Text":"模式","Value":"ReadMode","Desc":""}]�8	!� i9��_ ��Q�EHeidenhain[{"Text":"运行状态","Value":"ReadSysStatus","Desc":"STARTED = 0,STOPPED = 1,FINISHED = 2,CANCELED = 3,INTERRUPTED = 4,ERROR = 5,ERROR_CLEARED = 6,IDLE = 7"},{"Text":"报警状态","Value":"ReadSysAlarm","Desc":""},{"Text":"工作模式","Value":"ReadSysMode","Desc":"模式MANUAL = 0,MDI = 1,RPF = 2,SINGLESTEP = 3,AUTOMATIC = 4,OTHER = 5"},{"Text":"机械坐标","Value":"ReadMachinePos","Desc":""},{"Text":"切削坐标","Value":"ReadCurrentPos","Desc":""},{"Text":"工件数量","Value":"ReadCurrentProduceCount","Desc":""},{"Text":"程序名","Value":"ReadSystemProgramCurrent","Desc":""},{"Text":"运行时间","Value":"ReadRunTime","Desc":""},{"Text":"机器上电时间","Value":"ReadMachineAliveTime","Desc":""},{"Text":"程序上电时间","Value":"ReadNcAliveTime","Desc":""},{"Text":"进给倍率","Value":"ReadFeedRate","Desc":""},{"Text":"主轴倍率","Value":"ReadSpindleRate","Desc":""},{"Text":"快速倍率","Value":"ReadQuickRate","Desc":""},{"Text":"刀具号","Value":"ReadToolNum","Desc":""},{"Text":"轴刀号","Value":"ReadToolAxis","Desc":""},{"Text":"长度","Value":"ReadToolLen","Desc":""},{"Text":"直径","Value":"ReadToolRad","Desc":""},{"Text":"报警信息","Value":"ReadAlarmMessage","Desc":""},{"Text":"报警组","Value":"ReadAlarmGroup","Desc":""},{"Text":"报警ID","Value":"ReadAlarmId","Desc":""},{"Text":"报警类型","Value":"ReadSystemAlarmInfo","Desc":""}]    ","Value":"HighCombinePressure","Desc":""},{"Text":"高压合模流量","Value":"HighCombineFlow","Desc":""},{"Text":"高压合模时间","Value":"HighCombineTime","Desc":""},{"Text":"低速开模压力","Value":"LowSpeedOpenPressure","Desc":""},{"Text":"低速开模流量","Value":"LowSpeedOpenFlow","Desc":""},{"Text":"卸荷时间","Value":"UnloadTime","Desc":""},{"Text":"快速开模压力","Value":"FastMoldPressure","Desc":""},{"Text":"快速开模流量","Value":"FastMoldFlow","Desc":""},{"Text":"慢速开模时间","Value":"SlowOpenTime","Desc":""},{"Text":"低速开模时间","Value":"LowSpeedOpenTime","Desc":""},{"Text":"低压保护时间","Value":"LowPressureProtectionTime","Desc":""},{"Text":"开合模限时","Value":"MoldClosingTimeLimit","Desc":""},{"Text":"慢速开模时间","Value":"SlowMoldOpeningTime","Desc":""},{"Text":"低速开模时间","Value":"LowSpeedMoldOpeningTime","Desc":""},{"Text":"射出压力1","Value":"InjectionPressure1","Desc":""},{"Text":"射出流量1","Value":"InjectionFlow1","Desc":""},{"Text":"射出时间1","Value":"InjectionTime1","Desc":""},{"Text":"射出压力2","Value":"InjectionPressure2","Desc":""},{"Text":"射出流量2","Value":"InjectionFlow2","Desc":""},{"Text":"射出时间2","Value":"InjectionTime2","Desc":""},{"Text":"射出压力3","Value":"InjectionPressure3","Desc":""},{"Text":"射出流量3","Value":"InjectionFlow3","Desc":""},{"Text":"射出时间3","Value":"InjectionTime3","Desc":""},{"Text":"保压压力3","Value":"HoldingPressure3","Desc":""},{"Text":"保压流量3","Value":"HoldingFlow3","Desc":""},{"Text":"保压时间3","Value":"HoldingTime3","Desc":""},{"Text":"射出总时","Value":"TotalInjectionTime","Desc":""},{"Text":"射出监测点","Value":"InjectionMonitoringPoint","Desc":""},{"Text":"储料后射退压力","Value":"PostShootingBackPressure","Desc":""},{"Text":"储料后射退流量","Value":"PostShootingBackFlow","Desc":""},{"Text":"储料后射退时间","Value":"PostShootingBackTime","Desc":""},{"Text":"储料压力","Value":"MaterialStoragePressure","Desc":""},{"Text":"储料流量","Value":"MaterialStorageFlow","Desc":""},{"Text":"储料前射退压力","Value":"PreShootingBackPressure","Desc":""},{"Text":"储料前射退流量","Value":"PreShootingBackFlow","Desc":""},{"Text":"储料前射退时间","Value":"PreShootingBackTime","Desc":""},{"Text":"储料限时","Value":"MaterialStorageTimeLimit","Desc":""},{"Text":"先冷却时间","Value":"PreCoolingTime","Desc":""},{"Text":"后冷却时间","Value":"PostCoolingTime","Desc":""},{"Text":"清料射退压力","Value":"PurgeBackPressure","Desc":""},{"Text":"清料射退流量","Value":"PurgeBackFlow","Desc":""},{"Text":"清料射退时间","Value":"PurgeBackTime","Desc":""},{"Text":"清料压力","Value":"PurgePressure","Desc":""},{"Text":"清料流量","Value":"PurgeFlow","Desc":""},{"Text":"清料时间","Value":"PurgeTime","Desc":""},{"Text":"清料射出压力","Value":"PurgeInjectionPressure","Desc":""},{"Text":"清料射出流量","Value":"PurgeInjectionFlow","Desc":""},{"Text":"清料射出时间","Value":"PurgeInjectionTime","Desc":""},{"Text":"清料次数","Value":"PurgeCount","Desc":""},{"Text":"座台退压力","Value":"TableRetreatPressure","Desc":""},{"Text":"座台退流量","Value":"TableRetreatFlow","Desc":""},{"Text":"座台退时间","Value":"TableRetreatTime","Desc":""},{"Text":"座台进压力","Value":"TableAdvancePressure","Desc":""},{"Text":"座台进流量","Value":"TableAdvanceFlow","Desc":""},{"Text":"座台进时间","Value":"TableAdvanceTime","Desc":""},{"Text":"托模保持压力","Value":"MoldHoldPressure","Desc":""},{"Text":"托模保持流量","Value":"MoldHoldFlow","Desc":""},{"Text":"托模保持时间","Value":"MoldHoldTime","Desc":""},{"Text":"托模进压力","Value":"MoldAdvancePressure","Desc":""},{"Text":"托模进流量","Value":"MoldAdvanceFlow","Desc":""},{"Text":"托模进时间","Value":"MoldAdvanceTime","Desc":""},{"Text":"托模退压力","Value":"MoldRetreatPressure","Desc":""},{"Text":"托模退流量","Value":"MoldRetreatFlow","Desc":""}]
    �  �                                                                                                                                                                                                 �*>/� i9��k ��Q�EPORCHESON_Ps660Bm[{"Text":"托模退时间","Value":"MoldRetreatTime","Desc":""},{"Text":"托模次数","Value":"MoldCount","Desc":""},{"Text":"托模进延迟时间","Value":"MoldAdvanceDelayTime","Desc":""},{"Text":"托模退延迟时间","Value":"MoldRetreatDelayTime","Desc":""},{"Text":"润滑计时","Value":"LubricationTiming","Desc":""},{"Text":"润滑模数","Value":"LubricationCycle","Desc":""},{"Text":"循环等待时间","Value":"CycleWaitTime","Desc":""},{"Text":"手动动作限时","Value":"ManualActionTimeLimit","Desc":""},{"Text":"周期时间","Value":"CycleTime","Desc":""},{"Text":"故障告警时间","Value":"FaultWarningTime","Desc":""},{"Text":"射咀","Value":"Nozzle","Desc":""},{"Text":"螺杆冷启动时间","Value":"ScrewColdStartUpTime","Desc":""},{"Text":"设定模数","Value":"SetMoldCount","Desc":""},{"Text":"产量","Value":"OpenedMoldCount","Desc":""},{"Text":"卸荷压力","Value":"UnloadPressure","Desc":""},{"Text":"卸荷流量","Value":"UnloadFlow","Desc":""},{"Text":"保压压力1","Value":"GetHoldingPressure1","Desc":""},{"Text":"保压流量1","Value":"GetHoldingFlow1","Desc":""},{"Text":"保压时间1","Value":"GetHoldingTime1","Desc":""},{"Text":"保压压力2","Value":"GetHoldingPressure2","Desc":""},{"Text":"保压流量2","Value":"GetHoldingFlow2","Desc":""},{"Text":"保压时间2","Value":"GetHoldingTime2","Desc":""},{"Text":"合模压力","Value":"GetMoldClosingPressure","Desc":""},{"Text":"合模流量","Value":"GetMoldClosingFlow","Desc":""},{"Text":"开模压力","Value":"GetMoldOpeningPressure","Desc":""},{"Text":"开模流量","Value":"GetMoldOpeningFlow","Desc":""},{"Text":"温度设定1","Value":"GetTemperatureSetting1","Desc":""},{"Text":"温度设定2","Value":"GetTemperatureSetting2","Desc":""},{"Text":"温度设定3","Value":"GetTemperatureSetting3","Desc":""},{"Text":"温度设定4","Value":"GetTemperatureSetting4","Desc":""},{"Text":"温度设定5","Value":"GetTemperatureSetting5","Desc":""},{"Text":"温度设定6","Value":"GetTemperatureSetting6","Desc":""},{"Text":"温度设定7","Value":"GetTemperatureSetting7","Desc":""},{"Text":"温度设定8","Value":"GetTemperatureSetting8","Desc":""},{"Text":"温度设定9","Value":"GetTemperatureSetting9","Desc":""},{"Text":"温度设定10","Value":"GetTemperatureSetting10","Desc":""},{"Text":"温度设定上限1","Value":"GetTemperatureUpperLimit1","Desc":""},{"Text":"温度设定上限2","Value":"GetTemperatureUpperLimit2","Desc":""},{"Text":"温度设定上限3","Value":"GetTemperatureUpperLimit3","Desc":""},{"Text":"温度设定下限1","Value":"GetTemperatureLowerLimit1","Desc":""},{"Text":"温度设定下限2","Value":"GetTemperatureLowerLimit2","Desc":""},{"Text":"温度设定下限3","Value":"GetTemperatureLowerLimit3","Desc":""},{"Text":"温度1","Value":"GetTemperature1","Desc":""},{"Text":"温度2","Value":"GetTemperature2","Desc":""},{"Text":"温度3","Value":"GetTemperature3","Desc":""},{"Text":"温度4","Value":"GetTemperature4","Desc":""},{"Text":"温度5","Value":"GetTemperature5","Desc":""},{"Text":"温度6","Value":"GetTemperature6","Desc":""},{"Text":"温度7","Value":"GetTemperature7","Desc":""},{"Text":"温度8","Value":"GetTemperature8","Desc":""},{"Text":"温度9","Value":"GetTemperature9","Desc":""},{"Text":"温度10","Value":"GetTemperature10","Desc":""},{"Text":"良品数","Value":"GetGoodCount","Desc":""},{"Text":"预产模数","Value":"GetPlannedMoldCount","Desc":""},{"Text":"模式","Value":"Mode","Desc":"模式 1：马达开；2：手动；3：半自动；4：全自动"},{"Text":"快速合模压力","Value":"QuickCombinePressure","Desc":""},{"Text":"快速合模流量","Value":"QuickCombineFlow","Desc":""},{"Text":"低压合模压力","Value":"LowCombinePressure","Desc":""},{"Text":"低压合模流量","Value":"LowCombineFlow","Desc":""},{"Text":"高压合模压力   9   <"Desc":""},{"Text":"中子A退绞牙退二","Value":"GetCoreABackward2","Desc":""},{"Text":"中子B进动作位置","Value":"GetCoreBForwardPosition","Desc":""},{"Text":"中子B进压力","Value":"GetCoreBForwardPressure","Desc":""},{"Text":"中子B进速度","Value":"GetCoreBForwardSpeed","Desc":""},{"Text":"中子B进动作时间","Value":"GetCoreBForwardTime","Desc":""},{"Text":"中子B进绞牙计数","Value":"GetCoreBForwardCount","Desc":""},{"Text":"中子B退动作位置","Value":"GetCoreBBackwardPosition","Desc":""},{"Text":"中子B退压力","Value":"GetCoreBBackwardPressure","Desc":""},{"Text":"中子B退速度","Value":"GetCoreBBackwardSpeed","Desc":""},{"Text":"中子B退动作时间","Value":"GetCoreBBackwardTime","Desc":""},{"Text":"中子B退绞牙计数","Value":"GetCoreBBackwardCount","Desc":""},{"Text":"中子C进动作位置","Value":"GetCoreCForwardPosition","Desc":""},{"Text":"中子C进压力","Value":"GetCoreCForwardPressure","Desc":""},{"Text":"中子C进速度","Value":"GetCoreCForwardSpeed","Desc":""},{"Text":"中子C进动作时间","Value":"GetCoreCForwardTime","Desc":""},{"Text":"中子C进绞牙计数","Value":"GetCoreCForwardCount","Desc":""},{"Text":"中子C退动作位置","Value":"GetCoreCBackwardPosition","Desc":""},{"Text":"中子C退压力","Value":"GetCoreCBackwardPressure","Desc":""},{"Text":"中子C退速度","Value":"GetCoreCBackwardSpeed","Desc":""},{"Text":"中子C退动作时间","Value":"GetCoreCBackwardTime","Desc":""},{"Text":"中子C退绞牙计数","Value":"GetCoreCBackwardCount","Desc":""},{"Text":"中子D进动作位置","Value":"GetCoreDForwardPosition","Desc":""},{"Text":"中子D进压力","Value":"GetCoreDForwardPressure","Desc":""},{"Text":"中子D进速度","Value":"GetCoreDForwardSpeed","Desc":""},{"Text":"中子D进动作时间","Value":"GetCoreDForwardTime","Desc":""},{"Text":"中子D进绞牙计数","Value":"GetCoreDForwardCount","Desc":""},{"Text":"中子D退动作位置","Value":"GetCoreDBackwardPosition","Desc":""},{"Text":"中子D退压力","Value":"GetCoreDBackwardPressure","Desc":""},{"Text":"中子D退速度","Value":"GetCoreDBackwardSpeed","Desc":""},{"Text":"中子D退动作时间","Value":"GetCoreDBackwardTime","Desc":""},{"Text":"中子D退绞牙计数","Value":"GetCoreDBackwardCount","Desc":""},{"Text":"上模射出终点位置","Value":"MsInjectionEndPos","Desc":""},{"Text":"上模射出监控位置","Value":"MsInjectionMonPos","Desc":""},{"Text":"上模射出尖压","Value":"MsInjectionPeakPressure","Desc":""},{"Text":"上模储料尖压","Value":"MsStoragePeakPressure","Desc":""},{"Text":"上模最大射速","Value":"MsInjectionSpeedMax","Desc":""},{"Text":"上模取件时间","Value":"MsPickUpTime","Desc":""},{"Text":"模式","Value":"GetMode","Desc":"模式：0:为手动，3:为半自动，5为电眼自动，9为时间自动，16为调模"},{"Text":"报警信息","Value":"GetAlarmStr","Desc":""},{"Text":"输出压力","Value":"GetOutputPressure","Desc":""},{"Text":"输出速度","Value":"GetOutputSpeed","Desc":""},{"Text":"输出背压","Value":"GetOutputBackPressure","Desc":""},{"Text":"射出位置实时值","Value":"GetInjectionPositionAct","Desc":""},{"Text":"推力座位置","Value":"GetMoldPosition","Desc":""},{"Text":"托模位置","Value":"GetEjectorPosition","Desc":""},{"Text":"产量","Value":"GetMoldOpeningTotal","Desc":""},{"Text":"保压一段压力","Value":"HoldingPressure1","Desc":""},{"Text":"保压二段压力","Value":"HoldingPressure2","Desc":""},{"Text":"保压三段压力","Value":"HoldingPressure3","Desc":""},{"Text":"保压四段压力","Value":"HoldingPressure4","Desc":""},{"Text":"保压五段压力","Value":"HoldingPressure5","Desc":""},{"Text":"保压六段压力","Value":"HoldingPressure6","Desc":""},{"Text":"保压一段速度","Value":"HoldingSpeed1","Desc":""},{"Text":"保压二段速度","Value":"HoldingSpeed2","Desc":""},{"Text":"保压三段速度","Value":"HoldingSpeed3","Desc":""},{"Text":"保压四段速度","Value":"HoldingSpeed4","Desc":""},{"Text":"保压五段速度","Value":"HoldingSpeed5","Desc":""   =},{"Text":"保压六段速度","Value":"HoldingSpeed6","Desc":""},{"Text":"保压一段时间","Value":"HoldingTime1","Desc":""},{"Text":"保压二段时间","Value":"HoldingTime2","Desc":""},{"Text":"保压三段时间","Value":"HoldingTime3","Desc":""},{"Text":"保压四段时间","Value":"HoldingTime4","Desc":""},{"Text":"保压五段时间","Value":"HoldingTime5","Desc":""},{"Text":"保压六段时间","Value":"HoldingTime6","Desc":""},{"Text":"射出一段压力","Value":"InjectionPressure1","Desc":""},{"Text":"射出二段压力值","Value":"InjectionPressure2","Desc":""},{"Text":"射出三段压力值","Value":"InjectionPressure3","Desc":""},{"Text":"射出四段压力值","Value":"InjectionPressure4","Desc":""},{"Text":"射出五段压力值","Value":"InjectionPressure5","Desc":""},{"Text":"射出六段压力值","Value":"InjectionPressure6","Desc":""},{"Text":"射出一段速度","Value":"InjectionSpeed1","Desc":""},{"Text":"射出二段速度","Value":"InjectionSpeed2","Desc":""},{"Text":"射出三段速度","Value":"InjectionSpeed3","Desc":""},{"Text":"射出四段速度","Value":"InjectionSpeed4","Desc":""},{"Text":"射出五段速度","Value":"InjectionSpeed5","Desc":""},{"Text":"射出六段速度","Value":"InjectionSpeed6","Desc":""},{"Text":"射出一段位置","Value":"InjectionPosition1","Desc":""},{"Text":"射出二段位置","Value":"InjectionPosition2","Desc":""},{"Text":"射出三段位置","Value":"InjectionPosition3","Desc":""},{"Text":"射出四段位置","Value":"InjectionPosition4","Desc":""},{"Text":"射出五段位置","Value":"InjectionPosition5","Desc":""},{"Text":"射出六段位置","Value":"InjectionPosition6","Desc":""},{"Text":"转保压压力","Value":"GetSwitchHoldingPressure","Desc":""},{"Text":"转保压位置","Value":"GetSwitchHoldingPosition","Desc":""},{"Text":"转保压选择","Value":"GetSwitchHoldingSelection","Desc":""},{"Text":"转保压时间设定","Value":"GetSwitchHoldingTime","Desc":""},{"Text":"储料一段压力","Value":"StoragePressure1","Desc":""},{"Text":"储料二段压力","Value":"StoragePressure2","Desc":""},{"Text":"储料三段压力","Value":"StoragePressure3","Desc":""},{"Text":"储料四段压力","Value":"StoragePressure4","Desc":""},{"Text":"储料五段压力","Value":"StoragePressure5","Desc":""},{"Text":"储料一段背压","Value":"StorageBackPressure1","Desc":""},{"Text":"储料二段背压","Value":"StorageBackPressure2","Desc":""},{"Text":"储料三段背压","Value":"StorageBackPressure3","Desc":""},{"Text":"储料四段背压","Value":"StorageBackPressure4","Desc":""},{"Text":"储料五段背压","Value":"StorageBackPressure5","Desc":""},{"Text":"储料一段速度","Value":"StorageSpeed1","Desc":""},{"Text":"储料二段速度","Value":"StorageSpeed2","Desc":""},{"Text":"储料三段速度","Value":"StorageSpeed3","Desc":""},{"Text":"储料四段速度","Value":"StorageSpeed4","Desc":""},{"Text":"储料五段速度","Value":"StorageSpeed5","Desc":""},{"Text":"储料一段位置","Value":"StoragePosition1","Desc":""},{"Text":"储料二段位置","Value":"StoragePosition2","Desc":""},{"Text":"储料三段位置","Value":"StoragePosition3","Desc":""},{"Text":"储料四段位置","Value":"StoragePosition4","Desc":""},{"Text":"储料五段位置","Value":"StoragePosition5","Desc":""},{"Text":"储前冷却","Value":"CoolingBeforeStorage","Desc":""},{"Text":"射退压力","Value":"InjectionBackPressure","Desc":""},{"Text":"射退速度","Value":"InjectionBackSpeed","Desc":""},{"Text":"射退距离","Value":"InjectionBackDistance","Desc":""},{"Text":"射退模式","Value":"InjectionBackMode","Desc":"0:表示储料后；1:表示冷却后"},{"Text":"储前射退距离","Value":"InjectionBackDistanceBeforeStorage","Desc":""},{"Text":"托模进一段压力","Value":"EjectorForwardPressure1","Desc":""},{"Text":"托模进二段压力","Value":"EjectorForwardPressure2","Desc":""},{"Text":"托模进三段压力","Value":"EjectorForwardPressure3","Desc":""},{"Text":"托模进四段压力","Value":"EjectorForwardPressure4","Desc":""},{"Text":"温度设定    1","Value":"TempSet1","Desc":""},{"Text":"温度设定2","Value":"TempSet2","Desc":""},{"Text":"温度设定3","Value":"TempSet3","Desc":""},{"Text":"温度设定4","Value":"TempSet4","Desc":""},{"Text":"温度设定5","Value":"TempSet5","Desc":""},{"Text":"温度设定6","Value":"TempSet6","Desc":""},{"Text":"温度设定7","Value":"TempSet7","Desc":""},{"Text":"实际温度1","Value":"TempAct1","Desc":""},{"Text":"实际温度2","Value":"TempAct2","Desc":""},{"Text":"实际温度3","Value":"TempAct3","Desc":""},{"Text":"实际温度4","Value":"TempAct4","Desc":""},{"Text":"实际温度5","Value":"TempAct5","Desc":""},{"Text":"实际温度6","Value":"TempAct6","Desc":""},{"Text":"实际温度7","Value":"TempAct7","Desc":""},{"Text":"关模一段位置","Value":"MoldClosingPosition1","Desc":""},{"Text":"关模二段位置","Value":"MoldClosingPosition2","Desc":""},{"Text":"关模三段位置","Value":"MoldClosingPosition3","Desc":""},{"Text":"关模四段位置","Value":"MoldClosingPosition4","Desc":""},{"Text":"关模五段位置","Value":"MoldClosingPosition5","Desc":""},{"Text":"关模一段压力","Value":"MoldClosingPressure1","Desc":""},{"Text":"关模二段压力","Value":"MoldClosingPressure2","Desc":""},{"Text":"关模三段压力","Value":"MoldClosingPressure3","Desc":""},{"Text":"关模四段压力","Value":"MoldClosingPressure4","Desc":""},{"Text":"关模五段压力","Value":"MoldClosingPressure5","Desc":""},{"Text":"关模一段速度","Value":"MoldClosingSpeed1","Desc":""},{"Text":"关模二段速度","Value":"MoldClosingSpeed2","Desc":""},{"Text":"关模三段速度","Value":"MoldClosingSpeed3","Desc":""},{"Text":"关模四段速度","Value":"MoldClosingSpeed4","Desc":""},{"Text":"关模五段速度","Value":"MoldClosingSpeed5","Desc":""},{"Text":"模具冷却时间","Value":"MoldCoolingTime","Desc":""},{"Text":"再循环计时","Value":"RecyclingTime","Desc":""},{"Text":"开模一段压力","Value":"MoldOpeningPressure1","Desc":""},{"Text":"开模二段压力","Value":"MoldOpeningPressure2","Desc":""},{"Text":"开模三段压力","Value":"MoldOpeningPressure3","Desc":""},{"Text":"开模四段压力","Value":"MoldOpeningPressure4","Desc":""},{"Text":"开模五段压力","Value":"MoldOpeningPressure5","Desc":""},{"Text":"开模一段速度","Value":"MoldOpeningSpeed1","Desc":""},{"Text":"开模二段速度","Value":"MoldOpeningSpeed2","Desc":""},{"Text":"开模三段速度","Value":"MoldOpeningSpeed3","Desc":""},{"Text":"开模四段速度","Value":"MoldOpeningSpeed4","Desc":""},{"Text":"开模五段速度","Value":"MoldOpeningSpeed5","Desc":""},{"Text":"开模一段位置","Value":"MoldOpeningPosition1","Desc":""},{"Text":"开模二段位置","Value":"MoldOpeningPosition2","Desc":""},{"Text":"开模三段位置","Value":"MoldOpeningPosition3","Desc":""},{"Text":"开模四段位置","Value":"MoldOpeningPosition4","Desc":""},{"Text":"开模五段位置","Value":"MoldOpeningPosition5","Desc":""},{"Text":"开模行程","Value":"MoldOpeningStroke","Desc":""},{"Text":"模具冷却计时低位数据","Value":"MoldCoolingTimeLow","Desc":""},{"Text":"模具冷却计时低位数据 true = 低；false = 高","Value":"GetMoldCoolingTimeLow","Desc":""},{"Text":"模具冷却计时高位数据","Value":"MoldCoolingTimeHigh","Desc":""},{"Text":"上模循环时间","Value":"MsCycleTime","Desc":""},{"Text":"上模射出时间","Value":"MsInjectionTime","Desc":""},{"Text":"上模转保时间","Value":"MsHoldingTime","Desc":""},{"Text":"上模储料时间","Value":"MsStorageTime","Desc":""},{"Text":"上模关模计时","Value":"MsMoldCloseTime","Desc":""},{"Text":"上模低压计时","Value":"MsLowPressureTime","Desc":""},{"Text":"上模高压计时","Value":"MsHighPressureTime","Desc":""},{"Text":"上模推力座位置","Value":"MsEjectorPos","Desc":""},{"Text":"上模开模计时","Value":"MsMoldOpenTime","Desc":""},{"Text":"上模转保压力","Value":"MsHoldingPressure","Desc":""},{"Text":"上模射出起点","Value":"MsInjectionStartPos","Desc":""},{"Text":"上模保压起点","Value":"MsHoldingStartPos","Desc":""}]
   Z fZ                                                                                                                                                                                                                                                                                                                                              �	@;�A i9��m ��Q�EDeltaSerialAsciiOverTcp[{"Text":"S0-S127","Value":"ReadS0S127","Desc":""},{"Text":"X0-X177:输入继电器","Value":"ReadX0X177","Desc":""},{"Text":"Y0-Y177:输出继电器","Value":"ReadY0Y177","Desc":"地址8进制"},{"Text":"T0-T127:定时器","Value":"ReadT0T127","Desc":"如果是读位,就是通断继电器,如果是读字,就是当前值"},{"Text":"C0-C127 C232-C255:计数器","Value":"ReadC0C127C232C255","Desc":"如果是读位,就是通断继电器,如果是读字,就是当前值"},{"Text":"M0-M1279:内部继电器","Value":"ReadM0M1279","Desc":""},{"Text":"D0-D1311:数据寄存器","Value":"ReadD0D1311","Desc":""},{"Text":"S0-S1023","Value":"ReadS0S1023","Desc":""},{"Text":"T0-T255:定时器","Value":"ReadT0T255","Desc":""},{"Text":"C0-C199 C200-C255:计数器","Value":"ReadC0C199C200C255","Desc":"如果是读位,就是通断继电器,如果是读字,就是当前值"},{"Text":"M0-M4095:内部继电器","Value":"ReadM0M4095","Desc":""},{"Text":"D0-D4999:数据寄存器","Value":"ReadD0D4999","Desc":""},{"Text":"X0-X377:输入继电器","Value":"ReadX0X377","Desc":"只读操作,地址8进制"},{"Text":"Y0-Y377:输出继电器","Value":"ReadY0Y377","Desc":"地址8进制"},{"Text":"D0-D9999:数据寄存器","Value":"ReadD0D9999","Desc":""}]�?%��Q i9��l ��Q�ETechMationAk[{"Text":"托模退一段压力","Value":"EjectorBackwardPressure1","Desc":""},{"Text":"托模退二段压力","Value":"EjectorBackwardPressure2","Desc":""},{"Text":"托模退三段压力","Value":"EjectorBackwardPressure3","Desc":""},{"Text":"托模退四段压力","Value":"EjectorBackwardPressure4","Desc":""},{"Text":"托模进一段速度","Value":"EjectorForwardSpeed1","Desc":""},{"Text":"托模进二段速度","Value":"EjectorForwardSpeed2","Desc":""},{"Text":"托模进三段速度","Value":"EjectorForwardSpeed3","Desc":""},{"Text":"托模进四段速度","Value":"EjectorForwardSpeed4","Desc":""},{"Text":"托模退一段速度","Value":"EjectorBackwardSpeed1","Desc":""},{"Text":"托模退二段速度","Value":"EjectorBackwardSpeed2","Desc":""},{"Text":"托模退三段速度","Value":"EjectorBackwardSpeed3","Desc":""},{"Text":"托模退四段速度","Value":"EjectorBackwardSpeed4","Desc":""},{"Text":"托模进一段位置","Value":"EjectorForwardPosition1","Desc":""},{"Text":"托模进二段位置","Value":"EjectorForwardPosition2","Desc":""},{"Text":"托模进三段位置","Value":"EjectorForwardPosition3","Desc":""},{"Text":"托模进四段位置","Value":"EjectorForwardPosition4","Desc":""},{"Text":"托模退一段位置","Value":"EjectorBackwardPosition1","Desc":""},{"Text":"托模退二段位置","Value":"EjectorBackwardPosition2","Desc":""},{"Text":"托模退三段位置","Value":"EjectorBackwardPosition3","Desc":""},{"Text":"托模退四段位置","Value":"EjectorBackwardPosition4","Desc":""},{"Text":"托模进延时时间","Value":"EjectorForwardDelayTime","Desc":""},{"Text":"托模退延时时间","Value":"EjectorBackwardDelayTime","Desc":""},{"Text":"中子A进动作位置","Value":"GetCoreAForwardPosition","Desc":""},{"Text":"中子A进压力","Value":"GetCoreAForwardPressure","Desc":""},{"Text":"中子A进速度","Value":"GetCoreAForwardSpeed","Desc":""},{"Text":"中子A进动作时间","Value":"GetCoreAForwardTime","Desc":""},{"Text":"中子A进绞牙计数","Value":"GetCoreAForwardCount","Desc":""},{"Text":"中子A退动作位置","Value":"GetCoreABackwardPosition","Desc":""},{"Text":"中子A退压力","Value":"GetCoreABackwardPressure","Desc":""},{"Text":"中子A退速度","Value":"GetCoreABackwardSpeed","Desc":""},{"Text":"中子A退动作时间","Value":"GetCoreABackwardTime","Desc":""},{"Text":"中子A退绞牙计数","Value":"GetCoreABackwardCount",   ;    :""},{"Text":"中速开模流量","Value":"MediumSpeedMoldFlow","Desc":""},{"Text":"快速开模流量","Value":"FastSpeedMoldFlow","Desc":""},{"Text":"慢速开模流量","Value":"SlowSpeedMoldFlow","Desc":""},{"Text":"低速开模位置","Value":"LowSpeedMoldPosition","Desc":""},{"Text":"中速开模位置","Value":"MediumSpeedMoldPosition","Desc":""},{"Text":"快速开模位置","Value":"FastSpeedMoldPosition","Desc":""},{"Text":"慢速开模位置","Value":"SlowSpeedMoldPosition","Desc":""},{"Text":"慢速合模压力","Value":"SlowCombinePressure","Desc":""},{"Text":"慢速合模流量","Value":"SlowCombineFlow","Desc":""},{"Text":"差动锁模","Value":"DifferentialModeLock","Desc":""},{"Text":"快速合模压力","Value":"QuickCombinePressure","Desc":""},{"Text":"快速合模流量","Value":"QuickCombineFlow","Desc":""},{"Text":"低压合模压力","Value":"LowCombinePressure","Desc":""},{"Text":"低压合模流量","Value":"LowCombineFlow","Desc":""},{"Text":"高压合模压力","Value":"HighCombinePressure","Desc":""},{"Text":"高压合模流量","Value":"HighCombineFlow","Desc":""},{"Text":"低压保护时间","Value":"LowPressureProtectionTime","Desc":""},{"Text":"开合模限时","Value":"MoldClosingTimeLimit","Desc":""},{"Text":"射出压力1","Value":"InjectionPressure1","Desc":""},{"Text":"射出流量1","Value":"InjectionFlow1","Desc":""},{"Text":"射出压力2","Value":"InjectionPressure2","Desc":""},{"Text":"射出流量2","Value":"InjectionFlow2","Desc":""},{"Text":"射出压力3","Value":"InjectionPressure3","Desc":""},{"Text":"射出流量3","Value":"InjectionFlow3","Desc":""},{"Text":"托进快压力","Value":"MoldAdvanceFastPressure","Desc":""},{"Text":"托进慢压力","Value":"MoldAdvanceSlowPressure","Desc":""},{"Text":"托进快流量","Value":"MoldAdvanceFastFlow","Desc":""},{"Text":"托进慢流量","Value":"MoldAdvanceSlowFlow","Desc":""},{"Text":"托模退位置","Value":"MoldRetreatPosition","Desc":""},{"Text":"托进快位置","Value":"MoldAdvanceFastPosition","Desc":""},{"Text":"托进快流量","Value":"MoldAdvanceSlowPosition","Desc":""},{"Text":"座台进慢压力","Value":"TableAdvanceSlowPressure","Desc":""},{"Text":"座台进快压力","Value":"TableAdvanceFastPressure","Desc":""},{"Text":"座台进慢流量","Value":"TableAdvanceSlowFlow","Desc":""},{"Text":"座台进快流量","Value":"TableAdvanceFastFlow","Desc":""},{"Text":"座台进慢时间","Value":"TableAdvanceSlowTime","Desc":""},{"Text":"座台进快时间","Value":"TableAdvanceFastTime","Desc":""},{"Text":"座台退压力","Value":"TableRetreatPressure","Desc":""},{"Text":"座台退流量","Value":"TableRetreatFlow","Desc":""},{"Text":"座台退时间","Value":"TableRetreatTime","Desc":""},{"Text":"托模保持压力","Value":"MoldHoldPressure","Desc":""},{"Text":"托模退压力","Value":"MoldRetreatPressure","Desc":""},{"Text":"托模退流量","Value":"MoldRetreatFlow","Desc":""},{"Text":"润滑总时","Value":"TotalLubricationTime","Desc":""},{"Text":"润滑时间","Value":"LubricationTime","Desc":""},{"Text":"润滑间歇","Value":"LubricationInterval","Desc":""},{"Text":"润滑模数","Value":"LubricationCycle","Desc":""},{"Text":"循环等待时间","Value":"CycleWaitTime","Desc":""},{"Text":"手动动作限时","Value":"ManualActionTimeLimit","Desc":""},{"Text":"周期时间","Value":"CycleTime","Desc":""},{"Text":"故障告警时间","Value":"FaultWarningTime","Desc":""},{"Text":"产量","Value":"OpenedMoldCount","Desc":""},{"Text":"储料前射退压力","Value":"PreInjectionRetreatPressure","Desc":""},{"Text":"储料压力1","Value":"MaterialPressure1","Desc":""},{"Text":"储料压力2","Value":"MaterialPressure2","Desc":""},{"Text":"储料后射退压力","Value":"PostInjectionRetreatPressure","Desc":""},{"Text":"储料前射退背压","Value":"PreInjectionRetreatBackPressure","Desc":""},{"Text":"储料背压1","Value":"MaterialBackPressure1","Desc":""},{"Text":"储料背压2","Value":"MaterialBackPressure2","Desc":""},{"Text":"储料后射退背压","Value":"PostInjectionRetreatBackPressure","Desc":""}]
    �  �                                                                                                                                                                         �dB� i9��o ��Q�EMcNet[{"Text":"R015/R0.15 (XF,X1A0):输入继电器","Value":"Ro15X","Desc":"字+位组合(16)，或是三菱的X地址，范围: RO0000~R99915"},{"Text":"RO15/RO.15 (YF,Y1AO):输出继电器","Value":"Ro15Y","Desc":"字+位组合(16)，或是三菱的Y地址，范围:RO0000~R99915"},{"Text":"TNO:定时器当前值","Value":"TnO","Desc":"范围:TN0000~TN3999"},{"Text":"TCO:定时器线圈","Value":"TcO","Desc":"范围:TC0000~TC3999"},{"Text":"TSO:定时器触点","Value":"TsO","Desc":"范围: TS0000~TS3999"},{"Text":"CNO:计数器当前值","Value":"CnO","Desc":"范围: TN0000~TN3999"},{"Text":"CS0:计数器触点","Value":"Cs0","Desc":"范围:CS0000~Cs3999"},{"Text":"CC0:计数器线圈","Value":"Cc0","Desc":"范围:CC0000~CC3999"},{"Text":"BO:链接继电器","Value":"BO","Desc":"地址16进制，范围:B0~B7FFF"},{"Text":"MROOO/MRO.0:内部辅助继电器","Value":"Mr","Desc":"字+位组合(16)，范围: MR00000~MR99915"},{"Text":"LRO15/LRO.15:锁存继电器","Value":"Lr","Desc":"字+位组合(16)，范围: LR00000~LR99915"},{"Text":"CRO15/CR0.15:控制继电器","Value":"Cr","Desc":"字+位组合(16)，范围:CR0000~CR7915"},{"Text":"CMO:控制寄存器","Value":"CmO","Desc":"范围:CM0000~CM5999"},{"Text":"DMO:数据寄存器","Value":"Dm","Desc":"范围: DM00000~DM65534"},{"Text":"EMO:扩展数据寄存器","Value":"Em","Desc":"范围:EM00000~EM65534"},{"Text":"FMO:文件寄存器","Value":"FmO","Desc":"FMO0000~FM32767"},{"Text":"ZFO:文件寄存器","Value":"ZfO","Desc":"ZF000000~ZF524287"},{"Text":"WO:链路寄存器","Value":"Wo","Desc":"16进制地址，范围:W0000~W7FFF"}]�YA/�m i9��n ��Q�EPORCHESON_Ps660Am[{"Text":"储料前射退流量","Value":"PreInjectionRetreatFlow","Desc":""},{"Text":"储料流量1","Value":"MaterialFlow1","Desc":""},{"Text":"储料流量2","Value":"MaterialFlow2","Desc":""},{"Text":"储料后射退流量","Value":"PostInjectionRetreatFlow","Desc":""},{"Text":"储料前射退位置","Value":"PreInjectionRetreatPosition","Desc":""},{"Text":"储料位置1","Value":"MaterialPosition1","Desc":""},{"Text":"储料位置2","Value":"MaterialPosition2","Desc":""},{"Text":"储料后射退位置","Value":"PostInjectionRetreatPosition","Desc":""},{"Text":"冷却时间","Value":"CoolingTime","Desc":""},{"Text":"储料限时","Value":"MaterialLimitTime","Desc":""},{"Text":"温度设定1","Value":"GetTemperatureSetting1","Desc":""},{"Text":"温度设定2","Value":"GetTemperatureSetting2","Desc":""},{"Text":"温度设定3","Value":"GetTemperatureSetting3","Desc":""},{"Text":"温度设定4","Value":"GetTemperatureSetting4","Desc":""},{"Text":"温度设定上限1","Value":"GetTemperatureUpperLimit1","Desc":""},{"Text":"温度设定上限2","Value":"GetTemperatureUpperLimit2","Desc":""},{"Text":"温度设定上限3","Value":"GetTemperatureUpperLimit3","Desc":""},{"Text":"温度设定下限1","Value":"GetTemperatureLowerLimit1","Desc":""},{"Text":"温度设定下限2","Value":"GetTemperatureLowerLimit2","Desc":""},{"Text":"温度设定下限3","Value":"GetTemperatureLowerLimit3","Desc":""},{"Text":"实际射砠温度","Value":"GetActualInjectionTemperature","Desc":""},{"Text":"温度1","Value":"GetTemperature1","Desc":""},{"Text":"温度2","Value":"GetTemperature2","Desc":""},{"Text":"温度3","Value":"GetTemperature3","Desc":""},{"Text":"温度4","Value":"GetTemperature4","Desc":""},{"Text":"良品数","Value":"GetGoodCount","Desc":""},{"Text":"模式","Value":"Mode","Desc":"模式 1：马达开；2：手动；3：半自动；4：全自动"},{"Text":"低速开模压力","Value":"LowSpeedMoldPressure","Desc":""},{"Text":"中速开模压力","Value":"MediumSpeedMoldPressure","Desc":""},{"Text":"快速开模压力","Value":"FastSpeedMoldPressure","Desc":""},{"Text":"慢速开模压力","Value":"SlowSpeedMoldPressure","Desc":""},{"Text":"低速开模流量","Value":"LowSpeedMoldFlow","Desc"   ?
    	�	>                                                                                                                                                                                                                                                                  �+E!� i9��q ��Q�ENanoSerial[{"Text":"RO15/RO.15:R继电器","Value":"Ro15","Desc":"字+位组合(16)，范围:RO0000~R99915"},{"Text":"T0:定时器线圈","Value":"T0","Desc":"范围:T0000~T3999"},{"Text":"CTCO:高速计数器线圈","Value":"Ctco","Desc":"范围: CTC0 ~ CTC3"},{"Text":"CTHO:高速计数器","Value":"Ctho","Desc":"范围: CTHO ~ CTH1"},{"Text":"VBO:字线圈","Value":"Vbo","Desc":"16进制，范围:VB0000~VB3FFF"},{"Text":"TMO:临时数据寄存器","Value":"Tmo","Desc":"范围: TMO00~TM511"},{"Text":"Z0:变址寄存器","Value":"Z0","Desc":"范围: Z1~Z12"},{"Text":"TCO:定时器当前值","Value":"Tco","Desc":"范围:TC0000~TC3999"},{"Text":"TSO:定时器设定值","Value":"Tso","Desc":"范围: TS0000~TS3999"},{"Text":"CC0:计数器当前值","Value":"Cc0","Desc":"范围:CC0000~CC3999"},{"Text":"CS0:计数器设定值","Value":"Cs0","Desc":"范围:CS0000~CS3999"},{"Text":"CTC0:高速计数器设定值","Value":"Ctc0","Desc":"范围: CTC0~CTC3"},{"Text":"ATO:数字微调器","Value":"At0","Desc":"范围: ATO~AT7"},{"Text":"VMM:字存储器","Value":"Vmm","Desc":"范围: VMO~VM59999"},{"Text":"unit=0;100:扩展存储器模块","Value":"Unit","Desc":"读取扩展模块的数据"},{"Text":"BO:链接继电器","Value":"BO","Desc":"地址16进制，范围:B0~B7FFF"},{"Text":"MROOO/MRO.0:内部辅助继电器","Value":"Mr","Desc":"字+位组合(16)，范围: MR00000~MR99915"},{"Text":"LRO15/LRO.15:锁存继电器","Value":"Lr","Desc":"字+位组合(16)，范围: LR00000~LR99915"},{"Text":"CRO15/CR0.15:控制继电器","Value":"Cr","Desc":"字+位组合(16)，范围:CR0000~CR7915"},{"Text":"CMO:控制寄存器","Value":"CmO","Desc":"范围:CM0000~CM5999"},{"Text":"DMO:数据寄存器","Value":"Dm","Desc":"范围: DM00000~DM65534"},{"Text":"EMO:扩展数据寄存器","Value":"Em","Desc":"范围:EM00000~EM65534"},{"Text":"FMO:文件寄存器","Value":"FmO","Desc":"FMO0000~FM32767"},{"Text":"ZFO:文件寄存器","Value":"ZfO","Desc":"ZF000000~ZF524287"},{"Text":"WO:链路寄存器","Value":"Wo","Desc":"16进制地址，范围:W0000~W7FFF"}]TD%o i9��s ��Q�ESerialClient[{"Text":"读取","Value":"ReadY","Desc":"Read"}]�iC!� i9��p ��Q�EMcAsciiNet[{"Text":"R015/R0.15 (XF,X1A0):输入继电器","Value":"Ro15X","Desc":"字+位组合(16)，或是三菱的X地址，范围: RO0000~R99915"},{"Text":"RO15/RO.15 (YF,Y1AO):输出继电器","Value":"Ro15Y","Desc":"字+位组合(16)，或是三菱的Y地址，范围:RO0000~R99915"},{"Text":"TNO:定时器当前值","Value":"TnO","Desc":"范围:TN0000~TN3999"},{"Text":"TCO:定时器线圈","Value":"TcO","Desc":"范围:TC0000~TC3999"},{"Text":"TSO:定时器触点","Value":"TsO","Desc":"范围: TS0000~TS3999"},{"Text":"CNO:计数器当前值","Value":"CnO","Desc":"范围: TN0000~TN3999"},{"Text":"CS0:计数器触点","Value":"Cs0","Desc":"范围:CS0000~Cs3999"},{"Text":"CC0:计数器线圈","Value":"Cc0","Desc":"范围:CC0000~CC3999"},{"Text":"BO:链接继电器","Value":"BO","Desc":"地址16进制，范围:B0~B7FFF"},{"Text":"MROOO/MRO.0:内部辅助继电器","Value":"Mr","Desc":"字+位组合(16)，范围: MR00000~MR99915"},{"Text":"LRO15/LRO.15:锁存继电器","Value":"Lr","Desc":"字+位组合(16)，范围: LR00000~LR99915"},{"Text":"CRO15/CR0.15:控制继电器","Value":"Cr","Desc":"字+位组合(16)，范围:CR0000~CR7915"},{"Text":"CMO:控制寄存器","Value":"CmO","Desc":"范围:CM0000~CM5999"},{"Text":"DMO:数据寄存器","Value":"Dm","Desc":"范围: DM00000~DM65534"},{"Text":"EMO:扩展数据寄存器","Value":"Em","Desc":"范围:EM00000~EM65534"},{"Text":"FMO:文件寄存器","Value":"FmO","Desc":"FMO0000~FM32767"},{"Text":"ZFO:文件寄存器","Value":"ZfO","Desc":"ZF000000~ZF524287"},{"Text":"WO:链路寄存器","Value":"Wo","Desc":"16进制地址，范围:W0000~W7FFF"}]
   � �                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 �2F/� i9��r ��Q�ENanoSerialOverTcp[{"Text":"RO15/RO.15:R继电器","Value":"Ro15","Desc":"字+位组合(16)，范围:RO0000~R99915"},{"Text":"T0:定时器线圈","Value":"T0","Desc":"范围:T0000~T3999"},{"Text":"CTCO:高速计数器线圈","Value":"Ctco","Desc":"范围: CTC0 ~ CTC3"},{"Text":"CTHO:高速计数器","Value":"Ctho","Desc":"范围: CTHO ~ CTH1"},{"Text":"VBO:字线圈","Value":"Vbo","Desc":"16进制，范围:VB0000~VB3FFF"},{"Text":"TMO:临时数据寄存器","Value":"Tmo","Desc":"范围: TMO00~TM511"},{"Text":"Z0:变址寄存器","Value":"Z0","Desc":"范围: Z1~Z12"},{"Text":"TCO:定时器当前值","Value":"Tco","Desc":"范围:TC0000~TC3999"},{"Text":"TSO:定时器设定值","Value":"Tso","Desc":"范围: TS0000~TS3999"},{"Text":"CC0:计数器当前值","Value":"Cc0","Desc":"范围:CC0000~CC3999"},{"Text":"CS0:计数器设定值","Value":"Cs0","Desc":"范围:CS0000~CS3999"},{"Text":"CTC0:高速计数器设定值","Value":"Ctc0","Desc":"范围: CTC0~CTC3"},{"Text":"ATO:数字微调器","Value":"At0","Desc":"范围: ATO~AT7"},{"Text":"VMM:字存储器","Value":"Vmm","Desc":"范围: VMO~VM59999"},{"Text":"unit=0;100:扩展存储器模块","Value":"Unit","Desc":"读取扩展模块的数据"},{"Text":"BO:链接继电器","Value":"BO","Desc":"地址16进制，范围:B0~B7FFF"},{"Text":"MROOO/MRO.0:内部辅助继电器","Value":"Mr","Desc":"字+位组合(16)，范围: MR00000~MR99915"},{"Text":"LRO15/LRO.15:锁存继电器","Value":"Lr","Desc":"字+位组合(16)，范围: LR00000~LR99915"},{"Text":"CRO15/CR0.15:控制继电器","Value":"Cr","Desc":"字+位组合(16)，范围:CR0000~CR7915"},{"Text":"CMO:控制寄存器","Value":"CmO","Desc":"范围:CM0000~CM5999"},{"Text":"DMO:数据寄存器","Value":"Dm","Desc":"范围: DM00000~DM65534"},{"Text":"EMO:扩展数据寄存器","Value":"Em","Desc":"范围:EM00000~EM65534"},{"Text":"FMO:文件寄存器","Value":"FmO","Desc":"FMO0000~FM32767"},{"Text":"ZFO:文件寄存器","Value":"ZfO","Desc":"ZF000000~ZF524287"},{"Text":"WO:链路寄存器","Value":"Wo","Desc":"16进制地址，范围:W0000~W7FFF"}]