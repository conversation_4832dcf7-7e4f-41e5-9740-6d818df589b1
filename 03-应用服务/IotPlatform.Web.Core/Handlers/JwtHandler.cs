using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Furion;
using Furion.Authorization;
using Furion.DataEncryption;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.DependencyInjection;
using Systems.Core;
using Systems.Core.Config;

namespace IotPlatform.Web.Core.Handlers;

public class JwtHandler : AppAuthorizeHandler
{
    private readonly IServiceProvider _serviceProvider;

    public JwtHandler(IServiceProvider serviceProvider)
    {
        _serviceProvider = serviceProvider;
    }

    /// <summary>
    ///     自动刷新Token
    /// </summary>
    /// <param name="context"></param>
    /// <param name="httpContext"></param>
    /// <returns></returns>
    public override async System.Threading.Tasks.Task HandleAsync(AuthorizationHandlerContext context, DefaultHttpContext httpContext)
    {
        // var serviceProvider = context.GetCurrentHttpContext().RequestServices;
        using IServiceScope serviceScope = _serviceProvider.CreateScope();
        //
        // // 若当前账号存在黑名单中则授权失败
        // ICacheManager cache = serviceScope.ServiceProvider.GetService<ICacheManager>();
        // if (cache.Exists($"{CacheConst.KeyBlacklist}{context.User.FindFirst(ClaimConst.UserId)?.Value}"))
        // {
        //     context.Fail();
        //     context.GetCurrentHttpContext().SignoutToSwagger();
        //     return;
        // }

        SysConfigService sysConfigService = serviceScope.ServiceProvider.GetService<SysConfigService>();
        int tokenExpire = await sysConfigService.GetTokenExpire();
        int refreshTokenExpire = await sysConfigService.GetRefreshTokenExpire();
        if (JWTEncryption.AutoRefreshToken(context, context.GetCurrentHttpContext(), tokenExpire, refreshTokenExpire))
        {
            await AuthorizeHandleAsync(context);
        }
        else
        {
            context.Fail(); // 授权失败
            DefaultHttpContext currentHttpContext = context.GetCurrentHttpContext();
            if (currentHttpContext == null)
            {
                return;
            }

            currentHttpContext.SignoutToSwagger();
        }
    }

    public override async Task<bool> PipelineAsync(AuthorizationHandlerContext context, DefaultHttpContext httpContext)
    {
        // 已自动验证 Jwt Token 有效性
        return await CheckAuthorizeAsync(httpContext);
    }

    /// <summary>
    ///     权限校验核心逻辑
    /// </summary>
    /// <param name="httpContext"></param>
    /// <returns></returns>
    private static async Task<bool> CheckAuthorizeAsync(DefaultHttpContext httpContext)
    {
        // // 登录模式判断PC、APP
        // if (App.User.FindFirst(ClaimConst.LoginMode)?.Value == ((int) LoginModeEnum.APP).ToString())
        // {
        //     return true;
        // }
        //
        // // 排除超管
        // if (App.User.FindFirst(ClaimConst.AdminType)?.Value == ((int) AdminTypeEnum.SuperAdmin).ToString())
        // {
        //     return true;
        // }

        // 路由名称
        string routeName = httpContext.Request.Path.StartsWithSegments("/api")
            ? httpContext.Request.Path.Value[5..].Replace("/", ":")
            : httpContext.Request.Path.Value[1..].Replace("/", ":");

        // 获取用户拥有按钮权限集合
        List<string> ownBtnPermList = await App.GetService<SysMenuService>().GetOwnBtnPermList();
        // 获取系统所有按钮权限集合
        List<string> allBtnPermList = await App.GetService<SysMenuService>().GetAllBtnPermList();

        // 已拥有该按钮权限或者所有按钮集合里面不存在
        bool exist1 = ownBtnPermList.Exists(u => routeName.Equals(u, StringComparison.CurrentCultureIgnoreCase));
        bool exist2 = allBtnPermList.TrueForAll(u => !routeName.Equals(u, StringComparison.CurrentCultureIgnoreCase));
        return exist1 || exist2;
    }
}