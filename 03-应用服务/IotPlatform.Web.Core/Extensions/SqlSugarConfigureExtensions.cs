using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using Extras.DatabaseAccessor.SqlSugar.Extensions;
using Extras.DatabaseAccessor.SqlSugar.Options;
using Extras.DatabaseAccessor.SqlSugar.Repositories;
using Furion;
using IotPlatform.Core.Const;
using IotPlatform.Core.Enum;
using IotPlatform.Core.Extension;
using Mapster;
using Microsoft.Extensions.DependencyInjection;
using NPOI.SS.Formula.Functions;
using SqlSugar;
using Yitter.IdGenerator;
using DateTime = System.DateTime;
using Log = Furion.Logging.Log;

namespace IotPlatform.Web.Core.Extensions;

/// <summary>
///     SqlSugar配置拓展.
/// </summary>
public static class SqlSugarConfigureExtensions
{
    public static void SqlSugarConfigure(this IServiceCollection services)
    {
        StaticConfig.CustomSnowFlakeFunc = () => { return YitIdHelper.NextId(); };
        // 获取选项
        ConnectionStringsOptions dbOptions = App.GetOptions<ConnectionStringsOptions>();
        dbOptions.ConnectionConfigs.ForEach(SetDbConfig);
        SqlSugarScope sqlSugar = new(dbOptions.ConnectionConfigs.Adapt<List<ConnectionConfig>>(), db =>
        {
            dbOptions.ConnectionConfigs.ForEach(config =>
            {
                SqlSugarScopeProvider dbProvider = db.GetConnectionScope(config.ConfigId);
                SetDbAop(dbProvider,dbOptions.EnableConsoleSql);
            });
        });

        services.AddSingleton<ISqlSugarClient>(sqlSugar); // 单例注册
        services.AddScoped(typeof(ISqlSugarRepository<>), typeof(SqlSugarRepository<>)); // 仓储注册
        services.AddUnitOfWork<SqlSugarUnitOfWork>(); // 事务与工作单元注册
        
        // 初始化数据库表结构及种子数据
        dbOptions.ConnectionConfigs.ForEach(config => { InitDatabase(sqlSugar, config); });
    }

    /// <summary>
    ///     配置连接属性.
    /// </summary>
    /// <param name="config"></param>
    private static void SetDbConfig(DbConnectionConfig config)
    {
        config.ConnectionString = JNPFTenantExtensions.ToConnectionString(config);
        config.ConfigureExternalServices = new ConfigureExternalServices
        {
            EntityService = (type, column) => // 处理列
            {
                if (new NullabilityInfoContext().Create(type).WriteState is NullabilityState.Nullable)
                {
                    column.IsNullable = true;
                }

                if (config.DbType == DbType.Oracle)
                {
                    if (type.PropertyType == typeof(long) || type.PropertyType == typeof(long?))
                    {
                        column.DataType = "number(18)";
                    }

                    if (type.PropertyType == typeof(bool) || type.PropertyType == typeof(bool?))
                    {
                        column.DataType = "number(1)";
                    }
                }
            }
        };
        config.IsAutoCloseConnection = true;
        config.MoreSettings = new ConnMoreSettings
        {
            IsAutoRemoveDataCache = true,
            SqlServerCodeFirstNvarchar = true // 采用Nvarchar
        };
    }

    /// <summary>
    ///     配置Aop.
    /// </summary>
    /// <param name="db"></param>
    /// <param name="enableConsoleSql"></param>
    public static void SetDbAop(SqlSugarScopeProvider db, bool enableConsoleSql)
    {
        ConnectionConfig config = db.CurrentConnectionConfig;

        // 设置超时时间
        db.Ado.CommandTimeOut = 30;

        if (enableConsoleSql)
        {
            // 打印SQL语句
            db.Aop.OnLogExecuting = (sql, pars) =>
            {
                ConsoleColor originColor = Console.ForegroundColor;
                if (sql.StartsWith("SELECT", StringComparison.OrdinalIgnoreCase))
                {
                    Console.ForegroundColor = ConsoleColor.Green;
                }

                if (sql.StartsWith("UPDATE", StringComparison.OrdinalIgnoreCase) || sql.StartsWith("INSERT", StringComparison.OrdinalIgnoreCase))
                {
                    Console.ForegroundColor = ConsoleColor.Yellow;
                }

                if (sql.StartsWith("DELETE", StringComparison.OrdinalIgnoreCase))
                {
                    Console.ForegroundColor = ConsoleColor.Red;
                }

                Console.WriteLine("【" + DateTime.Now + "——执行SQL】\r\n" + UtilMethods.GetSqlString(config.DbType, sql, pars) + "\r\n");
                Console.ForegroundColor = originColor;
            };
            db.Aop.OnError = ex =>
            {
                if (ex.Parametres == null)
                {
                    return;
                }

                ConsoleColor originColor = Console.ForegroundColor;
                Console.ForegroundColor = ConsoleColor.DarkRed;
                string pars = db.Utilities.SerializeObject(((SugarParameter[]) ex.Parametres).ToDictionary(it => it.ParameterName, it => it.Value));
                Console.WriteLine("【" + DateTime.Now + "——错误SQL】\r\n" + UtilMethods.GetSqlString(config.DbType, ex.Sql, (SugarParameter[]) ex.Parametres) + "\r\n");
                Console.ForegroundColor = originColor;
            };
        }
       
        // 数据审计
        db.Aop.DataExecuting = (oldValue, entityInfo) =>
        {
            if (entityInfo.OperationType == DataFilterType.InsertByObject)
            {
                // 主键(long类型)且没有值的---赋值雪花Id
                if (entityInfo.EntityColumnInfo.IsPrimarykey && entityInfo.EntityColumnInfo.PropertyInfo.PropertyType == typeof(long))
                {
                    var id = entityInfo.EntityColumnInfo.PropertyInfo.GetValue(entityInfo.EntityValue);
                    if (id == null || (long) id == 0)
                        entityInfo.SetValue(YitIdHelper.NextId());
                }

                if (entityInfo.PropertyName == "CreatedTime")
                    entityInfo.SetValue(DateTime.Now);
                if (App.User != null)
                {
                    if (entityInfo.PropertyName == "CreatedUserId")
                    {
                        var createUserId = ((dynamic) entityInfo.EntityValue).CreatedUserId;
                        if (createUserId == 0 || createUserId == null)
                            entityInfo.SetValue(App.User.FindFirst(ClaimConst.UserId)?.Value);
                    }

                    if (entityInfo.PropertyName == "CreatedUserName")
                    {
                        var createdUserName = ((dynamic) entityInfo.EntityValue).CreatedUserName;
                        if (string.IsNullOrEmpty(createdUserName) || createdUserName == null)
                            entityInfo.SetValue(App.User.FindFirst(ClaimConst.RealName)?.Value);
                    }
                    if (entityInfo.PropertyName == "TenantId")
                        entityInfo.SetValue(App.User?.FindFirst(ClaimConst.TenantId)?.Value);
                }
            }

            if (entityInfo.OperationType == DataFilterType.UpdateByObject)
            {
                if (entityInfo.PropertyName == "UpdatedTime")
                    entityInfo.SetValue(DateTime.Now);
                if (entityInfo.PropertyName == "UpdatedUserId")
                    entityInfo.SetValue(App.User?.FindFirst(ClaimConst.UserId)?.Value);
                if (entityInfo.PropertyName == "UpdatedUserName")
                    entityInfo.SetValue(App.User?.FindFirst(ClaimConst.RealName)?.Value);
            }
        };
        
        // 超管排除其他过滤器
        if (App.User?.FindFirst(ClaimConst.AdminType)?.Value == ((int)AdminTypeEnum.SuperAdmin).ToString())
            return;
        // 配置租户过滤器
        var tenantId = App.User?.FindFirst(ClaimConst.TenantId)?.Value;
        if (!string.IsNullOrWhiteSpace(tenantId))
            db.QueryFilter.AddTableFilter<Systems.Entity.ITenantIdFilter>(u => u.TenantId == long.Parse(tenantId) || u.TenantId == null);
    }
    
     /// <summary>
    ///     初始化数据库
    /// </summary>
    /// <param name="db"></param>
    /// <param name="config"></param>
    private static void InitDatabase(SqlSugarScope db, DbConnectionConfig config)
    {
        SqlSugarScopeProvider dbProvider = db.GetConnectionScope(config.ConfigId);

        // 创建数据库
        dbProvider.DbMaintenance.CreateDatabase();

        // 初始化表结构
        if (config.EnableInitTable)
        {
            var entityTypes = App.EffectiveTypes.Where(u => !u.IsInterface && !u.IsAbstract && u.IsClass && u.IsDefined(typeof(SugarTable), false)).ToList();

            entityTypes = (string) config.ConfigId == SqlSugarConst.MainConfigId
                ? entityTypes.Where(u => !u.GetCustomAttributes<TenantAttribute>().Any()).ToList()
                : entityTypes.Where(u => u.GetCustomAttribute<TenantAttribute>()?.configId.ToString() == config.ConfigId).ToList(); // 自定义的库
            // 默认库（有系统表特性、没有日志表和租户表特性）
            foreach (var entityType in entityTypes)
            {
                try
                {
                    if (entityType.GetCustomAttribute<SplitTableAttribute>() == null)
                        dbProvider.CodeFirst.InitTables(entityType);
                    // else
                    //     dbProvider.CodeFirst.SplitTables().InitTables(entityType);
                }
                catch (Exception e)
                {
                    Log.Error($"数据库表：{entityType.FullName} 初始化异常：{e.Message}");
                }
            }
        }

        // 初始化种子数据
        if (config.EnableInitSeed)
        {
            var seedDataTypes = App.EffectiveTypes.Where(u => !u.IsInterface && !u.IsAbstract && u.IsClass && u.GetInterfaces().Any(i => i.HasImplementedRawGeneric(typeof(ISqlSugarEntitySeedData<>))))
                .ToList();

            foreach (var seedType in seedDataTypes)
            {
                try
                {
                    var entityType = seedType.GetInterfaces().First().GetGenericArguments().First();

                    if (config.ConfigId.ToString() == SqlSugarConst.MainConfigId) // 默认库（有系统表特性、没有日志表和租户表特性）
                    {
                        if (entityType.GetCustomAttribute<TenantAttribute>() != null)
                            continue;
                    }
                    else
                    {
                        var att = entityType.GetCustomAttribute<TenantAttribute>(); // 自定义的库
                        if (att == null || att.configId.ToString() != config.ConfigId.ToString()) continue;
                    }

                    var instance = Activator.CreateInstance(seedType);
                    var hasDataMethod = seedType.GetMethod("HasData");
                    var seedData = ((IEnumerable)hasDataMethod?.Invoke(instance, null))?.Cast<object>();
                    if (seedData == null) continue;

                    var entityInfo = dbProvider.EntityMaintenance.GetEntityInfo(entityType);
                    if (entityInfo.Columns.Any(u => u.IsPrimarykey))
                    {
                        // 按主键进行批量增加和更新
                        var storage = dbProvider.StorageableByObject(seedData.ToList()).ToStorage();
                        storage.AsInsertable.ExecuteCommand();
                        storage.AsUpdateable.ExecuteCommand();
                    }
                    else
                    {
                        // 无主键则只进行插入
                        if (!dbProvider.Queryable(entityInfo.DbTableName, entityInfo.DbTableName).Any())
                            dbProvider.InsertableByObject(seedData.ToList()).ExecuteCommand();
                    }
                }
                catch (Exception e)
                {
                    Log.Error($"数据库表：{seedType.FullName} 初始化异常：{e.Message}");
                }
            }
        }
    }
}