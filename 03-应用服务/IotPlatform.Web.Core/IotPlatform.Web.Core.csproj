<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <NoWarn>1701;1702;1591</NoWarn>
        <GenerateDocumentationFile>True</GenerateDocumentationFile>
    </PropertyGroup>

    <PropertyGroup Condition=" '$(Configuration)' == 'Release' ">
      <DebugSymbols>true</DebugSymbols>
      <DebugType>none</DebugType>
      <DocumentationFile />
    </PropertyGroup>

    <ItemGroup>
        <None Remove="IotPlatform.Web.Core.xml"/>
        <None Update="Configuration\App.json">
          <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="Configuration\Cache.json">
          <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="Configuration\Database.json">
          <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="Configuration\JWT.json">
          <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="Configuration\Logging.json">
          <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="Configuration\Swagger.json">
          <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="Configuration\Upload.json">
          <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="Configuration\EventBus.json">
          <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="Configuration\LinkMq.json">
          <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="Configuration\DongleLicense.json">
          <CopyToOutputDirectory>Always</CopyToOutputDirectory>
        </None>
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\..\01-架构核心\Extras.MQTT\Extras.MQTT.csproj" />
        <ProjectReference Include="..\..\02-应用模块\01-OAuth\OAuth\OAuth.csproj" />
        <ProjectReference Include="..\..\02-应用模块\03-BusApp\IotPlatform.Application\IotPlatform.Application.csproj" />
        <ProjectReference Include="..\..\02-应用模块\04-DataWeaving\IotPlatform.DataWeaving\IotPlatform.DataWeaving.csproj" />
        <ProjectReference Include="..\..\02-应用模块\05-Message\IotPlatform.PolicyMessage\IotPlatform.PolicyMessage.csproj" />
        <ProjectReference Include="..\..\02-应用模块\06-Task\00-Task\IotPlatform.Task\IotPlatform.Task.csproj" />
        <ProjectReference Include="..\..\02-应用模块\06-Task\01-WorkFlow\IotPlatform.WorkFlow\IotPlatform.WorkFlow.csproj" />
        <ProjectReference Include="..\..\02-应用模块\06-Task\02-ProgramBlock\IotPlatform.ProgramBlock\IotPlatform.ProgramBlock.csproj" />
        <ProjectReference Include="..\..\02-应用模块\07-Thing\01-Warning\IotPlatform.Thing.Warning\IotPlatform.Thing.Warning.csproj" />
        <ProjectReference Include="..\..\02-应用模块\07-Thing\02-StatisticalRule\IotPlatform.Thing.StatisticalRule\IotPlatform.Thing.StatisticalRule.csproj" />
        <ProjectReference Include="..\..\02-应用模块\07-Thing\03-RemoteControl\IotPlatform.Thing.RemoteControl\IotPlatform.Thing.RemoteControl.csproj" />
        <ProjectReference Include="..\..\02-应用模块\07-Thing\04-VideoDevice\IotPlatform.Video\IotPlatform.Video.csproj" />
        <ProjectReference Include="..\..\02-应用模块\08-VisualData\IotPlatform.VisualData\IotPlatform.VisualData.csproj" />
        <ProjectReference Include="..\..\02-应用模块\09-Engine\VisualDev.Engine\VisualDev.Engine.csproj" />
        <ProjectReference Include="..\..\02-应用模块\10-VisualDev\VisualDev\VisualDev.csproj" />
        <ProjectReference Include="..\..\02-应用模块\11-Extend\IotPlatform.Extend\IotPlatform.Extend.csproj" />
        <ProjectReference Include="..\..\02-应用模块\11-Extend\IotPlatform.DongleLicense\IotPlatform.DongleLicense.csproj" />
    </ItemGroup>

</Project>
