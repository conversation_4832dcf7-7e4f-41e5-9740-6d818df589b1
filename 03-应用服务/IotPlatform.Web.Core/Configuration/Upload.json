{
  "$schema": "https://gitee.com/dotnetchina/Furion/raw/v4/schemas/v4/furion-schema.json",
  "Upload": {
    "AllowUploadFileType": "jpg,gif,png,bmp,jpeg,doc,docx,ppt,pptx,xls,xlsx,pdf,txt,rar,zip,csv",
    "Path": "./Upload/",
    // 文件上传目录
    "MaxSize": 512000,
    // 文件最大限制KB：1024*20
    "ContentType": [
      "image/jpg",
      "image/png",
      "image/jpeg",
      "image/gif",
      "image/bmp",
      "text/plain",
      "application/pdf",
      "application/msword",
      "application/json; charset=utf-8",
      "application/vnd.ms-excel",
      "application/vnd.ms-powerpoint",
      "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
      "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
      "video/mp4"
    ],
    "EnableMd5": false
    // 启用文件MDF5验证-防止重复上传
  }
}