{
  "$schema": "https://gitee.com/dotnetchina/Furion/raw/v4/schemas/v4/furion-schema.json",
  "Urls": "http://*:9081",
  // 配置默认端口
  // "https_port": 44325,
  //================== 消息跳转配置 ============================== -->
  "Message": {
    "ApiDoMain": "http://***********:9081", // 后端Api路径  (发布时与DoMainPc一致)
    "DoMainPc": "http://***********:9004", // 前端PC外网能访问的地址(域名), 回调的时候拼接接口地址用
    "DoMainApp": "http://***********:8081", // 前端App外网能访问的地址(域名), 回调的时候拼接接口地址用
    "AppPushUrl": "https://8e84eea8-6922-4033-8e86-67ad7442e692.bspapp.com/unipush"
  },
  "JNPF_App":{
    // 文件预览
    "Domain": "http://iot.***********",
    "KKFileDomain": "http://***********:8012",
    // Aes加密key
    "AesKey": "EY8WePvjM5GGwQzn",
    "BackupPath": "/app/backup/",
    // 系统文件路径
    "SystemPath": "/app/cloudiot/Upload",
    //允许图片类型
    "AllowUploadImageType": [
      "jpg",
      "gif",
      "png",
      "bmp",
      "jpeg",
      "tiff",
      "psd",
      "swf",
      "svg",
      "pcx",
      "dxf",
      "wmf",
      "emf",
      "lic",
      "eps",
      "tga"
    ],
    //允许上传文件类型
    "AllowUploadFileType": [
      "jpg",
      "mp3",
      "gif",
      "png",
      "bmp",
      "jpeg",
      "doc",
      "docx",
      "ppt",
      "pptx",
      "xls",
      "xlsx",
      "pdf",
      "txt",
      "rar",
      "zip",
      "csv",
      "ogm",
      "wmv",
      "asx",
      "mpg",
      "webm",
      "mp4",
      "ogv",
      "mpeg",
      "mov",
      "m4v",
      "avi"
    ],
    //过滤上传文件名称特殊字符
    "SpecialString": [
      "/",
      "<",
      ">",
      "|",
      "?",
      ":",
      "*"
    ]
  },
  // PostgreSQL工具配置
  "PostgreSQL": {
    // pg_dump工具路径（可选，如果不配置将自动检测）
    "PgDumpPath": "",
    // pg_restore工具路径（可选，如果不配置将自动检测）
    "PgRestorePath": "",
    // psql工具路径（可选，如果不配置将自动检测）
    "PsqlPath": "",
    // PostgreSQL容器名称（用于Docker执行备份）
    "ContainerName": "postgresql",
    // PostgreSQL部署模式: "Host" - 宿主机部署, "Container" - 容器部署, "Auto" - 自动检测
    "DeploymentMode": "Host",
    // 当PostgreSQL在宿主机时，是否强制使用本地工具（而不是容器内工具）
    "ForceLocalTools": true,
    // 宿主机PostgreSQL连接配置（当应用在容器中而PostgreSQL在宿主机时使用）
    "HostConnection": {
      // 从容器访问宿主机的地址，通常是 "host.docker.internal" 或宿主机IP
      "HostAddress": "host.docker.internal"
    }
  },
  "AllowedHosts": "*",
  "AppSettings": {
    "InjectSpecificationDocument": true
    // 生产环境是否开启Swagger
  },
  "DynamicApiControllerSettings": {
    //"DefaultRoutePrefix": "api", // 默认路由前缀
    "CamelCaseSeparator": "",
    // 驼峰命名分隔符
    "SplitCamelCase": false,
    // 切割骆驼(驼峰)/帕斯卡命名
    "LowercaseRoute": false,
    // 小写路由格式
    "AsLowerCamelCase": false,
    // 小驼峰命名（首字母小写）
    "KeepVerb": false,
    // 保留动作方法请求谓词
    "KeepName": false
    // 保持原有名称不处理
  },
  "FriendlyExceptionSettings": {
    "DefaultErrorMessage": "系统异常，请联系管理员",
    "ThrowBah": true,
    // 是否将 Oops.Oh 默认抛出为业务异常
    "LogError": false
    // 是否输出异常日志
  },
  "LocalizationSettings": {
    "SupportedCultures": [
      "zh-CN",
      "en"
    ],
    // 语言列表
    "DefaultCulture": "zh-CN",
    // 默认语言
    "DateTimeFormatCulture": "zh-CN"
    // 固定时间区域为特定时区（多语言）
  },
  "CorsAccessorSettings": {
    "WithExposedHeaders": [
      "Content-Disposition",
      "X-Pagination",
      "access-token",
      "x-access-token"
    ],
    // 如果前端不代理且是axios请求
    "SignalRSupport": true
    // 启用 SignalR 跨域支持
  },
  "SnowId": {
    "WorkerId": 1,
    // 机器码 全局唯一
    "WorkerIdBitLength": 2,
    // 机器码位长 默认值6，取值范围 [1, 19]
    "SeqBitLength": 6,
    // 序列数位长 默认值6，取值范围 [3, 21]（建议不小于4，值越大性能越高、Id位数也更长）
    "WorkerPrefix": "cloudiot_"
    // 缓存前缀
  }
}