{
  "$schema": "https://gitee.com/dotnetchina/Furion/raw/v4/schemas/v4/furion-schema.json",
  "SpecificationDocumentSettings": {
    "DocumentTitle": "IotPlatform 开发平台",
    "GroupOpenApiInfos": [
      {
        "Group": "Default",
        "Title": "IotPlatform 开发平台",
        "Description": "",
        "Version": "5.0.0"
      },
      {
        "Group": "All Groups",
        "Title": "所有接口",
        "Version": "5.0.0"
      }
    ],
    "DefaultGroupName": "Default",
    // 默认分组名
    "DocExpansionState": "List",
    // List、Full、None
    "EnableAllGroups": true,
    "LoginInfo": {
      "Enabled": true,
      // 是否开启Swagger登录
      "CheckUrl": "/swagger/checkUrl",
      "SubmitUrl": "/swagger/submitUrl"
    },
    "EnumToNumber": true
    // 枚举类型生成值类型
  }
}