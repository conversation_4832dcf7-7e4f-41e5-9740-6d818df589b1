using System.Text.Json;
using Common.Core.EventBus;
using Common.Core.Job;
using Common.Core.Logging;
using Common.Hubs;
using Common.Options;
using Furion;
using Furion.Logging;
using Furion.VirtualFileServer;
using IotPlatform.Application.Filter;
using IotPlatform.Core;
using IotPlatform.Core.Extension;
using IotPlatform.Web.Core.Extensions;
using IotPlatform.Web.Core.Handlers;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.HttpOverrides;
using Microsoft.AspNetCore.StaticFiles;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using NewLife.Caching;
using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;
using Yitter.IdGenerator;

namespace IotPlatform.Web.Core;

public class Startup : AppStartup
{
    public void ConfigureServices(IServiceCollection services)
    {
        YitIdHelper.SetIdGenerator(new IdGeneratorOptions { WorkerId = 3 });
        // 配置选项
        services.AddProjectOptions();
        // 缓存注册
        services.AddCache();
        // SqlSugar
        services.SqlSugarConfigure();
        // JWT
        services.AddJwt<JwtHandler>(enableGlobalAuthorize: true);
        // 允许跨域
        services.AddCorsAccessor();
        // 远程请求
        services.AddHttpRemote();
        // 任务队列
        services.AddTaskQueue();
        // 任务调度
        services.AddSchedule(options =>
        {
            options.LogEnabled = false;
            options.AddPersistence<DbJobPersistence>(); // 添加作业持久化器
            // options.AddJob<LogJob>(Triggers.Workday());
        });
        // 脱敏检测
        services.AddSensitiveDetection();

        // Json序列化设置
        static void SetNewtonsoftJsonSetting(JsonSerializerSettings setting)
        {
            setting.DateFormatHandling = DateFormatHandling.IsoDateFormat;
            setting.DateTimeZoneHandling = DateTimeZoneHandling.Local;
            setting.DateFormatString = "yyyy-MM-dd HH:mm:ss"; // 时间格式化
            setting.ReferenceLoopHandling = ReferenceLoopHandling.Ignore; // 忽略循环引用
            setting.ContractResolver = new DefaultContractResolver();
            // setting.ContractResolver = new CamelCasePropertyNamesContractResolver(); // 解决动态对象属性名大写
            // setting.NullValueHandling = NullValueHandling.Ignore; // 忽略空值
            // setting.Converters.AddLongTypeConverters(); // long转string（防止js精度溢出） 超过16位开启
            // setting.MetadataPropertyHandling = MetadataPropertyHandling.Ignore; // 解决DateTimeOffset异常
            // setting.DateParseHandling = DateParseHandling.None; // 解决DateTimeOffset异常
            // setting.Converters.Add(new IsoDateTimeConverter { DateTimeStyles = DateTimeStyles.AssumeUniversal }); // 解决DateTimeOffset异常
        }

        #region 标准请求和返回配置

        services.AddControllersWithViews()
            .AddMvcFilter<RequestActionFilter>()
            .AddAppLocalization()
            .AddNewtonsoftJson(options => SetNewtonsoftJsonSetting(options.SerializerSettings))
            //.AddXmlSerializerFormatters()
            //.AddXmlDataContractSerializerFormatters()
            .AddInjectWithUnifyResult();

        services.AddControllers()
            .AddJsonOptions(config =>
            {
                config.JsonSerializerOptions.Converters.AddClayConverters();
                config.JsonSerializerOptions.PropertyNamingPolicy = null;
            });

        #endregion 标准请求和返回配置

        // 配置Nginx转发获取客户端真实IP
        // 注1：如果负载均衡不是在本机通过 Loopback 地址转发请求的，一定要加上options.KnownNetworks.Clear()和options.KnownProxies.Clear()
        // 注2：如果设置环境变量 ASPNETCORE_FORWARDEDHEADERS_ENABLED 为 True，则不需要下面的配置代码
        services.Configure<ForwardedHeadersOptions>(options =>
        {
            options.ForwardedHeaders = ForwardedHeaders.All;
            options.KnownNetworks.Clear();
            options.KnownProxies.Clear();
        });
        // 事件总线
        services.AddEventBus(options =>
        {
            options.UseUtcTimestamp = false;
            // 不启用事件日志
            options.LogEnabled = false;
            // 事件执行器（重试后依然处理未处理异常的处理器）
            options.UnobservedTaskExceptionHandler = (obj, args) =>
            {
                if (args.Exception?.Message != null)
                {
                    Log.Error($"EeventBus 有未处理异常 ：{args.Exception?.Message} ", args.Exception);
                }
            };
            // 事件执行器（失败重试）
            options.AddExecutor<RetryEventHandlerExecutor>();

            #region Redis消息队列

            EventBusOptions config = App.GetOptions<EventBusOptions>();
            if (config.EventBusType == EventBusType.Redis)
            {
                // 替换事件源存储器为Redis
                options.ReplaceStorer(serviceProvider =>
                {
                    ICacheProvider cacheProvider = serviceProvider.GetRequiredService<ICacheProvider>();
                    // 创建默认内存通道事件源对象
                    return new RedisEventSourceStorer(cacheProvider, config);
                });
            }

            #endregion Redis消息队列
        });

        // 即时通讯
        services.AddSignalR();

        // 系统日志
        services.AddLoggingSetup();

        // 控制台logo
        services.AddLogoDisplay();
    }

    public void Configure(IApplicationBuilder app, IWebHostEnvironment env)
    {
        if (env.IsDevelopment())
        {
            app.UseDeveloperExceptionPage();
            app.UseForwardedHeaders();
        }
        else
        {
            app.UseExceptionHandler("/Home/Error");
            app.UseForwardedHeaders();
            app.UseHsts();
        }

        // 添加状态码拦截中间件
        app.UseUnifyResultStatusCodes();

        //// 启用HTTPS
        //app.UseHttpsRedirection();

        // 特定文件类型（文件后缀）处理
        FileExtensionContentTypeProvider contentTypeProvider = FS.GetFileExtensionContentTypeProvider();
        // contentTypeProvider.Mappings[".文件后缀"] = "MIME 类型";
        app.UseStaticFiles(new StaticFileOptions
        {
            ContentTypeProvider = contentTypeProvider
        });
        app.UseRouting();
        //
        app.EnableBuffering();

        app.UseCorsAccessor();

        app.UseAuthentication();
        app.UseAuthorization();

        // 任务调度看板
        app.UseScheduleUI(options =>
        {
            options.DisplayEmptyTriggerJobs = false;
            options.DisplayHead = false;
        });

        // // 配置Swagger-Knife4UI（路由前缀一致代表独立，不同则代表共存）
        // app.UseKnife4UI(options =>
        // {
        //     options.RoutePrefix = "kapi";
        //     foreach (SpecificationOpenApiInfo groupInfo in SpecificationDocumentBuilder.GetOpenApiGroups())
        //     {
        //         options.SwaggerEndpoint("/" + groupInfo.RouteTemplate, groupInfo.Title);
        //     }
        // });

        app.UseInject(string.Empty);

        app.UseEndpoints(endpoints =>
        {
            // 注册集线器
            endpoints.MapHubs();
            endpoints.MapHub<DeviceHub>("/device");
            endpoints.MapControllerRoute(
                "default",
                "{controller=Home}/{action=Index}/{id?}");
        });
    }
}