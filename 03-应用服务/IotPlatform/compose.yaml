services:
  db:
    image: postgres:15.3
    container_name: postgresql
    restart: always
    volumes:
      - /home/<USER>/postgresql:/var/lib/postgresql/data
      - /app/backup:/tmp/backup  # 添加备份目录映射，方便文件传输
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: "Fh@201001."
      POSTGRES_DB: postgres
      TZ: Asia/Shanghai
      POSTGRES_INITDB_ARGS: "--encoding=UTF8"
    ports:
      - 5432:5432
  redis:
    image: redis
    container_name: redis
    restart: always
    volumes:
      - /home/<USER>/redis:/data
      - ./config/redis/redis.conf:/etc/redis/redis.conf
    ports:
      - 6379:6379
    command:
      redis-server /etc/redis/redis.conf --requirepass Fh@201001. --appendonly yes
  
  fengiot:
    image: fengiot/cloud
    container_name: cloudiotv1
    build:
      context: /app/cloudiot/
      dockerfile: /app/cloudiot/Dockerfile
    restart: always
    depends_on:
      - db
      - redis
    volumes:
      - /app/cloudiot/:/app/cloudiot/
      - /app/Upload/:/app/Upload/
    ports:
      - "9081:9081"
    links:
      - db
      - redis
    environment:
      TZ: Asia/Shanghai
