#See https://aka.ms/customizecontainer to learn how to customize your debug container and how Visual Studio uses this Dockerfile to build your images for faster debugging.

#FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
FROM mcr.microsoft.com/dotnet/aspnet:8.0
WORKDIR /app/cloudiot
EXPOSE 80
EXPOSE 443
EXPOSE 9081

COPY . .
#TDengine连接客户端
COPY TDengine-client-3.3.6.9 /app/TDengine-client-3.3.6.9
RUN  chmod -R 777 /app/TDengine-client-3.3.6.9

# 直接执行TDengine安装指令（替代原脚本）
RUN cd /app/TDengine-client-3.3.6.9 && ./install_client.sh

ENTRYPOINT ["dotnet", "IotPlatform.dll"]