var db = {
    init: function (type, ip, port, dbName, pwd, uid) {
        let accumulatedSql = ""
        database.GetConnectoin(type, ip, port, dbName, pwd, uid)
        const api = {
            select: function (sql = accumulatedSql) {
                accumulatedSql = ""
                return JSON.parse(database.Select(sql))
            },
            select_single: function (sql = accumulatedSql) {
                accumulatedSql = ""
                return JSON.parse(database.SelectSingleLine(sql))
            },
            insert: function (table, dictionary) {
                accumulatedSql = ""
                return JSON.parse(database.Insert(table, dictionary))
            },
            update: function (table, dictionary, whereDictionary) {
                accumulatedSql = ""
                return JSON.parse(database.Update(table, dictionary, whereDictionary))
            },
            delete: function (table, dictionary) {
                accumulatedSql = ""
                return JSON.parse(database.Delete(table, dictionary))
            },
            execute: function (sql = accumulatedSql) {
                accumulatedSql = ""
                return database.Execute(sql)
            },
            appendSql: function (sql, param) {
                let formattedParam = param
                if (param === null || param === undefined) {
                    formattedParam = 'NULL'
                } else if (typeof param === 'string') {
                    formattedParam = "'" + param.replace(/'/g, "''") + "'"
                } else if (param instanceof Date) {
                    const year = param.getFullYear()
                    const month = String(param.getMonth() + 1).padStart(2, '0')
                    const day = String(param.getDate()).padStart(2, '0')
                    const hours = String(param.getHours()).padStart(2, '0')
                    const minutes = String(param.getMinutes()).padStart(2, '0')
                    const seconds = String(param.getSeconds()).padStart(2, '0')
                    formattedParam = `'${year}-${month}-${day} ${hours}:${minutes}:${seconds}'`
                } else if (typeof param === 'boolean') {
                    formattedParam = param ? 1 : 0
                }
                accumulatedSql += sql.replace(/\?/, formattedParam)
                return api
            },
            console: function () {
                return accumulatedSql
            },
        }
        return api
    },
    connect: function (uid) {
        let accumulatedSql = ""
        database.Connect(uid)
        const api = {
            select: function (sql = accumulatedSql) {
                accumulatedSql = ""
                return JSON.parse(database.Select(sql))
            },
            select_single: function (sql = accumulatedSql) {
                accumulatedSql = ""
                return JSON.parse(database.SelectSingleLine(sql))
            },
            insert: function (table, dictionary) {
                accumulatedSql = ""
                return JSON.parse(database.Insert(table, dictionary))
            },
            update: function (table, dictionary, whereDictionary) {
                accumulatedSql = ""
                return JSON.parse(database.Update(table, dictionary, whereDictionary))
            },
            delete: function (table, dictionary) {
                accumulatedSql = ""
                return JSON.parse(database.Delete(table, dictionary))
            },
            execute: function (sql = accumulatedSql) {
                accumulatedSql = ""
                return database.Execute(sql)
            },
            appendSql: function (sql, param) {
                let formattedParam = param
                if (param === null || param === undefined) {
                    formattedParam = 'NULL'
                } else if (typeof param === 'string') {
                    formattedParam = "'" + param.replace(/'/g, "''") + "'"
                } else if (param instanceof Date) {
                    const year = param.getFullYear()
                    const month = String(param.getMonth() + 1).padStart(2, '0')
                    const day = String(param.getDate()).padStart(2, '0')
                    const hours = String(param.getHours()).padStart(2, '0')
                    const minutes = String(param.getMinutes()).padStart(2, '0')
                    const seconds = String(param.getSeconds()).padStart(2, '0')
                    formattedParam = `'${year}-${month}-${day} ${hours}:${minutes}:${seconds}'`
                } else if (typeof param === 'boolean') {
                    formattedParam = param ? 1 : 0
                }
                accumulatedSql += sql.replace(/\?/, formattedParam)
                return api
            },
            console: function () {
                return accumulatedSql
            },
        }
        return api
    },
    current: function () {
        let accumulatedSql = ""
        const api = {
            select: function (sql = accumulatedSql) {
                accumulatedSql = ""
                return JSON.parse(currentLocal.Select(sql))
            },
            select_single: function (sql = accumulatedSql) {
                accumulatedSql = ""
                return JSON.parse(currentLocal.SelectSingleLine(sql))
            },
            insert: function (table, dictionary) {
                accumulatedSql = ""
                return JSON.parse(currentLocal.Insert(table, dictionary))
            },
            update: function (table, dictionary, whereDictionary) {
                accumulatedSql = ""
                return JSON.parse(currentLocal.Update(table, dictionary, whereDictionary))
            },
            delete: function (table, dictionary) {
                accumulatedSql = ""
                return JSON.parse(currentLocal.Delete(table, dictionary))
            },
            execute: function (sql = accumulatedSql) {
                accumulatedSql = ""
                return currentLocal.Execute(sql)
            },
            appendSql: function (sql, param) {
                let formattedParam = param
                if (param === null || param === undefined) {
                    formattedParam = 'NULL'
                } else if (typeof param === 'string') {
                    formattedParam = "'" + param.replace(/'/g, "''") + "'"
                } else if (param instanceof Date) {
                    const year = param.getFullYear()
                    const month = String(param.getMonth() + 1).padStart(2, '0')
                    const day = String(param.getDate()).padStart(2, '0')
                    const hours = String(param.getHours()).padStart(2, '0')
                    const minutes = String(param.getMinutes()).padStart(2, '0')
                    const seconds = String(param.getSeconds()).padStart(2, '0')
                    formattedParam = `'${year}-${month}-${day} ${hours}:${minutes}:${seconds}'`
                } else if (typeof param === 'boolean') {
                    formattedParam = param ? 1 : 0
                }
                accumulatedSql += sql.replace(/\?/, formattedParam)
                return api
            },
            console: function () {
                return accumulatedSql
            },
        }
        return api
    },
}
