var request = {
    get: function (params) {
        var res = http.Get(JSON.stringify(params));
        try {
            var parse = JSON.parse(res);
            return this.isObj(parse) ? parse : res
        } catch (e) {
            return "";
        }

    },
    post: function (data) {
        var res = http.Post(JSON.stringify(data));
        try {
            var parse = JSON.parse(res);
            return this.isObj(parse) ? parse : res
        } catch (e) {
            return "";
        }
    },
    isObj: function (val) {
        return ["Array", "Object"].includes(Object.prototype.toString.call(val).slice(8, -1))
    }
};