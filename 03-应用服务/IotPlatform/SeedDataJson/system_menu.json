{"RECORDS": [{"Id": "31518262738949", "Pid": "400756744691909", "Pids": "[0],[400756744691909],", "Name": "差异日志", "Code": "diff-log", "Type": "1", "Icon": "", "Router": "/log/difflog", "Component": "log/difflog", "Permission": "", "Application": "back", "OpenType": "1", "Visible": "1", "Link": "", "Redirect": "", "Weight": "1", "Sort": "100", "Remark": "", "Status": "1", "CreatedTime": "13/6/2024 16:21:45", "UpdatedTime": "15/10/2024 15:55:44", "CreatedUserId": "1300000000101", "CreatedUserName": "超级管理员", "UpdatedUserId": "", "UpdatedUserName": "", "F_MODULE_ID": ""}, {"Id": "31518275928325", "Pid": "0", "Pids": "[0],", "Name": "租户管理", "Code": "tenant", "Type": "1", "Icon": "usergroup-add", "Router": "/authority/tenant", "Component": "authority/tenant", "Permission": "", "Application": "authority", "OpenType": "1", "Visible": "1", "Link": "", "Redirect": "", "Weight": "1", "Sort": "100", "Remark": "", "Status": "1", "CreatedTime": "13/6/2024 16:21:45", "UpdatedTime": "15/10/2024 15:55:44", "CreatedUserId": "1300000000101", "CreatedUserName": "超级管理员", "UpdatedUserId": "", "UpdatedUserName": "", "F_MODULE_ID": ""}, {"Id": "265078079512645", "Pid": "0", "Pids": "[0],", "Name": "任务定义", "Code": "task", "Type": "0", "Icon": "file-protect", "Router": "/task", "Component": "", "Permission": "", "Application": "iotworks", "OpenType": "0", "Visible": "1", "Link": "", "Redirect": "/task/calendar", "Weight": "1", "Sort": "100", "Remark": "", "Status": "1", "CreatedTime": "13/6/2024 16:21:45", "UpdatedTime": "15/10/2024 15:55:44", "CreatedUserId": "", "CreatedUserName": "", "UpdatedUserId": "", "UpdatedUserName": "", "F_MODULE_ID": ""}, {"Id": "265078225690693", "Pid": "265078079512645", "Pids": "[0],[265078079512645],", "Name": "任务日历", "Code": "calendar", "Type": "1", "Icon": "", "Router": "/task/calendar", "Component": "task/calendar", "Permission": "", "Application": "iotworks", "OpenType": "1", "Visible": "1", "Link": "", "Redirect": "", "Weight": "1", "Sort": "100", "Remark": "", "Status": "1", "CreatedTime": "13/6/2024 16:21:45", "UpdatedTime": "15/10/2024 15:55:44", "CreatedUserId": "", "CreatedUserName": "", "UpdatedUserId": "", "UpdatedUserName": "", "F_MODULE_ID": ""}, {"Id": "265078307254341", "Pid": "265078079512645", "Pids": "[0],[265078079512645],", "Name": "任务配置", "Code": "configure", "Type": "1", "Icon": "", "Router": "/task/configure", "Component": "task/configure", "Permission": "", "Application": "iotworks", "OpenType": "1", "Visible": "1", "Link": "", "Redirect": "", "Weight": "1", "Sort": "100", "Remark": "", "Status": "1", "CreatedTime": "13/6/2024 16:21:45", "UpdatedTime": "15/10/2024 15:55:44", "CreatedUserId": "", "CreatedUserName": "", "UpdatedUserId": "", "UpdatedUserName": "", "F_MODULE_ID": ""}, {"Id": "265078529916997", "Pid": "265078079512645", "Pids": "[0],[265078079512645],", "Name": "任务流", "Code": "flow", "Type": "1", "Icon": "", "Router": "/task/flow", "Component": "task/flow", "Permission": "", "Application": "iotworks", "OpenType": "1", "Visible": "1", "Link": "", "Redirect": "", "Weight": "1", "Sort": "100", "Remark": "", "Status": "1", "CreatedTime": "13/6/2024 16:21:45", "UpdatedTime": "15/10/2024 15:55:44", "CreatedUserId": "", "CreatedUserName": "", "UpdatedUserId": "", "UpdatedUserName": "", "F_MODULE_ID": ""}, {"Id": "265078902702149", "Pid": "0", "Pids": "[0],", "Name": "可视化管理", "Code": "dashboard", "Type": "0", "Icon": "dashboard", "Router": "/dashboard", "Component": "", "Permission": "", "Application": "cms", "OpenType": "0", "Visible": "1", "Link": "", "Redirect": "/dashboard/data-view", "Weight": "1", "Sort": "100", "Remark": "", "Status": "1", "CreatedTime": "13/6/2024 16:21:45", "UpdatedTime": "15/10/2024 15:55:44", "CreatedUserId": "", "CreatedUserName": "", "UpdatedUserId": "", "UpdatedUserName": "", "F_MODULE_ID": ""}, {"Id": "265078983860293", "Pid": "265078902702149", "Pids": "[0],[265078902702149],", "Name": "大屏设计", "Code": "bigScreen", "Type": "1", "Icon": "", "Router": "/dashboard/big-screen", "Component": "dashboard/big-screen", "Permission": "", "Application": "cms", "OpenType": "1", "Visible": "1", "Link": "", "Redirect": "", "Weight": "1", "Sort": "100", "Remark": "", "Status": "1", "CreatedTime": "13/6/2024 16:21:45", "UpdatedTime": "15/10/2024 15:55:44", "CreatedUserId": "", "CreatedUserName": "", "UpdatedUserId": "", "UpdatedUserName": "", "F_MODULE_ID": ""}, {"Id": "318919179276485", "Pid": "0", "Pids": "[0],", "Name": "数据统计", "Code": "statistics", "Type": "0", "Icon": "bar-chart", "Router": "/statistics", "Component": "", "Permission": "", "Application": "cms", "OpenType": "0", "Visible": "1", "Link": "", "Redirect": "", "Weight": "1", "Sort": "3", "Remark": "", "Status": "1", "CreatedTime": "13/6/2024 16:21:45", "UpdatedTime": "15/10/2024 15:55:44", "CreatedUserId": "", "CreatedUserName": "", "UpdatedUserId": "", "UpdatedUserName": "", "F_MODULE_ID": ""}, {"Id": "318919460937925", "Pid": "318919179276485", "Pids": "[0],[318919179276485],", "Name": "趋势分析器", "Code": "trend-analyzer", "Type": "1", "Icon": "", "Router": "/statistics/trend-analyzer", "Component": "statistics/trend-analyzer", "Permission": "", "Application": "cms", "OpenType": "1", "Visible": "1", "Link": "", "Redirect": "", "Weight": "1", "Sort": "100", "Remark": "", "Status": "1", "CreatedTime": "13/6/2024 16:21:45", "UpdatedTime": "15/10/2024 15:55:44", "CreatedUserId": "", "CreatedUserName": "", "UpdatedUserId": "", "UpdatedUserName": "", "F_MODULE_ID": ""}, {"Id": "340515074896069", "Pid": "0", "Pids": "[0],", "Name": "数据应用", "Code": "data-application", "Type": "0", "Icon": "fund", "Router": "/data-application", "Component": "", "Permission": "", "Application": "iotworks", "OpenType": "0", "Visible": "1", "Link": "", "Redirect": "", "Weight": "1", "Sort": "100", "Remark": "", "Status": "1", "CreatedTime": "13/6/2024 16:21:45", "UpdatedTime": "15/10/2024 15:55:44", "CreatedUserId": "", "CreatedUserName": "", "UpdatedUserId": "", "UpdatedUserName": "", "F_MODULE_ID": ""}, {"Id": "343331267989701", "Pid": "0", "Pids": "[0],", "Name": "物", "Code": "thing", "Type": "0", "Icon": "pic-center", "Router": "/thing", "Component": "", "Permission": "", "Application": "cms", "OpenType": "0", "Visible": "1", "Link": "", "Redirect": "", "Weight": "1", "Sort": "2", "Remark": "", "Status": "1", "CreatedTime": "13/6/2024 16:21:45", "UpdatedTime": "15/10/2024 15:55:44", "CreatedUserId": "", "CreatedUserName": "", "UpdatedUserId": "", "UpdatedUserName": "", "F_MODULE_ID": ""}, {"Id": "343331576471749", "Pid": "343331267989701", "Pids": "[0],[343331267989701],", "Name": "物模型", "Code": "thing-model", "Type": "1", "Icon": "", "Router": "/thing/thing-model", "Component": "thing/thing-model", "Permission": "", "Application": "cms", "OpenType": "1", "Visible": "1", "Link": "", "Redirect": "", "Weight": "1", "Sort": "100", "Remark": "", "Status": "1", "CreatedTime": "13/6/2024 16:21:45", "UpdatedTime": "15/10/2024 15:55:44", "CreatedUserId": "", "CreatedUserName": "", "UpdatedUserId": "", "UpdatedUserName": "", "F_MODULE_ID": ""}, {"Id": "343331769192645", "Pid": "343331267989701", "Pids": "[0],[343331267989701],", "Name": "物实例", "Code": "thing-example", "Type": "1", "Icon": "", "Router": "/thing/thing-example", "Component": "thing/thing-example", "Permission": "", "Application": "cms", "OpenType": "1", "Visible": "1", "Link": "", "Redirect": "", "Weight": "1", "Sort": "100", "Remark": "", "Status": "1", "CreatedTime": "13/6/2024 16:21:45", "UpdatedTime": "15/10/2024 15:55:44", "CreatedUserId": "", "CreatedUserName": "", "UpdatedUserId": "", "UpdatedUserName": "", "F_MODULE_ID": ""}, {"Id": "343332023390405", "Pid": "343331267989701", "Pids": "[0],[343331267989701],", "Name": "模型模板库", "Code": "model-template", "Type": "1", "Icon": "", "Router": "/thing/model-template", "Component": "thing/model-template", "Permission": "", "Application": "cms", "OpenType": "1", "Visible": "1", "Link": "", "Redirect": "", "Weight": "1", "Sort": "100", "Remark": "", "Status": "1", "CreatedTime": "13/6/2024 16:21:45", "UpdatedTime": "15/10/2024 15:55:44", "CreatedUserId": "", "CreatedUserName": "", "UpdatedUserId": "", "UpdatedUserName": "", "F_MODULE_ID": ""}, {"Id": "347269321015493", "Pid": "340515074896069", "Pids": "[0],[340515074896069],", "Name": "数据连接", "Code": "data-link", "Type": "1", "Icon": "", "Router": "/data-application/data-link", "Component": "data-application/data-link", "Permission": "", "Application": "iotworks", "OpenType": "1", "Visible": "1", "Link": "", "Redirect": "", "Weight": "1", "Sort": "100", "Remark": "", "Status": "1", "CreatedTime": "13/6/2024 16:21:45", "UpdatedTime": "15/10/2024 15:55:44", "CreatedUserId": "", "CreatedUserName": "", "UpdatedUserId": "", "UpdatedUserName": "", "F_MODULE_ID": ""}, {"Id": "400756296605893", "Pid": "0", "Pids": "[0],", "Name": "系统管理", "Code": "system", "Type": "0", "Icon": "setting", "Router": "/system", "Component": "", "Permission": "", "Application": "back", "OpenType": "0", "Visible": "1", "Link": "", "Redirect": "", "Weight": "1", "Sort": "100", "Remark": "", "Status": "1", "CreatedTime": "13/6/2024 16:21:45", "UpdatedTime": "15/10/2024 15:55:44", "CreatedUserId": "", "CreatedUserName": "", "UpdatedUserId": "", "UpdatedUserName": "", "F_MODULE_ID": ""}, {"Id": "400756526760133", "Pid": "400756296605893", "Pids": "[0],[400756296605893],", "Name": "菜单管理", "Code": "system-menu", "Type": "1", "Icon": "", "Router": "/system/menu", "Component": "system/menu", "Permission": "", "Application": "back", "OpenType": "1", "Visible": "1", "Link": "", "Redirect": "", "Weight": "1", "Sort": "100", "Remark": "", "Status": "1", "CreatedTime": "13/6/2024 16:21:45", "UpdatedTime": "15/10/2024 15:55:44", "CreatedUserId": "", "CreatedUserName": "", "UpdatedUserId": "", "UpdatedUserName": "", "F_MODULE_ID": ""}, {"Id": "400756744691909", "Pid": "0", "Pids": "[0],", "Name": "日志管理", "Code": "log", "Type": "0", "Icon": "read", "Router": "/log", "Component": "", "Permission": "", "Application": "back", "OpenType": "0", "Visible": "1", "Link": "", "Redirect": "", "Weight": "1", "Sort": "100", "Remark": "", "Status": "1", "CreatedTime": "13/6/2024 16:21:45", "UpdatedTime": "15/10/2024 15:55:44", "CreatedUserId": "", "CreatedUserName": "", "UpdatedUserId": "", "UpdatedUserName": "", "F_MODULE_ID": ""}, {"Id": "400756944064709", "Pid": "400756744691909", "Pids": "[0],[400756744691909],", "Name": "异常日志", "Code": "exlog", "Type": "1", "Icon": "", "Router": "/log/exlog", "Component": "log/exlog", "Permission": "", "Application": "back", "OpenType": "1", "Visible": "1", "Link": "", "Redirect": "", "Weight": "1", "Sort": "100", "Remark": "", "Status": "1", "CreatedTime": "13/6/2024 16:21:45", "UpdatedTime": "15/10/2024 15:55:44", "CreatedUserId": "", "CreatedUserName": "", "UpdatedUserId": "", "UpdatedUserName": "", "F_MODULE_ID": ""}, {"Id": "400757033369797", "Pid": "400756744691909", "Pids": "[0],[400756744691909],", "Name": "访问日志", "Code": "vislog", "Type": "1", "Icon": "", "Router": "/log/vislog", "Component": "log/vislog", "Permission": "", "Application": "back", "OpenType": "1", "Visible": "1", "Link": "", "Redirect": "", "Weight": "1", "Sort": "100", "Remark": "", "Status": "1", "CreatedTime": "13/6/2024 16:21:45", "UpdatedTime": "15/10/2024 15:55:44", "CreatedUserId": "", "CreatedUserName": "", "UpdatedUserId": "", "UpdatedUserName": "", "F_MODULE_ID": ""}, {"Id": "400757105782981", "Pid": "400756744691909", "Pids": "[0],[400756744691909],", "Name": "操作日志", "Code": "oplog", "Type": "1", "Icon": "", "Router": "/log/oplog", "Component": "log/oplog", "Permission": "", "Application": "back", "OpenType": "1", "Visible": "1", "Link": "", "Redirect": "", "Weight": "1", "Sort": "100", "Remark": "", "Status": "1", "CreatedTime": "13/6/2024 16:21:45", "UpdatedTime": "15/10/2024 15:55:44", "CreatedUserId": "", "CreatedUserName": "", "UpdatedUserId": "", "UpdatedUserName": "", "F_MODULE_ID": ""}, {"Id": "405938148200645", "Pid": "318919179276485", "Pids": "[0],[318919179276485],", "Name": "数据查看", "Code": "data-view", "Type": "1", "Icon": "", "Router": "/statistics/data-view", "Component": "statistics/data-view", "Permission": "", "Application": "cms", "OpenType": "1", "Visible": "1", "Link": "", "Redirect": "", "Weight": "1", "Sort": "98", "Remark": "", "Status": "1", "CreatedTime": "13/6/2024 16:21:45", "UpdatedTime": "15/10/2024 15:55:44", "CreatedUserId": "", "CreatedUserName": "", "UpdatedUserId": "", "UpdatedUserName": "", "F_MODULE_ID": ""}, {"Id": "437426586763461", "Pid": "0", "Pids": "[0],", "Name": "备份管理", "Code": "backup", "Type": "0", "Icon": "database", "Router": "/backup", "Component": "", "Permission": "", "Application": "back", "OpenType": "0", "Visible": "1", "Link": "", "Redirect": "", "Weight": "1", "Sort": "100", "Remark": "", "Status": "1", "CreatedTime": "13/6/2024 16:21:45", "UpdatedTime": "15/10/2024 15:55:44", "CreatedUserId": "", "CreatedUserName": "", "UpdatedUserId": "", "UpdatedUserName": "", "F_MODULE_ID": ""}, {"Id": "437426834989253", "Pid": "437426586763461", "Pids": "[0],[437426586763461],", "Name": "存储管理", "Code": "storage-manage", "Type": "1", "Icon": "", "Router": "/backup/storage", "Component": "backup/storage", "Permission": "", "Application": "back", "OpenType": "1", "Visible": "1", "Link": "", "Redirect": "", "Weight": "1", "Sort": "100", "Remark": "", "Status": "1", "CreatedTime": "13/6/2024 16:21:45", "UpdatedTime": "15/10/2024 15:55:44", "CreatedUserId": "", "CreatedUserName": "", "UpdatedUserId": "", "UpdatedUserName": "", "F_MODULE_ID": ""}, {"Id": "437427081531589", "Pid": "437426586763461", "Pids": "[0],[437426586763461],", "Name": "数据备份", "Code": "data-backup", "Type": "1", "Icon": "", "Router": "/backup/data-backup", "Component": "backup/data-backup", "Permission": "", "Application": "back", "OpenType": "1", "Visible": "1", "Link": "", "Redirect": "", "Weight": "1", "Sort": "100", "Remark": "", "Status": "1", "CreatedTime": "13/6/2024 16:21:45", "UpdatedTime": "15/10/2024 15:55:44", "CreatedUserId": "", "CreatedUserName": "", "UpdatedUserId": "", "UpdatedUserName": "", "F_MODULE_ID": ""}, {"Id": "437427202363589", "Pid": "437426586763461", "Pids": "[0],[437426586763461],", "Name": "数据恢复", "Code": "data-recovery", "Type": "1", "Icon": "", "Router": "/backup/data-recovery", "Component": "backup/data-recovery", "Permission": "", "Application": "back", "OpenType": "1", "Visible": "1", "Link": "", "Redirect": "", "Weight": "1", "Sort": "100", "Remark": "", "Status": "1", "CreatedTime": "13/6/2024 16:21:45", "UpdatedTime": "15/10/2024 15:55:44", "CreatedUserId": "", "CreatedUserName": "", "UpdatedUserId": "", "UpdatedUserName": "", "F_MODULE_ID": ""}, {"Id": "441016065081541", "Pid": "0", "Pids": "[0],", "Name": "对接配置", "Code": "abutment", "Type": "0", "Icon": "cloud", "Router": "/abutment", "Component": "", "Permission": "", "Application": "back", "OpenType": "0", "Visible": "1", "Link": "", "Redirect": "", "Weight": "1", "Sort": "100", "Remark": "", "Status": "1", "CreatedTime": "13/6/2024 16:21:45", "UpdatedTime": "15/10/2024 15:55:44", "CreatedUserId": "", "CreatedUserName": "", "UpdatedUserId": "", "UpdatedUserName": "", "F_MODULE_ID": ""}, {"Id": "441016236593349", "Pid": "441016065081541", "Pids": "[0],[441016065081541],", "Name": "开放API", "Code": "open-api", "Type": "1", "Icon": "", "Router": "/abutment/open-api", "Component": "abutment/open-api", "Permission": "", "Application": "back", "OpenType": "1", "Visible": "1", "Link": "", "Redirect": "", "Weight": "1", "Sort": "100", "Remark": "", "Status": "1", "CreatedTime": "13/6/2024 16:21:45", "UpdatedTime": "15/10/2024 15:55:44", "CreatedUserId": "", "CreatedUserName": "", "UpdatedUserId": "", "UpdatedUserName": "", "F_MODULE_ID": ""}, {"Id": "460435590893765", "Pid": "0", "Pids": "[0],", "Name": "物资源总览", "Code": "thing-dashboard", "Type": "1", "Icon": "pie-chart", "Router": "/thing/dashboard", "Component": "thing/dashboard", "Permission": "", "Application": "cms", "OpenType": "1", "Visible": "1", "Link": "", "Redirect": "", "Weight": "1", "Sort": "1", "Remark": "", "Status": "1", "CreatedTime": "13/6/2024 16:21:45", "UpdatedTime": "15/10/2024 15:55:44", "CreatedUserId": "", "CreatedUserName": "", "UpdatedUserId": "", "UpdatedUserName": "", "F_MODULE_ID": ""}, {"Id": "461230143656133", "Pid": "0", "Pids": "[0],", "Name": "报警管理", "Code": "alarm-manage", "Type": "0", "Icon": "alert", "Router": "/alarm-manage", "Component": "", "Permission": "", "Application": "cms", "OpenType": "0", "Visible": "1", "Link": "", "Redirect": "", "Weight": "1", "Sort": "6", "Remark": "", "Status": "1", "CreatedTime": "13/6/2024 16:21:45", "UpdatedTime": "15/10/2024 15:55:44", "CreatedUserId": "", "CreatedUserName": "", "UpdatedUserId": "", "UpdatedUserName": "", "F_MODULE_ID": ""}, {"Id": "461230285488325", "Pid": "461230143656133", "Pids": "[0],[461230143656133],", "Name": "报警消息", "Code": "alarm-list", "Type": "1", "Icon": "", "Router": "/alarm-manage/alarm-list", "Component": "alarm-manage/alarm-list", "Permission": "", "Application": "cms", "OpenType": "1", "Visible": "1", "Link": "", "Redirect": "", "Weight": "1", "Sort": "100", "Remark": "", "Status": "1", "CreatedTime": "13/6/2024 16:21:45", "UpdatedTime": "15/10/2024 15:55:44", "CreatedUserId": "", "CreatedUserName": "", "UpdatedUserId": "", "UpdatedUserName": "", "F_MODULE_ID": ""}, {"Id": "461231655665861", "Pid": "0", "Pids": "[0],", "Name": "数据管理", "Code": "data-manage", "Type": "0", "Icon": "partition", "Router": "/data-manage", "Component": "", "Permission": "", "Application": "idf", "OpenType": "0", "Visible": "1", "Link": "", "Redirect": "", "Weight": "1", "Sort": "100", "Remark": "", "Status": "1", "CreatedTime": "13/6/2024 16:21:45", "UpdatedTime": "15/10/2024 15:55:44", "CreatedUserId": "", "CreatedUserName": "", "UpdatedUserId": "", "UpdatedUserName": "", "F_MODULE_ID": ""}, {"Id": "461231981719749", "Pid": "0", "Pids": "[0],", "Name": "建模", "Code": "data-model", "Type": "0", "Icon": "partition", "Router": "/data-model", "Component": "", "Permission": "", "Application": "idf", "OpenType": "0", "Visible": "1", "Link": "", "Redirect": "", "Weight": "1", "Sort": "100", "Remark": "", "Status": "1", "CreatedTime": "13/6/2024 16:21:45", "UpdatedTime": "15/10/2024 15:55:44", "CreatedUserId": "", "CreatedUserName": "", "UpdatedUserId": "", "UpdatedUserName": "", "F_MODULE_ID": ""}, {"Id": "461232278253765", "Pid": "461231655665861", "Pids": "[0],[461231655665861],", "Name": "业务数据", "Code": "business-data", "Type": "1", "Icon": "", "Router": "/data-manage/business-data", "Component": "data-manage/business-data", "Permission": "", "Application": "idf", "OpenType": "1", "Visible": "1", "Link": "", "Redirect": "", "Weight": "1", "Sort": "100", "Remark": "", "Status": "1", "CreatedTime": "13/6/2024 16:21:45", "UpdatedTime": "15/10/2024 15:55:44", "CreatedUserId": "", "CreatedUserName": "", "UpdatedUserId": "", "UpdatedUserName": "", "F_MODULE_ID": ""}, {"Id": "461232489201861", "Pid": "461231981719749", "Pids": "[0],[461231981719749],", "Name": "数据建模", "Code": "configure-list", "Type": "1", "Icon": "", "Router": "/data-model/list", "Component": "data-model/list", "Permission": "", "Application": "idf", "OpenType": "1", "Visible": "1", "Link": "", "Redirect": "", "Weight": "1", "Sort": "100", "Remark": "", "Status": "1", "CreatedTime": "13/6/2024 16:21:45", "UpdatedTime": "15/10/2024 15:55:44", "CreatedUserId": "", "CreatedUserName": "", "UpdatedUserId": "", "UpdatedUserName": "", "F_MODULE_ID": ""}, {"Id": "461233482997957", "Pid": "461231655665861", "Pids": "[0],[461231655665861],", "Name": "字典数据", "Code": "dict-data", "Type": "1", "Icon": "", "Router": "/data-manage/dict-data", "Component": "data-manage/dict-data", "Permission": "", "Application": "idf", "OpenType": "1", "Visible": "1", "Link": "", "Redirect": "", "Weight": "1", "Sort": "100", "Remark": "", "Status": "1", "CreatedTime": "13/6/2024 16:21:45", "UpdatedTime": "15/10/2024 15:55:44", "CreatedUserId": "", "CreatedUserName": "", "UpdatedUserId": "", "UpdatedUserName": "", "F_MODULE_ID": ""}, {"Id": "461233644998853", "Pid": "461231981719749", "Pids": "[0],[461231981719749],", "Name": "维度管理", "Code": "dimensions", "Type": "1", "Icon": "", "Router": "/data-model/dimensions", "Component": "data-model/dimensions", "Permission": "", "Application": "idf", "OpenType": "1", "Visible": "1", "Link": "", "Redirect": "", "Weight": "1", "Sort": "100", "Remark": "", "Status": "1", "CreatedTime": "13/6/2024 16:21:45", "UpdatedTime": "15/10/2024 15:55:44", "CreatedUserId": "", "CreatedUserName": "", "UpdatedUserId": "", "UpdatedUserName": "", "F_MODULE_ID": ""}, {"Id": "462267096289477", "Pid": "0", "Pids": "[0],", "Name": "组织机构", "Code": "organization", "Type": "1", "Icon": "deployment-unit", "Router": "/authority/organization", "Component": "authority/organization", "Permission": "", "Application": "authority", "OpenType": "1", "Visible": "1", "Link": "", "Redirect": "", "Weight": "1", "Sort": "100", "Remark": "", "Status": "1", "CreatedTime": "13/6/2024 16:21:45", "UpdatedTime": "15/10/2024 15:55:44", "CreatedUserId": "", "CreatedUserName": "", "UpdatedUserId": "", "UpdatedUserName": "", "F_MODULE_ID": ""}, {"Id": "462267194294469", "Pid": "0", "Pids": "[0],", "Name": "角色管理", "Code": "role", "Type": "1", "Icon": "team", "Router": "/authority/role", "Component": "authority/role", "Permission": "", "Application": "authority", "OpenType": "1", "Visible": "1", "Link": "", "Redirect": "", "Weight": "1", "Sort": "100", "Remark": "", "Status": "1", "CreatedTime": "13/6/2024 16:21:45", "UpdatedTime": "15/10/2024 15:55:44", "CreatedUserId": "", "CreatedUserName": "", "UpdatedUserId": "", "UpdatedUserName": "", "F_MODULE_ID": ""}, {"Id": "462267273044165", "Pid": "0", "Pids": "[0],", "Name": "用户管理", "Code": "user", "Type": "1", "Icon": "user", "Router": "/authority/user", "Component": "authority/user", "Permission": "", "Application": "authority", "OpenType": "1", "Visible": "1", "Link": "", "Redirect": "", "Weight": "1", "Sort": "100", "Remark": "", "Status": "1", "CreatedTime": "13/6/2024 16:21:45", "UpdatedTime": "15/10/2024 15:55:44", "CreatedUserId": "", "CreatedUserName": "", "UpdatedUserId": "", "UpdatedUserName": "", "F_MODULE_ID": ""}, {"Id": "462315708022981", "Pid": "0", "Pids": "[0],", "Name": "消息策略", "Code": "message-strategy", "Type": "1", "Icon": "send", "Router": "/message/strategy", "Component": "message/strategy", "Permission": "", "Application": "mcs", "OpenType": "1", "Visible": "1", "Link": "", "Redirect": "", "Weight": "1", "Sort": "100", "Remark": "", "Status": "1", "CreatedTime": "13/6/2024 16:21:45", "UpdatedTime": "15/10/2024 15:55:44", "CreatedUserId": "", "CreatedUserName": "", "UpdatedUserId": "", "UpdatedUserName": "", "F_MODULE_ID": ""}, {"Id": "462315810865349", "Pid": "0", "Pids": "[0],", "Name": "消息模板", "Code": "message-template", "Type": "1", "Icon": "file-text", "Router": "/message/template", "Component": "message/template", "Permission": "", "Application": "mcs", "OpenType": "1", "Visible": "1", "Link": "", "Redirect": "", "Weight": "1", "Sort": "100", "Remark": "", "Status": "1", "CreatedTime": "13/6/2024 16:21:45", "UpdatedTime": "15/10/2024 15:55:44", "CreatedUserId": "", "CreatedUserName": "", "UpdatedUserId": "", "UpdatedUserName": "", "F_MODULE_ID": ""}, {"Id": "462315908608197", "Pid": "0", "Pids": "[0],", "Name": "渠道配置", "Code": "channel", "Type": "1", "Icon": "compress", "Router": "/message/channel", "Component": "message/channel", "Permission": "", "Application": "mcs", "OpenType": "1", "Visible": "1", "Link": "", "Redirect": "", "Weight": "1", "Sort": "100", "Remark": "", "Status": "1", "CreatedTime": "13/6/2024 16:21:45", "UpdatedTime": "15/10/2024 15:55:44", "CreatedUserId": "", "CreatedUserName": "", "UpdatedUserId": "", "UpdatedUserName": "", "F_MODULE_ID": ""}, {"Id": "***************", "Pid": "0", "Pids": "[0],", "Name": "消息接收组", "Code": "receive-group", "Type": "1", "Icon": "idcard", "Router": "/message/receive-group", "Component": "message/receive-group", "Permission": "", "Application": "mcs", "OpenType": "1", "Visible": "1", "Link": "", "Redirect": "", "Weight": "1", "Sort": "100", "Remark": "", "Status": "1", "CreatedTime": "13/6/2024 16:21:45", "UpdatedTime": "15/10/2024 15:55:44", "CreatedUserId": "", "CreatedUserName": "", "UpdatedUserId": "", "UpdatedUserName": "", "F_MODULE_ID": ""}, {"Id": "***************", "Pid": "0", "Pids": "[0],", "Name": "推送日志", "Code": "push-log", "Type": "1", "Icon": "read", "Router": "/message/push-log", "Component": "message/push-log", "Permission": "", "Application": "mcs", "OpenType": "1", "Visible": "1", "Link": "", "Redirect": "", "Weight": "1", "Sort": "100", "Remark": "", "Status": "1", "CreatedTime": "13/6/2024 16:21:45", "UpdatedTime": "15/10/2024 15:55:44", "CreatedUserId": "", "CreatedUserName": "", "UpdatedUserId": "", "UpdatedUserName": "", "F_MODULE_ID": ""}, {"Id": "***************", "Pid": "461230143656133", "Pids": "[0],[461230143656133],", "Name": "报警设置", "Code": "alarm-config", "Type": "1", "Icon": "", "Router": "/alarm-manage/alarm-config", "Component": "alarm-manage/alarm-config", "Permission": "", "Application": "cms", "OpenType": "1", "Visible": "1", "Link": "", "Redirect": "", "Weight": "1", "Sort": "100", "Remark": "", "Status": "1", "CreatedTime": "13/6/2024 16:21:45", "UpdatedTime": "15/10/2024 15:55:44", "CreatedUserId": "", "CreatedUserName": "", "UpdatedUserId": "", "UpdatedUserName": "", "F_MODULE_ID": ""}, {"Id": "462320027500741", "Pid": "0", "Pids": "[0],", "Name": "报警查询", "Code": "alarm-query", "Type": "1", "Icon": "file-search", "Router": "/alarm-center/alarm-query", "Component": "alarm-center/alarm-query", "Permission": "", "Application": "alarm", "OpenType": "1", "Visible": "1", "Link": "", "Redirect": "", "Weight": "1", "Sort": "100", "Remark": "", "Status": "1", "CreatedTime": "13/6/2024 16:21:45", "UpdatedTime": "15/10/2024 15:55:44", "CreatedUserId": "", "CreatedUserName": "", "UpdatedUserId": "", "UpdatedUserName": "", "F_MODULE_ID": ""}, {"Id": "462320104063173", "Pid": "0", "Pids": "[0],", "Name": "报警类型", "Code": "alarm-type", "Type": "1", "Icon": "appstore", "Router": "/alarm-center/alarm-type", "Component": "alarm-center/alarm-type", "Permission": "", "Application": "alarm", "OpenType": "1", "Visible": "1", "Link": "", "Redirect": "", "Weight": "1", "Sort": "100", "Remark": "", "Status": "1", "CreatedTime": "13/6/2024 16:21:45", "UpdatedTime": "15/10/2024 15:55:44", "CreatedUserId": "", "CreatedUserName": "", "UpdatedUserId": "", "UpdatedUserName": "", "F_MODULE_ID": ""}, {"Id": "462320188383429", "Pid": "0", "Pids": "[0],", "Name": "报警级别", "Code": "alarm-level", "Type": "1", "Icon": "warning", "Router": "/alarm-center/alarm-level", "Component": "alarm-center/alarm-level", "Permission": "", "Application": "alarm", "OpenType": "1", "Visible": "1", "Link": "", "Redirect": "", "Weight": "1", "Sort": "100", "Remark": "", "Status": "1", "CreatedTime": "13/6/2024 16:21:45", "UpdatedTime": "15/10/2024 15:55:44", "CreatedUserId": "", "CreatedUserName": "", "UpdatedUserId": "", "UpdatedUserName": "", "F_MODULE_ID": ""}, {"Id": "463301401456837", "Pid": "400756296605893", "Pids": "[0],[400756296605893],", "Name": "服务管理", "Code": "system-services", "Type": "1", "Icon": "", "Router": "/system/services", "Component": "system/services", "Permission": "", "Application": "back", "OpenType": "1", "Visible": "1", "Link": "", "Redirect": "", "Weight": "1", "Sort": "100", "Remark": "", "Status": "1", "CreatedTime": "13/6/2024 16:21:45", "UpdatedTime": "15/10/2024 15:55:44", "CreatedUserId": "", "CreatedUserName": "", "UpdatedUserId": "", "UpdatedUserName": "", "F_MODULE_ID": ""}, {"Id": "463302611812549", "Pid": "0", "Pids": "[0],", "Name": "应用管理", "Code": "application-manage", "Type": "1", "Icon": "code-sandbox", "Router": "/application-manage", "Component": "application-manage", "Permission": "", "Application": "ads", "OpenType": "1", "Visible": "1", "Link": "", "Redirect": "", "Weight": "1", "Sort": "100", "Remark": "", "Status": "1", "CreatedTime": "13/6/2024 16:21:45", "UpdatedTime": "15/10/2024 15:55:44", "CreatedUserId": "", "CreatedUserName": "", "UpdatedUserId": "", "UpdatedUserName": "", "F_MODULE_ID": ""}, {"Id": "493125278474437", "Pid": "340515074896069", "Pids": "[0],[340515074896069],", "Name": "数据查询", "Code": "data-query", "Type": "1", "Icon": "", "Router": "/data-application/data-query", "Component": "data-application/data-query", "Permission": "", "Application": "iotworks", "OpenType": "1", "Visible": "1", "Link": "", "Redirect": "", "Weight": "1", "Sort": "100", "Remark": "", "Status": "1", "CreatedTime": "13/6/2024 16:21:45", "UpdatedTime": "15/10/2024 15:55:44", "CreatedUserId": "", "CreatedUserName": "", "UpdatedUserId": "", "UpdatedUserName": "", "F_MODULE_ID": ""}, {"Id": "500808374173893", "Pid": "0", "Pids": "[0],", "Name": "支路用能", "Code": "branch-energy", "Type": "0", "Icon": "flag", "Router": "/branch-energy", "Component": "", "Permission": "", "Application": "energy", "OpenType": "0", "Visible": "1", "Link": "", "Redirect": "", "Weight": "1", "Sort": "100", "Remark": "", "Status": "1", "CreatedTime": "13/6/2024 16:21:45", "UpdatedTime": "15/10/2024 15:55:44", "CreatedUserId": "", "CreatedUserName": "", "UpdatedUserId": "", "UpdatedUserName": "", "F_MODULE_ID": ""}, {"Id": "500808553619653", "Pid": "0", "Pids": "[0],", "Name": "应用配置", "Code": "energy-configure", "Type": "0", "Icon": "setting", "Router": "/energy-configure", "Component": "", "Permission": "", "Application": "energy", "OpenType": "0", "Visible": "1", "Link": "", "Redirect": "", "Weight": "1", "Sort": "100", "Remark": "", "Status": "1", "CreatedTime": "13/6/2024 16:21:45", "UpdatedTime": "15/10/2024 15:55:44", "CreatedUserId": "", "CreatedUserName": "", "UpdatedUserId": "", "UpdatedUserName": "", "F_MODULE_ID": ""}, {"Id": "500808889675973", "Pid": "500808553619653", "Pids": "[0],[500808553619653],", "Name": "能源模型", "Code": "energy-model", "Type": "1", "Icon": "", "Router": "/energy/configure/energy-model", "Component": "energy/configure/energy-model", "Permission": "", "Application": "energy", "OpenType": "1", "Visible": "1", "Link": "", "Redirect": "", "Weight": "1", "Sort": "100", "Remark": "", "Status": "1", "CreatedTime": "13/6/2024 16:21:45", "UpdatedTime": "15/10/2024 15:55:44", "CreatedUserId": "", "CreatedUserName": "", "UpdatedUserId": "", "UpdatedUserName": "", "F_MODULE_ID": ""}, {"Id": "500810384322757", "Pid": "500808374173893", "Pids": "[0],[500808374173893],", "Name": "实时参数", "Code": "realtime-parameter", "Type": "1", "Icon": "", "Router": "/energy/branch/realtime-parameter", "Component": "energy/branch/realtime-parameter", "Permission": "", "Application": "energy", "OpenType": "1", "Visible": "1", "Link": "", "Redirect": "", "Weight": "1", "Sort": "100", "Remark": "", "Status": "1", "CreatedTime": "13/6/2024 16:21:45", "UpdatedTime": "21/10/2024 10:46:49", "CreatedUserId": "", "CreatedUserName": "", "UpdatedUserId": "1300000000101", "UpdatedUserName": "超级管理员", "F_MODULE_ID": ""}, {"Id": "500810507591877", "Pid": "500808374173893", "Pids": "[0],[500808374173893],", "Name": "用能概况", "Code": "energy-summary", "Type": "1", "Icon": "", "Router": "/energy/branch/energy-summary", "Component": "energy/branch/energy-summary", "Permission": "", "Application": "energy", "OpenType": "1", "Visible": "1", "Link": "", "Redirect": "", "Weight": "1", "Sort": "100", "Remark": "", "Status": "1", "CreatedTime": "13/6/2024 16:21:45", "UpdatedTime": "15/10/2024 15:55:44", "CreatedUserId": "", "CreatedUserName": "", "UpdatedUserId": "", "UpdatedUserName": "", "F_MODULE_ID": ""}, {"Id": "500811476263109", "Pid": "500808553619653", "Pids": "[0],[500808553619653],", "Name": "抄表明细", "Code": "copy-details", "Type": "1", "Icon": "", "Router": "/energy/configure/copy-details", "Component": "energy/configure/copy-details", "Permission": "", "Application": "energy", "OpenType": "1", "Visible": "1", "Link": "", "Redirect": "", "Weight": "1", "Sort": "100", "Remark": "", "Status": "1", "CreatedTime": "13/6/2024 16:21:45", "UpdatedTime": "15/10/2024 15:55:44", "CreatedUserId": "", "CreatedUserName": "", "UpdatedUserId": "", "UpdatedUserName": "", "F_MODULE_ID": ""}, {"Id": "500811653226693", "Pid": "500808553619653", "Pids": "[0],[500808553619653],", "Name": "数据集抄", "Code": "data-copy", "Type": "1", "Icon": "", "Router": "/energy/configure/data-copy", "Component": "energy/configure/data-copy", "Permission": "", "Application": "energy", "OpenType": "1", "Visible": "1", "Link": "", "Redirect": "", "Weight": "1", "Sort": "100", "Remark": "", "Status": "1", "CreatedTime": "13/6/2024 16:21:45", "UpdatedTime": "15/10/2024 15:55:44", "CreatedUserId": "", "CreatedUserName": "", "UpdatedUserId": "", "UpdatedUserName": "", "F_MODULE_ID": ""}, {"Id": "500814224212165", "Pid": "500808374173893", "Pids": "[0],[500808374173893],", "Name": "日数据监控", "Code": "day-data-monitor", "Type": "1", "Icon": "", "Router": "/energy/branch/day-data-monitor", "Component": "energy/branch/day-data-monitor", "Permission": "", "Application": "energy", "OpenType": "1", "Visible": "1", "Link": "", "Redirect": "", "Weight": "1", "Sort": "100", "Remark": "", "Status": "1", "CreatedTime": "13/6/2024 16:21:45", "UpdatedTime": "15/10/2024 15:55:44", "CreatedUserId": "", "CreatedUserName": "", "UpdatedUserId": "", "UpdatedUserName": "", "F_MODULE_ID": ""}, {"Id": "500814386061509", "Pid": "500808374173893", "Pids": "[0],[500808374173893],", "Name": "月数据监控", "Code": "month-data-monitor", "Type": "1", "Icon": "", "Router": "/energy/branch/month-data-monitor", "Component": "energy/branch/month-data-monitor", "Permission": "", "Application": "energy", "OpenType": "1", "Visible": "1", "Link": "", "Redirect": "", "Weight": "1", "Sort": "100", "Remark": "", "Status": "1", "CreatedTime": "13/6/2024 16:21:45", "UpdatedTime": "15/10/2024 15:55:44", "CreatedUserId": "", "CreatedUserName": "", "UpdatedUserId": "", "UpdatedUserName": "", "F_MODULE_ID": ""}, {"Id": "500814552174789", "Pid": "500808374173893", "Pids": "[0],[500808374173893],", "Name": "年数据监控", "Code": "year-data-monitor", "Type": "1", "Icon": "", "Router": "/energy/branch/year-data-monitor", "Component": "energy/branch/year-data-monitor", "Permission": "", "Application": "energy", "OpenType": "1", "Visible": "1", "Link": "", "Redirect": "", "Weight": "1", "Sort": "100", "Remark": "", "Status": "1", "CreatedTime": "13/6/2024 16:21:45", "UpdatedTime": "15/10/2024 15:55:44", "CreatedUserId": "", "CreatedUserName": "", "UpdatedUserId": "", "UpdatedUserName": "", "F_MODULE_ID": ""}, {"Id": "500814702420165", "Pid": "500808374173893", "Pids": "[0],[500808374173893],", "Name": "用电负荷", "Code": "electricity-load", "Type": "1", "Icon": "", "Router": "/energy/branch/electricity-load", "Component": "energy/branch/electricity-load", "Permission": "", "Application": "energy", "OpenType": "1", "Visible": "0", "Link": "", "Redirect": "", "Weight": "1", "Sort": "100", "Remark": "", "Status": "1", "CreatedTime": "13/6/2024 16:21:45", "UpdatedTime": "29/10/2024 17:29:34", "CreatedUserId": "", "CreatedUserName": "", "UpdatedUserId": "1300000000101", "UpdatedUserName": "超级管理员", "F_MODULE_ID": ""}, {"Id": "500814868295877", "Pid": "500808374173893", "Pids": "[0],[500808374173893],", "Name": "分时电量", "Code": "time-power", "Type": "1", "Icon": "", "Router": "/energy/branch/time-power", "Component": "energy/branch/time-power", "Permission": "", "Application": "energy", "OpenType": "1", "Visible": "1", "Link": "", "Redirect": "", "Weight": "1", "Sort": "100", "Remark": "", "Status": "1", "CreatedTime": "13/6/2024 16:21:45", "UpdatedTime": "15/10/2024 15:55:44", "CreatedUserId": "", "CreatedUserName": "", "UpdatedUserId": "", "UpdatedUserName": "", "F_MODULE_ID": ""}, {"Id": "500815195398341", "Pid": "500808374173893", "Pids": "[0],[500808374173893],", "Name": "功率因素", "Code": "power-factor", "Type": "1", "Icon": "", "Router": "/energy/branch/power-factor", "Component": "energy/branch/power-factor", "Permission": "", "Application": "energy", "OpenType": "1", "Visible": "0", "Link": "", "Redirect": "", "Weight": "1", "Sort": "100", "Remark": "", "Status": "1", "CreatedTime": "13/6/2024 16:21:45", "UpdatedTime": "29/10/2024 17:29:31", "CreatedUserId": "", "CreatedUserName": "", "UpdatedUserId": "1300000000101", "UpdatedUserName": "超级管理员", "F_MODULE_ID": ""}, {"Id": "500815360569541", "Pid": "500808374173893", "Pids": "[0],[500808374173893],", "Name": "用能分析", "Code": "energy-analysis", "Type": "1", "Icon": "", "Router": "/energy/branch/energy-analysis", "Component": "energy/branch/energy-analysis", "Permission": "", "Application": "energy", "OpenType": "1", "Visible": "1", "Link": "", "Redirect": "", "Weight": "1", "Sort": "100", "Remark": "", "Status": "1", "CreatedTime": "13/6/2024 16:21:45", "UpdatedTime": "15/10/2024 15:55:44", "CreatedUserId": "", "CreatedUserName": "", "UpdatedUserId": "", "UpdatedUserName": "", "F_MODULE_ID": ""}, {"Id": "500815500599493", "Pid": "500808374173893", "Pids": "[0],[500808374173893],", "Name": "同比分析", "Code": "yoy-analysis", "Type": "1", "Icon": "", "Router": "/energy/branch/yoy-analysis", "Component": "energy/branch/yoy-analysis", "Permission": "", "Application": "energy", "OpenType": "1", "Visible": "1", "Link": "", "Redirect": "", "Weight": "1", "Sort": "100", "Remark": "", "Status": "1", "CreatedTime": "13/6/2024 16:21:45", "UpdatedTime": "15/10/2024 15:55:44", "CreatedUserId": "", "CreatedUserName": "", "UpdatedUserId": "", "UpdatedUserName": "", "F_MODULE_ID": ""}, {"Id": "500815652434117", "Pid": "500808374173893", "Pids": "[0],[500808374173893],", "Name": "用能统计", "Code": "energy-statistics", "Type": "1", "Icon": "", "Router": "/energy/branch/energy-statistics", "Component": "energy/branch/energy-statistics", "Permission": "", "Application": "energy", "OpenType": "1", "Visible": "1", "Link": "", "Redirect": "", "Weight": "1", "Sort": "100", "Remark": "", "Status": "1", "CreatedTime": "13/6/2024 16:21:45", "UpdatedTime": "15/10/2024 15:55:44", "CreatedUserId": "", "CreatedUserName": "", "UpdatedUserId": "", "UpdatedUserName": "", "F_MODULE_ID": ""}, {"Id": "544366059094213", "Pid": "0", "Pids": "[0],", "Name": "功能设计", "Code": "web-design", "Type": "1", "Icon": "icon-ym icon-ym-webDesign", "Router": "/web-design", "Component": "web-design", "Permission": "", "Application": "ads", "OpenType": "1", "Visible": "1", "Link": "", "Redirect": "", "Weight": "1", "Sort": "100", "Remark": "", "Status": "1", "CreatedTime": "13/6/2024 16:21:45", "UpdatedTime": "15/10/2024 15:55:44", "CreatedUserId": "1300000000101", "CreatedUserName": "超级管理员", "UpdatedUserId": "", "UpdatedUserName": "", "F_MODULE_ID": ""}, {"Id": "544733577011397", "Pid": "461231981719749", "Pids": "[0],[461231981719749],", "Name": "实体管理", "Code": "entity", "Type": "1", "Icon": "", "Router": "/data-model/entity", "Component": "data-model/entity", "Permission": "", "Application": "idf", "OpenType": "1", "Visible": "1", "Link": "", "Redirect": "", "Weight": "1", "Sort": "100", "Remark": "", "Status": "1", "CreatedTime": "13/6/2024 16:21:45", "UpdatedTime": "15/10/2024 15:55:44", "CreatedUserId": "1300000000101", "CreatedUserName": "超级管理员", "UpdatedUserId": "", "UpdatedUserName": "", "F_MODULE_ID": ""}, {"Id": "556668620554437", "Pid": "0", "Pids": "[0],", "Name": "程序块库", "Code": "program-library", "Type": "1", "Icon": "codepen", "Router": "/program-library", "Component": "program-library", "Permission": "", "Application": "iotworks", "OpenType": "1", "Visible": "1", "Link": "", "Redirect": "", "Weight": "1", "Sort": "100", "Remark": "", "Status": "1", "CreatedTime": "13/6/2024 16:21:45", "UpdatedTime": "15/10/2024 15:55:44", "CreatedUserId": "1300000000101", "CreatedUserName": "超级管理员", "UpdatedUserId": "", "UpdatedUserName": "", "F_MODULE_ID": ""}, {"Id": "559926063071429", "Pid": "0", "Pids": "[0],", "Name": "任务运维", "Code": "task-operation", "Type": "0", "Icon": "fund", "Router": "/task-operation", "Component": "", "Permission": "", "Application": "iotworks", "OpenType": "0", "Visible": "1", "Link": "", "Redirect": "", "Weight": "1", "Sort": "100", "Remark": "", "Status": "1", "CreatedTime": "20/6/2024 14:45:01", "UpdatedTime": "15/10/2024 15:55:44", "CreatedUserId": "1300000000101", "CreatedUserName": "超级管理员", "UpdatedUserId": "", "UpdatedUserName": "", "F_MODULE_ID": ""}, {"Id": "559926609408197", "Pid": "559926063071429", "Pids": "[0],[559926063071429],", "Name": "错误日志", "Code": "error-log", "Type": "1", "Icon": "", "Router": "/task-operation/error-log", "Component": "task-operation/error-log", "Permission": "", "Application": "iotworks", "OpenType": "1", "Visible": "1", "Link": "", "Redirect": "", "Weight": "1", "Sort": "100", "Remark": "", "Status": "1", "CreatedTime": "20/6/2024 14:47:14", "UpdatedTime": "15/10/2024 15:55:44", "CreatedUserId": "1300000000101", "CreatedUserName": "超级管理员", "UpdatedUserId": "", "UpdatedUserName": "", "F_MODULE_ID": ""}, {"Id": "560209535004869", "Pid": "0", "Pids": "[0],", "Name": "远程控制", "Code": "remote-control", "Type": "0", "Icon": "stock", "Router": "/remote-control", "Component": "", "Permission": "", "Application": "cms", "OpenType": "0", "Visible": "1", "Link": "", "Redirect": "", "Weight": "1", "Sort": "5", "Remark": "", "Status": "1", "CreatedTime": "21/6/2024 09:58:28", "UpdatedTime": "15/10/2024 15:55:44", "CreatedUserId": "1300000000101", "CreatedUserName": "超级管理员", "UpdatedUserId": "", "UpdatedUserName": "", "F_MODULE_ID": ""}, {"Id": "560209676669125", "Pid": "560209535004869", "Pids": "[0],[560209535004869],", "Name": "文件管理", "Code": "file-manage", "Type": "1", "Icon": "", "Router": "/remote-control/file-manage", "Component": "remote-control/file-manage", "Permission": "", "Application": "cms", "OpenType": "1", "Visible": "1", "Link": "", "Redirect": "", "Weight": "1", "Sort": "100", "Remark": "", "Status": "1", "CreatedTime": "21/6/2024 09:59:03", "UpdatedTime": "15/10/2024 15:55:44", "CreatedUserId": "1300000000101", "CreatedUserName": "超级管理员", "UpdatedUserId": "", "UpdatedUserName": "", "F_MODULE_ID": ""}, {"Id": "560209830936773", "Pid": "560209535004869", "Pids": "[0],[560209535004869],", "Name": "固件管理", "Code": "firmware-manage", "Type": "1", "Icon": "", "Router": "/remote-control/firmware-manage", "Component": "remote-control/firmware-manage", "Permission": "", "Application": "cms", "OpenType": "1", "Visible": "1", "Link": "", "Redirect": "", "Weight": "1", "Sort": "100", "Remark": "", "Status": "1", "CreatedTime": "21/6/2024 09:59:40", "UpdatedTime": "15/10/2024 15:55:44", "CreatedUserId": "1300000000101", "CreatedUserName": "超级管理员", "UpdatedUserId": "", "UpdatedUserName": "", "F_MODULE_ID": ""}, {"Id": "582534298259653", "Pid": "0", "Pids": "[0],", "Name": "打印设计", "Code": "print-dev", "Type": "1", "Icon": "printer", "Router": "/print-dev", "Component": "print-dev", "Permission": "", "Application": "ads", "OpenType": "1", "Visible": "1", "Link": "", "Redirect": "", "Weight": "1", "Sort": "100", "Remark": "", "Status": "1", "CreatedTime": "23/8/2024 11:58:10", "UpdatedTime": "15/10/2024 15:44:02", "CreatedUserId": "1300000000101", "CreatedUserName": "超级管理员", "UpdatedUserId": "1300000000101", "UpdatedUserName": "超级管理员", "F_MODULE_ID": ""}, {"Id": "594203694338245", "Pid": "340515074896069", "Pids": "[0],[340515074896069],", "Name": "数据接口", "Code": "data-interface", "Type": "1", "Icon": "", "Router": "/data-application/data-interface", "Component": "data-application/data-interface", "Permission": "", "Application": "iotworks", "OpenType": "1", "Visible": "1", "Link": "", "Redirect": "", "Weight": "1", "Sort": "100", "Remark": "", "Status": "1", "CreatedTime": "25/9/2024 11:21:03", "UpdatedTime": "", "CreatedUserId": "1300000000101", "CreatedUserName": "超级管理员", "UpdatedUserId": "", "UpdatedUserName": "", "F_MODULE_ID": ""}, {"Id": "594203798261957", "Pid": "340515074896069", "Pids": "[0],[340515074896069],", "Name": "接口认证", "Code": "interface-o<PERSON><PERSON>", "Type": "1", "Icon": "", "Router": "/data-application/interface-oauth", "Component": "data-application/interface-oauth", "Permission": "", "Application": "iotworks", "OpenType": "1", "Visible": "1", "Link": "", "Redirect": "", "Weight": "1", "Sort": "100", "Remark": "", "Status": "1", "CreatedTime": "25/9/2024 11:21:29", "UpdatedTime": "", "CreatedUserId": "1300000000101", "CreatedUserName": "超级管理员", "UpdatedUserId": "", "UpdatedUserName": "", "F_MODULE_ID": ""}, {"Id": "599588941545669", "Pid": "0", "Pids": "[0],", "Name": "单据模板", "Code": "billRule", "Type": "1", "Icon": "icon-ym icon-ym-systemTemplate", "Router": "/billRule", "Component": "billRule", "Permission": "", "Application": "ads", "OpenType": "1", "Visible": "0", "Link": "", "Redirect": "", "Weight": "1", "Sort": "100", "Remark": "", "Status": "1", "CreatedTime": "10/10/2024 16:33:41", "UpdatedTime": "16/10/2024 18:15:43", "CreatedUserId": "1300000000101", "CreatedUserName": "超级管理员", "UpdatedUserId": "1300000000101", "UpdatedUserName": "超级管理员", "F_MODULE_ID": ""}, {"Id": "613384495775941", "Pid": "0", "Pids": "[0],", "Name": "门户设计", "Code": "visualPortal", "Type": "1", "Icon": "icon-ym icon-ym-portalDesign", "Router": "/visual-portal", "Component": "visual-portal", "Permission": "", "Application": "ads", "OpenType": "1", "Visible": "1", "Link": "", "Redirect": "", "Weight": "1", "Sort": "100", "Remark": "", "Status": "1", "CreatedTime": "18/11/2024 16:07:56", "UpdatedTime": "", "CreatedUserId": "1300000000101", "CreatedUserName": "超级管理员", "UpdatedUserId": "", "UpdatedUserName": "", "F_MODULE_ID": ""}, {"Id": "621113136308421", "Pid": "0", "Pids": "[0],", "Name": "视频中心", "Code": "video-center", "Type": "0", "Icon": "icon-ym icon-ym-portal-video", "Router": "/video-center", "Component": "", "Permission": "", "Application": "cms", "OpenType": "0", "Visible": "1", "Link": "", "Redirect": "", "Weight": "1", "Sort": "4", "Remark": "", "Status": "1", "CreatedTime": "10/12/2024 12:15:51", "UpdatedTime": "", "CreatedUserId": "1300000000101", "CreatedUserName": "超级管理员", "UpdatedUserId": "", "UpdatedUserName": "", "F_MODULE_ID": ""}, {"Id": "621113439097029", "Pid": "621113136308421", "Pids": "[0],[621113136308421],", "Name": "视频设备", "Code": "video-equipment", "Type": "1", "Icon": "", "Router": "/video-center/video-equipment", "Component": "video-center/video-equipment", "Permission": "", "Application": "cms", "OpenType": "1", "Visible": "1", "Link": "", "Redirect": "", "Weight": "1", "Sort": "100", "Remark": "", "Status": "1", "CreatedTime": "10/12/2024 12:17:05", "UpdatedTime": "", "CreatedUserId": "1300000000101", "CreatedUserName": "超级管理员", "UpdatedUserId": "", "UpdatedUserName": "", "F_MODULE_ID": ""}]}