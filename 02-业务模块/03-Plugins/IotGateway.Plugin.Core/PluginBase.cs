using IotGateway.Plugin.Core.Models;

namespace IotGateway.Plugin.Core;

/// <summary>
/// 插件基类
/// </summary>
public abstract class PluginBase : IPlugin
{
  /// <summary>
  /// 是否启用  
  /// </summary>
  protected bool _enabled;

  /// <summary>
  /// 配置
  /// </summary>
  protected object _configuration;

  /// <summary>
  /// DNC配置集合
  /// </summary>
  protected List<DncConfig> _dncConfigs = new List<DncConfig>();

  /// <summary>
  /// 插件名称
  /// </summary>
  public abstract string Name { get; }

  /// <summary>
  /// 插件版本
  /// </summary>
  public abstract string Version { get; }

  /// <summary>
  /// 插件描述
  /// </summary>
  public abstract string Description { get; }

  /// <summary>
  /// 插件分类
  /// </summary>
  public virtual string Category { get; } = "通用";

  /// <summary>
  /// 是否启用
  /// </summary>
  public bool Enabled => _enabled;

  /// <summary>
  /// 初始化
  /// </summary>
  public virtual async Task InitializeAsync()
  {
    // 基础初始化逻辑
    await Task.CompletedTask;
  }

  /// <summary>
  /// 启动
  /// </summary>
  public virtual async Task StartAsync()
  {
    _enabled = true;
    await Task.CompletedTask;
  }

  /// <summary>
  /// 停止
  /// </summary>
  public virtual async Task StopAsync()
  {
    _enabled = false;
    await Task.CompletedTask;
  }

  /// <summary>
  /// 获取配置
  /// </summary>
  public virtual async Task<object> GetConfigurationAsync()
  {
    return await Task.FromResult(_configuration);
  }

  /// <summary>
  /// 获取配置Schema
  /// </summary>
  public virtual async Task<object?> GetConfigurationSchemaAsync()
  {
    // 先尝试调用GetConfigurationSchema方法
    if (_configuration is IPluginConfig config)
    {
      var getSchema = config.GetConfigurationSchema();
      if (getSchema != null)
        return await Task.FromResult(getSchema);
    }
    return null;
  }

  /// <summary>
  /// 更新配置
  /// </summary>
  public virtual async Task UpdateConfigurationAsync(object configuration)
  {
    if (_configuration != null)
    {
      var configType = _configuration.GetType();
      if (configuration is System.Text.Json.JsonElement jsonElement)
      {
        // 如果是 JsonElement，将其转换为原始配置类型
        _configuration = System.Text.Json.JsonSerializer.Deserialize(
          jsonElement.GetRawText(),
          configType,
          new System.Text.Json.JsonSerializerOptions
          {
            PropertyNameCaseInsensitive = true
          }
        );
      }
      else
      {
        // 如果不是 JsonElement，尝试直接转换
        _configuration = System.Text.Json.JsonSerializer.Deserialize(
          System.Text.Json.JsonSerializer.Serialize(configuration),
          configType,
          new System.Text.Json.JsonSerializerOptions
          {
            PropertyNameCaseInsensitive = true
          }
        );
      }
    }
    else
    {
      _configuration = configuration;
    }

    await Task.CompletedTask;
  }

  /// <summary>
  /// 启用或禁用插件
  /// </summary>
  /// <param name="enable">是否启用</param>
  /// <returns>操作结果</returns>
  public virtual async Task ToggleEnableAsync(bool enable)
  {
    if (enable && !_enabled)
    {
      // 如果需要启用且当前未启用，则启动插件
      await StartAsync();
    }
    else if (!enable && _enabled)
    {
      // 如果需要禁用且当前已启用，则停止插件
      await StopAsync();
    }

    // 更新配置中的启用状态（如果配置实现了IPluginConfig接口）
    if (_configuration is IPluginConfig config)
    {
      config.Enabled = enable;

      // 更新配置
      await UpdateConfigurationAsync(_configuration);
    }
  }

  /// <summary>
  /// 获取FTP服务器文件列表
  /// </summary>
  public virtual async Task<Dictionary<string, List<FtpFileInfo>>> GetFtpFiles(string? folderType = null)
  {
    return await Task.FromResult(new Dictionary<string, List<FtpFileInfo>>());
  }

  /// <summary>
  /// 获取FTP服务器文件内容
  /// </summary>
  public virtual async Task<string> GetFileContent(string filePath)
  {
    return await Task.FromResult(string.Empty);
  }

  /// <summary>
  /// 设置DNC配置
  /// </summary>
  /// <param name="dncConfig">DNC配置</param>
  /// <returns>设置结果</returns>
  public virtual async Task SetDncConfigAsync(DncConfig dncConfig)
  {
    if (dncConfig == null)
      return;

    // 检查是否已存在相同ID的配置
    var existingConfig = _dncConfigs.FirstOrDefault(c => c.Id == dncConfig.Id);
    if (existingConfig != null)
    {
      // 更新已存在的配置
      var index = _dncConfigs.IndexOf(existingConfig);
      _dncConfigs[index] = dncConfig;
    }
    else
    {
      // 添加新配置
      _dncConfigs.Add(dncConfig);
    }

    await Task.CompletedTask;
  }

  /// <summary>
  /// 获取DNC配置
  /// </summary>
  /// <returns>DNC配置集合</returns>
  public virtual async Task<List<DncConfig>> GetDncConfigsAsync()
  {
    return await Task.FromResult(_dncConfigs);
  }

  /// <summary>
  /// 获取DNC配置
  /// </summary>
  /// <returns>DNC配置，如有多个则返回第一个</returns>
  public virtual async Task<DncConfig> GetDncConfigAsync()
  {
    return await Task.FromResult(_dncConfigs.FirstOrDefault());
  }

  /// <summary>
  /// 获取指定设备编码的DNC配置
  /// </summary>
  /// <param name="deviceCode">设备编码</param>
  /// <returns>DNC配置</returns>
  public virtual async Task<DncConfig> GetDncConfigByDeviceCodeAsync(string deviceCode)
  {
    return await Task.FromResult(_dncConfigs.FirstOrDefault(c => c.DeviceCode == deviceCode));
  }

  /// <summary>
  /// 移除DNC配置
  /// </summary>
  /// <param name="id">配置ID</param>
  /// <returns>操作结果</returns>
  public virtual async Task RemoveDncConfigAsync(long id)
  {
    var config = _dncConfigs.FirstOrDefault(c => c.Id == id);
    if (config != null)
    {
      _dncConfigs.Remove(config);
    }

    await Task.CompletedTask;
  }
}