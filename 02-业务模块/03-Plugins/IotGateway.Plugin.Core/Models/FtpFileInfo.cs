using System;

namespace IotGateway.Plugin.Core.Models;

/// <summary>
/// FTP文件信息模型
/// </summary>
public class FtpFileInfo
{
  /// <summary>
  /// 文件名称
  /// </summary>
  public string FileName { get; set; } = string.Empty;

  /// <summary>
  /// 修改时间
  /// </summary>
  public DateTime ModifiedTime { get; set; }

  /// <summary>
  /// 文件大小（字节）
  /// </summary>
  public string Size { get; set; }

  /// <summary>
  /// 文件类型（扩展名）
  /// </summary>
  public string FileType { get; set; } = string.Empty;

  /// <summary>
  /// 完整文件路径
  /// </summary>
  public string FullPath { get; set; } = string.Empty;

  /// <summary>
  /// 源文件路径
  /// </summary>
  public string Source { get; set; } = string.Empty;
}