using Extras.DatabaseAccessor.SqlSugar.Options;
using Furion;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.DependencyInjection;
using SqlSugar;

namespace Extras.DatabaseAccessor.SqlSugar.Repositories;

/// <summary>
///     SqlSugar 仓储实现类
/// </summary>
/// <typeparam name="TEntity"></typeparam>
public class SqlSugarRepository<TEntity> : SimpleClient<TEntity>, ISqlSugarRepository<TEntity>
    where TEntity : class, new()
{
    /// <summary>
    ///     构造函数
    /// </summary>
    public SqlSugarRepository(IServiceProvider serviceProvider, ISqlSugarClient context = null) : base(context)
    {
        using IServiceScope serviceScope = serviceProvider.CreateScope();
        // ICacheManager? _cacheManager = serviceScope.ServiceProvider.GetService<ICacheManager>();

        // 获取数据库连接选项
        ConnectionStringsOptions connectionStrings = App.GetConfig<ConnectionStringsOptions>("ConnectionStrings", true);

        // 获取多租户选项
        TenantOptions tenant = App.GetConfig<TenantOptions>("Tenant", true);
        HttpContext? httpContext = App.HttpContext;

        Context = (SqlSugarScope) context;

        string tenantId = connectionStrings.DefaultConnectionConfig.ConfigId.ToString();
        if (httpContext?.GetEndpoint()?.Metadata?.GetMetadata<AllowAnonymousAttribute>() == null)
        {
            Context = Context.AsTenant().GetConnectionScope(tenantId);
        }

        // 设置超时时间
        Context.Ado.CommandTimeOut = 30;

        if (connectionStrings.EnableConsoleSql)
        {
            Context.Aop.OnLogExecuting = (sql, pars) =>
            {
                if (sql.StartsWith("SELECT", StringComparison.OrdinalIgnoreCase))
                {
                    Console.ForegroundColor = ConsoleColor.Green;
                }

                if (sql.StartsWith("UPDATE", StringComparison.OrdinalIgnoreCase) || sql.StartsWith("INSERT", StringComparison.OrdinalIgnoreCase))
                {
                    Console.ForegroundColor = ConsoleColor.White;
                }

                if (sql.StartsWith("DELETE", StringComparison.OrdinalIgnoreCase))
                {
                    Console.ForegroundColor = ConsoleColor.Blue;
                }

                // 在控制台输出sql语句
                Console.WriteLine("【" + DateTime.Now + "——执行SQL】\r\n" + UtilMethods.GetSqlString(Context.CurrentConnectionConfig.DbType, sql, pars) + "\r\n");
                // App.PrintToMiniProfiler("SqlSugar", "Info", sql + "\r\n" + base.Context.Utilities.SerializeObject(pars.ToDictionary(it => it.ParameterName, it => it.Value)));
            };

            Context.Aop.OnError = ex =>
            {
                Console.ForegroundColor = ConsoleColor.Red;
                string? pars = Context.Utilities.SerializeObject(((SugarParameter[]) ex.Parametres).ToDictionary(it => it.ParameterName, it => it.Value));
                Console.WriteLine("【" + DateTime.Now + "——错误SQL】\r\n" + UtilMethods.GetSqlString(Context.CurrentConnectionConfig.DbType, ex.Sql, (SugarParameter[]) ex.Parametres) + "\r\n");
                // App.PrintToMiniProfiler("SqlSugar", "Error", $"{ex.Message}{Environment.NewLine}{ex.Sql}{pars}{Environment.NewLine}");
            };
        }

        if (Context.CurrentConnectionConfig.DbType == DbType.Oracle)
        {
            Context.Aop.OnExecutingChangeSql = (sql, pars) =>
            {
                if (pars != null)
                {
                    foreach (SugarParameter? item in pars)
                    {
                        //如果是DbTppe=string设置成OracleDbType.Nvarchar2 
                        item.IsNvarchar2 = true;
                    }
                }
                return new KeyValuePair<string, SugarParameter[]>(sql, pars);
            };
        }
    }
}