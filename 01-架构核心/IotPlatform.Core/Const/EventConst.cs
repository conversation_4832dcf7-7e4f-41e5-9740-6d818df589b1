namespace IotPlatform.Core.Const;

/// <summary>
///     事件总线订阅配置
/// </summary>
public class EventConst
{
    /// <summary>
    ///     转换成-物实例数据
    /// </summary>
    public const string ThingExampleData = "ThingExampleData";

    /// <summary>
    ///     触发消息通知
    /// </summary>
    public const string PolicyMessage = "PolicyMessage";
    
    /// <summary>
    ///     物实例报警抑制解除
    /// </summary>
    public const string ThingRestrainClose = "ThingRestrainClose";
    
    /// <summary>
    ///     物实例报警抑制解除-天
    /// </summary>
    public const string ThingRestrainCloseByDay = "ThingRestrainCloseByDay";
    /// <summary>
    ///     物实例报警-手动解除
    /// </summary>
    public const string ThingWarningHandClose = "ThingWarningHandClose";
    
    /// <summary>
    ///     同步物模型下实例内容
    /// </summary>
    public const string ThingSynchronization = "ThingSynchronization";
    
    /// <summary>
    ///     物实例-报警
    /// </summary>
    public const string ThingWarning = "ThingWarning";
    
    /// <summary>
    ///     物模型示例-报警
    /// </summary>
    public const string ModelThingWarning = "ModelThingWarning_{0}";
}