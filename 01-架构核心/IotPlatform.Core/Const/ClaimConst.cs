namespace IotPlatform.Core.Const;

/// <summary>
/// Claim相关常量
/// </summary>
public class ClaimConst
{
    /// <summary>
    /// 用户Id
    /// </summary>
    public const string UserId = "UserId";

    /// <summary>
    /// 账号
    /// </summary>
    public const string Account = "Account";

    /// <summary>
    /// 真实姓名
    /// </summary>
    public const string RealName = "RealName";

    /// <summary>
    /// 昵称
    /// </summary>
    public const string NickName = "NickName";

    /// <summary>
    /// 账号类型
    /// </summary>
    public const string AdminType = "AdminType";

    /// <summary>
    /// 租户Id
    /// </summary>
    public const string TenantId = "TenantId";

    /// <summary>
    /// 组织机构Id
    /// </summary>
    public const string OrgId = "OrgId";

    /// <summary>
    /// 组织机构名称
    /// </summary>
    public const string OrgName = "OrgName";

    /// <summary>
    /// 登录模式PC、APP
    /// </summary>
    public const string LoginMode = "LoginMode";
}