namespace IotPlatform.Core.Const;

/// <summary>
///     通用常量
/// </summary>
public class CommonConst
{
    /// <summary>
    /// 单据编码缓存.
    /// </summary>
    public const string CACHEKEYBILLRULE = "billrule_";
    
    /// <summary>
    /// 外链密码开关(1：开 , 0：关).
    /// </summary>
    public const int OnlineDevData_State_Enable = 1;
    
    /// <summary>
    /// 代码生成远端数据缓存.
    /// </summary>
    public const string CodeGenDynamic = "codegendynamic_";
    
    /// <summary>
    ///     全局租户缓存.
    /// </summary>
    public const string GLOBALTENANT = "iot:global:tenant";

    /// <summary>
    ///     默认密码
    /// </summary>
    public const string SysPassword = "sys_password";

    /// <summary>
    ///     Token过期时间
    /// </summary>
    public const string SysTokenExpire = "sys_token_expire";

    /// <summary>
    ///     RefreshToken过期时间
    /// </summary>
    public const string SysRefreshTokenExpire = "sys_refresh_token_expire";

    /// <summary>
    ///     系统管理员角色编码
    /// </summary>
    public const string SysAdminRole = "sys_admin";
    
    /// <summary>
    /// 在线开发缓存.
    /// </summary>
    public const string VISUALDEV = "visualdev_";
    
    /// <summary>
    /// 单用户登录
    /// </summary>
    public const string SysSingleLogin = "sys_single_login";
}