using Microsoft.Extensions.DependencyInjection;

namespace IotPlatform.Core.Extension;

/// <summary>
///     控制台logo
/// </summary>
public static class ConsoleLogoSetup
{
    public static void AddLogoDisplay(this IServiceCollection services)
    {
        Console.ForegroundColor = ConsoleColor.White;
        Console.WriteLine(@"  

             ______                     _____ ____ _______ 
            |  ____|                   |_   _/ __ \__   __|
            | |__ ___ _ __   __ _        | || |  | | | |   
            |  __/ _ \ '_ \ / _` |       | || |  | | | |   
            | | |  __/ | | | (_| |      _| || |__| | | |   
            |_|  \___|_| |_|\__, |     |_____\____/  |_|   
                             __/ |                         
                            |___/       
                   
 ");
        Console.WriteLine(@"杭州峰回科技有限公司");
    }
}