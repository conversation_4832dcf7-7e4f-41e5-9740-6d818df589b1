using System.Data;

namespace IotPlatform.Core.Extension;

/// <summary>
///     字典辅助扩展方法.
/// </summary>
[SuppressSniffer]
public static class DictionaryExtensions
{
    /// <summary>
    ///     从字典中获取值，不存在则返回字典<typeparamref name="TValue" />类型的默认值.
    /// </summary>
    /// <typeparam name="TKey">字典键类型.</typeparam>
    /// <typeparam name="TValue">字典值类型.</typeparam>
    /// <param name="dictionary">要操作的字典.</param>
    /// <param name="key">指定键名.</param>
    /// <returns>获取到的值.</returns>
    public static TValue GetOrDefault<TKey, TValue>(this IDictionary<TKey, TValue> dictionary, TKey key)
    {
        return dictionary.TryGetValue(key, out TValue value) ? value : default;
    }

    /// <summary>
    ///     获取指定键的值，不存在则按指定委托添加值.
    /// </summary>
    /// <typeparam name="TKey">字典键类型.</typeparam>
    /// <typeparam name="TValue">字典值类型.</typeparam>
    /// <param name="dictionary">要操作的字典.</param>
    /// <param name="key">指定键名.</param>
    /// <param name="addFunc">添加值的委托.</param>
    /// <returns>获取到的值.</returns>
    public static TValue GetOrAdd<TKey, TValue>(this IDictionary<TKey, TValue> dictionary, TKey key, Func<TValue> addFunc)
    {
        if (dictionary.TryGetValue(key, out TValue value))
        {
            return value;
        }

        return dictionary[key] = addFunc();
    }

    /// <summary>
    ///     替换值.
    /// </summary>
    /// <param name="dictionary1"></param>
    /// <param name="dictionary2"></param>
    public static void ReplaceValue(this Dictionary<string, object> dictionary1, Dictionary<string, object> dictionary2)
    {
        foreach (string item in dictionary2.Keys)
        {
            if (dictionary1.ContainsKey(item))
            {
                dictionary1[item] = dictionary2[item];
            }
        }
    }

    /// <summary>
    ///     DataTable转DicList.
    /// </summary>
    /// <param name="dt"></param>
    /// <returns></returns>
    public static List<Dictionary<string, object>> DataTableToDicList(DataTable dt)
    {
        return dt.AsEnumerable().Select(
            row => dt.Columns.Cast<DataColumn>().ToDictionary(
                column => column.ColumnName,
                column => row[column])).ToList();
    }
    /// <summary>
    /// 字典转对象
    /// </summary>
    /// <param name="dictionary"></param>
    /// <returns></returns>
    public static dynamic ConvertDictionaryToDynamic(this Dictionary<string, object> dictionary)
    {
        dynamic obj = new ExpandoObject();
        var objDict = (IDictionary<string, object>)obj;

        foreach (var kvp in dictionary)
        {
            objDict[kvp.Key] = kvp.Value;
        }
        return obj;
    }
    /// <summary>
    /// 将对象转换为字典
    /// </summary>
    /// <param name="obj">要转换的对象</param>
    /// <returns>转换后的字典</returns>
    public static Dictionary<string, object> ToDictionary(this object obj)
    {
        if (obj == null)
        {
            return new Dictionary<string, object>();
        }

        // 如果已经是字典类型，直接转换返回
        if (obj is Dictionary<string, object> dictionary)
        {
            return dictionary;
        }

        // 处理动态对象
        if (obj is ExpandoObject expandoObject)
        {
            return new Dictionary<string, object>((IDictionary<string, object>)expandoObject);
        }

        // 使用反射获取对象的属性
        Dictionary<string, object> result = new Dictionary<string, object>();
        foreach (PropertyInfo property in obj.GetType().GetProperties(BindingFlags.Public | BindingFlags.Instance))
        {
            if (property.CanRead)
            {
                result[property.Name] = property.GetValue(obj);
            }
        }

        return result;
    }

}