using System.Text;
using System.Text.Json.Serialization;
using Furion;
using Furion.HttpRemote;
using Microsoft.AspNetCore.Mvc;

namespace Extras.Thridparty.WeChat;

public class WeChatScriptUtil
{
    /// <summary>
    ///     获取企业微信token
    /// </summary>
    /// <param name="corpId">企业Id</param>
    /// <param name="corpSecret">应用secret</param>
    /// <returns></returns>
    [NonAction]
    public async Task<string> GetAccessToken(string corpId, string corpSecret)
    {
        IHttpRemoteService httpRemoteService = App.GetRequiredService<IHttpRemoteService>();
        AccessTokenResp? res = await httpRemoteService
            .GetAsAsync<AccessTokenResp>($"https://qyapi.weixin.qq.com/cgi-bin/gettoken?corpid={corpId}&corpsecret={corpSecret}",
                builder =>
                    builder.SetContentType("application/json")
                        .SetContentEncoding(Encoding.UTF8));
        return res?.AccessToken;
    }

    /// <summary>
    ///     发送文本消息
    /// </summary>
    /// <param name="accessToken">token</param>
    /// <param name="toUser">发送给谁</param>
    /// <param name="agentId">应用Id</param>
    /// <param name="content">发送内容</param>
    /// <returns></returns>
    [NonAction]
    public async Task<bool> SendTextMessage(string accessToken, string toUser, string agentId, string content)
    {
        Dictionary<string, object> reqData = new();
        reqData.Add("touser", toUser);
        reqData.Add("msgtype", "text");
        reqData.Add("agentid", agentId);
        reqData.Add("text", new Dictionary<string, object> { { "content", content } });
        IHttpRemoteService httpRemoteService = App.GetRequiredService<IHttpRemoteService>();
        string? res = await httpRemoteService
            .PostAsStringAsync($"https://qyapi.weixin.qq.com/cgi-bin/message/send?access_token={accessToken}",
                builder =>
                    builder.SetContentType("application/json")
                        .SetContentEncoding(Encoding.UTF8)
                        .SetContent(reqData));

        return res.Contains("\"errcode\":0");
    }

    /// <summary>
    ///     发送卡片消息
    /// </summary>
    /// <param name="accessToken">token</param>
    /// <param name="toUser">发送给谁</param>
    /// <param name="agentId">应用Id</param>
    /// <param name="title">消息标题</param>
    /// <param name="content">发送内容</param>
    /// <param name="linkUrl"></param>
    /// <param name="link"></param>
    /// <returns></returns>
    [NonAction]
    public async Task<bool> SendCardMessage(string accessToken, string toUser, string agentId, string title, string content,
        string linkUrl, string link)
    {
        Dictionary<string, object> reqData = new();
        reqData.Add("touser", toUser);
        reqData.Add("msgtype", "textcard");
        reqData.Add("agentid", agentId);
        reqData.Add("textcard",
            new Dictionary<string, object>
                { { "title", title }, { "description", content }, { "url", linkUrl }, { "btntxt", link } });
        IHttpRemoteService httpRemoteService = App.GetRequiredService<IHttpRemoteService>();
        string? res = await httpRemoteService
            .PostAsStringAsync($"https://qyapi.weixin.qq.com/cgi-bin/message/send?access_token={accessToken}",
                builder =>
                    builder.SetContentType("application/json")
                        .SetContentEncoding(Encoding.UTF8)
                        .SetContent(reqData));

        return res.Contains("\"errcode\":0");
    }

    /// <summary>
    ///     发送图文消息
    /// </summary>
    /// <param name="accessToken">token</param>
    /// <param name="toUser">发送给谁</param>
    /// <param name="agentId">应用Id</param>
    /// <param name="title">消息标题</param>
    /// <param name="content">发送内容</param>
    /// <param name="linkUrl"></param>
    /// <param name="imageUrl"></param>
    /// <returns></returns>
    [NonAction]
    public async Task<bool> SendImageMessage(string accessToken, string toUser, string agentId, string title, string content,
        string linkUrl, string imageUrl)
    {
        Dictionary<string, object> reqData = new();
        reqData.Add("touser", toUser);
        reqData.Add("msgtype", "news");
        reqData.Add("agentid", agentId);
        reqData.Add("news", new Dictionary<string, object>
        {
            {
                "articles", new List<Dictionary<string, object>>
                {
                    new()
                    {
                        { "title", title },
                        { "description", content },
                        { "url", linkUrl },
                        { "picurl", imageUrl }
                        // {"appid",agentId},
                        // {"pagepath",linkUrl},
                    }
                }
            }
        });

        IHttpRemoteService httpRemoteService = App.GetRequiredService<IHttpRemoteService>();
        string? res = await httpRemoteService
            .PostAsStringAsync($"https://qyapi.weixin.qq.com/cgi-bin/message/send?access_token={accessToken}",
                builder =>
                    builder.SetContentType("application/json")
                        .SetContentEncoding(Encoding.UTF8)
                        .SetContent(reqData));

        return res.Contains("\"errcode\":0");
    }
}

/// <summary>
///     获取access_token返回结果
/// </summary>
public class AccessTokenResp
{
    /// <summary>
    /// </summary>
    [JsonPropertyName("errcode")]
    public int Errcode { get; set; }

    /// <summary>
    /// </summary>
    [JsonPropertyName("errmsg")]
    public string Errmsg { get; set; }

    /// <summary>
    /// </summary>
    [JsonPropertyName("access_token")]
    public string AccessToken { get; set; }

    /// <summary>
    /// </summary>
    [JsonPropertyName("expires_in")]
    public long ExpiresIn { get; set; }
}

/// <summary>
///     企业微信配置
/// </summary>
public class WechatConf
{
    /// <summary>
    ///     公司Id
    /// </summary>
    public string CompanyId { get; set; }

    /// <summary>
    ///     应用的凭证密钥
    /// </summary>
    public string CompanySecret { get; set; }

    /// <summary>
    ///     应用Id
    /// </summary>
    public string AgentId { get; set; }
}