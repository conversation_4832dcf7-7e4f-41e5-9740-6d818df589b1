namespace Extras.TDengine.Model;

/// <summary>
///     查询指定物实例的历史数据
/// </summary>
public class GetHistoricalDataOfInstanceInput
{
    /// <summary>
    ///     模型Id
    /// </summary>
    [Description("模型Id")]
    [Required]
    public string ModelId { get; set; }

    /// <summary>
    ///     物标识
    /// </summary>
    [Description("物标识")]
    [Required]
    public string ThingId { get; set; }

    /// <summary>
    ///     属性列表
    /// </summary>
    [Description("属性列表")]
    public List<string> Properties { get; set; } = new();

    /// <summary>
    ///     开始时间(毫秒时间戳)
    /// </summary>
    [Description("开始时间(毫秒时间戳)")]
    [Required]
    public long StartTime { get; set; }

    /// <summary>
    ///     结束时间(毫秒时间戳)
    /// </summary>
    [Description("结束时间(毫秒时间戳)")]
    [Required]
    public long EndTime { get; set; }

    /// <summary>
    ///     查询返回的数据最大条数
    /// </summary>
    [Description("查询返回的数据最大条数(默认是1000，默认配置下limit不能超过100000)")]
    public int Limit { get; set; } = 1000;
}