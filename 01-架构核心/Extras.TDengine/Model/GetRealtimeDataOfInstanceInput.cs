namespace Extras.TDengine.Model;

/// <summary>
///     查询指定物实例的实时数据
/// </summary>
public class GetRealtimeDataOfInstanceInput
{
    /// <summary>
    ///     模型Id
    /// </summary>
    [Description("模型Id")]
    [Required]
    public string ModelId { get; set; }

    /// <summary>
    ///     物标识
    /// </summary>
    [Description("物标识")]
    [Required]
    public string ThingId { get; set; }

    /// <summary>
    ///     属性列表
    /// </summary>
    [Description("属性列表")]
    public List<string> Properties { get; set; } = new();
}