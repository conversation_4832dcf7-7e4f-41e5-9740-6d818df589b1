namespace Extras.TDengine.Model;

/// <summary>
///     查询同一模型的一组物实例的时间窗口数据
/// </summary>
public class GetTimeWindowDataOfModelInstancesInput
{
    /// <summary>
    ///     模型Id
    /// </summary>
    [Description("模型Id")]
    [Required]
    public string ModelId { get; set; }

    /// <summary>
    ///     物实例标识集合（空返回全部）
    /// </summary>
    [Description("物实例标识集合（空返回全部）")]
    [Required]
    public List<string> ThingIds { get; set; } = new();

    /// <summary>
    ///     物实例属性集合（空返回全部）
    /// </summary>
    [Description("物实例属性集合（空返回全部）")]
    public List<string> Properties { get; set; } = new();

    /// <summary>
    ///     开始时间(毫秒时间戳)
    /// </summary>
    [Description("开始时间(毫秒时间戳)")]
    [Required]
    public long StartTime { get; set; }

    /// <summary>
    ///     结束时间(毫秒时间戳)
    /// </summary>
    [Description("结束时间(毫秒时间戳)")]
    [Required]
    public long EndTime { get; set; }
}