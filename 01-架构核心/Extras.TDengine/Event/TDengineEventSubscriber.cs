using Extras.TDengine.TDengIne;
using Furion.EventBus;
using IotPlatform.Core.Const;

namespace Extras.TDengine.Event;

/// <summary>
///     处理完成的实时数据
/// </summary>
public class TDengineEventSubscriber : IEventSubscriber
{
    private readonly WriteService _writeService;

    /// <summary>
    /// </summary>
    /// <param name="writeService"></param>
    public TDengineEventSubscriber(WriteService writeService)
    {
        _writeService = writeService;
    }

    /// <summary>
    ///     时序库写入
    /// </summary>
    /// <param name="context"></param>
    [EventSubscribe(EventConst.ThingExampleData)]
    public async Task WriteDataBase(EventHandlerExecutingContext context)
    {
        await _writeService.Write(context.GetPayload<WriteTDengIne>());
    }
}