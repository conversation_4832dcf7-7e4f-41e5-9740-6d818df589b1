using DateTime = IotPlatform.Core.Extension.DateTime;

namespace Extras.TDengine.TDengIne;

/// <summary>
///     向TDengIne写入数据
/// </summary>
public class WriteService : ISingleton
{
    /// <summary>
    ///     TDengIne的连接
    /// </summary>
    private readonly ConnectService _connectService;

    /// <summary>
    /// </summary>
    private readonly ILogger<WriteService> _logger;

    public WriteService(ConnectService connectService, ILogger<WriteService> logger)
    {
        _connectService = connectService;
        _logger = logger;
    }

    /// <summary>
    ///     采集数据上报
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public async Task Write(WriteTDengIne input)
    {
        string sql = $"INSERT INTO {input.DeviceName} USING {input.Uuid} TAGS('{input.DeviceName}') (ts,`cloudTime`";
        string valueSql = $"VALUES({input.ParentTime},{DateTime.ToLong(System.DateTime.UtcNow)}";
        foreach ((string key, WriteParam value) in input.Params)
        {
            string? val = value.Value;
            if (val == null)
            {
                continue;
            }

            try
            {
                switch (value.DataType)
                {
                    case WriteParamValueDataTypeEnum.Number:
                    {
                        double doubleValue = Convert.ToDouble(val.Trim());
                        sql += $",`{key}`";
                        valueSql += $",{doubleValue}";
                        break;
                    }
                    case WriteParamValueDataTypeEnum.Bool:
                        sql += $",`{key}`";
                        valueSql += $",{val}";
                        break;
                    case WriteParamValueDataTypeEnum.String:
                    default:
                    {
                        sql += $",`{key}`";
                        valueSql += $",'{val}'";
                        break;
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError($"【TDengIne】 【{input.DeviceName}】 【{key}】 数据转换错误:{ex.Message}");
            }
        }

        if (sql.TrimEnd().EndsWith("INTO"))
        {
            return;
        }

        sql += ") " + valueSql + ");";
        try
        {
            int res = await _connectService.Connect().CopyNew().Ado.ExecuteCommandAsync(sql);
            if (res <= 0)
            {
                _logger.LogError($"【TDengIne】 [{input.DeviceName}] failed to insert data,Sql:【{sql}】");
            }
        }
        catch (Exception e)
        {
            _logger.LogError($"【TDengIne】 [{input.DeviceName}]写入异常，ERROR: {e.Message}，Sql:【{sql}】");
        }
    }
}