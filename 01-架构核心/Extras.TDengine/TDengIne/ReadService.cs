namespace Extras.TDengine.TDengIne;

/// <summary>
///     读取数据
/// </summary>
public class ReadService : ISingleton
{
    /// <summary>
    ///     TDengIne的连接
    /// </summary>
    private readonly ConnectService _connectService;

    /// <summary>
    ///     TDengIne 封装的方法
    /// </summary>
    private readonly ExecuteService _executeService;

    public ReadService(ConnectService connectService, ExecuteService executeService)
    {
        _connectService = connectService;
        _executeService = executeService;
    }

    /// <summary>
    ///     趋势图数据
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public ReadDataListOutput ReadDataList(ReadDataInput input)
    {
        ReadDataListOutput output = new()
        {
            Columns = new List<string>(),
            Rows = new List<List<object>>()
        };
        string interval = input.TimeInterval.IsNotEmptyOrNull() ? input.TimeInterval : "1s";
        input.AggFunc = input.AggFunc == "APERCENTILE"
            ? $"{input.AggFunc}(`{input.Properties}`, 50) as {input.Properties}"
            : $"{input.AggFunc}(`{input.Properties}`) as {input.Properties}";

        string sql = $"SELECT _wstart,{input.AggFunc}";
        sql +=
            $" FROM {input.ThingName} where ts >= {input.StartTime} and ts <= {input.EndTime} interval({interval}) fill({input.Fill}) limit {input.Limit} ;"; // //从指定库中查询数据
        Console.WriteLine($"【趋势图】：{sql}");
        DataTable? res = _connectService.Connect().Ado.GetDataTable(sql);
        if (res == null)
        {
            return output;
        }

        foreach (DataColumn dc in res.Columns)
        {
            output.Columns.Add(dc.ColumnName);
        }

        foreach (DataRow dr in res.Rows)
        {
            List<object> row = (from DataColumn dc in res.Columns
                select Convert.IsDBNull(dr[dc.ColumnName]) ? null : dr[dc.ColumnName]).ToList();
            output.Rows.Add(row);
        }

        return output;
    }

    /// <summary>
    ///     查询时间段数据总数
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public int ReadDataCount(ReadDataCountInput input)
    {
        if (!input.Properties.Any())
        {
            throw Oops.Oh("Properties Is Null");
        }

        List<int> count = new();
        string sql ="SELECT count(*)";
        sql +=
            $" FROM {input.ThingName} where ts >= {input.StartTime} and ts <= {input.EndTime};"; // //从指定库中查询数据
        try
        {
            Log.Information("【查询时间段数据总数】：" + sql);
            DataTable? res = _connectService.Connect().Ado.GetDataTable(sql);
            if (res == null)
            {
                return 0;
            }

            foreach (DataRow dr in res.Rows)
            {
                count.AddRange(from DataColumn dc in res.Columns select Convert.ToInt32(dr[dc.ColumnName]));
            }

            return count.Max();
        }
        catch (Exception e)
        {
            Log.Error($"【查询时间段数据总数】 Error:{e.Message}");
            return 0;
        }
    }

    /// <summary>
    ///     物实例的历史数据导出
    /// </summary>
    /// <param name="input"></param>
    /// <param name="thingIdentification">物实例唯一标识</param>
    /// <returns></returns>
    /// <exception cref="AppFriendlyException"></exception>
    public dynamic HistorianExport(HistorianExportInput input, string thingIdentification)
    {
        string sql = "SELECT ";
        if (!input.Properties.Any())
        {
            throw Oops.Oh("Properties Is Null");
        }

        sql += "ts";
        sql = input.Properties.Aggregate(sql, (current, propertie) => current + $",`{propertie}`");
        sql +=
            $" FROM {thingIdentification} WHERE ts>={input.StartTime} and ts<={input.EndTime} order by ts {input.Sort};";

        return _executeService.Columns(sql);
    }

    /// <summary>
    ///     查询物实例的历史数据
    /// </summary>
    /// <returns></returns>
    public dynamic GetHistoricalData(HistoricalDataInput input)
    {
        string sql = "SELECT ";
        if (!input.Properties.Any())
        {
            throw Oops.Oh("Properties Is Null");
        }

        sql += "ts";
        sql = input.Properties.Aggregate(sql, (current, propertie) => current + $",`{propertie}`");
        sql +=
            $" FROM {input.ThingName} WHERE ts >= {input.StartTime} and ts <= {input.EndTime} ";
        if (input.Filter.IsNotEmptyOrNull())
        {
            sql += $" and {input.Filter}";
        }

        sql += $" order by ts {input.Sort} limit {input.PageSize} offset {(input.PageNo - 1) * input.PageSize};";
        return _executeService.Columns(sql);
    }

    /// <summary>
    ///     查询同一模型的物实例的实时数据
    /// </summary>
    /// <returns></returns>
    public dynamic GetRealtimeDataOfModelInstances(GetRealtimeDataOfModelInstancesInput input)
    {
        Dictionary<string, Dictionary<string, object>> output = new(StringComparer.OrdinalIgnoreCase);
        foreach (string thingId in input.ThingIds)
        {
            string sql = "SELECT last_row(";
            if (!input.Properties.Any())
            {
                sql += $"*) FROM {input.ModelId} WHERE deviceName='{thingId}';";
            }
            else
            {
                sql += "ts";
                sql = input.Properties.Aggregate(sql, (current, propertie) => current + $",`{propertie}` ");
                sql += $") FROM {input.ModelId} WHERE deviceName='{thingId}';";
            }

            Dictionary<string, object> data = _executeService.First(sql);
            Dictionary<string, object> newData = new(StringComparer.OrdinalIgnoreCase);
            foreach ((string key, object val) in data)
            {
                newData.Add(key.Contains("last_row") ? key.Replace("last_row(", "").Replace(")", "") : key, val);
            }

            output.Add(thingId, newData);
        }

        return output;
    }

    /// <summary>
    ///     查询指定物实例的实时数据
    /// </summary>
    /// <returns></returns>
    public dynamic GetRealtimeDataOfInstance(GetRealtimeDataOfInstanceInput input)
    {
        Dictionary<string, Dictionary<string, object>> output = new(StringComparer.OrdinalIgnoreCase);
        string sql = "SELECT last_row(";
        if (!input.Properties.Any())
        {
            sql += $"*) FROM {input.ModelId} WHERE deviceName='{input.ThingId}';";
        }
        else
        {
            sql += "ts";
            sql = input.Properties.Aggregate(sql, (current, propertie) => current + $",`{propertie}`");
            sql += $") FROM {input.ModelId} WHERE deviceName='{input.ThingId}';";
        }

        Dictionary<string, object> data = _executeService.First(sql);
        Dictionary<string, object> newData = new(StringComparer.OrdinalIgnoreCase);
        foreach ((string key, object val) in data)
        {
            newData.Add(key.Contains("last_row") ? key.Replace("last_row(", "").Replace(")", "") : key, val);
        }

        output.Add(input.ModelId, newData);
        return output;
    }

    /// <summary>
    ///     查询同一模型的一组物实例的历史数据
    /// </summary>
    /// <returns></returns>
    public dynamic GetModelInstancesHistoricalData(GetModelInstancesHistoricalDataInput input)
    {
        Dictionary<string, List<Dictionary<string, object>>> output = new(StringComparer.OrdinalIgnoreCase);
        foreach (string thingId in input.ThingIds)
        {
            string sql = "SELECT ";
            if (!input.Properties.Any())
            {
                sql +=
                    $"* FROM {input.ModelId} WHERE ts>={input.StartTime} and ts<={input.EndTime} and deviceName='{thingId}' limit {input.Limit};";
            }
            else
            {
                sql += "ts";
                sql = input.Properties.Aggregate(sql, (current, propertie) => current + $",`{propertie}`");
                sql +=
                    $"FROM {input.ModelId} WHERE ts>={input.StartTime} and ts<={input.EndTime} and deviceName='{thingId}' limit {input.Limit};";
            }

            output.Add(thingId, _executeService.Select(sql));
        }

        return output;
    }

    /// <summary>
    ///     查询同一模型的一组物实例的时间窗口数据
    /// </summary>
    /// <returns></returns>
    public dynamic GetTimeWindowDataOfModelInstances(GetTimeWindowDataOfModelInstancesInput input)
    {
        Dictionary<string, object> output = new(StringComparer.OrdinalIgnoreCase);
        foreach (string thingId in input.ThingIds)
        {
            Dictionary<string, List<Dictionary<string, object>>> propertiesDic = new(StringComparer.OrdinalIgnoreCase);
            foreach (string properties in input.Properties)
            {
                string sql =
                    $"SELECT * FROM (SELECT _wstart as fst,_WEND as ent,_WDURATION as totalTime, COUNT(*) AS cnt,  `{properties}` FROM {input.ModelId} where ts >= {input.StartTime} and ts <= {input.EndTime} and deviceName='{thingId}'  STATE_WINDOW(`{properties}`)) t ; ";
                propertiesDic.Add(properties, _executeService.Select(sql));
            }

            output.Add(thingId, propertiesDic);
        }

        return output;
    }

    /// <summary>
    ///     查询指定物实例的时间聚集数据
    /// </summary>
    /// <returns></returns>
    public dynamic GetAggregatedDataOfInstance(GetAggregatedDataOfInstanceInput input)
    {
        Dictionary<string, List<Dictionary<string, object>>> output = new(StringComparer.OrdinalIgnoreCase);
        foreach (string thingId in input.ThingIds)
        foreach (string properties in input.Properties)
        {
            string sql =
                $"SELECT _wstart as time,{input.AggFunc}(`{properties}`) as `{properties}` FROM {input.ModelId} WHERE ts>={input.StartTime} and ts<={input.EndTime} and deviceName='{thingId}' ";
            if (input.TimeInterval.IsNotEmptyOrNull())
            {
                sql += $"INTERVAL({input.TimeInterval})";
            }

            sql += $" limit {input.Limit};";

            List<Dictionary<string, object?>> dataList = _executeService.Select(sql);
            foreach (Dictionary<string, object?> data in dataList)
            {
                Dictionary<string, object> newData = new(StringComparer.OrdinalIgnoreCase);
                foreach ((string key, object? val) in data)
                {
                    newData.Add(
                        key.Contains($"{input.AggFunc.ToLower()}")
                            ? key.Replace($"{input.AggFunc.ToLower()}(", "").Replace(")", "")
                            : key, val);
                }

                if (!output.ContainsKey(thingId))
                {
                    output.TryAdd(thingId, new List<Dictionary<string, object>> { newData });
                }
                else
                {
                    List<Dictionary<string, object>> thingDic = output[thingId];
                    thingDic.Add(newData);
                    output[thingId] = thingDic;
                }
            }
        }

        return output;
    }

    /// <summary>
    ///     查询指定物实例的历史数据
    /// </summary>
    /// <returns></returns>
    public dynamic GetHistoricalDataOfInstance(GetHistoricalDataOfInstanceInput input)
    {
        string sql = "SELECT ";
        if (!input.Properties.Any())
        {
            sql +=
                $"* FROM {input.ModelId} WHERE ts>={input.StartTime} and ts<={input.EndTime} and deviceName='{input.ModelId}' limit {input.Limit};";
        }
        else
        {
            sql += "ts";
            sql = input.Properties.Aggregate(sql, (current, propertie) => current + $",`{propertie}`");
            sql +=
                $"FROM {input.ModelId} WHERE ts>={input.StartTime} and ts<={input.EndTime} and deviceName='{input.ModelId}' limit {input.Limit};";
        }

        List<Dictionary<string, object?>> output = _executeService.Select(sql);

        return output;
    }
}