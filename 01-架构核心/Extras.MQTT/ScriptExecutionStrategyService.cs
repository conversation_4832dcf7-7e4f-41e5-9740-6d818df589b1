using Common.Models;
using Extras.DatabaseAccessor.SqlSugar.Internal;
using Extras.MQTT.Dto;
using Extras.MQTT.Models;
using FengLink_Application_Entity.Entity;
using Furion.DatabaseAccessor;
using Furion.DependencyInjection;
using Furion.DynamicApiController;
using Furion.JsonSerialization;
using IotPlatform.Core.Extension;
using Mapster;
using Microsoft.AspNetCore.Mvc;
using MiniExcelLibs;
using Yitter.IdGenerator;

namespace Extras.MQTT;

/// <summary>
///     边缘计算-执行策略
/// </summary>
public class ScriptExecutionStrategyService : ITransient, IDynamicApiController
{
    private readonly MqttService _mqttService;

    public ScriptExecutionStrategyService(
        MqttService mqttService)
    {
        _mqttService = mqttService;
    }

    /// <summary>
    ///     边缘计算-执行策略列表
    /// </summary>
    /// <returns></returns>
    [HttpGet("/edgeComputing/{sn}/strategy/page")]
    public async Task<SqlSugarPagedList<ScriptExecutionStrategy>> Page(string sn, [FromQuery] BasePageInput input)
    {
        var config = await _mqttService.LoadDataFromJsonFileAsync<GatewayConfig>($"{sn}.json");
        if (config == null)
        {
            throw Oops.Oh("该网关配置不存在！");
        }

        var strategies = config.ScriptStrategy;
        // 执行搜索和分页
        var filteredStrategies = strategies;
        if (!string.IsNullOrEmpty(input.SearchValue))
        {
            filteredStrategies = strategies.Where(w => w.Describe?.Contains(input.SearchValue) ?? false).ToList();
        }

        var pagedStrategies = filteredStrategies
            .Skip((input.PageNo - 1) * input.PageSize)
            .Take(input.PageSize)
            .ToList();

        return new SqlSugarPagedList<ScriptExecutionStrategy>
        {
            Rows = pagedStrategies,
            TotalRows = filteredStrategies.Count,
            PageSize = input.PageSize,
            PageNo = input.PageNo
        };
    }

    /// <summary>
    ///     边缘计算-执行策略详情
    /// </summary>
    /// <returns></returns>
    [HttpGet("/edgeComputing/{sn}/strategy/detail")]
    public async Task<ScriptExecutionStrategy> Detail(string sn, [FromQuery] BaseId input)
    {
        var config = await _mqttService.LoadDataFromJsonFileAsync<GatewayConfig>($"{sn}.json");
        if (config == null)
        {
            throw Oops.Oh("该网关配置不存在！");
        }

        List<ScriptExecutionStrategy> strategies = config.ScriptStrategy;
        var strategy = strategies.FirstOrDefault(f => f.Id == input.Id);

        if (strategy == null)
            throw Oops.Oh("数据不存在！");

        return strategy;
    }

    /// <summary>
    ///     边缘计算-执行策略-新增
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/edgeComputing/{sn}/strategy/add")]
    public async Task Add(string sn, EdgeScriptExecutionStrategyAddInput input)
    {
        var config = await _mqttService.LoadDataFromJsonFileAsync<GatewayConfig>($"{sn}.json");
        if (config == null)
        {
            throw Oops.Oh("该网关配置不存在！");
        }

        // 查找脚本
        var script = config.ScriptStrategy.FirstOrDefault(f => f.EdgeComputingScriptId == input.EdgeComputingScriptId);
        if (script == null)
            throw Oops.Oh("脚本已经被删除!");

        var strategy = input.Adapt<ScriptExecutionStrategy>();
        switch (strategy.ExecutionStrategyType)
        {
            case ExecutionStrategyTypeEnum.网关启动触发:
                strategy.Config = "";
                break;
            case ExecutionStrategyTypeEnum.定时触发:
                if (input.ExecutionTime == null)
                    throw Oops.Oh("定时触发任务配置出错！");
                strategy.Config = input.ExecutionTime.ToJson();
                break;
            case ExecutionStrategyTypeEnum.属性触发:
                if (input.Propertys == null)
                    throw Oops.Oh("属性触发任务配置出错！");
                strategy.Config = input.Propertys.ToJson();
                break;
            case ExecutionStrategyTypeEnum.变量触发:
                if (input.Variable == null)
                    throw Oops.Oh("变量触发任务配置出错！");
                strategy.Config = input.Variable.ToJson();
                break;
        }

        // 增加策略任务
        strategy.EdgeComputingScriptId = script.EdgeComputingScriptId;
        strategy.Id = YitIdHelper.NextId();

        // 添加到配置中
        config.ScriptStrategy.Add(strategy);

        // 保存配置
        await _mqttService.SaveDataToJsonFileAsync($"{sn}.json", config);
    }

    /// <summary>
    ///     边缘计算-执行策略-修改
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/edgeComputing/{sn}/strategy/update")]
    public async Task Update(string sn, ScriptExecutionStrategyUpdateInput input)
    {
        var config = await _mqttService.LoadDataFromJsonFileAsync<GatewayConfig>($"{sn}.json");
        if (config == null)
        {
            throw Oops.Oh("该网关配置不存在！");
        }

        var strategy = config.ScriptStrategy.FirstOrDefault(f => f.Id == input.Id);
        if (strategy == null)
            throw Oops.Oh("数据不存在！");

        strategy.Describe = input.Describe;
        switch (strategy.ExecutionStrategyType)
        {
            case ExecutionStrategyTypeEnum.网关启动触发:
                strategy.Config = "";
                break;
            case ExecutionStrategyTypeEnum.定时触发:
                if (input.ExecutionTime == null)
                    throw Oops.Oh("定时触发任务配置出错！");
                strategy.Config = input.ExecutionTime.ToJson();
                break;
            case ExecutionStrategyTypeEnum.属性触发:
                if (input.Propertys == null)
                    throw Oops.Oh("属性触发任务配置出错！");
                strategy.Config = input.Propertys.ToJson();
                break;
            case ExecutionStrategyTypeEnum.变量触发:
                if (input.Variable == null)
                    throw Oops.Oh("变量触发任务配置出错！");
                strategy.Config = input.Variable.ToJson();
                break;
        }

        strategy.ExecutionStrategyType = input.ExecutionStrategyType;
        await _mqttService.SaveDataToJsonFileAsync($"{sn}.json", config);
    }

    /// <summary>
    ///     边缘计算-执行策略-删除
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/edgeComputing/{sn}/strategy/delete")]
    public async Task Delete(string sn, BaseId<List<long>> input)
    {
        var config = await _mqttService.LoadDataFromJsonFileAsync<GatewayConfig>($"{sn}.json");
        if (config == null)
        {
            throw Oops.Oh("该网关配置不存在！");
        }

        config.ScriptStrategy.RemoveAll(x => input.Id.Contains(x.Id));
        await _mqttService.SaveDataToJsonFileAsync($"{sn}.json", config);
    }

    /// <summary>
    ///     边缘计算-执行策略-启用/禁用
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/edgeComputing/{sn}/strategy/enable")]
    public async Task<bool> Enable(string sn, EnableInput<List<long>> input)
    {
        var config = await _mqttService.LoadDataFromJsonFileAsync<GatewayConfig>($"{sn}.json");
        if (config == null)
        {
            throw Oops.Oh("该网关配置不存在！");
        }

        foreach (var strategy in config.ScriptStrategy.Where(x => input.Id.Contains(x.Id)))
        {
            strategy.Enable = input.Enable;
        }

        await _mqttService.SaveDataToJsonFileAsync($"{sn}.json", config);
        return true;
    }

    /// <summary>
    ///     导入边缘计算-策略任务
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/edgeComputing/{sn}/strategy/inPort")]
    [UnitOfWork]
    public async Task<DeviceVariableOutput> InPort(string sn, [FromForm] ComputingScriptInPortInput input)
    {
        var config = await _mqttService.LoadDataFromJsonFileAsync<GatewayConfig>($"{sn}.json");
        if (config == null)
        {
            throw Oops.Oh("该网关配置不存在！");
        }
        var stream = new MemoryStream();
        await input.File.CopyToAsync(stream);
        // 返回结果
        var result = new DeviceVariableOutput { ErrorColumn = new List<ErrorColumn>() };
        // 需要新增的脚本
        var computingScriptList = new List<Script>();
        // 需要新增的策略
        var scriptExecutionStrategyList = new List<ScriptExecutionStrategy>();
        // 当前所在行
        var line = 2;
        foreach (var computingScriptInPort in await stream.QueryAsync<ComputingScriptInPortDto>("脚本信息", startCell: "A2"))
            try
            {
                //检查必填项
                var success = CheckInPortScriptData(computingScriptInPort, line, result);
                if (!success)
                    continue;
                var computingScript = computingScriptInPort.Adapt<Script>();
                computingScript.Id = YitIdHelper.NextId();
                computingScriptList.Add(computingScript);
                line++;
            }
            catch (Exception e)
            {
                result.ErrorCount += 1;
                result.ErrorColumn.Add(new ErrorColumn { Error = $"【脚本信息】 第【{line}】行 Error:【{e.Message}】 " });
            }

        foreach (var inPort in await stream.QueryAsync<ScriptExecutionInPortDtoInput>("执行策略", startCell: "A2"))
            try
            {
                // 检查必填项
                var success = CheckInPortData(inPort, line, result);
                if (!success)
                    continue;

                var scriptExecutionStrategy = inPort.Adapt<ScriptExecutionStrategy>();
                scriptExecutionStrategy.Id = YitIdHelper.NextId();

                // 根据策略类型设置配置
                switch (scriptExecutionStrategy.ExecutionStrategyType)
                {
                    case ExecutionStrategyTypeEnum.定时触发:
                        if (string.IsNullOrEmpty(inPort.PeriodUnit))
                            throw Oops.Oh("定时触发任务周期单位不能为空！");

                        scriptExecutionStrategy.Config = JSON.Serialize(new ExecutionTime
                        {
                            PeriodUnit = inPort.PeriodUnit.GetEnum<ExecutionStrategyByTimePeriodUnitEnum>(),
                            Period = inPort.Period
                        });
                        break;
                    case ExecutionStrategyTypeEnum.属性触发:
                        if (string.IsNullOrEmpty(inPort.Name))
                            throw Oops.Oh("属性触发任务属性名称不能为空！");
                        if (string.IsNullOrEmpty(inPort.Compare))
                            throw Oops.Oh("属性触发任务比较符不能为空！");

                        scriptExecutionStrategy.Config = JSON.Serialize(new PropertyModel
                        {
                            Name = JSON.Deserialize<List<string>>(inPort.Name),
                            Compare = inPort.Compare.GetEnum<CompareEnum>(),
                            FirstEven = inPort.FirstEven,
                            Value = inPort.Value
                        });
                        break;
                    case ExecutionStrategyTypeEnum.变量触发:
                        scriptExecutionStrategy.Config = JSON.Serialize(new VariableModel
                        {
                            Key = inPort.Name
                        });
                        break;
                    case ExecutionStrategyTypeEnum.网关启动触发:
                        break;
                    default:
                        result.ErrorCount += 1;
                        result.ErrorColumn.Add(new ErrorColumn { Error = $"【执行策略】 第【{line}】行 Error:【执行模式】不支持 " });
                        break;
                }

                // 查找对应的脚本
                var computingScript = computingScriptList.FirstOrDefault(f => f.Name == inPort.ScriptName);
                if (computingScript == null)
                {
                    result.ErrorCount += 1;
                    result.ErrorColumn.Add(new ErrorColumn { Error = $"【执行策略】 第【{line}】行 Error:【脚本名称不存在】 " });
                    continue;
                }

                scriptExecutionStrategy.EdgeComputingScriptId = computingScript.Id;
                scriptExecutionStrategyList.Add(scriptExecutionStrategy);
                line++;
            }
            catch (Exception e)
            {
                result.ErrorCount += 1;
                result.ErrorColumn.Add(new ErrorColumn { Error = $"【执行策略】 第【{line}】行 Error:【{e.Message}】 " });
            }

        // 查找出来相同名称脚本
        var computingScriptAny = config.ScriptStrategy.Where(w => computingScriptList.Select(s => s.Id).Contains(w.EdgeComputingScriptId))
            .ToList();

        foreach (var computingScript in computingScriptAny)
        {
            var deviceVariableVal = computingScriptList.FirstOrDefault(f => f.Id == computingScript.EdgeComputingScriptId);
            if (deviceVariableVal == null)
                continue;
            if (input.InPortType == GatewayDeviceVariableInPortTypeEnum.OverLook)
            {
                computingScriptList.Remove(deviceVariableVal);
            }
            else
            {
                // 覆盖原始数据
                // 覆盖属性
                // 移除对象,修改Id后重新加入
                computingScriptList.Remove(deviceVariableVal);
                deviceVariableVal.Id = computingScript.Id;
                computingScriptList.Add(deviceVariableVal);
            }
        }

        if (computingScriptList.Any())
        {
            config.ScriptStrategy.AddRange(computingScriptList.Select(s => s.Adapt<ScriptExecutionStrategy>()));
        }
        config.ScriptStrategy.AddRange(scriptExecutionStrategyList);

        await _mqttService.SaveDataToJsonFileAsync($"{sn}.json", config);

        result.SuccessCount = line - 1 - result.ErrorCount < 0 ? 0 : line - 2 - result.ErrorCount;
        return result;
    }

    /// <summary>
    ///     导出边缘计算-策略任务
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/edgeComputing/{sn}/strategy/exPort")]
    public async Task<IActionResult> ExPort(string sn, BaseId<List<long>> input)
    {
        var config = await _mqttService.LoadDataFromJsonFileAsync<GatewayConfig>($"{sn}.json");
        if (config == null)
        {
            throw Oops.Oh("该网关配置不存在！");
        }

        // 获取策略
        var executionStrategyList = config.ScriptStrategy.Where(x => input.Id.Contains(x.Id)).ToList();
        // 获取脚本
        var scriptList = config.Script.Where(x => executionStrategyList.Select(s => s.EdgeComputingScriptId).Contains(x.Id))
        .ToList();
        var value = new
        {
            strategy = executionStrategyList.Select(s => new
            {
                ScriptName = scriptList.FirstOrDefault(f => f.Id == s.EdgeComputingScriptId)?.Name,
                ExecutionStrategyType = s.ExecutionStrategyType.GetDescription(),
                s.ExecutionTime?.Period,
                PeriodUnit = s.ExecutionTime != null ? s.ExecutionTime.PeriodUnit.GetDescription() : "",
                s.Describe,
                s.Enable,
                Name = s.Propertys != null ? JSON.Serialize(s.Propertys?.Name) : "",
                Compare = s.Propertys != null ? s.Propertys.Compare.GetDescription() : "",
                s.Propertys?.Value,
                s.Propertys?.FirstEven,
                s.Variable?.Key
            }),
            script = executionStrategyList.GroupBy(w => w.EdgeComputingScriptId)
                .Select((id, value) => new
                {
                    ScriptName = scriptList.FirstOrDefault(f => f.Id == value)?.Name,
                    Content = scriptList.FirstOrDefault(f => f.Id == value)?.Content,
                    Describe = scriptList.FirstOrDefault(f => f.Id == value)?.Describe,
                    Enable = scriptList.FirstOrDefault(f => f.Id == value)?.Enable
                })
        };

        try
        {
            var memoryStream = new MemoryStream();
            await memoryStream.SaveAsByTemplateAsync("Templates/EdgeComputingPolicyImport.xlsx", value);
            memoryStream.Seek(0, SeekOrigin.Begin);

            return new FileStreamResult(memoryStream, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
            {
                FileDownloadName = $"{System.DateTime.Now:yyyyMMddHHmmssfff}.xlsx"
            };
        }
        catch (Exception e)
        {
            throw Oops.Bah($"导出失败：{e.Message}");
        }
    }

    /// <summary>
    ///     检查脚本导入必填项
    /// </summary>
    private bool CheckInPortScriptData(ComputingScriptInPortDto inPort, int line, DeviceVariableOutput result)
    {
        if (string.IsNullOrEmpty(inPort.ScriptName))
        {
            result.ErrorCount += 1;
            result.ErrorColumn.Add(new ErrorColumn { Error = $"【脚本信息】 第【{line}】行 【脚本名称】 不能是空！ ", Line = line });
            return false;
        }

        if (string.IsNullOrEmpty(inPort.Content))
        {
            result.ErrorCount += 1;
            result.ErrorColumn.Add(new ErrorColumn { Error = $"【脚本信息】 第【{line}】行 【脚本内容】 不能是空！ ", Line = line });
            return false;
        }

        return true;
    }

    /// <summary>
    ///     检查执行策略导入必填项
    /// </summary>
    private bool CheckInPortData(ScriptExecutionInPortDtoInput inPort, int line, DeviceVariableOutput result)
    {
        if (string.IsNullOrEmpty(inPort.ScriptName))
        {
            result.ErrorCount += 1;
            result.ErrorColumn.Add(new ErrorColumn { Error = $"【执行策略】 第【{line}】行 【脚本名称】 不能是空！ ", Line = line });
            return false;
        }

        switch (inPort.ExecutionStrategyType.GetEnum<ExecutionStrategyTypeEnum>())
        {
            case ExecutionStrategyTypeEnum.网关启动触发:
                break;
            case ExecutionStrategyTypeEnum.定时触发:
                {
                    if (inPort.Period <= 0)
                    {
                        result.ErrorCount += 1;
                        result.ErrorColumn.Add(new ErrorColumn { Error = $"【执行策略】 第【{line}】行 【定时执行】 执行周期是必填！ ", Line = line });
                        return false;
                    }

                    if (inPort.PeriodUnit.GetEnum<ExecutionStrategyByTimePeriodUnitEnum>() <= 0)
                    {
                        result.ErrorCount += 1;
                        result.ErrorColumn.Add(new ErrorColumn { Error = $"【执行策略】 第【{line}】行 【定时执行】 执行周期单位是必填！ ", Line = line });
                        return false;
                    }

                    break;
                }

            case ExecutionStrategyTypeEnum.属性触发:
                if (string.IsNullOrEmpty(inPort.Name))
                {
                    result.ErrorCount += 1;
                    result.ErrorColumn.Add(new ErrorColumn { Error = $"【执行策略】 第【{line}】行 【属性触发】 属性名称是必填项！ ", Line = line });
                    return false;
                }

                if (string.IsNullOrEmpty(inPort.Compare))
                {
                    result.ErrorCount += 1;
                    result.ErrorColumn.Add(new ErrorColumn { Error = $"【执行策略】 第【{line}】行 【属性触发】 操作符是必填项！ ", Line = line });
                    return false;
                }

                break;
            case ExecutionStrategyTypeEnum.变量触发:
                break;
            default:
                result.ErrorCount += 1;
                result.ErrorColumn.Add(new ErrorColumn { Error = $"【执行策略】 第【{line}】行 请选择执行模式！ ", Line = line });
                return false;
        }

        return true;
    }
}