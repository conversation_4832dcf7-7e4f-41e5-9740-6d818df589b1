using FengLink_Application_Entity.Entity;

namespace Extras.MQTT.Models;
public class GatewayConfig
{
    /// <summary>
    /// 网关
    /// </summary>
    public Gateway Gateway { get; set; }
    /// <summary>
    /// 驱动
    /// </summary>
    public List<Driver> Driver { get; set; }
    /// <summary>
    /// 网络配置
    /// </summary>
    public string NetWork { get; set; }
    /// <summary>
    /// 传输池
    /// </summary>
    public List<TransPond> TransPond { get; set; }
    /// <summary>
    /// 脚本
    /// </summary>
    public List<Script> Script { get; set; }
    /// <summary>
    /// 脚本策略
    /// </summary>
    public List<ScriptExecutionStrategy> ScriptStrategy { get; set; }
    /// <summary>
    /// 设备列表
    /// </summary>
    public List<GatewayDevice> Device { get; set; }
}
