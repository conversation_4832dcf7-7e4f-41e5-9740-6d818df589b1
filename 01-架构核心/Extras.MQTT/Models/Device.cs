using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using Extras.MQTT.Dto;

namespace Extras.MQTT.Models;

public class GatewayDevice
{
    /// <summary>
    /// 设备名称
    /// </summary>
    public string DeviceName { get; set; }

    /// <summary>
    /// 设备别名
    /// </summary>
    public string OtherName { get; set; }

    /// <summary>
    /// 数据采集上报规则
    /// </summary>
    public int DataCollectionReportingRule { get; set; }

    /// <summary>
    /// 排序
    /// </summary>
    public int Index { get; set; }

    /// <summary>
    /// 描述
    /// </summary>
    public string Description { get; set; }

    /// <summary>
    /// 驱动ID
    /// </summary>
    public long DriverId { get; set; }

    /// <summary>
    /// 设备信息
    /// </summary>
    public object DeviceInfo { get; set; }

    /// <summary>
    /// 设备信息扩展
    /// </summary>
    public List<object> DeviceInfoExtensions { get; set; }

    /// <summary>
    /// 是否启用
    /// </summary>
    public bool Enable { get; set; }

    /// <summary>
    /// 是否释放
    /// </summary>
    public bool Release { get; set; }

    /// <summary>
    /// 设备组ID
    /// </summary>
    public long DeviceGroupId { get; set; }

    /// <summary>
    /// 驱动
    /// </summary>
    public object Driver { get; set; }

    /// <summary>
    /// 设备配置
    /// </summary>
    public List<DeviceConfig> DeviceConfigs { get; set; }

    /// <summary>
    /// 设备变量
    /// </summary>
    public List<DeviceVariable> DeviceVariable { get; set; }

    /// <summary>
    /// 设备事件日志
    /// </summary>
    public object DeviceEventLog { get; set; }

    /// <summary>
    /// 设备事件
    /// </summary>
    public List<DeviceEvent> DeviceEvent { get; set; }

    /// <summary>
    /// 设备变量映射
    /// </summary>
    public object DeviceVariableMapping { get; set; }

    /// <summary>
    /// 数据采集规则名称
    /// </summary>
    public string DataCollectionReportingRuleName { get; set; }

    /// <summary>
    /// 驱动名称
    /// </summary>
    public string DriverName { get; set; }

    /// <summary>
    /// IP地址
    /// </summary>
    public string IpAddress { get; set; }

    /// <summary>
    /// 是否连接
    /// </summary>
    public bool IsConnect { get; set; }

    /// <summary>
    /// 是否释放所有
    /// </summary>
    public bool ReleaseAll { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime? CreatedTime { get; set; }

    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime? UpdatedTime { get; set; }

    /// <summary>
    /// 创建用户ID
    /// </summary>
    public long? CreatedUserId { get; set; }

    /// <summary>
    /// 更新用户ID
    /// </summary>
    public long? UpdatedUserId { get; set; }

    /// <summary>
    /// 创建用户名称
    /// </summary>
    public string CreatedUserName { get; set; }

    /// <summary>
    /// 更新用户名称
    /// </summary>
    public string UpdatedUserName { get; set; }

    /// <summary>
    /// ID
    /// </summary>
    public long Id { get; set; }
}

/// <summary>
/// 设备配置
/// </summary>
public class DeviceConfig
{
    /// <summary>
    /// 设备配置名称
    /// </summary>
    public string DeviceConfigName { get; set; }

    /// <summary>
    /// 描述
    /// </summary>
    public string Description { get; set; }

    /// <summary>
    /// 值
    /// </summary>
    public string Value { get; set; }

    /// <summary>
    /// 枚举信息
    /// </summary>
    public string EnumInfo { get; set; }

    /// <summary>
    /// 设备ID
    /// </summary>
    public long DeviceId { get; set; }

    /// <summary>
    /// 设备
    /// </summary>
    public object Device { get; set; }

    /// <summary>
    /// 组名称
    /// </summary>
    public string GroupName { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    public string Remark { get; set; }

    /// <summary>
    /// 是否必填
    /// </summary>
    public bool IsRequired { get; set; }

    /// <summary>
    /// 是否显示
    /// </summary>
    public bool Display { get; set; }

    /// <summary>
    /// 显示表达式
    /// </summary>
    public string DisplayExpress { get; set; }

    /// <summary>
    /// 排序
    /// </summary>
    public int Order { get; set; }

    /// <summary>
    /// 类型
    /// </summary>
    public string Type { get; set; }

    /// <summary>
    /// ID
    /// </summary>
    public long Id { get; set; }
}

/// <summary>
/// 设备事件
/// </summary>
public class DeviceEvent
{
    /// <summary>
    /// 设备ID
    /// </summary>
    public long DeviceId { get; set; }

    /// <summary>
    /// 触发事件类型
    /// </summary>
    public int TriggerEventType { get; set; }

    /// <summary>
    /// 事件名称
    /// </summary>
    public string EventName { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    public bool Status { get; set; }

    /// <summary>
    /// 自定义事件配置
    /// </summary>
    public CustomEventConfig CustomEventConfig { get; set; }

    /// <summary>
    /// 自定义事件条件列表
    /// </summary>
    public List<CustomEventWhere> CustomEventWhereList { get; set; }

    /// <summary>
    /// 触发事件类型显示
    /// </summary>
    public string TriggerEventTypeDisplay { get; set; }

    /// <summary>
    /// 最后时间
    /// </summary>
    public string LastTime { get; set; }

    /// <summary>
    /// 设备
    /// </summary>
    public object Device { get; set; }

    /// <summary>
    /// 设备事件日志
    /// </summary>
    public object DeviceEventLog { get; set; }

    /// <summary>
    /// ID
    /// </summary>
    public long Id { get; set; }
}

/// <summary>
/// 自定义事件配置
/// </summary>
public class CustomEventConfig
{
    /// <summary>
    /// 事件定时配置
    /// </summary>
    public EventTimedConfig EventTimedConfig { get; set; }

    /// <summary>
    /// 事件设备配置
    /// </summary>
    public object EventDeviceConfig { get; set; }

    /// <summary>
    /// 事件设备变量配置
    /// </summary>
    public object EventDeviceVariableConfig { get; set; }
}

/// <summary>
/// 事件定时配置
/// </summary>
public class EventTimedConfig
{
    /// <summary>
    /// 执行类型
    /// </summary>
    public int ExecutionType { get; set; }

    /// <summary>
    /// 执行时间
    /// </summary>
    public object ExecutionTime { get; set; }

    /// <summary>
    /// 执行Cron
    /// </summary>
    public ExecutionCron ExecutionCron { get; set; }
}

/// <summary>
/// 执行Cron
/// </summary>
public class ExecutionCron
{
    /// <summary>
    /// Cron表达式
    /// </summary>
    public string Cron { get; set; }
}

/// <summary>
/// 自定义事件条件
/// </summary>
public class CustomEventWhere
{
    /// <summary>
    /// 触发条件列表
    /// </summary>
    public List<TriggerCondition> TriggerConditionList { get; set; }

    /// <summary>
    /// 自定义事件动作列表
    /// </summary>
    public List<CustomEventAction> CustomEventActionList { get; set; }

    /// <summary>
    /// 表达式
    /// </summary>
    public string Expressions { get; set; }

    /// <summary>
    /// 防抖
    /// </summary>
    public bool AntiShake { get; set; }

    /// <summary>
    /// 防抖时间
    /// </summary>
    public int AntiShakeTime { get; set; }

    /// <summary>
    /// 排序
    /// </summary>
    public int Order { get; set; }

    /// <summary>
    /// GUID
    /// </summary>
    public string Guid { get; set; }
}

/// <summary>
/// 触发条件
/// </summary>
public class TriggerCondition
{
    /// <summary>
    /// 事件设备变量
    /// </summary>
    public List<EventDeviceVariable> EventDeviceVariable { get; set; }

    /// <summary>
    /// 类型
    /// </summary>
    public string Type { get; set; }
}

/// <summary>
/// 事件设备变量
/// </summary>
public class EventDeviceVariable
{
    /// <summary>
    /// 标识符
    /// </summary>
    public string Identifier { get; set; }

    /// <summary>
    /// 比较
    /// </summary>
    public int Compare { get; set; }

    /// <summary>
    /// 值
    /// </summary>
    public string Value { get; set; }

    /// <summary>
    /// 数据类型
    /// </summary>
    public int DataType { get; set; }

    /// <summary>
    /// 类型
    /// </summary>
    public string Type { get; set; }
}

/// <summary>
/// 自定义事件动作
/// </summary>
public class CustomEventAction
{
    /// <summary>
    /// 排序
    /// </summary>
    public int Order { get; set; }

    /// <summary>
    /// 动作名称
    /// </summary>
    public string ActionName { get; set; }

    /// <summary>
    /// 事件动作类型
    /// </summary>
    public int EventActionType { get; set; }

    /// <summary>
    /// 设备变量写入
    /// </summary>
    public List<object> DeviceVariableWrite { get; set; }

    /// <summary>
    /// 设备变量读取
    /// </summary>
    public List<object> DeviceVariableRead { get; set; }

    /// <summary>
    /// 消息推送配置
    /// </summary>
    public object MessagePushConfig { get; set; }

    /// <summary>
    /// 脚本配置
    /// </summary>
    public ScriptConfig ScriptConfig { get; set; }

    /// <summary>
    /// 休眠配置
    /// </summary>
    public object SleepConfig { get; set; }

    /// <summary>
    /// 触发条件列表
    /// </summary>
    public List<object> TriggerConditionList { get; set; }

    /// <summary>
    /// 表达式
    /// </summary>
    public object Expressions { get; set; }
}

/// <summary>
/// 脚本配置
/// </summary>
public class ScriptConfig
{
    /// <summary>
    /// 内容
    /// </summary>
    public string Content { get; set; }

    /// <summary>
    /// 参数
    /// </summary>
    public object Params { get; set; }
}

/// <summary>
/// 设备变量
/// </summary>
public class DeviceVariable
{
    /// <summary>
    /// 标识符
    /// </summary>
    public string Identifier { get; set; }

    /// <summary>
    /// 名称
    /// </summary>
    public string Name { get; set; }

    /// <summary>
    /// 默认值
    /// </summary>
    public string DefaultValue { get; set; }

    /// <summary>
    /// 长度
    /// </summary>
    public int Length { get; set; }

    /// <summary>
    /// 转换类型
    /// </summary>
    public TransPondDataTypeEnum TransitionType { get; set; }

    /// <summary>
    /// 值来源
    /// </summary>
    public ValueSourceEnum ValueSource { get; set; }

    /// <summary>
    /// 单位
    /// </summary>
    public string Unit { get; set; }

    /// <summary>
    /// 排序
    /// </summary>
    public int Order { get; set; }

    /// <summary>
    /// 自定义
    /// </summary>
    public string Custom { get; set; }

    /// <summary>
    /// 表达式
    /// </summary>
    public string Expressions { get; set; }

    /// <summary>
    /// 脚本
    /// </summary>
    public string Script { get; set; }

    /// <summary>
    /// 设备ID
    /// </summary>
    public long DeviceId { get; set; }

    /// <summary>
    /// 发送类型
    /// </summary>
    public SendTypeEnum SendType { get; set; }

    /// <summary>
    /// 是否启用
    /// </summary>
    public bool Enable { get; set; }

    /// <summary>
    /// 标签
    /// </summary>
    public List<string> Tags { get; set; }

    /// <summary>
    /// 周期
    /// </summary>
    public int Period { get; set; }

    /// <summary>
    /// 归档时间
    /// </summary>
    public int ArchiveTime { get; set; }

    /// <summary>
    /// 描述
    /// </summary>
    public string Description { get; set; }

    /// <summary>
    /// 变量过滤器
    /// </summary>
    public DeviceVariableFilter DeviceVariableFilter { get; set; }

    /// <summary>
    /// 变量扩展信息
    /// </summary>
    public DeviceVariableEx DeviceVariableEx { get; set; }

    /// <summary>
    /// 是否系统变量
    /// </summary>
    public bool IsSystem { get; set; }

    /// <summary>
    /// 是否持久化
    /// </summary>
    public bool Persistence { get; set; }

    /// <summary>
    /// 是否释放
    /// </summary>
    public bool Release { get; set; }

    /// <summary>
    /// 设备名称
    /// </summary>
    public string DeviceName { get; set; }

    /// <summary>
    /// 设备变量映射
    /// </summary>
    public object DeviceVariableMapping { get; set; }

    /// <summary>
    /// ID
    /// </summary>
    public long Id { get; set; }
}

/// <summary>
///     数据来源 1:直接取值; 2:计算赋值；3：手动写值
/// </summary>
public enum ValueSourceEnum
{
    /// <summary>
    ///     直接取值
    /// </summary>
    [Description("直接取值")]
    [Display(Name = "直接取值")]
    Get = 1,

    /// <summary>
    ///     计算赋值
    /// </summary>
    [Description("计算赋值")]
    [Display(Name = "计算赋值")]
    Calculate = 2,

    /// <summary>
    ///     手动写值
    /// </summary>
    [Description("手动写值")]
    [Display(Name = "手动写值")]
    Static = 3
}

/// <summary>
/// 设备变量过滤器
/// </summary>
public class DeviceVariableFilter
{
    /// <summary>
    /// 最小值
    /// </summary>
    public decimal Min { get; set; }

    /// <summary>
    /// 最大值
    /// </summary>
    public decimal Max { get; set; }

    /// <summary>
    /// 最小过滤类型
    /// </summary>
    public int MinFilterType { get; set; }

    /// <summary>
    /// 设置最小值
    /// </summary>
    public decimal SetMin { get; set; }

    /// <summary>
    /// 设置最大值
    /// </summary>
    public decimal SetMax { get; set; }

    /// <summary>
    /// 最大过滤类型
    /// </summary>
    public int MaxFilterType { get; set; }

    /// <summary>
    /// 是否保存
    /// </summary>
    public bool Save { get; set; }
}

/// <summary>
/// 设备变量扩展信息
/// </summary>
public class DeviceVariableEx
{
    /// <summary>
    /// 保护类型
    /// </summary>
    public ProtectTypeEnum ProtectType { get; set; }

    /// <summary>
    /// 保护类型名称
    /// </summary>
    public string ProtectTypeName { get; set; }

    /// <summary>
    /// 方法
    /// </summary>
    public string Method { get; set; }

    /// <summary>
    /// 寄存器地址
    /// </summary>
    public string RegisterAddress { get; set; }

    /// <summary>
    /// 数据类型
    /// </summary>
    public GatewayDataTypeEnum DataType { get; set; }

    /// <summary>
    /// 数据类型名称
    /// </summary>
    public string DataTypeName { get; set; }

    /// <summary>
    /// 编码
    /// </summary>
    public StringEnum Encoding { get; set; }

    /// <summary>
    /// 编码名称
    /// </summary>
    public string EncodingName { get; set; }

    /// <summary>
    /// 长度
    /// </summary>
    public int Length { get; set; }
}