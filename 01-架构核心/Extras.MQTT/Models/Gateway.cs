namespace Extras.MQTT.Models;

public class Gateway
{
    /// <summary>
    ///     名称
    /// </summary>
    public string Name { get; set; }
    /// <summary>
    ///     SN
    /// </summary>
    public string Sn { get; set; }
    /// <summary>
    ///     型号
    /// </summary>
    public string Model { get; set; }
    /// <summary>
    ///     版本
    /// </summary>
    public string Version { get; set; }
    /// <summary>
    ///     运行时间
    /// </summary>
    public string Config { get; set; }
    /// <summary>
    ///     脚本
    /// </summary>
    public List<Script> Script { get; set; }
}

public class Script
{
    /// <summary>
    /// 主键
    /// </summary>
    public long Id { get; set; }
    /// <summary>
    /// 名称
    /// </summary>
    public string Name { get; set; }
    /// <summary>
    /// 脚本类型
    /// </summary>
    public int ScriptType { get; set; }
    /// <summary>
    /// 脚本内容
    /// </summary>
    public string Content { get; set; }
    /// <summary>
    /// 脚本类型名称
    /// </summary>
    public string ScriptTypeName { get; set; }
    /// <summary>
    /// 描述
    /// </summary>
    public string Describe { get; set; }
    /// <summary>
    /// 方法
    /// </summary>
    public int Method { get; set; }
    /// <summary>
    /// 是否启用
    /// </summary>
    public bool Enable { get; set; }
}