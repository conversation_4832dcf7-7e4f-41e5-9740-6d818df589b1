using MQTTnet.Extensions.Rpc;

namespace FengLink_MQTT;

public sealed class TgMqttRpcClientTopicGenerationStrategy : IMqttRpcClientTopicGenerationStrategy
{
    public const string RpcTopic = "{0}/+";
    public string sn = "";

    public MqttRpcTopicPair CreateRpcTopics(TopicGenerationContext context)
    {
        if (context == null)
        {
            throw new ArgumentNullException(nameof(context));
        }

        if (context.MethodName.Contains("/") || context.MethodName.Contains("+") || context.MethodName.Contains("#"))
        {
            throw new ArgumentException("The method name cannot contain /, + or #.");
        }

        var requestTopic = $"{context.MethodName}/{sn}";
        var responseTopic = requestTopic + "/response"; 
            
        return new MqttRpcTopicPair
        {
            RequestTopic = requestTopic,
            ResponseTopic = responseTopic
        };
    }
}