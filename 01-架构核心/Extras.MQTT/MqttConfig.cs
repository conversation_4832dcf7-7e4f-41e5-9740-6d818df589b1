namespace Extras.MQTT;

/// <summary>
///     MQTT配置
/// </summary>
public class MqttConfig
{
    /// <summary>
    ///     主机地址
    /// </summary>
    public string Host { get; set; } = "127.0.0.1";
    /// <summary>
    ///     端口
    /// </summary>
    public int Port { get; set; } = 8090;
    /// <summary>
    ///     用户名
    /// </summary>
    public string Username { get; set; } = "fengedge";
    /// <summary>
    ///     密码
    /// </summary>
    public string Password { get; set; } = "123456";
    /// <summary>
    ///     连接队列长度
    /// </summary>
    public int ConnectionBacklog { get; set; } = 1000;
}