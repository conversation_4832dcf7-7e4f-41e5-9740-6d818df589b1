using System.ComponentModel;
using Common.Models;
using Extras.MQTT.Dto;
using Extras.MQTT.Models;
using Furion.DependencyInjection;
using Furion.DynamicApiController;
using Mapster;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Yitter.IdGenerator;

namespace Extras.MQTT;

/// <summary>
///     子设备列表
/// </summary>
public class GatewayDeviceService : IDynamicApiController, ITransient
{
    /// <summary>
    ///     MQTT服务
    /// </summary>
    private readonly MqttService _mqttServer;

    /// <summary>
    ///     忽略的驱动
    /// </summary>
    private readonly List<string> _ignoreList = new() { "PORCHESON_Ps660Bm.dll", "TechMationAk.dll", "PORCHESON_Ps660Am.dll" };

    /// <summary>
    ///     构造函数
    /// </summary>
    /// <param name="mqttServer"></param>
    public GatewayDeviceService(MqttService mqttServer)
    {
        _mqttServer = mqttServer;
    }

    /// <summary>
    ///     子设备列表
    /// </summary>
    /// <returns></returns>
    [HttpGet("/gatewayDevice/{sn}/page")]
    [DisplayName("网关子设备-列表")]
    public async Task<List<GatewayDevice>> GatewayDevicePage(string sn)
    {
        try
        {
            // 获取配置
            GatewayConfig? config = await _mqttServer.LoadDataFromJsonFileAsync<GatewayConfig>($"{sn}.json");
            if (config?.Device == null)
            {
                return new List<GatewayDevice>();
            }

            // 获取设备列表
            List<GatewayDevice> devices = config.Device;

            // 清空每个设备的 DeviceVariable
            foreach (GatewayDevice device in devices)
            {
                device.DeviceVariable = new List<DeviceVariable>();
                device.DeviceEvent = new List<DeviceEvent>();
            }

            return devices;
        }
        catch
        {
            return new List<GatewayDevice>();
        }
    }

    // /// <summary>
    // ///     子设备详情
    // /// </summary>
    // /// <returns></returns>
    // [HttpGet("/gatewayDevice/detail")]
    // [DisplayName("网关子设备-详情")]
    // public async Task<GatewayDevice> GatewayDeviceDetail([FromQuery] BaseId input)
    // {
    //     return await _device.AsQueryable()
    //         .Where(w => w.Id == input.Id)
    //         .Includes(w => w.DeviceConfigs)
    //         .Includes(w => w.DeviceVariable)
    //         .FirstAsync();
    // }

    /// <summary>
    ///     子设备创建
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/gatewayDevice/{sn}/add")]
    [DisplayName("网关子设备-创建")]
    public async Task<long> GatewayDeviceAdd(string sn, GatewayDeviceAddInput input)
    {
        GatewayConfig? config = await _mqttServer.LoadDataFromJsonFileAsync<GatewayConfig>($"{sn}.json");
        if (config == null)
        {
            config = new GatewayConfig
            {
                Device = new List<GatewayDevice>()
            };
        }

        // 验证协议是否存在
        List<Driver> drivers = config.Driver
            .Where(d => d.Id == input.DriverId)
            .ToList();

        if (!drivers.Any())
        {
            throw Oops.Oh("协议版本不一致，请先同步配置");
        }

        Driver driver = drivers.First();

        GatewayDevice device = input.Adapt<GatewayDevice>();
        device.Enable = true;
        device.Index = 99;
        device.Id = YitIdHelper.NextId();
        device.DeviceConfigs = new List<DeviceConfig>();
        device.DeviceVariable = new List<DeviceVariable>();

        if (driver.Configs != null)
        {
            foreach (Config driConfig in driver.Configs)
            {
                DeviceConfig deviceConfig = driConfig.Adapt<DeviceConfig>();
                deviceConfig.DeviceId = device.Id;
                device.DeviceConfigs.Add(deviceConfig);
            }
        }

        // cnc设备添加标准属性
        if (driver.DriverType == (int)DriverTypeEnum.Cnc ||
            (driver.DriverType == (int)DriverTypeEnum.Plc && _ignoreList.Contains(driver.DriverName)))
        {
            device.DeviceVariable.AddRange(CreateCncVariables(device, driver));
        }

        // 添加设备到配置
        config.Device.Add(device);

        // 保存配置
        await _mqttServer.SaveDataToJsonFileAsync($"{sn}.json", config);

        return device.Id;
    }

    /// <summary>
    ///     导入设备
    /// </summary>
    /// <returns></returns>
    [HttpPost("/gatewayDevice/{sn}/inPortAll")]
    [DisplayName("网关子设备-导入")]
    public async Task InPortAll(string sn, [FromForm] InPortAllInput input)
    {
        GatewayConfig? config = await _mqttServer.LoadDataFromJsonFileAsync<GatewayConfig>($"{sn}.json");
        if (config == null)
        {
            throw Oops.Oh("暂未同步配置");
        }

        string content;
        using (StreamReader reader = new(input.File.OpenReadStream()))
        {
            content = await reader.ReadToEndAsync();
        }

        List<GatewayDevice>? deviceList = new();
        try
        {
            deviceList = JsonConvert.DeserializeObject<List<GatewayDevice>>(content);
        }
        catch
        {
            deviceList.Add(JsonConvert.DeserializeObject<GatewayDevice>(content));
        }

        // 保存配置
        await _mqttServer.SaveDataToJsonFileAsync($"{sn}.json", config);
    }

    /// <summary>
    ///     导出设备全部信息
    /// </summary>
    /// <returns></returns>
    [HttpPost("/gatewayDevice/{sn}/exPortAll")]
    [DisplayName("网关子设备-导出全部")]
    public async Task<List<GatewayDevice>> ExPortAll(string sn)
    {
        GatewayConfig? config = await _mqttServer.LoadDataFromJsonFileAsync<GatewayConfig>($"{sn}.json");
        if (config == null)
        {
            throw Oops.Oh("暂未同步配置");
        }

        return config.Device;
    }

    /// <summary>
    ///     设备复制
    /// </summary>
    /// <returns></returns>
    [HttpPost("/gatewayDevice/{sn}/copy")]
    [DisplayName("网关子设备-复制")]
    public async Task Copy(string sn, BaseId input)
    {
        GatewayConfig? config = await _mqttServer.LoadDataFromJsonFileAsync<GatewayConfig>($"{sn}.json");
        if (config == null)
            throw Oops.Oh("暂未同步配置");

        var sourceDevice = config.Device.FirstOrDefault(f => f.Id == input.Id);
        if (sourceDevice == null)
            throw Oops.Oh("设备不存在！");

        // 创建设备的深拷贝
        var newDevice = JsonConvert.DeserializeObject<GatewayDevice>(JsonConvert.SerializeObject(sourceDevice));
        if (newDevice == null)
            throw Oops.Oh("复制设备失败！");

        // 设置新设备的属性
        newDevice.Id = YitIdHelper.NextId();
        newDevice.DeviceName += "_copy" + DateTime.Now.ToString("ssff");
        newDevice.Enable = false;

        // 更新设备配置的ID和关联
        foreach (var deviceConfig in newDevice.DeviceConfigs)
        {
            deviceConfig.Id = YitIdHelper.NextId();
            deviceConfig.DeviceId = newDevice.Id;
        }

        // 更新设备变量的ID和关联
        foreach (var variable in newDevice.DeviceVariable)
        {
            variable.Id = YitIdHelper.NextId();
            variable.DeviceId = newDevice.Id;
        }

        // 添加新设备到配置中
        config.Device.Add(newDevice);

        // 保存配置
        await _mqttServer.SaveDataToJsonFileAsync($"{sn}.json", config);
    }

    /// <summary>
    ///     修改设备
    /// </summary>
    /// <param name="sn"></param>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/gatewayDevice/{sn}/update")]
    [DisplayName("网关子设备-修改")]
    public async Task Update(string sn, GatewayDevice input)
    {
        GatewayConfig? config = await _mqttServer.LoadDataFromJsonFileAsync<GatewayConfig>($"{sn}.json");
        if (config == null)
        {
            throw Oops.Oh("配置不存在！");
        }

        List<GatewayDevice> devices = config.Device;

        // 检查设备名称是否重复
        if (devices.Any(d => d.Id != input.Id &&
                             d.DeviceName == input.DeviceName))
        {
            throw Oops.Oh("设备名称已存在！");
        }

        // 查找并更新设备
        int deviceIndex = devices.FindIndex(d => d.Id == input.Id);
        if (deviceIndex == -1)
        {
            throw Oops.Oh("设备不存在！");
        }

        // 保留原有的 Status、DriverId、EdgeGatewayId
        GatewayDevice originalDevice = devices[deviceIndex];
        input.DriverId = originalDevice.DriverId;

        // 更新设备
        devices[deviceIndex] = input;

        await _mqttServer.SaveDataToJsonFileAsync($"{sn}.json", config);
    }

    /// <summary>
    ///     设备启用/禁用
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/gatewayDevice/{sn}/enable")]
    [DisplayName("网关子设备-启用/禁用")]
    public async Task Enable(string sn, EnableInput<long> input)
    {
        GatewayConfig? config = await _mqttServer.LoadDataFromJsonFileAsync<GatewayConfig>($"{sn}.json");
        if (config == null)
        {
            throw Oops.Oh("配置不存在！");
        }

        List<GatewayDevice> devices = config.Device;
        GatewayDevice? device = devices.FirstOrDefault(d => d.Id == input.Id);

        if (device == null)
        {
            throw Oops.Oh("设备不存在！");
        }

        device.Enable = input.Enable;
        await _mqttServer.SaveDataToJsonFileAsync($"{sn}.json", config);
    }

    /// <summary>
    ///     删除设备
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/gatewayDevice/{sn}/delete")]
    [DisplayName("网关子设备-删除")]
    public async Task<bool> Delete(string sn, BaseId input)
    {
        GatewayConfig? config = await _mqttServer.LoadDataFromJsonFileAsync<GatewayConfig>($"{sn}.json");
        if (config == null)
        {
            throw Oops.Oh("配置不存在！");
        }

        // 
        List<GatewayDevice> devices = config.Device;
        int deviceIndex = devices.FindIndex(d => d.Id == input.Id);
        // 
        if (deviceIndex == -1)
        {
            throw Oops.Oh("设备不存在！");
        }

        // 
        devices.RemoveAt(deviceIndex);
        await _mqttServer.SaveDataToJsonFileAsync($"{sn}.json", config);

        return true;
    }

    #region 私有方法

    /// <summary>
    ///     增加Cnc类型标准属性
    /// </summary>
    private List<DeviceVariable> CreateCncVariables(GatewayDevice device, Driver driver)
    {
        List<DeviceVariable> deviceVariableList = (from method in driver.Methods
                                                   select new DeviceVariable
                                                   {
                                                       Id = YitIdHelper.NextId(),
                                                       SendType = SendTypeEnum.Always,
                                                       Description = method.Desc,
                                                       Identifier = method.Identifier,
                                                       Length = method.TransitionType == TransPondDataTypeEnum.Double ? 3 : method.TransitionType == TransPondDataTypeEnum.String ? 30 : 0,
                                                       Name = method.Text,
                                                       DeviceId = device.Id,
                                                       ValueSource = ValueSourceEnum.Get,
                                                       TransitionType = method.TransitionType,
                                                       DeviceVariableEx = new DeviceVariableEx { Method = method.Value, ProtectType = ProtectTypeEnum.ReadOnly },
                                                       Enable = true
                                                   }).ToList();

        return deviceVariableList;
    }

    #endregion
}