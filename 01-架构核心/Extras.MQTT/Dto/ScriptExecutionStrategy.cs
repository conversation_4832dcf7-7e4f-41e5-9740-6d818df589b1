using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using Extras.MQTT.Dto;
using IotPlatform.Core.Extension;
using Microsoft.AspNetCore.Http;
using MiniExcelLibs.Attributes;
using Newtonsoft.Json;

namespace FengLink_Application_Entity.Entity;

/// <summary>
///     新增执行策略
/// </summary>
public class EdgeScriptExecutionStrategyAddInput
{
    /// <summary>
    ///     边缘计算脚本Id
    /// </summary>
    [Required]
    public long EdgeComputingScriptId { get; set; }

    /// <summary>
    ///     执行模式:1网关启动；2定时；3属性；4变量；5消息；6Http
    /// </summary>
    [Required]
    public ExecutionStrategyTypeEnum ExecutionStrategyType { get; set; }

    /// <summary>
    ///     周期执行的配置实体
    /// </summary>
    public ExecutionTime? ExecutionTime { get; set; }

    /// <summary>
    ///     属性触发任务的配置实体
    /// </summary>
    public PropertyModel? Propertys { get; set; }

    /// <summary>
    ///     变量触发的配置实体
    /// </summary>
    public VariableModel? Variable { get; set; }

    /// <summary>
    ///     描述
    /// </summary>
    public string Describe { get; set; }
}

/// <summary>
///     更新执行策略
/// </summary>
public class ScriptExecutionStrategyUpdateInput : EdgeScriptExecutionStrategyAddInput
{
    /// <summary>
    ///     Id
    /// </summary>
    [Required]
    public long Id { get; set; }
}

/// <summary>
///     脚本执行策略
/// </summary>
public class ScriptExecutionStrategy
{
    /// <summary>
    /// 
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    ///     边缘计算脚本Id
    /// </summary>
    public long EdgeComputingScriptId { get; set; }

    /// <summary>
    ///     执行模式:1网关启动；2定时；3属性；4变量
    /// </summary>
    public ExecutionStrategyTypeEnum ExecutionStrategyType { get; set; }

    /// <summary>
    ///     执行模式配置
    /// </summary>
    public string? Config { get; set; }

    /// <summary>
    ///     描述
    /// </summary>
    public string? Describe { get; set; }

    /// <summary>
    ///     启用/禁用
    /// </summary>
    public bool Enable { get; set; } = true;

    /// <summary>
    ///     周期执行的配置实体
    /// </summary>
    [SugarColumn(IsIgnore = true)]
    public ExecutionTime? ExecutionTime => ExecutionStrategyType == ExecutionStrategyTypeEnum.定时触发 && Config != null ? JsonConvert.DeserializeObject<ExecutionTime>(Config) : null;

    /// <summary>
    ///     属性触发任务的配置实体
    /// </summary>
    public PropertyModel? Propertys => ExecutionStrategyType == ExecutionStrategyTypeEnum.属性触发 && Config != null ? JsonConvert.DeserializeObject<PropertyModel>(Config) : null;

    /// <summary>
    ///     变量触发的配置实体
    /// </summary>
    public VariableModel? Variable => ExecutionStrategyType == ExecutionStrategyTypeEnum.变量触发 && Config != null ? JsonConvert.DeserializeObject<VariableModel>(Config) : null;

    #region 忽略字段

    /// <summary>
    ///     执行模式:1网关启动；2定时；3属性；4变量；5消息；6Http
    /// </summary>
    public string ExecutionStrategyTypeName => ExecutionStrategyType.GetDescription();

    #endregion
}

/// <summary>
///     周期执行
/// </summary>
public class ExecutionTime
{
    /// <summary>
    ///     执行方式：1周期执行；2Cron
    /// </summary>
    public ExecutionTypeEnum ExecutionType { get; set; } = ExecutionTypeEnum.周期执行;

    /// <summary>
    ///     表达式
    /// </summary>
    public string? Cron { get; set; }

    /// <summary>
    ///     执行周期
    /// </summary>
    [SugarColumn(ColumnDescription = "采集周期")]
    public int Period { get; set; } = 5;

    /// <summary>
    ///     时间单位:1秒；2分钟；3小时
    /// </summary>
    [SugarColumn(ColumnDescription = "时间单位:1秒；2分钟；3小时")]
    public ExecutionStrategyByTimePeriodUnitEnum PeriodUnit { get; set; } = ExecutionStrategyByTimePeriodUnitEnum.秒;

    /// <summary>
    ///     1：触发时总是执行；2：触发时上周期未执行完毕则不执行；
    /// </summary>
    public short? Type { get; set; }
}
/// <summary>
/// 
/// </summary>
public class EdgeComputingScriptAddInput
{
    /// <summary>
    ///     脚本名称
    /// </summary>
    [Required]
    public string Name { get; set; }

    /// <summary>
    ///     脚本内容
    /// </summary>
    [Required]
    public string Content { get; set; }

    /// <summary>
    ///     描述
    /// </summary>
    public string Describe { get; set; }
}

/// <summary>
///     更新脚本
/// </summary>
public class EdgeComputingScriptUpdateInput : EdgeComputingScriptAddInput
{
    /// <summary>
    ///     Id
    /// </summary>
    [Required]
    public long Id { get; set; }
}

/// <summary>
///     执行方式：1周期执行；2Cron
/// </summary>
public enum ExecutionTypeEnum
{
    /// <summary>
    ///     周期执行
    /// </summary>
    [Description("周期执行")] 周期执行 = 1,

    /// <summary>
    ///     Cron
    /// </summary>
    [Description("Cron")] Cron = 2
}

/// <summary>
///     属性触发任务配置
/// </summary>
public class PropertyModel
{
    /// <summary>
    ///     属性名称
    /// </summary>
    public List<string> Name { get; set; }

    /// <summary>
    ///     1.值变化时执行;2:时间触发时执行;3:等于;4：大于；5：小于；6：不等于
    /// </summary>
    public CompareEnum Compare { get; set; }

    /// <summary>
    ///     值
    /// </summary>
    public string Value { get; set; }

    /// <summary>
    ///     首次是否触发
    /// </summary>
    public bool FirstEven { get; set; }
}

/// <summary>
///     变量触发任务
/// </summary>
public class VariableModel
{
    /// <summary>
    ///     变量Key
    /// </summary>
    public string Key { get; set; }
}

/// <summary>
///     操作符：1值变化时执行；2时间变化时执行；3
/// </summary>
public enum CompareEnum
{
    /// <summary>
    ///     值变化时执行
    /// </summary>
    [Description("值变化时执行")] 值变化时执行 = 1,

    /// <summary>
    ///     时间变化时执行
    /// </summary>
    [Description("时间变化时执行")] 时间变化时执行 = 2,

    /// <summary>
    ///     等于
    /// </summary>
    [Description("等于")] 等于 = 3,

    /// <summary>
    ///     大于
    /// </summary>
    [Description("大于")] 大于 = 4,

    /// <summary>
    ///     小于
    /// </summary>
    [Description("小于")] 小于 = 5,

    /// <summary>
    ///     不等于
    /// </summary>
    [Description("不等于")] 不等于 = 6
}

/// <summary>
///     周期执行-时间单位
/// </summary>
public enum ExecutionStrategyByTimePeriodUnitEnum
{
    /// <summary>
    ///     秒
    /// </summary>
    [Description("秒")] 秒 = 1,

    /// <summary>
    ///     分钟
    /// </summary>
    [Description("分钟")] 分钟 = 2,

    /// <summary>
    ///     小时
    /// </summary>
    [Description("小时")] 小时 = 3
}
/// <summary>
/// 边缘计算-执行策略导入
/// </summary>
public class ScriptExecutionInPortDtoInput
{
    /// <summary>
    /// 脚本名称
    /// </summary>
    [ExcelColumnName("脚本名称")]
    public string ScriptName { get; set; }
    /// <summary>
    ///     执行模式:1:网关启动；2：定时；3：属性；4：变量
    /// </summary>
    [ExcelColumnName("执行模式")]
    public string ExecutionStrategyType { get; set; }

    /// <summary>
    ///     执行周期
    /// </summary>
    [ExcelColumnName("执行周期 选填")]
    public int Period { get; set; }

    /// <summary>
    ///     时间单位:1秒；2分钟；3小时
    /// </summary>
    [ExcelColumnName("执行周期单位 选填")]
    public string PeriodUnit { get; set; }

    /// <summary>
    ///     属性名称
    /// </summary>
    [ExcelColumnName("属性名称 选填")]
    public string Name { get; set; }

    /// <summary>
    ///    操作符
    /// </summary>
    [ExcelColumnName("操作符 选填")]
    public string Compare { get; set; }

    /// <summary>
    ///     值
    /// </summary>
    [ExcelColumnName("值 选填")]
    public string Value { get; set; }

    /// <summary>
    ///     首次是否触发
    /// </summary>
    [ExcelColumnName("首次是否触发 选填")]
    public bool FirstEven { get; set; }

    /// <summary>
    ///     触发变量
    /// </summary>
    [ExcelColumnName("触发变量 选填")]
    public string Key { get; set; }

    /// <summary>
    ///     描述
    /// </summary>
    [SugarColumn(ColumnDescription = "描述 选填")]
    public string Describe { get; set; }

    /// <summary>
    /// 启用/禁用
    /// </summary>
    [SugarColumn(ColumnDescription = "是否启用")]
    public bool Enable { get; set; } = true;

}

/// <summary>
///     边缘计算-脚本导入
/// </summary>
public class ComputingScriptInPortDto
{
    /// <summary>
    ///     脚本名称
    /// </summary>
    [ExcelColumnName("脚本名称")]
    public string ScriptName { get; set; }

    /// <summary>
    ///     脚本内容
    /// </summary>
    [ExcelColumnName("脚本内容")]
    public string Content { get; set; }

    /// <summary>
    ///     启用/禁用
    /// </summary>
    [ExcelColumnName("是否启用")]
    public bool Enable { get; set; }

    /// <summary>
    ///     描述
    /// </summary>
    [ExcelColumnName("描述 选填")]
    public string Describe { get; set; }
}

/// <summary>
/// </summary>
public class ComputingScriptInPortInput
{
    /// <summary>
    ///     上传文件
    /// </summary>
    public IFormFile File { get; set; }

    /// <summary>
    ///     1:覆盖原数据,2:忽略
    /// </summary>
    public GatewayDeviceVariableInPortTypeEnum InPortType { get; set; }
}