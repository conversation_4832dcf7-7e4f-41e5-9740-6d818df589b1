using System.ComponentModel;
using Common.Models;
using Extras.DatabaseAccessor.SqlSugar.Internal;
using Extras.MQTT.Dto;
using Extras.MQTT.Models;
using FengLink_Application_Entity.Entity;
using Furion.DatabaseAccessor;
using Furion.DependencyInjection;
using Furion.DynamicApiController;
using Mapster;
using Microsoft.AspNetCore.Mvc;
using MiniExcelLibs;
using Yitter.IdGenerator;

namespace Extras.MQTT;

/// <summary>
///     边缘计算-脚本
/// </summary>
public class EdgeComputingScriptService : ITransient, IDynamicApiController
{
    private readonly MqttService _mqttServer;

    public EdgeComputingScriptService(MqttService serverHosted)
    {
        _mqttServer = serverHosted;
    }

    /// <summary>
    ///     边缘计算-脚本列表
    /// </summary>
    /// <returns></returns>
    [HttpGet("/edgeComputing/{sn}/script/page")]
    [DisplayName("边缘计算-脚本列表")]
    public async Task<SqlSugarPagedList<Script>> Page(string sn, [FromQuery] BasePageInput input)
    {
        GatewayConfig? config = await _mqttServer.LoadDataFromJsonFileAsync<GatewayConfig>($"{sn}.json");
        if (config == null)
        {
            return new SqlSugarPagedList<Script>();
        }

        var edgeComputingScriptList = config.Script;
        if (!string.IsNullOrEmpty(input.SearchValue))
        {
            edgeComputingScriptList = edgeComputingScriptList.Where(w => w.Name.Contains(input.SearchValue)
                                                                    || w.Describe.Contains(input.SearchValue)).ToList();
        }

        edgeComputingScriptList = edgeComputingScriptList.OrderBy(u => u.Name).Skip((input.PageNo - 1) * input.PageSize).Take(input.PageSize).ToList();

        return new SqlSugarPagedList<Script>
        {
            PageNo = input.PageNo,
            PageSize = input.PageSize,
            TotalRows = edgeComputingScriptList.Count,
            TotalPage = (int)Math.Ceiling(edgeComputingScriptList.Count / (double)input.PageSize),
            Rows = edgeComputingScriptList,
            HasPrevPage = input.PageNo > 1,
            HasNextPage = input.PageNo < (int)Math.Ceiling(edgeComputingScriptList.Count / (double)input.PageSize),
        };
    }

    /// <summary>
    ///     边缘计算-脚本详情
    /// </summary>
    /// <returns></returns>
    [HttpGet("/edgeComputing/{sn}/script/detail")]
    [DisplayName("边缘计算-脚本详情")]
    public async Task<Script> Detail(string sn, [FromQuery] BaseId input)
    {
        // 获取网关配置
        GatewayConfig? config = await _mqttServer.LoadDataFromJsonFileAsync<GatewayConfig>($"{sn}.json");
        if (config == null)
        {
            throw Oops.Oh("请先同步配置");
        }
        // 获取脚本
        var edgeComputingScript = config.Script.FirstOrDefault(f => f.Id == input.Id);
        if (edgeComputingScript == null)
            throw Oops.Oh("数据不存在！");
        return edgeComputingScript;
    }

    /// <summary>
    ///     边缘计算-脚本下拉
    /// </summary>
    /// <returns></returns>
    [HttpGet("/edgeComputing/{sn}/script/select")]
    [DisplayName("边缘计算-脚本下拉")]
    public async Task<dynamic> Select(string sn)
    {
        GatewayConfig? config = await _mqttServer.LoadDataFromJsonFileAsync<GatewayConfig>($"{sn}.json");
        if (config == null)
        {
            throw Oops.Oh("请先同步配置");
        }
        return config.Script
            .Where(w => w.Enable)
            .OrderBy(u => u.Name)
            .Select(s => new
            {
                Id = s.Id,
                Name = s.Name
            })
            .ToList();
    }

    /// <summary>
    ///     边缘计算-脚本-新增
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/edgeComputing/{sn}/script/add")]
    [DisplayName("边缘计算-脚本-新增")]
    public async Task Add(string sn, EdgeComputingScriptAddInput input)
    {
        GatewayConfig? config = await _mqttServer.LoadDataFromJsonFileAsync<GatewayConfig>($"{sn}.json");
        if (config == null)
        {
            throw Oops.Oh("请先同步配置");
        }
        var edgeComputingScript = config.Script.FirstOrDefault(f => f.Name == input.Name);
        if (edgeComputingScript != null)
            throw Oops.Oh($"脚本名称:【{input.Name}】,已存在！");
        edgeComputingScript = input.Adapt<Script>();
        edgeComputingScript.Id = YitIdHelper.NextId();
        config.Script.Add(edgeComputingScript);
        await _mqttServer.SaveDataToJsonFileAsync($"{sn}.json", config);
    }

    /// <summary>
    ///     边缘计算-脚本-修改
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/edgeComputing/{sn}/script/update")]
    [DisplayName("边缘计算-脚本-修改")]
    public async Task Update(string sn, EdgeComputingScriptUpdateInput input)
    {
        GatewayConfig? config = await _mqttServer.LoadDataFromJsonFileAsync<GatewayConfig>($"{sn}.json");
        if (config == null)
        {
            throw Oops.Oh("请先同步配置");
        }
        var edgeComputingScript = config.Script.FirstOrDefault(f => f.Name == input.Name);
        if (edgeComputingScript == null)
            throw Oops.Oh($"脚本名称:【{input.Name}】,不存在！");
        edgeComputingScript.Name = input.Name;
        edgeComputingScript.Content = input.Content;
        edgeComputingScript.Describe = input.Describe;
        await _mqttServer.SaveDataToJsonFileAsync($"{sn}.json", config);
    }

    /// <summary>
    ///     边缘计算-脚本-删除
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/edgeComputing/{sn}/script/delete")]
    [DisplayName("边缘计算-脚本-删除")]
    public async Task Delete(string sn, BaseId<List<long>> input)
    {
        GatewayConfig? config = await _mqttServer.LoadDataFromJsonFileAsync<GatewayConfig>($"{sn}.json");
        if (config == null)
        {
            throw Oops.Oh("请先同步配置");
        }
        var edgeComputingScriptList = config.Script.Where(f => input.Id.Contains(f.Id)).ToList();
        foreach (var edgeComputingScript in edgeComputingScriptList)
        {
            var scriptExecutionStrategy = config.ScriptStrategy.FirstOrDefault(f => f.EdgeComputingScriptId == edgeComputingScript.Id);
            if (scriptExecutionStrategy != null)
                throw Oops.Oh($"【{edgeComputingScript.Name}】 正在使用中,请先删除对应策略");
        }
        config.Script.RemoveAll(f => input.Id.Contains(f.Id));
        await _mqttServer.SaveDataToJsonFileAsync($"{sn}.json", config);
    }

    /// <summary>
    ///     边缘计算-脚本-启用/禁用
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/edgeComputing/{sn}/script/enable")]
    [DisplayName("边缘计算-脚本-启用/禁用")]
    public async Task<bool> Enable(string sn, EnableInput<List<long>> input)
    {
        GatewayConfig? config = await _mqttServer.LoadDataFromJsonFileAsync<GatewayConfig>($"{sn}.json");
        if (config == null)
        {
            throw Oops.Oh("请先同步配置");
        }
        var edgeComputingScriptList = config.Script.Where(w => input.Id.Contains(w.Id)).ToList();
        foreach (var edgeComputingScript in edgeComputingScriptList)
        {
            edgeComputingScript.Enable = input.Enable;
        }
        await _mqttServer.SaveDataToJsonFileAsync($"{sn}.json", config);
        return true;
    }

    /// <summary>
    ///     导入边缘计算-脚本
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/edgeComputing/{sn}/script/inPort")]
    [UnitOfWork]
    [DisplayName("导入边缘计算-脚本")]
    public async Task<DeviceVariableOutput> InPort(string sn, [FromForm] ComputingScriptInPortInput input)
    {
        GatewayConfig? config = await _mqttServer.LoadDataFromJsonFileAsync<GatewayConfig>($"{sn}.json");
        if (config == null)
        {
            throw Oops.Oh("请先同步配置");
        }
        var stream = new MemoryStream();
        await input.File.CopyToAsync(stream);
        // 返回结果
        var result = new DeviceVariableOutput { ErrorColumn = new List<ErrorColumn>() };
        // 需要新增的属性
        var computingScriptList = new List<Script>();
        // 当前所在行
        var line = 2;
        foreach (var computingScriptInPort in await stream.QueryAsync<ComputingScriptInPortDto>())
            try
            {
                //检查必填项
                var success = CheckInPortData(computingScriptInPort, line, result);
                if (!success)
                    continue;
                var computingScript = computingScriptInPort.Adapt<Script>();
                computingScript.Id = YitIdHelper.NextId();
                computingScriptList.Add(computingScript);
                line++;
            }
            catch (Exception e)
            {
                result.ErrorCount += 1;
                result.ErrorColumn.Add(new ErrorColumn { Error = $"第【{line}】行 Error:【{e.Message}】 " });
            }

        // 查找出来相同名称脚本
        var computingScriptAny = config.Script
            .Where(w => computingScriptList.Select(s => s.Name).Contains(w.Name))
            .ToList();

        foreach (var computingScript in computingScriptAny)
        {
            var deviceVariableVal = computingScriptList.FirstOrDefault(f => f.Name == computingScript.Name);
            if (deviceVariableVal == null)
                continue;
            if (input.InPortType == GatewayDeviceVariableInPortTypeEnum.OverLook)
            {
                computingScriptList.Remove(deviceVariableVal);
            }
            else
            {
                //覆盖原始数据
                //1.覆盖属性
                //1.1 移除对象,修改Id后重新加入
                computingScriptList.Remove(deviceVariableVal);
                deviceVariableVal.Id = computingScript.Id;
                computingScriptList.Add(deviceVariableVal);
            }
        }

        if (computingScriptList.Any())
            await _mqttServer.SaveDataToJsonFileAsync($"{sn}.json", config);
        result.SuccessCount = line - 1 - result.ErrorCount < 0 ? 0 : line - 2 - result.ErrorCount;
        return result;
    }

    /// <summary>
    ///     导出边缘计算-脚本
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/edgeComputing/{sn}/script/exPort")]
    [DisplayName("导出边缘计算-脚本")]
    public async Task<IActionResult> ExPort(string sn, BaseId<List<long>> input)
    {
        GatewayConfig? config = await _mqttServer.LoadDataFromJsonFileAsync<GatewayConfig>($"{sn}.json");
        if (config == null)
        {
            throw Oops.Oh("请先同步配置");
        }

        var computingScriptList = config.Script.Where(f => input.Id.Contains(f.Id)).ToList();

        var value = new
        {
            script = computingScriptList.Select(s => new
            {
                s.Name,
                s.Content,
                s.Describe,
                s.Enable
            })
        };

        try
        {
            var memoryStream = new MemoryStream();
            await memoryStream.SaveAsByTemplateAsync("Templates/EdgeComputingScriptImport.xlsx", value);
            memoryStream.Seek(0, SeekOrigin.Begin);

            return new FileStreamResult(memoryStream, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
            {
                FileDownloadName = $"{DateTime.Now:yyyyMMddHHmmssfff}.xlsx"
            };
        }
        catch (Exception e)
        {
            throw Oops.Bah($"导出失败：{e.Message}");
        }
    }

    /// <summary>
    ///     检查属性导入必填项
    /// </summary>
    private bool CheckInPortData(ComputingScriptInPortDto deviceVariableExPort, int line, DeviceVariableOutput result)
    {
        if (string.IsNullOrEmpty(deviceVariableExPort.ScriptName))
        {
            result.ErrorCount += 1;
            result.ErrorColumn.Add(new ErrorColumn { Error = $"第【{line}】行 【ScriptName】 不能是空！ ", Line = line });
            return false;
        }

        if (string.IsNullOrEmpty(deviceVariableExPort.Content))
        {
            result.ErrorCount += 1;
            result.ErrorColumn.Add(new ErrorColumn { Error = $"第【{line}】行 【Content】 不能是空！ ", Line = line });
            return false;
        }

        return true;
    }
}