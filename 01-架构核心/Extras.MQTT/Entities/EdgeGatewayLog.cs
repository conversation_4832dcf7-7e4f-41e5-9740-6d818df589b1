// using IotPlatform.Thing.Entity;
// using Systems.Entity;

// namespace FengLink_Application_Entity.Model;

// /// <summary>
// ///     边缘网关日志
// /// </summary>
// [SugarTable("EdgeGatewayLog", "边缘网关日志")]
// public class EdgeGatewayLog : EntityTenant
// {
//     /// <summary>
//     ///     边缘网关Id
//     /// </summary>
//     [SugarColumn(ColumnDescription = "边缘网关Id")]
//     public long EdgeGatewayId { get; set; }

//     /// <summary>
//     /// 日志内容
//     /// </summary>
//     [SugarColumn(ColumnDescription = "日志内容", IsJson = true, ColumnDataType = "longtext,text,clob")]
//     public dynamic? Message { get; set; }

//     /// <summary>
//     /// 日志类型:
//     /// </summary>
//     [SugarColumn(ColumnDescription = "日志级别")]
//     public string Type { get; set; }

//     /// <summary>
//     ///     创建时间
//     /// </summary>
//     [SugarColumn(ColumnDescription = "创建时间", IsOnlyIgnoreUpdate = true)]
//     public System.DateTime? CreatedTime { get; set; }

//     #region 关联表

//     /// <summary>
//     ///     边缘网关
//     /// </summary>
//     [SugarColumn(IsIgnore = true)]
//     [Navigate(NavigateType.OneToOne, nameof(EdgeGatewayId))]
//     public ModelThing? EdgeGateway { get; set; }


//     #endregion
// }