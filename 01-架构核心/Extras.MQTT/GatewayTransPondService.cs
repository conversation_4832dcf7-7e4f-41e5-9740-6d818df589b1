using System.ComponentModel;
using Common.Models;
using Extras.DatabaseAccessor.SqlSugar.Repositories;
using Extras.MQTT.Dto;
using Extras.MQTT.Models;
using Furion.DependencyInjection;
using Furion.DynamicApiController;
using Mapster;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Yitter.IdGenerator;

namespace Extras.MQTT;

/// <summary>
///     网关转发配置
/// </summary>
public class GatewayTransPondService : ITransient, IDynamicApiController
{
    private readonly MqttService _mqttServer;

    public GatewayTransPondService(MqttService mqttServer)
    {
        _mqttServer = mqttServer;
    }

    /// <summary>
    ///     全部转发配置（不分页）
    /// </summary>
    /// <returns></returns>
    [HttpGet("/transPond/{sn}/list")]
    [DisplayName("网关转发-集合")]
    public async Task<List<TransPond>> List(string sn)
    {
        GatewayConfig? config = await _mqttServer.LoadDataFromJsonFileAsync<GatewayConfig>($"{sn}.json");
        if (config == null)
        {
            throw Oops.Oh("网关暂未同步");
        }
        var transPondList = config.TransPond.ToList();
        return transPondList;
    }

    /// <summary>
    ///     转发配置属性详情
    /// </summary>
    /// <returns></returns>
    [HttpGet("/transPond/{sn}/detail")]
    [DisplayName("网关转发-详情")]
    public async Task<TransPond> Detail(string sn, [FromQuery] BaseId input)
    {
        GatewayConfig? config = await _mqttServer.LoadDataFromJsonFileAsync<GatewayConfig>($"{sn}.json");
        if (config == null)
        {
            throw Oops.Oh("网关暂未同步");
        }
        var transPond = config.TransPond.FirstOrDefault(f => f.Id == input.Id);
        if (transPond == null)
            throw Oops.Oh("转发未配置！");
        return transPond;
    }

    /// <summary>
    ///     创建转发配置
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/transPond/{sn}/add")]
    [DisplayName("网关转发-新增")]
    public async Task<long> Add(string sn, TransPondAddInput input)
    {
        GatewayConfig? config = await _mqttServer.LoadDataFromJsonFileAsync<GatewayConfig>($"{sn}.json");
        if (config == null)
        {
            throw Oops.Oh("网关暂未同步");
        }
        var transPond = config.TransPond.FirstOrDefault(f => f.Identifier == input.Identifier);
        if (transPond != null)
            throw Oops.Oh("标识符已经存在");

        //
        transPond = input.Adapt<TransPond>();
        transPond.Id = YitIdHelper.NextId();
        // 默认第一个是master节点
        if (config.TransPond.Count == 0)
            transPond.Master = true;
        // 如果mqtt节点的处理
        switch (transPond.TransPondType)
        {
            case TransPondTypeEnum.Mqtt:
                {
                    transPond.Config = JsonConvert.SerializeObject(input.MqttConfModel);
                    transPond.TransPondTopic ??= new();
                    transPond.TransPondTopic = InitTransPondTopic(transPond, input.MqttConfModel);
                    break;
                }
        }

        config.TransPond.Add(transPond);
        await _mqttServer.SaveDataToJsonFileAsync($"{sn}.json", config);
        return transPond.Id;
    }

    /// <summary>
    ///     修改转发配置
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/transPond/{sn}/update")]
    [DisplayName("网关转发-修改")]
    public async Task Update(string sn, TransPondUpdateInput input)
    {
        GatewayConfig? config = await _mqttServer.LoadDataFromJsonFileAsync<GatewayConfig>($"{sn}.json");
        if (config == null)
        {
            throw Oops.Oh("网关暂未同步");
        }
        var transPond = config.TransPond.FirstOrDefault(f => f.Identifier == input.Identifier);
        if (transPond == null)
            throw Oops.Oh("转发不存在！");

        switch (transPond.TransPondType)
        {
            case TransPondTypeEnum.Mqtt:
                {
                    transPond.Config = JsonConvert.SerializeObject(input.MqttConfModel);
                    // 初始化Topic，保证配置都是最新的
                    transPond.TransPondTopic.AddRange(InitTransPondTopic(transPond, input.MqttConfModel));
                    break;
                }
        }
        transPond = input.Adapt<TransPond>();
        transPond.Enable = input.Enable;
        await _mqttServer.SaveDataToJsonFileAsync($"{sn}.json", config);
    }

    /// <summary>
    ///     转发配置启用/禁用
    /// </summary>
    /// <param name="input"></param>
    [HttpPost("/transPond/{sn}/enable")]
    [DisplayName("网关转发-启用/禁用")]
    public async Task Enable(string sn, EnableInput<long> input)
    {
        GatewayConfig? config = await _mqttServer.LoadDataFromJsonFileAsync<GatewayConfig>($"{sn}.json");
        if (config == null)
        {
            throw Oops.Oh("网关暂未同步");
        }
        var transPond = config.TransPond.FirstOrDefault(f => f.Id == input.Id);
        if (transPond == null)
            throw Oops.Oh("转发不存在！");

        switch (transPond.TransPondType)
        {
            case TransPondTypeEnum.Mqtt:
                {
                    var mqttConfModel = JsonConvert.DeserializeObject<MqttConfModel>(transPond.Config);
                    // 初始化Topic，保证配置都是最新的
                    transPond.TransPondTopic.AddRange(InitTransPondTopic(transPond, mqttConfModel));
                    break;
                }
        }

        transPond.Enable = input.Enable;
        await _mqttServer.SaveDataToJsonFileAsync($"{sn}.json", config);
    }

    /// <summary>
    ///     转发配置删除
    /// </summary>
    /// <param name="input"></param>
    [HttpPost("/transPond/{sn}/delete")]
    [DisplayName("网关转发-删除")]
    public async Task Delete(string sn, BaseId input)
    {
        GatewayConfig? config = await _mqttServer.LoadDataFromJsonFileAsync<GatewayConfig>($"{sn}.json");
        if (config == null)
        {
            throw Oops.Oh("网关暂未同步");
        }
        var transPond = config.TransPond.FirstOrDefault(f => f.Id == input.Id);
        if (transPond == null)
            throw Oops.Oh("转发未配置");
        config.TransPond.Remove(transPond);
        await _mqttServer.SaveDataToJsonFileAsync($"{sn}.json", config);
    }

    #region 私有方法

    /// <summary>
    ///     初始化各平台Topic
    /// </summary>
    /// <param name="transPond"></param>
    /// <param name="input"></param>
    /// <returns></returns>
    private List<TransPondTopic> InitTransPondTopic(TransPond transPond, MqttConfModel input)
    {
        switch (input.IoTPlatformType)
        {
            case IoTPlatformType.ELink:
                {
                    return CreateELink(transPond);
                }
            case IoTPlatformType.SupOS:
                {
                    var authToken = input.MqttConfigExtend.FirstOrDefault(f => f.Name == "AuthToken")?.Value;
                    return CreateSupOS(transPond, authToken);
                }
            default:
                return new List<TransPondTopic>();
        }
    }

    /// <summary>
    ///     ELink平台Topic
    /// </summary>
    /// <param name="transPond"></param>
    /// <returns></returns>
    private List<TransPondTopic> CreateELink(TransPond transPond)
    {
        var topicList = new List<TransPondTopic>();
        // 已经配置过的Topic
        var transPondTopicList = transPond.TransPondTopic.Select(s => s.TransPondTopicPurpose).ToList();

        if (!transPondTopicList.Contains(TransPondTopicPurposeEnum.OffLine))
            topicList.Add(new TransPondTopic
            {
                Topic = $"{transPond.Identifier}/offline/data/pub",
                Description = "离线数据上报",
                TransPondId = transPond.Id,
                TransPondTopicType = TransPondTopicTypeEnum.Pub,
                TransPondTopicPurpose = TransPondTopicPurposeEnum.OffLine,
                Config =
                    "/**\r\n* 将设备自定义topic数据转换为json格式数据, 设备上报数据到物联网平台时调用\r\n* 入参：rawData 发送源数据对象 不能为空\r\n* 出参：jsonObj JSON 对象 不能为空\r\n*/\r\nfunction transformPayload(rawData) {\r\n    var jsonObj = JSON.stringify(rawData) ;\r\n    return jsonObj;\r\n}\r\n\r\nvar data = payload;\r\ntransformPayload(data);"
            });
        if (!transPondTopicList.Contains(TransPondTopicPurposeEnum.OnLine))
            topicList.Add(new TransPondTopic
            {
                Topic = $"{transPond.Identifier}/online/data/pub",
                Description = "实时数据上报",
                TransPondId = transPond.Id,
                TransPondTopicType = TransPondTopicTypeEnum.Pub,
                TransPondTopicPurpose = TransPondTopicPurposeEnum.OnLine,
                Config =
                    "/**\r\n* 将设备自定义topic数据转换为json格式数据, 设备上报数据到物联网平台时调用\r\n* 入参：rawData 发送源数据对象 不能为空\r\n* 出参：jsonObj JSON 对象 不能为空\r\n*/\r\nfunction transformPayload(rawData) {\r\n    var jsonObj = JSON.stringify(rawData) ;\r\n    return jsonObj;\r\n}\r\n\r\nvar data = payload;\r\ntransformPayload(data);"
            });
        if (!transPondTopicList.Contains(TransPondTopicPurposeEnum.SysTime))
            topicList.Add(new TransPondTopic
            {
                Topic = $"{transPond.Identifier}/SystemServices/broker/uptime",
                Description = "时钟同步请求",
                TransPondId = transPond.Id,
                TransPondTopicType = TransPondTopicTypeEnum.Pub,
                TransPondTopicPurpose = TransPondTopicPurposeEnum.SysTime,
                Config = ""
            });
        if (!transPondTopicList.Contains(TransPondTopicPurposeEnum.PubOta))
            topicList.Add(new TransPondTopic
            {
                Topic = $"{transPond.Identifier}/resp/post//ota/cmd",
                Description = "Ota响应",
                TransPondId = transPond.Id,
                TransPondTopicType = TransPondTopicTypeEnum.Pub,
                TransPondTopicPurpose = TransPondTopicPurposeEnum.PubOta,
                Config = ""
            });
        if (!transPondTopicList.Contains(TransPondTopicPurposeEnum.SysTimeSub))
            topicList.Add(new TransPondTopic
            {
                Topic = $"{transPond.Identifier}/SystemServices/broker/uptime_reply",
                Description = "时钟同步响应",
                TransPondId = transPond.Id,
                TransPondTopicType = TransPondTopicTypeEnum.Sub,
                TransPondTopicPurpose = TransPondTopicPurposeEnum.SysTimeSub,
                Config = ""
            });
        if (!transPondTopicList.Contains(TransPondTopicPurposeEnum.Variable))
            topicList.Add(new TransPondTopic
            {
                Topic = $"{transPond.Identifier}/v1/p/write",
                Description = "云端下写属性",
                TransPondId = transPond.Id,
                TransPondTopicType = TransPondTopicTypeEnum.Sub,
                TransPondTopicPurpose = TransPondTopicPurposeEnum.Variable,
                Config = ""
            });
        if (!transPondTopicList.Contains(TransPondTopicPurposeEnum.Share))
            topicList.Add(new TransPondTopic
            {
                Topic = $"{transPond.Identifier}/v1/p/share",
                Description = "云端下写标签",
                TransPondId = transPond.Id,
                TransPondTopicType = TransPondTopicTypeEnum.Sub,
                TransPondTopicPurpose = TransPondTopicPurposeEnum.Share,
                Config = ""
            });

        if (!transPondTopicList.Contains(TransPondTopicPurposeEnum.SubOta))
            topicList.Add(new TransPondTopic
            {
                Topic = $"{transPond.Identifier}/post//ota/cmd",
                Description = "ota升级",
                TransPondId = transPond.Id,
                TransPondTopicType = TransPondTopicTypeEnum.Sub,
                TransPondTopicPurpose = TransPondTopicPurposeEnum.SubOta,
                Config = ""
            });
        if (!transPondTopicList.Contains(TransPondTopicPurposeEnum.Cmd))
            topicList.Add(new TransPondTopic
            {
                Topic = $"{transPond.Identifier}/v1/p/cmd",
                Description = "指令下发",
                TransPondId = transPond.Id,
                TransPondTopicType = TransPondTopicTypeEnum.Sub,
                TransPondTopicPurpose = TransPondTopicPurposeEnum.Cmd,
                Config = ""
            });
        if (!transPondTopicList.Contains(TransPondTopicPurposeEnum.DeviceCmd))
            topicList.Add(new TransPondTopic
            {
                Topic = $"{transPond.Identifier}/v1/p/device/cmd",
                Description = "设备指令下发",
                TransPondId = transPond.Id,
                TransPondTopicType = TransPondTopicTypeEnum.Sub,
                TransPondTopicPurpose = TransPondTopicPurposeEnum.DeviceCmd,
                Config = ""
            });
        if (!transPondTopicList.Contains(TransPondTopicPurposeEnum.SubDeviceResource))
            topicList.Add(new TransPondTopic
            {
                Topic = $"{transPond.Identifier}/v1/p/device/resource",
                Description = "资源库下发",
                TransPondId = transPond.Id,
                TransPondTopicType = TransPondTopicTypeEnum.Sub,
                TransPondTopicPurpose = TransPondTopicPurposeEnum.SubDeviceResource,
                Config = ""
            });
        if (!transPondTopicList.Contains(TransPondTopicPurposeEnum.PubDeviceResource))
            topicList.Add(new TransPondTopic
            {
                Topic = $"{transPond.Identifier}/v1/s/device/resource",
                Description = "资源库上报",
                TransPondId = transPond.Id,
                TransPondTopicType = TransPondTopicTypeEnum.Pub,
                TransPondTopicPurpose = TransPondTopicPurposeEnum.PubDeviceResource,
                Config = ""
            });

        return topicList;
    }

    /// <summary>
    ///     SupOS平台Topic
    /// </summary>
    /// <param name="transPond">转发配置</param>
    /// <param name="authToken">token</param>
    /// <returns></returns>
    private List<TransPondTopic> CreateSupOS(TransPond transPond, string authToken)
    {
        var topicList = new List<TransPondTopic>();
        // 已经配置过的Topic
        var transPondTopicList = transPond.TransPondTopic.Select(s => s.TransPondTopicPurpose).ToList();
        if (!transPondTopicList.Contains(TransPondTopicPurposeEnum.Meta))
            topicList.Add(new TransPondTopic
            {
                Topic = $"/{authToken}/{transPond.Id}/{transPond.Identifier}/metatag/retain",
                Description = "设备属性上报",
                TransPondId = transPond.Id,
                TransPondTopicType = TransPondTopicTypeEnum.Pub,
                TransPondTopicPurpose = TransPondTopicPurposeEnum.Meta,
                Config = ""
            });
        if (!transPondTopicList.Contains(TransPondTopicPurposeEnum.OnLine))
            topicList.Add(new TransPondTopic
            {
                Topic = $"/{authToken}/{transPond.Id}/{transPond.Identifier}/rtdvalue/report",
                Description = "实时数据上报",
                TransPondId = transPond.Id,
                TransPondTopicType = TransPondTopicTypeEnum.Pub,
                TransPondTopicPurpose = TransPondTopicPurposeEnum.OnLine,
                Config = ""
            });
        if (!transPondTopicList.Contains(TransPondTopicPurposeEnum.SysTime))
            topicList.Add(new TransPondTopic
            {
                Topic = $"/{authToken}/{transPond.Id}/{transPond.Identifier}/rtdevent/get",
                Description = "时钟同步请求",
                TransPondId = transPond.Id,
                TransPondTopicType = TransPondTopicTypeEnum.Pub,
                TransPondTopicPurpose = TransPondTopicPurposeEnum.SysTime,
                Config = ""
            });
        if (!transPondTopicList.Contains(TransPondTopicPurposeEnum.OffLine))
            topicList.Add(new TransPondTopic
            {
                Topic = $"/{authToken}/{transPond.Id}/{transPond.Identifier}/cachevalue/report",
                Description = "离线数据上报",
                TransPondId = transPond.Id,
                TransPondTopicType = TransPondTopicTypeEnum.Pub,
                TransPondTopicPurpose = TransPondTopicPurposeEnum.OffLine,
                Config = ""
            });
        if (!transPondTopicList.Contains(TransPondTopicPurposeEnum.MetaPush))
            topicList.Add(new TransPondTopic
            {
                Topic = $"/{authToken}/{transPond.Id}/{transPond.Identifier}/metatag/push",
                Description = "云端要求同步设备属性",
                TransPondId = transPond.Id,
                TransPondTopicType = TransPondTopicTypeEnum.Sub,
                TransPondTopicPurpose = TransPondTopicPurposeEnum.MetaPush,
                Config = ""
            });
        if (!transPondTopicList.Contains(TransPondTopicPurposeEnum.SysTimeSub))
            topicList.Add(new TransPondTopic
            {
                Topic = $"/{authToken}/{transPond.Id}/{transPond.Identifier}/rtdevent/get_reply",
                Description = "时钟同步响应",
                TransPondId = transPond.Id,
                TransPondTopicType = TransPondTopicTypeEnum.Sub,
                TransPondTopicPurpose = TransPondTopicPurposeEnum.SysTimeSub,
                Config = ""
            });
        if (!transPondTopicList.Contains(TransPondTopicPurposeEnum.Variable))
            topicList.Add(new TransPondTopic
            {
                Topic = $"/{authToken}/{transPond.Id}/{transPond.Identifier}/rtdnotify/push",
                Description = "属性下写",
                TransPondId = transPond.Id,
                TransPondTopicType = TransPondTopicTypeEnum.Sub,
                TransPondTopicPurpose = TransPondTopicPurposeEnum.Variable,
                Config = ""
            });
        if (!transPondTopicList.Contains(TransPondTopicPurposeEnum.MetaPushReply))
            topicList.Add(new TransPondTopic
            {
                Topic = $"/{authToken}/{transPond.Id}/{transPond.Identifier}/metatag/push_reply",
                Description = "云端要求同步设备属性回复",
                TransPondId = transPond.Id,
                TransPondTopicType = TransPondTopicTypeEnum.Pub,
                TransPondTopicPurpose = TransPondTopicPurposeEnum.MetaPushReply,
                Config = ""
            });
        if (!transPondTopicList.Contains(TransPondTopicPurposeEnum.CloudRequestRefreshRealTimeDeviceData))
            topicList.Add(new TransPondTopic
            {
                Topic = $"/{authToken}/{transPond.Id}/{transPond.Identifier}/rtdvalue/push",
                Description = "云端要求刷新设备实时数据",
                TransPondId = transPond.Id,
                TransPondTopicType = TransPondTopicTypeEnum.Sub,
                TransPondTopicPurpose = TransPondTopicPurposeEnum.CloudRequestRefreshRealTimeDeviceData,
                Config = ""
            });
        if (!transPondTopicList.Contains(TransPondTopicPurposeEnum.CloudRequestRefreshRealTimeDeviceDataReply))
            topicList.Add(new TransPondTopic
            {
                Topic = $"/{authToken}/{transPond.Id}/{transPond.Identifier}/rtdvalue/push_reply",
                Description = "云端要求刷新设备实时数据回复",
                TransPondId = transPond.Id,
                TransPondTopicType = TransPondTopicTypeEnum.Pub,
                TransPondTopicPurpose = TransPondTopicPurposeEnum.CloudRequestRefreshRealTimeDeviceDataReply,
                Config = ""
            });
        return topicList;
    }

    #endregion
}